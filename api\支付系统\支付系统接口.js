/**
 * 支付系统接口
 * 管理充值、会员、订单、金币等支付相关功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 💰 金币管理接口
// ================================

/**
 * 获取用户金币余额
 */
export async function 获取用户金币余额() {
	return await apiRequest('payment/coin-balance');
}

/**
 * 获取金币充值套餐
 */
export async function 获取金币充值套餐() {
	return await apiRequest('payment/coin-packages');
}

/**
 * 购买金币套餐
 * @param {Object} purchaseData - 购买数据
 */
export async function 购买金币套餐(purchaseData) {
	return await apiRequest('payment/purchase-coins', {
		method: 'POST',
		body: purchaseData
	});
}

/**
 * 获取金币消费记录
 * @param {Object} params - 查询参数
 */
export async function 获取金币消费记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/coin-history?${queryParams}`);
}

/**
 * 扣除金币
 * @param {Object} deductData - 扣费数据
 */
export async function 扣除金币(deductData) {
	return await apiRequest('payment/deduct-coins', {
		method: 'POST',
		body: deductData
	});
}

/**
 * 退还金币
 * @param {Object} refundData - 退还数据
 */
export async function 退还金币(refundData) {
	return await apiRequest('payment/refund-coins', {
		method: 'POST',
		body: refundData
	});
}

// ================================
// 👑 会员管理接口
// ================================

/**
 * 获取用户会员信息
 */
export async function 获取用户会员信息() {
	return await apiRequest('payment/membership-info');
}

/**
 * 获取会员套餐
 */
export async function 获取会员套餐() {
	return await apiRequest('payment/membership-packages');
}

/**
 * 购买会员套餐
 * @param {Object} purchaseData - 购买数据
 */
export async function 购买会员套餐(purchaseData) {
	return await apiRequest('payment/purchase-membership', {
		method: 'POST',
		body: purchaseData
	});
}

/**
 * 获取会员权益
 */
export async function 获取会员权益() {
	return await apiRequest('payment/membership-benefits');
}

/**
 * 获取会员消费记录
 * @param {Object} params - 查询参数
 */
export async function 获取会员消费记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/membership-history?${queryParams}`);
}

/**
 * 取消会员自动续费
 */
export async function 取消会员自动续费() {
	return await apiRequest('payment/cancel-auto-renewal', {
		method: 'POST'
	});
}

/**
 * 开启会员自动续费
 */
export async function 开启会员自动续费() {
	return await apiRequest('payment/enable-auto-renewal', {
		method: 'POST'
	});
}

// ================================
// 📋 订单管理接口
// ================================

/**
 * 创建支付订单
 * @param {Object} orderData - 订单数据
 */
export async function 创建支付订单(orderData) {
	return await apiRequest('payment/create-order', {
		method: 'POST',
		body: orderData
	});
}

/**
 * 获取订单详情
 * @param {string} orderId - 订单ID
 */
export async function 获取订单详情(orderId) {
	return await apiRequest(`payment/order-detail?orderId=${orderId}`);
}

/**
 * 获取订单列表
 * @param {Object} params - 查询参数
 */
export async function 获取订单列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/orders?${queryParams}`);
}

/**
 * 查询订单状态
 * @param {string} orderId - 订单ID
 */
export async function 查询订单状态(orderId) {
	return await apiRequest(`payment/order-status?orderId=${orderId}`);
}

/**
 * 取消订单
 * @param {string} orderId - 订单ID
 * @param {string} reason - 取消原因
 */
export async function 取消订单(orderId, reason) {
	return await apiRequest('payment/cancel-order', {
		method: 'POST',
		body: { orderId, reason }
	});
}

/**
 * 申请退款
 * @param {Object} refundData - 退款数据
 */
export async function 申请订单退款(refundData) {
	return await apiRequest('payment/request-refund', {
		method: 'POST',
		body: refundData
	});
}

// ================================
// 💳 支付方式接口
// ================================

/**
 * 获取支持的支付方式
 */
export async function 获取支持的支付方式() {
	return await apiRequest('payment/payment-methods');
}

/**
 * 发起支付
 * @param {Object} paymentData - 支付数据
 */
export async function 发起支付(paymentData) {
	return await apiRequest('payment/initiate-payment', {
		method: 'POST',
		body: paymentData
	});
}

/**
 * 查询支付状态
 * @param {string} paymentId - 支付ID
 */
export async function 查询支付状态(paymentId) {
	return await apiRequest(`payment/payment-status?paymentId=${paymentId}`);
}

/**
 * 支付回调处理
 * @param {Object} callbackData - 回调数据
 */
export async function 处理支付回调(callbackData) {
	return await apiRequest('payment/payment-callback', {
		method: 'POST',
		body: callbackData
	});
}

// ================================
// 🎫 优惠券接口
// ================================

/**
 * 获取用户优惠券
 * @param {Object} params - 查询参数
 */
export async function 获取用户优惠券(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/user-coupons?${queryParams}`);
}

/**
 * 获取可用优惠券
 * @param {Object} params - 查询参数
 */
export async function 获取可用优惠券(params) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/available-coupons?${queryParams}`);
}

/**
 * 领取优惠券
 * @param {string} couponId - 优惠券ID
 */
export async function 领取优惠券(couponId) {
	return await apiRequest('payment/claim-coupon', {
		method: 'POST',
		body: { couponId }
	});
}

/**
 * 使用优惠券
 * @param {Object} usageData - 使用数据
 */
export async function 使用优惠券(usageData) {
	return await apiRequest('payment/use-coupon', {
		method: 'POST',
		body: usageData
	});
}

// ================================
// 📊 财务统计接口
// ================================

/**
 * 获取消费统计
 * @param {Object} params - 统计参数
 */
export async function 获取消费统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/spending-stats?${queryParams}`);
}

/**
 * 获取充值统计
 * @param {Object} params - 统计参数
 */
export async function 获取充值统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`payment/recharge-stats?${queryParams}`);
}

/**
 * 获取月度账单
 * @param {string} month - 月份 (YYYY-MM)
 */
export async function 获取月度账单(month) {
	return await apiRequest(`payment/monthly-bill?month=${month}`);
}

// ================================
// 🔄 自动续费接口
// ================================

/**
 * 获取自动续费设置
 */
export async function 获取自动续费设置() {
	return await apiRequest('payment/auto-renewal-settings');
}

/**
 * 更新自动续费设置
 * @param {Object} settings - 续费设置
 */
export async function 更新自动续费设置(settings) {
	return await apiRequest('payment/auto-renewal-settings', {
		method: 'PUT',
		body: settings
	});
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整充值流程
 * @param {string} packageId - 套餐ID
 * @param {string} paymentMethod - 支付方式
 * @param {Object} options - 选项
 */
export async function 完整充值流程(packageId, paymentMethod, options = {}) {
	try {
		// 1. 创建订单
		const order = await 创建支付订单({
			type: 'coins',
			packageId,
			paymentMethod,
			...options
		});
		
		// 2. 发起支付
		const payment = await 发起支付({
			orderId: order.data.orderId,
			paymentMethod,
			...options
		});
		
		return {
			success: true,
			data: {
				order: order.data,
				payment: payment.data
			}
		};
		
	} catch (error) {
		console.error('完整充值流程失败:', error);
		throw error;
	}
}

/**
 * 完整会员购买流程
 * @param {string} packageId - 套餐ID
 * @param {string} paymentMethod - 支付方式
 * @param {Object} options - 选项
 */
export async function 完整会员购买流程(packageId, paymentMethod, options = {}) {
	try {
		// 1. 创建订单
		const order = await 创建支付订单({
			type: 'membership',
			packageId,
			paymentMethod,
			...options
		});
		
		// 2. 发起支付
		const payment = await 发起支付({
			orderId: order.data.orderId,
			paymentMethod,
			...options
		});
		
		return {
			success: true,
			data: {
				order: order.data,
				payment: payment.data
			}
		};
		
	} catch (error) {
		console.error('完整会员购买流程失败:', error);
		throw error;
	}
}

/**
 * 轮询支付状态直到完成
 * @param {string} orderId - 订单ID
 * @param {Function} onProgress - 进度回调
 * @param {number} maxAttempts - 最大尝试次数
 */
export async function 轮询支付状态直到完成(orderId, onProgress, maxAttempts = 30) {
	let attempts = 0;
	
	while (attempts < maxAttempts) {
		try {
			const result = await 查询订单状态(orderId);
			
			if (onProgress) {
				onProgress(result.data);
			}
			
			if (result.data.status === 'paid' || result.data.status === 'completed') {
				return {
					success: true,
					data: result.data
				};
			}
			
			if (result.data.status === 'failed' || result.data.status === 'cancelled') {
				throw new Error(`支付失败: ${result.data.failureReason || '未知原因'}`);
			}
			
			// 等待2秒后重试
			await new Promise(resolve => setTimeout(resolve, 2000));
			attempts++;
			
		} catch (error) {
			console.error(`轮询支付状态失败 (第${attempts + 1}次):`, error);
			attempts++;
			
			if (attempts >= maxAttempts) {
				throw new Error('支付状态查询超时');
			}
			
			// 等待2秒后重试
			await new Promise(resolve => setTimeout(resolve, 2000));
		}
	}
	
	throw new Error('支付状态查询超时');
}

/**
 * 检查创作费用并扣费
 * @param {Object} creationParams - 创作参数
 */
export async function 检查创作费用并扣费(creationParams) {
	try {
		// 1. 获取用户金币余额
		const balance = await 获取用户金币余额();
		
		// 2. 计算创作费用（这里需要根据实际业务逻辑计算）
		const cost = 计算创作费用(creationParams);
		
		// 3. 检查余额是否足够
		if (balance.data.balance < cost) {
			return {
				success: false,
				errorCode: 'INSUFFICIENT_BALANCE',
				message: '金币余额不足',
				data: {
					required: cost,
					current: balance.data.balance,
					shortage: cost - balance.data.balance
				}
			};
		}
		
		// 4. 扣除金币
		const deductResult = await 扣除金币({
			amount: cost,
			reason: '音乐创作',
			relatedData: creationParams
		});
		
		return {
			success: true,
			data: {
				cost,
				remainingBalance: deductResult.data.remainingBalance,
				transactionId: deductResult.data.transactionId
			}
		};
		
	} catch (error) {
		console.error('检查创作费用并扣费失败:', error);
		throw error;
	}
}

/**
 * 计算创作费用
 * @param {Object} creationParams - 创作参数
 */
function 计算创作费用(creationParams) {
	// 这里根据实际业务逻辑计算费用
	// 例如：简单模式10金币，高级模式20金币，纯音乐15金币
	const baseCosts = {
		'simple': 10,
		'advanced': 20,
		'instrumental': 15
	};
	
	return baseCosts[creationParams.mode] || 10;
}

export default {
	获取用户金币余额,
	获取金币充值套餐,
	购买金币套餐,
	获取金币消费记录,
	扣除金币,
	退还金币,
	获取用户会员信息,
	获取会员套餐,
	购买会员套餐,
	获取会员权益,
	获取会员消费记录,
	取消会员自动续费,
	开启会员自动续费,
	创建支付订单,
	获取订单详情,
	获取订单列表,
	查询订单状态,
	取消订单,
	申请订单退款,
	获取支持的支付方式,
	发起支付,
	查询支付状态,
	处理支付回调,
	获取用户优惠券,
	获取可用优惠券,
	领取优惠券,
	使用优惠券,
	获取消费统计,
	获取充值统计,
	获取月度账单,
	获取自动续费设置,
	更新自动续费设置,
	完整充值流程,
	完整会员购买流程,
	轮询支付状态直到完成,
	检查创作费用并扣费
};
