# Project Coding Rules (Non-Obvious Only)

## Critical Implementation Patterns
- Always use `platformAdapter.getPlatformType()` instead of direct platform checks - provides unified detection
- API calls must use Chinese function names from `api/index.js` (e.g., `音乐创作完整业务流程`, `检查创作费用并扣费`)
- All creation workflows require payment check via `检查创作费用并扣费()` before execution
- Router navigation MUST use `utils/router.js` enhanced methods - uni.navigateTo is overridden with H5 fallbacks

## Hidden Dependencies
- `mixins/platform-adaptive.js` auto-injected into ALL components - provides `$platform`, `$isMobile`, `$safeAreaStyle`
- Global scroll fix in `utils/simple-scroll-fix.js` auto-applies on DOM ready - modifies all scroll behavior
- Touch directive `v-touch-scroll` is intentionally disabled (line 139 in touch-scroll-directive.js)
- CSS custom properties set at runtime by platform adapter (--status-bar-height, --safe-area-inset-*)

## Required Import Patterns
- Platform detection: `import { platformAdapter } from '@/utils/platform.js'`
- API functions: `import { 音乐创作完整业务流程, 检查创作费用并扣费 } from '@/api/index.js'`
- Enhanced navigation: `import { safeNavigateTo } from '@/utils/router.js'`
- Adaptive components: Use `AdaptiveScrollView` instead of raw `scroll-view`

## Critical File Locations
- Entry point: `main.js` (not `src/main.js`) - uni-app convention
- Routes: `pages.json` defines all navigation (not Vue Router)
- Global styles: `uni.scss` uses uni-app variables, not standard CSS
- Static assets: `static/` directory (not `public/`)

## Platform Conditional Syntax
```javascript
// #ifdef H5
// H5-specific code
// #endif

// #ifdef APP-PLUS
// App-specific code  
// #endif

// #ifndef H5
// Non-H5 code (App + MiniProgram)
// #endif