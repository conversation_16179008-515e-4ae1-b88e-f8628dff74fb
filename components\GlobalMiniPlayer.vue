<template>
	<MiniPlayer
		v-if="showMiniPlayer"
		:show="true"
		:currentMusic="currentMusic"
		:isPlaying="isPlaying"
		:progress="progress"
		@expand="expandPlayer"
		@close="closeMiniPlayer"
		@toggle-play="togglePlay"
	/>
</template>

<script>
import MiniPlayer from './MusicPlayer/MiniPlayer.vue';
import globalPlayer from '@/utils/globalPlayer.js';

export default {
	name: 'GlobalMiniPlayer',
	components: {
		MiniPlayer
	},
	data() {
		return {
			showMiniPlayer: false,
			miniPlayerClosed: false,
			currentMusic: {
				id: 0,
				title: '未知歌曲',
				artist: '未知艺术家',
				cover: '/static/images/default-music-cover.jpg',
				audioUrl: ''
			},
			isPlaying: false,
			progress: 0
		}
	},
	mounted() {
		console.log('🎵 [GlobalMiniPlayer] 组件已挂载');
		
		// 从localStorage读取关闭状态
		const closed = uni.getStorageSync('miniPlayerClosed');
		this.miniPlayerClosed = closed === 'true';
		console.log('读取迷你播放器关闭状态:', this.miniPlayerClosed);
		
		// 设置globalPlayer监听器
		this.setupGlobalPlayerListeners();
		
		// 初始同步
		this.syncWithGlobalPlayer();
	},
	methods: {
		// 设置globalPlayer监听器
		setupGlobalPlayerListeners() {
			console.log('🎧 [GlobalMiniPlayer] 设置globalPlayer监听器');

			// 监听播放状态变化
			globalPlayer.on('play', () => {
				console.log('▶️ [GlobalMiniPlayer] 播放事件触发');
				this.isPlaying = true;
				// 播放时重置关闭状态，显示迷你播放器
				this.miniPlayerClosed = false;
				uni.setStorageSync('miniPlayerClosed', 'false');
				this.showMiniPlayer = true;
				this.syncWithGlobalPlayer();
			});

			globalPlayer.on('pause', () => {
				console.log('⏸️ [GlobalMiniPlayer] 暂停事件触发');
				this.isPlaying = false;
			});

			// 监听时间更新
			globalPlayer.on('timeUpdate', (data) => {
				this.progress = (data.currentTime / data.duration) * 100;
			});

			// 监听歌曲切换
			globalPlayer.on('musicChange', (data) => {
				console.log('🎵 [GlobalMiniPlayer] 歌曲切换:', data.music.title);
				this.currentMusic = {
					id: data.music.id,
					title: data.music.title || '未知歌曲',
					artist: data.music.artist || '未知艺术家',
					cover: data.music.cover || '/static/images/default-music-cover.jpg',
					audioUrl: data.music.audioUrl
				};
			});
		},

		// 同步globalPlayer状态
		syncWithGlobalPlayer() {
			console.log('🔄 [GlobalMiniPlayer] 同步globalPlayer状态');
			const state = globalPlayer.getState();
			console.log('📊 [GlobalMiniPlayer] globalPlayer状态:', state);

			// 只要有音乐且用户没有手动关闭，就显示全局MiniPlayer
			if ((state.currentMusic || globalPlayer.audioContext) && !this.miniPlayerClosed) {
				console.log('✅ [GlobalMiniPlayer] 显示MiniPlayer');
				this.showMiniPlayer = true;
				
				// 确保正确复制音乐信息（处理Proxy对象）
				if (state.currentMusic) {
					this.currentMusic = {
						id: state.currentMusic.id,
						title: state.currentMusic.title || '未知歌曲',
						artist: state.currentMusic.artist || '未知艺术家',
						cover: state.currentMusic.cover || '/static/images/default-music-cover.jpg',
						audioUrl: state.currentMusic.audioUrl
					};
					console.log('✅ [GlobalMiniPlayer] 音乐信息已复制:', this.currentMusic.title);
				}
				
				this.isPlaying = state.isPlaying;
				this.progress = state.progress || 0;
			} else {
				this.showMiniPlayer = false;
				console.log('❌ [GlobalMiniPlayer] 隐藏迷你播放器');
			}
		},

		// 展开为全屏播放器
		expandPlayer() {
			console.log('📱 [GlobalMiniPlayer] 展开为全屏播放器');
			uni.navigateTo({
				url: `/pages/music/player/index?id=${this.currentMusic.id}`
			});
		},

		// 关闭迷你播放器
		closeMiniPlayer() {
			console.log('❌ [GlobalMiniPlayer] 用户手动关闭迷你播放器');
			this.showMiniPlayer = false;
			this.miniPlayerClosed = true;
			// 保存关闭状态到localStorage
			uni.setStorageSync('miniPlayerClosed', 'true');
			console.log('已保存关闭状态到localStorage');
			// 停止播放
			globalPlayer.stop();
		},

		// 切换播放/暂停
		togglePlay() {
			console.log('⏯️ [GlobalMiniPlayer] 切换播放/暂停');
			if (this.isPlaying) {
				globalPlayer.pause();
			} else {
				globalPlayer.play();
				// 播放时重置关闭状态
				this.miniPlayerClosed = false;
				uni.setStorageSync('miniPlayerClosed', 'false');
			}
		}
	}
}
</script>

<style scoped>
/* 全局MiniPlayer不需要额外样式，使用MiniPlayer组件自己的样式 */
</style>

