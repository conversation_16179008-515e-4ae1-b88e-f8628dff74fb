<template>
  <view class="typewriter-container">
    <!-- 显示当前已经"打印"的文本 -->
    <text class="typewriter-text" :class="{'typewriter-done': isCompleted}">{{ displayText }}</text>
    
    <!-- 打字指示符 -->
    <text v-if="!isCompleted && showCursor" class="typewriter-cursor">|</text>
  </view>
</template>

<script>
export default {
  name: 'TypewriterText',
  props: {
    // 要显示的完整文本
    text: {
      type: String,
      default: ''
    },
    // 每个字符的打字速度(毫秒)
    typingSpeed: {
      type: Number,
      default: 30  // 默认30ms一个字
    },
    // 是否自动开始打字
    autoStart: {
      type: Boolean,
      default: true
    },
    // 是否显示光标
    showCursor: {
      type: Boolean,
      default: true
    },
    // 初始延迟(毫秒)
    initialDelay: {
      type: Number,
      default: 200
    },
    // 打字过程中的通知频率(每打印多少个字符发送一次进度事件)
    progressNotifyInterval: {
      type: Number,
      default: 5  // 默认每5个字符通知一次
    }
  },
  data() {
    return {
      displayText: '', // 当前显示的文本
      currentIndex: 0, // 当前字符索引
      isTyping: false, // 是否正在打字中
      isCompleted: false, // 是否完成打字
      typewriterTimer: null, // 定时器引用
      lastText: '', // 上一次的文本，用于检测变化
      lastProgressNotify: 0 // 上次发送进度通知的索引
    };
  },
  watch: {
    text: {
      handler(newText, oldText) {
        if (newText !== oldText) {
          this.lastText = newText;
          
          // 如果新文本不同，重置打字状态
          this.reset();
          
          // 如果设置了自动开始，则开始打字
          if (this.autoStart && newText) {
            this.startTyping();
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.autoStart && this.text) {
      this.startTyping();
    }
  },
  beforeUnmount() {
    this.stopTyping(); // 清除定时器
  },
  methods: {
    // 开始打字效果
    startTyping() {
      // 如果已经完成或正在打字，则不重复开始
      if (this.isTyping || this.isCompleted) return;
      
      this.isTyping = true;
      this.isCompleted = false;

      // 先等待初始延迟
      setTimeout(() => {
        this.typeNextChar();
      }, this.initialDelay);
      
      // 通知父组件打字开始
      this.$emit('typing-start');
    },
    
    // 打字下一个字符
    typeNextChar() {
      if (this.currentIndex < this.text.length) {
        // 设置定时器打印下一个字符
        this.typewriterTimer = setTimeout(() => {
          // 添加下一个字符
          this.displayText += this.text.charAt(this.currentIndex);
          this.currentIndex++;
          
          // 根据设置的间隔发送进度通知
          if (this.currentIndex - this.lastProgressNotify >= this.progressNotifyInterval) {
            this.notifyProgress();
          }
          
          // 特殊字符处理（添加额外停顿）
          const currentChar = this.text.charAt(this.currentIndex - 1);
          const nextChar = this.text.charAt(this.currentIndex);
          
          // 如果是句子结束（句号、问号、感叹号），则添加额外停顿
          if (['.', '?', '!', '。', '？', '！'].includes(currentChar)) {
            setTimeout(() => {
              this.typeNextChar();
            }, this.typingSpeed * 5); // 句尾停顿时间是正常的5倍
          } 
          // 如果是逗号、分号等，添加短暂停顿
          else if ([',', ';', '，', '；', '、'].includes(currentChar)) {
            setTimeout(() => {
              this.typeNextChar();
            }, this.typingSpeed * 2); // 停顿时间是正常的2倍
          } 
          else {
            // 继续打印下一个字符
            this.typeNextChar();
          }
        }, this.typingSpeed);
      } else {
        // 打字完成
        this.completeTyping();
      }
    },
    
    // 发送进度通知
    notifyProgress() {
      this.lastProgressNotify = this.currentIndex;
      this.$emit('typing-progress', {
        progress: this.currentIndex / this.text.length,
        currentIndex: this.currentIndex,
        currentText: this.displayText
      });
    },
    
    // 停止打字
    stopTyping() {
      if (this.typewriterTimer) {
        clearTimeout(this.typewriterTimer);
        this.typewriterTimer = null;
      }
      this.isTyping = false;
    },
    
    // 立即完成打字
    completeTyping() {
      this.stopTyping();
      this.displayText = this.text;
      this.currentIndex = this.text.length;
      this.isCompleted = true;
      this.isTyping = false;
      
      // 通知父组件打字完成
      this.$emit('typing-complete');
    },
    
    // 重置打字状态
    reset() {
      this.stopTyping();
      this.displayText = '';
      this.currentIndex = 0;
      this.isCompleted = false;
      this.lastProgressNotify = 0;
    },
    
    // 暂停打字
    pauseTyping() {
      if (this.isTyping) {
        this.stopTyping();
        this.$emit('typing-pause');
      }
    },
    
    // 恢复打字
    resumeTyping() {
      if (!this.isTyping && !this.isCompleted) {
        this.startTyping();
        this.$emit('typing-resume');
      }
    }
  }
};
</script>

<style>
.typewriter-container {
  display: inline-block;
  position: relative;
  width: 100%;
}

.typewriter-text {
  word-break: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
  display: inline;
}

.typewriter-cursor {
  display: inline-block;
  opacity: 1;
  animation: blink 0.7s infinite;
  color: #4d9dff;
  font-weight: bold;
  vertical-align: middle;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.typewriter-done + .typewriter-cursor {
  display: none;
}
</style> 