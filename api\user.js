/**
 * 用户相关API
 */

import { request } from './request';

/**
 * 登录
 * @param {Object} data - 登录参数
 * @returns {Promise} 登录结果
 */
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'POST',
    data
  });
}

/**
 * 注册
 * @param {Object} data - 注册参数
 * @returns {Promise} 注册结果
 */
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'POST',
    data
  });
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
export function getUserInfo() {
  return request({
    url: '/user/info'
  });
}

/**
 * 更新用户信息
 * @param {Object} data - 更新的用户信息
 * @returns {Promise} 更新结果
 */
export function updateUserInfo(data) {
  return request({
    url: '/user/info',
    method: 'PUT',
    data
  });
}

/**
 * 获取用户余额
 * @returns {Promise} 用户余额信息
 */
export function getUserBalance() {
  return request({
    url: '/user/balance'
  });
}

/**
 * 检查登录状态
 * @returns {Boolean} 是否已登录
 */
export function checkLogin() {
  return !!uni.getStorageSync('token');
}

/**
 * 退出登录
 */
export function logout() {
  uni.removeStorageSync('token');
  uni.removeStorageSync('userInfo');
}

export default {
  login,
  register,
  getUserInfo,
  updateUserInfo,
  getUserBalance,
  checkLogin,
  logout
}; 