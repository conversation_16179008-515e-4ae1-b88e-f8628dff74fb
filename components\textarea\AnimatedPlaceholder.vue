<template>
  <view 
    class="animated-placeholder"
    :class="{ 'is-focused': isFocused }"
  >
    <text class="placeholder-text">{{ currentPlaceholder }}</text>
  </view>
</template>

<script>
export default {
  name: 'AnimatedPlaceholder',
  props: {
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    isFocused: {
      type: Boolean,
      default: false
    },
    animationSpeed: {
      type: Number,
      default: 3000 // 切换间隔，单位毫秒
    }
  },
  data() {
    return {
      currentPlaceholder: '',
      placeholderIndex: 0,
      placeholderTimer: null
    };
  },
  watch: {
    placeholders: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.currentPlaceholder = newVal[0];
          this.startPlaceholderAnimation();
        } else {
          this.currentPlaceholder = this.placeholder;
        }
      }
    }
  },
  methods: {
    startPlaceholderAnimation() {
      // 清除现有定时器
      if (this.placeholderTimer) {
        clearInterval(this.placeholderTimer);
      }
      
      // 如果有多个提示词，启动动画
      if (this.placeholders && this.placeholders.length > 1) {
        this.placeholderTimer = setInterval(() => {
          this.placeholderIndex = (this.placeholderIndex + 1) % this.placeholders.length;
          this.currentPlaceholder = this.placeholders[this.placeholderIndex];
        }, this.animationSpeed);
      }
    },
    stopPlaceholderAnimation() {
      if (this.placeholderTimer) {
        clearInterval(this.placeholderTimer);
        this.placeholderTimer = null;
      }
    }
  },
  beforeUnmount() {
    this.stopPlaceholderAnimation();
  }
}
</script>

<style scoped>
.animated-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  z-index: 0;
  overflow: hidden;
}

.placeholder-text {
  font-size: 14px;
  color: rgba(200, 200, 210, 0.6);
  transition: opacity 0.3s ease;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
}

.animated-placeholder.is-focused .placeholder-text {
  opacity: 0.8;
}

@keyframes placeholderPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.animated-placeholder .placeholder-text {
  animation: placeholderPulse 2s ease-in-out infinite;
}
</style> 