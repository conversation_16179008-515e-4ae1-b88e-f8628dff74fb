<template>
	<view class="date-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
			</view>

			<!-- 列标签 -->
			<view class="column-labels">
				<text class="column-label">年</text>
				<text class="column-label">月</text>
				<text class="column-label">日</text>
			</view>

			<!-- 日期选择器 -->
			<view class="picker-container">
				<view class="picker-indicator"></view>
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
				>
					<picker-view-column>
						<view v-for="(year, index) in years" :key="index" class="picker-item">
							{{ year }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(month, index) in months" :key="index" class="picker-item">
							{{ String(month).padStart(2, '0') }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(day, index) in days" :key="index" class="picker-item">
							{{ String(day).padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DatePicker',
	props: {
		visible: { type: Boolean, default: false }
	},
	data() {
		const now = new Date();
		return {
			selectedYear: now.getFullYear(),
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate(),
			pickerValue: [0, 0, 0],
			years: this.generateYears(),
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
		};
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) this.initPicker();
		}
	},
	methods: {
		generateYears() {
			const years = [];
			const currentYear = new Date().getFullYear();
			for (let i = 1900; i <= currentYear + 20; i++) {
				years.push(i);
			}
			return years;
		},
		initPicker() {
			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.selectedMonth - 1;
			const dayIndex = this.selectedDay - 1;
			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0
			];
		},
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex] = e.detail.value;
			this.selectedYear = this.years[yearIndex];
			this.selectedMonth = monthIndex + 1;
			this.selectedDay = this.days[dayIndex];
			this.pickerValue = [yearIndex, monthIndex, dayIndex];
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const year = String(this.selectedYear);
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateStr = `${year}-${month}-${day}`;
			this.$emit('confirm', dateStr);
		}
	}
};
</script>

<style scoped>
.date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 750rpx;
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx 30rpx 30rpx;
}

.picker-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.column-labels {
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
	padding: 0 60rpx;
}

.column-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 600;
}

.picker-container {
	position: relative;
	height: 400rpx;
	margin-bottom: 40rpx;
}

.picker-indicator {
	position: absolute;
	top: 50%;
	left: 30rpx;
	right: 30rpx;
	height: 80rpx;
	transform: translateY(-50%);
	background: rgba(0, 122, 255, 0.08);
	border: 2rpx solid #007aff;
	border-radius: 12rpx;
	pointer-events: none;
	z-index: 1;
}

.picker-view {
	width: 100%;
	height: 100%;
}

.picker-item {
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.picker-footer {
	display: flex;
	gap: 20rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 600;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.btn-confirm {
	background: #007aff;
	color: white;
}
</style>
