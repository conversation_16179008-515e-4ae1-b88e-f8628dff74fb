<template>
  <view class="scenario-selector">
    <!-- 场景选择按钮 -->
    <view class="scenario-trigger" @click="showSelector = !showSelector">
      <view class="current-scenario">
        <text class="scenario-icon">{{ currentScenarioInfo.icon }}</text>
        <text class="scenario-name">{{ currentScenarioInfo.name }}</text>
      </view>
      <text class="arrow" :class="{ active: showSelector }">▼</text>
    </view>

    <!-- 场景选择面板 -->
    <view class="scenario-panel" v-if="showSelector">
      <view class="scenario-categories">
        <view 
          class="category-tab"
          :class="{ active: activeCategory === category }"
          v-for="(categoryInfo, category) in scenarioCategories"
          :key="category"
          @click="switchCategory(category)"
        >
          {{ categoryInfo.name }}
        </view>
      </view>
      
      <scroll-view class="scenario-list" scroll-y="true">
        <view 
          class="scenario-item"
          :class="{ active: scenario.id === currentScenario }"
          v-for="scenario in currentCategoryScenarios"
          :key="scenario.id"
          @click="selectScenario(scenario)"
        >
          <view class="scenario-info">
            <text class="scenario-icon">{{ scenario.icon }}</text>
            <view class="scenario-details">
              <text class="scenario-name">{{ scenario.name }}</text>
              <text class="scenario-desc">{{ scenario.description }}</text>
            </view>
          </view>
          <text class="check-icon" v-if="scenario.id === currentScenario">✓</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ScenarioSelector',
  props: {
    value: {
      type: String,
      default: 'general'
    }
  },
  data() {
    return {
      showSelector: false,
      currentScenario: this.value,
      activeCategory: 'creative',
      
      // 场景分类
      scenarioCategories: {
        creative: { name: '创意写作', icon: '✍️' },
        professional: { name: '专业文档', icon: '📄' },
        utility: { name: '实用工具', icon: '🔧' }
      },
      
      // 场景配置
      scenarios: {
        creative: [
          { id: 'script', name: '剧本创作', description: '编写电影、电视剧、话剧剧本', icon: '🎭' },
          { id: 'lyrics', name: '歌词创作', description: '创作歌曲歌词，支持多种风格', icon: '🎵' },
          { id: 'novel', name: '小说创作', description: '长篇小说、短篇故事创作', icon: '📚' },
          { id: 'poetry', name: '诗歌创作', description: '现代诗、古体诗创作', icon: '🌸' },
          { id: 'story', name: '故事创作', description: '儿童故事、寓言故事等', icon: '📖' }
        ],
        professional: [
          { id: 'article', name: '文章写作', description: '新闻稿、博客文章、专业文章', icon: '📝' },
          { id: 'resume', name: '简历制作', description: '个人简历、求职信撰写', icon: '👤' },
          { id: 'report', name: '报告撰写', description: '工作报告、研究报告', icon: '📊' },
          { id: 'proposal', name: '方案书', description: '项目方案、商业计划书', icon: '💼' }
        ],
        utility: [
          { id: 'general', name: '通用写作', description: '通用文本生成和处理', icon: '✏️' },
          { id: 'translate', name: '翻译工具', description: '多语言翻译服务', icon: '🌐' },
          { id: 'summary', name: '内容总结', description: '文本摘要和要点提取', icon: '📋' },
          { id: 'grammar', name: '语法检查', description: '语法纠错和文本优化', icon: '🔍' }
        ]
      }
    };
  },
  computed: {
    currentScenarioInfo() {
      for (const category of Object.values(this.scenarios)) {
        const scenario = category.find(s => s.id === this.currentScenario);
        if (scenario) return scenario;
      }
      return { name: '通用写作', icon: '✏️' };
    },
    
    currentCategoryScenarios() {
      return this.scenarios[this.activeCategory] || [];
    }
  },
  methods: {
    switchCategory(category) {
      this.activeCategory = category;
    },
    
    selectScenario(scenario) {
      this.currentScenario = scenario.id;
      this.showSelector = false;
      
      // 触发事件
      this.$emit('input', scenario.id);
      this.$emit('change', scenario);
      
      // 显示选择提示
      uni.showToast({
        title: `已切换到${scenario.name}`,
        icon: 'none',
        duration: 1500
      });
    }
  },
  watch: {
    value(newVal) {
      this.currentScenario = newVal;
    }
  }
};
</script>

<style scoped>
.scenario-selector {
  position: relative;
  margin-bottom: 15px;
}

.scenario-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.scenario-trigger:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.current-scenario {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scenario-icon {
  font-size: 18px;
}

.scenario-name {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.arrow {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  transition: transform 0.3s ease;
}

.arrow.active {
  transform: rotate(180deg);
}

.scenario-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1a2332;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  z-index: 100;
  animation: slideDown 0.3s ease;
}

.scenario-categories {
  display: flex;
  padding: 12px;
  gap: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-tab {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-tab.active {
  background: #007AFF;
  color: #ffffff;
}

.scenario-list {
  max-height: 300px;
  padding: 8px;
}

.scenario-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scenario-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.scenario-item.active {
  background: rgba(0, 122, 255, 0.2);
  border: 1px solid rgba(0, 122, 255, 0.5);
}

.scenario-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.scenario-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.scenario-item .scenario-name {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.scenario-desc {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.check-icon {
  color: #007AFF;
  font-size: 16px;
  font-weight: bold;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
