/**
 * 全应用多端适配配置
 * 统一管理所有平台的配置差异
 */

// 平台配置
export const PlatformConfig = {
  // 当前平台
  get platform() {
    // #ifdef H5
    return 'h5'
    // #endif
    // #ifdef APP-PLUS
    return 'app'
    // #endif
    // #ifdef MP-WEIXIN
    return 'mp-weixin'
    // #endif
    // #ifdef MP-ALIPAY
    return 'mp-alipay'
    // #endif
    return 'unknown'
  },

  // 页面配置
  pages: {
    // 首页配置
    index: {
      navigationBarTitleText: 'AI创作助手',
      // #ifdef H5
      navigationStyle: 'default',
      // #endif
      // #ifdef APP-PLUS
      navigationStyle: 'custom',
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      navigationStyle: 'default',
      // #endif
      // #endif
    },

    // 创作页面配置
    create: {
      navigationBarTitleText: '创作中心',
      enablePullDownRefresh: true,
      // #ifdef APP-PLUS
      bounce: 'vertical',
      // #endif
    },

    // 用户页面配置
    user: {
      navigationBarTitleText: '个人中心',
      // #ifdef H5
      navigationBarBackgroundColor: '#ffffff',
      // #endif
      // #ifdef APP-PLUS
      navigationBarBackgroundColor: '#f8f9fa',
      // #endif
    }
  },

  // 组件默认配置
  components: {
    // 滚动组件配置
    scroll: {
      // #ifdef H5
      showScrollbar: true,
      enableBackToTop: false,
      scrollWithAnimation: false,
      // #endif
      // #ifdef APP-PLUS
      showScrollbar: false,
      enableBackToTop: true,
      scrollWithAnimation: true,
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      showScrollbar: false,
      enableBackToTop: false,
      scrollWithAnimation: false,
      // #endif
      // #endif
    },

    // 弹窗组件配置
    popup: {
      // #ifdef H5
      maskClosable: true,
      animation: 'fade',
      // #endif
      // #ifdef APP-PLUS
      maskClosable: true,
      animation: 'slide-up',
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      maskClosable: true,
      animation: 'fade',
      // #endif
      // #endif
    },

    // 表单组件配置
    form: {
      // #ifdef H5
      validateTrigger: 'blur',
      showErrorMessage: true,
      // #endif
      // #ifdef APP-PLUS
      validateTrigger: 'change',
      showErrorMessage: true,
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      validateTrigger: 'submit',
      showErrorMessage: false,
      // #endif
      // #endif
    }
  },

  // 样式配置
  styles: {
    // 主题色配置
    colors: {
      primary: '#007aff',
      success: '#28a745',
      warning: '#ffc107',
      danger: '#dc3545',
      info: '#17a2b8'
    },

    // 字体配置
    fonts: {
      // #ifdef H5
      base: '16px',
      small: '14px',
      large: '18px',
      // #endif
      // #ifdef APP-PLUS
      base: '15px',
      small: '13px',
      large: '17px',
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      base: '14px',
      small: '12px',
      large: '16px',
      // #endif
      // #endif
    },

    // 间距配置
    spacing: {
      // #ifdef H5
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      // #endif
      // #ifdef APP-PLUS
      xs: '6px',
      sm: '12px',
      md: '20px',
      lg: '28px',
      xl: '36px',
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      xs: '4px',
      sm: '8px',
      md: '15px',
      lg: '22px',
      xl: '30px',
      // #endif
      // #endif
    }
  },

  // API配置
  api: {
    // 请求超时时间
    timeout: {
      // #ifdef H5
      default: 10000,
      upload: 30000,
      // #endif
      // #ifdef APP-PLUS
      default: 15000,
      upload: 60000,
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      default: 8000,
      upload: 20000,
      // #endif
      // #endif
    },

    // 重试配置
    retry: {
      // #ifdef H5
      times: 3,
      delay: 1000,
      // #endif
      // #ifdef APP-PLUS
      times: 2,
      delay: 1500,
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      times: 1,
      delay: 2000,
      // #endif
      // #endif
    }
  },

  // 功能开关
  features: {
    // 是否支持文件上传
    fileUpload: {
      // #ifdef H5
      enabled: true,
      maxSize: 10 * 1024 * 1024, // 10MB
      // #endif
      // #ifdef APP-PLUS
      enabled: true,
      maxSize: 50 * 1024 * 1024, // 50MB
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      enabled: true,
      maxSize: 5 * 1024 * 1024, // 5MB
      // #endif
      // #endif
    },

    // 是否支持摄像头
    camera: {
      // #ifdef H5
      enabled: true,
      // #endif
      // #ifdef APP-PLUS
      enabled: true,
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      enabled: true,
      // #endif
      // #endif
    },

    // 是否支持分享
    share: {
      // #ifdef H5
      enabled: false,
      // #endif
      // #ifdef APP-PLUS
      enabled: true,
      // #endif
      // #ifndef H5
      // #ifndef APP-PLUS
      enabled: true,
      // #endif
      // #endif
    }
  }
}

// 获取当前平台配置
export function getCurrentPlatformConfig() {
  return {
    platform: PlatformConfig.platform,
    isH5: PlatformConfig.platform === 'h5',
    isApp: PlatformConfig.platform === 'app',
    isMiniProgram: PlatformConfig.platform.startsWith('mp-'),
    config: PlatformConfig
  }
}

// 获取组件配置
export function getComponentConfig(componentName) {
  return PlatformConfig.components[componentName] || {}
}

// 获取样式配置
export function getStyleConfig() {
  return PlatformConfig.styles
}

// 获取功能配置
export function getFeatureConfig(featureName) {
  return PlatformConfig.features[featureName] || { enabled: false }
}

export default PlatformConfig
