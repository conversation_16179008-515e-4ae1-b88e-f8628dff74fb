<template>
  <view class="selection-handler-container" :class="{'selecting': isLongPress || selectionEnabled || isTextSelectionActive()}">
    <slot></slot>
  </view>
</template>

<script>
import { isTextSelectionActive, getTextSelection, setTextSelection } from './TextAreaUtils.js';

export default {
  name: 'TextAreaSelectionHandler',
  props: {
    contentElement: {
      type: Object,
      default: null
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLongPress: false,
      longPressTimer: null,
      lastClickTime: 0,
      selectionEnabled: false,
      selectionLogShown: false,
      selection: {
        start: 0,
        end: 0
      }
    };
  },
  methods: {
    isTextSelectionActive,
    
    handleTouchStart(e) {
      // 检查是否为长按
      this.longPressTimer = setTimeout(() => {
        this.isLongPress = true;
        this.enableSelection();
        this.$emit('long-press', e);
      }, 500); // 500ms 触发长按
      
      this.$emit('touch-start', e);
    },
    
    handleTouchEnd() {
      // 清除长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }
      
      // 处理长按结束
      if (this.isLongPress) {
        this.isLongPress = false;
        
        // 这里可以添加长按后的操作，如显示菜单等
        
        this.$emit('long-press-end');
      }
    },
    
    handleClick(e) {
      const now = Date.now();
      const isDoubleClick = now - this.lastClickTime < 300; // 300ms内的点击视为双击
      this.lastClickTime = now;
      
      if (isDoubleClick) {
        this.handleDoubleClick(e);
      }
      
      this.$emit('click', e);
    },
    
    handleDoubleClick() {
      // 双击开启文本选择
      this.enableSelection();
      
      // 尝试选中当前词或行
      this.selectCurrentWord();
      
      this.$emit('double-click');
    },
    
    enableSelection() {
      this.selectionEnabled = true;
      
      // 一段时间后自动关闭选择模式
      setTimeout(() => {
        this.selectionEnabled = false;
      }, 10000); // 10秒后自动关闭选择模式
      
      this.$emit('selection-change', { enabled: true });
    },
    
    disableSelection() {
      this.selectionEnabled = false;
      this.$emit('selection-change', { enabled: false });
    },
    
    getCurrentSelection() {
      if (!this.contentElement) {
        return { start: 0, end: 0 };
      }
      
      try {
        this.selection = getTextSelection(this.contentElement);
        return this.selection;
      } catch (e) {
        console.error('获取文本选择失败:', e);
        return { start: 0, end: 0 };
      }
    },
    
    setSelection(start, end) {
      if (!this.contentElement) return;
      
      try {
        setTextSelection(this.contentElement, start, end);
        this.selection = { start, end };
        this.$emit('selection-change', { selection: this.selection });
      } catch (e) {
        console.error('设置文本选择失败:', e);
      }
    },
    
    selectCurrentWord() {
      if (!this.contentElement || !this.value) return;
      
      try {
        // 获取当前光标位置
        const currentPos = this.contentElement.selectionStart;
        const text = this.value;
        
        // 查找当前光标位置的单词边界
        let start = currentPos;
        let end = currentPos;
        
        // 定义单词边界的字符
        const wordBoundary = /\s\n\t,.;:'"!?()[]{}\/\\|<>-+*/;
        
        // 向前找单词起始
        while (start > 0 && !wordBoundary.test(text[start - 1])) {
          start--;
        }
        
        // 向后找单词结束
        while (end < text.length && !wordBoundary.test(text[end])) {
          end++;
        }
        
        // 设置选择区域
        this.setSelection(start, end);
      } catch (e) {
        console.error('选择当前单词失败:', e);
      }
    },
    
    selectAll() {
      if (!this.contentElement || !this.value) return;
      
      try {
        this.setSelection(0, this.value.length);
      } catch (e) {
        console.error('全选文本失败:', e);
      }
    },
    
    getSelectedText() {
      if (!this.value) return '';
      
      const { start, end } = this.getCurrentSelection();
      if (start === end) return '';
      
      return this.value.substring(start, end);
    }
  },
  beforeUnmount() {
    // 清理定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }
}
</script>

<style scoped>
.selection-handler-container {
  position: relative;
  width: 100%;
}

.selection-handler-container.selecting {
  /* 选择模式下的样式 */
  user-select: text;
  -webkit-user-select: text;
}
</style> 