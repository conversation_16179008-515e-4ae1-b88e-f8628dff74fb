<template>
  <view>
    <!-- 功能弹出页面背景层 -->
    <view class="feature-popup-overlay" v-if="show" @tap="close"></view>
    
    <!-- 功能弹出页面 -->
    <view class="feature-popup" v-if="show">
      <view class="feature-popup-header">
        <text class="feature-popup-title">选择功能</text>
        <view class="feature-popup-close" @tap="close">×</view>
      </view>
      <view class="feature-popup-content">
        <view 
          v-for="(feature, index) in features" 
          :key="index"
          class="feature-popup-item"
          @tap="selectFeature(feature)"
        >
          <view class="feature-icon" :style="{ backgroundColor: feature.color || 'rgba(110, 86, 207, 0.8)' }">
            <text>{{ feature.icon }}</text>
          </view>
          <view class="feature-info">
            <text class="feature-name">{{ feature.name }}</text>
            <text class="feature-desc">{{ feature.description }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FeatureMenu',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    features: {
      type: Array,
      default: () => [
        {
          name: '上传图片',
          icon: '🖼️',
          action: 'uploadImage',
          description: '上传图片进行分析或生成相关内容',
          color: 'rgba(64, 169, 151, 0.8)'
        },
        {
          name: '上传文档',
          icon: '📄',
          action: 'uploadDocument',
          description: '上传文档进行分析或摘要',
          color: 'rgba(82, 130, 201, 0.8)'
        },
        {
          name: '语音输入',
          icon: '🎤',
          action: 'voiceInput',
          description: '使用语音输入内容',
          color: 'rgba(110, 86, 207, 0.8)'
        },
        {
          name: '从历史选择',
          icon: '📋',
          action: 'selectFromHistory',
          description: '从历史记录中选择内容',
          color: 'rgba(130, 95, 185, 0.8)'
        },
        {
          name: '拍照识别',
          icon: '📸',
          action: 'takePhoto',
          description: '拍照并识别内容',
          color: 'rgba(59, 126, 164, 0.8)'
        }
      ]
    }
  },
  methods: {
    close() {
      this.$emit('close');
    },
    
    selectFeature(feature) {
      if (!feature) return;
      
      this.$emit('select', feature);
      this.close();
    }
  }
}
</script>

<style scoped>
.feature-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

.feature-popup {
  position: fixed;
  left: 10%;
  right: 10%;
  bottom: 200rpx;
  background-color: rgba(40, 40, 60, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
  animation: popup-slide-up 0.3s ease;
}

@keyframes popup-slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.feature-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid rgba(80, 80, 100, 0.3);
}

.feature-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #e8e8e8;
}

.feature-popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: rgba(200, 200, 210, 0.7);
  border-radius: 30rpx;
}

.feature-popup-close:active {
  background-color: rgba(80, 80, 100, 0.3);
}

.feature-popup-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 10rpx 0;
}

.feature-popup-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  transition: background-color 0.2s ease;
}

.feature-popup-item:active {
  background-color: rgba(80, 80, 100, 0.3);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(110, 86, 207, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 36rpx;
}

.feature-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-name {
  font-size: 28rpx;
  color: #e8e8e8;
  margin-bottom: 6rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: rgba(200, 200, 210, 0.6);
}
</style> 