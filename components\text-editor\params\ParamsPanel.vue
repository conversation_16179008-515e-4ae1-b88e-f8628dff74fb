<template>
  <view class="params-panel" :class="{'params-panel-expanded': !isCollapsed, 'params-panel-animate': isAnimating, 'h5-panel': isH5Platform}">
    <scroll-view 
      scroll-y 
      class="params-scroll-view"
      :show-scrollbar="isH5Platform"
      :enhanced="true"
      :bounces="true"
      :scroll-with-animation="true"
      :class="{'h5-scrollbar': isH5Platform}"
    >
      <view class="params-content">
        <!-- 媒体预览区 - 放在参数面板最顶部 -->
        <view class="media-preview-container" v-if="hasMediaContent" style="position: relative; height: auto; min-height: 24px; margin-bottom: 10px; background-color: rgba(15, 52, 96, 0.7); border-radius: 8px; padding: 2px;">
          <view class="media-items-container">
            <view 
              class="media-item-card" 
              v-for="(item, index) in mediaItems" 
              :key="index"
            >
              <!-- 图片预览 -->
              <image 
                v-if="item.type === 'image'" 
                class="media-preview-image" 
                :src="item.src" 
                mode="aspectFill" 
                @tap="previewImage(item.src)"
                @longtap="handleImageLongPress(item.src)" 
              />
              
              <!-- 文档预览 -->
              <view v-if="item.type === 'document'" class="media-preview-doc" @tap="openDocument(item)">
                <text class="doc-preview-icon">📄</text>
                <text class="doc-preview-name">{{item.name}}</text>
              </view>
              
              <!-- 移除按钮 -->
              <view class="media-item-remove" @tap="removeMediaItem(index)">×</view>
            </view>
          </view>
        </view>

        <!-- 预设提示词部分 -->
        <slot name="prompt-tags"></slot>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'ParamsPanel',
  props: {
    isCollapsed: {
      type: Boolean,
      default: true
    },
    isAnimating: {
      type: Boolean,
      default: false
    },
    isH5Platform: {
      type: Boolean,
      default: false
    },
    mediaItems: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    hasMediaContent() {
      return this.mediaItems && this.mediaItems.length > 0;
    }
  },
  methods: {
    previewImage(src) {
      if (!src) return;
      
      try {
        uni.previewImage({
          urls: [src],
          current: src
        });
      } catch (e) {
        console.error('预览图片失败:', e);
      }
      
      this.$emit('preview-image', src);
    },
    
    handleImageLongPress(src) {
      this.$emit('image-long-press', src);
    },
    
    openDocument(item) {
      this.$emit('open-document', item);
    },
    
    removeMediaItem(index) {
      this.$emit('remove-media', index);
    }
  }
}
</script>

<style scoped>
.params-panel {
  position: absolute;
  z-index: 10;
  left: 0;
  right: 0;
  bottom: 100%;
  background-color: rgba(30, 30, 50, 0.95);
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.params-panel-expanded {
  max-height: 700rpx;
}

.params-panel-animate {
  transition: max-height 0.3s ease;
}

.params-scroll-view {
  height: 100%;
  width: 100%;
  max-height: 700rpx;
}

.params-content {
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.h5-panel {
  backdrop-filter: blur(5px);
}

.h5-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.h5-scrollbar::-webkit-scrollbar-track {
  background: rgba(30, 30, 50, 0.2);
  border-radius: 3px;
}

.h5-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(110, 86, 207, 0.5);
  border-radius: 3px;
}

.h5-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(110, 86, 207, 0.7);
}

/* 媒体预览区样式 */
.media-preview-container {
  margin-bottom: 20rpx;
}

.media-items-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.media-item-card {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.media-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-preview-doc {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(60, 60, 100, 0.4);
}

.doc-preview-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.doc-preview-name {
  font-size: 20rpx;
  color: #e8e8e8;
  text-align: center;
  width: 100%;
  padding: 0 4rpx;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-item-remove {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  border-bottom-left-radius: 8rpx;
  z-index: 2;
}
</style> 