<template>
	<view class="time-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生时间</text>
			</view>

			<!-- 列标签 -->
			<view class="column-labels">
				<text class="column-label">时</text>
				<text class="column-label">分</text>
			</view>

			<!-- 时间选择器 -->
			<view class="picker-container">
				<view class="picker-indicator"></view>
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
				>
					<picker-view-column>
						<view v-for="(hour, index) in hours" :key="index" class="picker-item">
							{{ String(hour).padStart(2, '0') }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(minute, index) in minutes" :key="index" class="picker-item">
							{{ String(minute).padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TimePicker',
	props: {
		visible: { type: Boolean, default: false }
	},
	data() {
		const now = new Date();
		return {
			selectedHour: now.getHours(),
			selectedMinute: now.getMinutes(),
			pickerValue: [0, 0],
			hours: this.generateHours(),
			minutes: this.generateMinutes()
		};
	},
	watch: {
		visible(newVal) {
			if (newVal) this.initPicker();
		}
	},
	methods: {
		generateHours() {
			const hours = [];
			for (let i = 0; i < 24; i++) {
				hours.push(i);
			}
			return hours;
		},
		generateMinutes() {
			const minutes = [];
			for (let i = 0; i < 60; i++) {
				minutes.push(i);
			}
			return minutes;
		},
		initPicker() {
			const hourIndex = this.selectedHour;
			const minuteIndex = this.selectedMinute;
			this.pickerValue = [hourIndex, minuteIndex];
		},
		onPickerChange(e) {
			const [hourIndex, minuteIndex] = e.detail.value;
			this.selectedHour = this.hours[hourIndex];
			this.selectedMinute = this.minutes[minuteIndex];
			this.pickerValue = [hourIndex, minuteIndex];
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');
			const timeStr = `${hour}:${minute}`;
			this.$emit('confirm', timeStr);
		}
	}
};
</script>

<style scoped>
.time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 750rpx;
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx 30rpx 30rpx;
}

.picker-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.column-labels {
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
	padding: 0 120rpx;
}

.column-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 600;
}

.picker-container {
	position: relative;
	height: 400rpx;
	margin-bottom: 40rpx;
}

.picker-indicator {
	position: absolute;
	top: 50%;
	left: 30rpx;
	right: 30rpx;
	height: 80rpx;
	transform: translateY(-50%);
	background: rgba(0, 122, 255, 0.08);
	border: 2rpx solid #007aff;
	border-radius: 12rpx;
	pointer-events: none;
	z-index: 1;
}

.picker-view {
	width: 100%;
	height: 100%;
}

.picker-item {
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.picker-footer {
	display: flex;
	gap: 20rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 600;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.btn-confirm {
	background: #007aff;
	color: white;
}
</style>
