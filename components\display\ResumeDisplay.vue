<template>
  <view class="resume-display">
    <view class="resume-header">
      <text class="resume-name">{{ content.personName || '未填写姓名' }}</text>
    </view>
    
    <view class="resume-content">
      <text class="resume-main-content" :class="{'selectable-text': true}">{{ content.content }}</text>
    </view>
    
    <view class="resume-experience" v-if="hasExperience">
      <text class="section-title">工作经验</text>
      <view class="experience-item" v-for="(exp, index) in content.experience" :key="index">
        <text class="experience-company">{{ exp.company || `公司 ${index+1}` }}</text>
        <text class="experience-position">{{ exp.position || '职位未填写' }}</text>
        <text class="experience-period">{{ exp.period || '时间未填写' }}</text>
        <text class="experience-description" :class="{'selectable-text': true}">{{ exp.description || '职责描述未填写' }}</text>
      </view>
    </view>
    
    <view class="resume-education" v-if="hasEducation">
      <text class="section-title">教育背景</text>
      <view class="education-item" v-for="(edu, index) in content.education" :key="index">
        <text class="education-school">{{ edu.school || `学校 ${index+1}` }}</text>
        <text class="education-degree">{{ edu.degree || '学位未填写' }}</text>
        <text class="education-period">{{ edu.period || '时间未填写' }}</text>
        <text class="education-major">{{ edu.major || '专业未填写' }}</text>
      </view>
    </view>
    
    <view class="resume-skills" v-if="hasSkills">
      <text class="section-title">专业技能</text>
      <view class="skills-container">
        <text class="skill-item" v-for="(skill, index) in content.skills" :key="index">{{ skill }}</text>
      </view>
    </view>
    
    <view class="resume-actions">
      <view class="action-btn edit-btn" @tap="handleAction('edit')">
        <text class="action-icon">✏️</text>
        <text class="action-text">编辑</text>
      </view>
      <view class="action-btn download-btn" @tap="handleAction('download')">
        <text class="action-icon">📥</text>
        <text class="action-text">下载</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ResumeDisplay',
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  computed: {
    hasExperience() {
      return this.content.experience && this.content.experience.length > 0;
    },
    hasEducation() {
      return this.content.education && this.content.education.length > 0;
    },
    hasSkills() {
      return this.content.skills && this.content.skills.length > 0;
    }
  },
  methods: {
    handleAction(action, data) {
      // 将动作向上传递
      this.$emit('action', action, data || this.content);
    }
  }
}
</script>

<style scoped>
.resume-display {
  width: 100%;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.resume-header {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(to right, #e8eaf6, #f5f7fa);
  border-radius: 12rpx 12rpx 0 0;
  text-align: center;
}

.resume-name {
  font-size: 38rpx;
  font-weight: bold;
  color: #3f51b5;
  display: block;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.05);
}

.resume-content {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  line-height: 1.8;
}

.resume-main-content {
  font-size: 28rpx;
  color: #424242;
  white-space: pre-wrap;
  line-height: 1.8;
}

.resume-experience,
.resume-education,
.resume-skills {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #3f51b5;
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #c5cae9;
  display: block;
}

.experience-item,
.education-item {
  margin-bottom: 20rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #f5f7ff;
}

.experience-item {
  border-left: 6rpx solid #7986cb;
}

.education-item {
  border-left: 6rpx solid #9fa8da;
}

.experience-company,
.education-school {
  font-size: 30rpx;
  font-weight: bold;
  color: #303f9f;
  display: block;
  margin-bottom: 6rpx;
}

.experience-position,
.education-degree {
  font-size: 28rpx;
  color: #3949ab;
  display: block;
  margin-bottom: 6rpx;
}

.experience-period,
.education-period,
.education-major {
  font-size: 26rpx;
  color: #5c6bc0;
  display: block;
  margin-bottom: 6rpx;
}

.experience-description {
  font-size: 26rpx;
  color: #424242;
  margin-top: 10rpx;
  display: block;
  line-height: 1.6;
  padding-top: 10rpx;
  border-top: 1px dashed #e0e0e0;
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.skill-item {
  padding: 8rpx 20rpx;
  margin: 8rpx;
  background-color: #e8eaf6;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #3949ab;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.resume-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
  padding: 10rpx 0;
}

.action-btn {
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex: 1;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.edit-btn {
  background-color: #e8eaf6;
  color: #3949ab;
}

.download-btn {
  background-color: #c5cae9;
  color: #283593;
}

.action-text {
  font-size: 26rpx;
  font-weight: 500;
}

.selectable-text {
  user-select: text;
}
</style>