/**
 * 工作流配置文件
 * 定义统一的变量名和工作流映射关系
 */

// 统一的工作流变量名（所有工作流都使用这个变量名作为输入）
export const UNIFIED_VARIABLE_NAME = 'musicCreationData';

// 工作流类型定义
export const WORKFLOW_TYPES = {
	SIMPLE: 'simple_mode_workflow',      // 简单模式工作流ID
	ADVANCED: 'advanced_mode_workflow',  // 高级模式工作流ID
	INSTRUMENTAL: 'instrumental_workflow' // 纯音乐模式工作流ID
};

// 创作模式到工作流的映射
export const MODE_TO_WORKFLOW = {
	'simple': WORKFLOW_TYPES.SIMPLE,
	'advanced': WORKFLOW_TYPES.ADVANCED,
	'instrumental': WORKFLOW_TYPES.INSTRUMENTAL
};

// 工作流配置
export const WORKFLOW_CONFIG = {
	[WORKFLOW_TYPES.SIMPLE]: {
		name: '简单模式工作流',
		description: '快速创作，基础质量',
		timeout: 300,        // 5分钟超时
		priority: 'normal',
		retryCount: 2,
		estimatedTime: 120   // 预计2分钟完成
	},
	
	[WORKFLOW_TYPES.ADVANCED]: {
		name: '高级模式工作流',
		description: '专业创作，高级质量',
		timeout: 600,        // 10分钟超时
		priority: 'high',
		retryCount: 3,
		estimatedTime: 300   // 预计5分钟完成
	},
	
	[WORKFLOW_TYPES.INSTRUMENTAL]: {
		name: '纯音乐工作流',
		description: '器乐创作，无人声',
		timeout: 480,        // 8分钟超时
		priority: 'normal',
		retryCount: 2,
		estimatedTime: 240   // 预计4分钟完成
	}
};

// 数据结构模板（定义每种模式的数据格式）
export const DATA_TEMPLATES = {
	// 简单模式数据模板
	simple: {
		mode: 'simple',
		prompt: '',           // 用户输入的提示词
		styles: [],          // 选择的音乐风格数组
		vocalGender: 'auto', // 人声性别：'male', 'female', 'auto', 'none'
		duration: 180,       // 时长（秒）
		complexity: 'basic', // 复杂度
		quality: 'standard'  // 质量等级
	},
	
	// 高级模式数据模板
	advanced: {
		mode: 'advanced',
		lyrics: '',          // 自定义歌词
		description: '',     // 歌曲描述
		tags: {},           // 选择的标签对象
		vocalStyle: [],     // 人声风格
		genreTags: [],      // 曲风标签
		moodTags: [],       // 情绪标签
		duration: 240,      // 时长（秒）
		complexity: 'advanced', // 复杂度
		quality: 'high'     // 质量等级
	},
	
	// 纯音乐模式数据模板
	instrumental: {
		mode: 'instrumental',
		style: 'classical',  // 音乐风格
		mood: 'peaceful',    // 情感基调
		instruments: [],     // 乐器数组
		structure: 'standard', // 音乐结构
		duration: 300,       // 时长（秒）
		complexity: 'instrumental', // 复杂度
		quality: 'high'      // 质量等级
	}
};

// API端点配置
export const API_ENDPOINTS = {
	development: {
		baseURL: 'http://localhost:8080/api/workflows',
		execute: '/execute',
		status: '/status',
		result: '/result'
	},
	
	staging: {
		baseURL: 'https://staging-api.yunchuangai.com/workflows',
		execute: '/execute',
		status: '/status',
		result: '/result'
	},
	
	production: {
		baseURL: 'https://api.yunchuangai.com/workflows',
		execute: '/execute',
		status: '/status',
		result: '/result'
	}
};

// 获取当前环境的API配置
export function getAPIConfig() {
	const env = process.env.NODE_ENV || 'development';
	return API_ENDPOINTS[env] || API_ENDPOINTS.development;
}

// 工作流请求格式模板
export const REQUEST_TEMPLATE = {
	// 请求标识
	requestId: '',           // 唯一请求ID
	userId: '',             // 用户ID
	timestamp: 0,           // 时间戳
	
	// 工作流路由
	workflowType: '',       // 工作流类型（从MODE_TO_WORKFLOW获取）
	
	// 统一数据变量（核心！）
	[UNIFIED_VARIABLE_NAME]: {} // 所有工作流都从这个变量获取数据
};

// 响应格式模板
export const RESPONSE_TEMPLATE = {
	// 基础信息
	success: true,
	requestId: '',
	workflowId: '',
	
	// 状态信息
	status: 'pending', // pending, processing, completed, failed
	progress: 0,       // 0-100
	estimatedTime: 0,  // 剩余时间（秒）
	
	// 结果数据
	result: null,      // 创作结果
	error: null        // 错误信息
};

// 工作流状态定义
export const WORKFLOW_STATUS = {
	PENDING: 'pending',       // 等待执行
	PROCESSING: 'processing', // 执行中
	COMPLETED: 'completed',   // 已完成
	FAILED: 'failed'         // 执行失败
};

// 数据验证规则
export const VALIDATION_RULES = {
	simple: {
		prompt: { required: true, minLength: 1, maxLength: 200 },
		styles: { required: true, minItems: 1 },
		vocalGender: { required: true, enum: ['male', 'female', 'auto', 'none'] },
		duration: { required: true, min: 30, max: 600 }
	},
	
	advanced: {
		description: { required: true, minLength: 1, maxLength: 500 },
		duration: { required: true, min: 60, max: 900 },
		tags: { required: false }
	},
	
	instrumental: {
		style: { required: true, minLength: 1 },
		mood: { required: true, minLength: 1 },
		duration: { required: true, min: 60, max: 1200 }
	}
};

// 工具函数：验证数据格式
export function validateModeData(mode, data) {
	const rules = VALIDATION_RULES[mode];
	if (!rules) {
		throw new Error(`未知的模式: ${mode}`);
	}
	
	const errors = [];
	
	for (const [field, rule] of Object.entries(rules)) {
		const value = data[field];
		
		// 必填检查
		if (rule.required && (value === undefined || value === null || value === '')) {
			errors.push(`${field} 是必填字段`);
			continue;
		}
		
		// 跳过非必填的空值
		if (!rule.required && (value === undefined || value === null || value === '')) {
			continue;
		}
		
		// 字符串长度检查
		if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
			errors.push(`${field} 长度不能少于 ${rule.minLength} 个字符`);
		}
		
		if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
			errors.push(`${field} 长度不能超过 ${rule.maxLength} 个字符`);
		}
		
		// 数值范围检查
		if (rule.min && typeof value === 'number' && value < rule.min) {
			errors.push(`${field} 不能小于 ${rule.min}`);
		}
		
		if (rule.max && typeof value === 'number' && value > rule.max) {
			errors.push(`${field} 不能大于 ${rule.max}`);
		}
		
		// 数组长度检查
		if (rule.minItems && Array.isArray(value) && value.length < rule.minItems) {
			errors.push(`${field} 至少需要 ${rule.minItems} 个项目`);
		}
		
		// 枚举值检查
		if (rule.enum && !rule.enum.includes(value)) {
			errors.push(`${field} 必须是以下值之一: ${rule.enum.join(', ')}`);
		}
	}
	
	return errors;
}

// 工具函数：生成请求ID
export function generateRequestId() {
	return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 工具函数：获取工作流配置
export function getWorkflowConfig(workflowType) {
	return WORKFLOW_CONFIG[workflowType] || null;
}

// 工具函数：创建标准请求
export function createWorkflowRequest(mode, data, userInfo) {
	const workflowType = MODE_TO_WORKFLOW[mode];
	if (!workflowType) {
		throw new Error(`未知的创作模式: ${mode}`);
	}
	
	// 验证数据
	const validationErrors = validateModeData(mode, data);
	if (validationErrors.length > 0) {
		throw new Error(`数据验证失败: ${validationErrors.join(', ')}`);
	}
	
	// 创建请求
	const request = {
		requestId: generateRequestId(),
		userId: userInfo.userId || 'anonymous',
		timestamp: Date.now(),
		workflowType: workflowType,
		[UNIFIED_VARIABLE_NAME]: data
	};
	
	return request;
}

export default {
	UNIFIED_VARIABLE_NAME,
	WORKFLOW_TYPES,
	MODE_TO_WORKFLOW,
	WORKFLOW_CONFIG,
	DATA_TEMPLATES,
	API_ENDPOINTS,
	REQUEST_TEMPLATE,
	RESPONSE_TEMPLATE,
	WORKFLOW_STATUS,
	VALIDATION_RULES,
	getAPIConfig,
	validateModeData,
	generateRequestId,
	getWorkflowConfig,
	createWorkflowRequest
};
