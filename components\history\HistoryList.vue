<template>
  <view class="history-list">
    <view class="empty-state" v-if="historyItems.length === 0">
      <image class="empty-image" src="/static/images/empty-history.png" mode="aspectFit"></image>
      <text class="empty-text">{{ emptyText }}</text>
    </view>
    
    <view v-else>
      <!-- 日期分组标题 -->
      <view v-for="(group, date) in groupedItems" :key="date" class="history-group">
        <view class="date-header">
          <text class="date-text">{{ formatDateHeader(date) }}</text>
        </view>
        
        <!-- 历史记录项 -->
        <view 
          class="history-item"
          v-for="(item, index) in group" 
          :key="item.id"
          @tap="onItemTap(item)"
        >
          <!-- 左侧缩略图 -->
          <view class="item-thumbnail" :class="[`item-thumbnail-${item.type}`]">
            <image 
              v-if="item.type === 'image' || item.type === 'video'" 
              :src="item.thumbnail || defaultThumbnails[item.type]" 
              mode="aspectFill"
            ></image>
            <view v-else-if="item.type === 'text'" class="text-thumbnail">
              <text class="text-content">{{ item.content ? item.content.substring(0, 50) + '...' : '' }}</text>
            </view>
            <view v-else-if="item.type === 'music'" class="music-thumbnail">
              <image src="/static/icons/music-note.png" mode="aspectFit"></image>
            </view>
          </view>
          
          <!-- 右侧信息 -->
          <view class="item-info">
            <view class="item-header">
              <text class="item-title">{{ item.title || getDefaultTitle(item) }}</text>
              <text class="item-time">{{ formatTime(item.createTime) }}</text>
            </view>
            
            <text class="item-desc">{{ item.prompt ? (item.prompt.length > 80 ? item.prompt.substring(0, 80) + '...' : item.prompt) : '' }}</text>
            
            <!-- 标签和操作按钮 -->
            <view class="item-footer">
              <view class="item-tag" :class="[`item-tag-${item.type}`]">
                <text>{{ getTypeText(item.type) }}</text>
              </view>
              
              <!-- 操作按钮 -->
              <view class="item-actions">
                <view class="action-btn share-btn" @tap.stop="onShareTap(item)">
                  <image src="/static/icons/share.png" mode="aspectFit"></image>
                </view>
                <view class="action-btn delete-btn" @tap.stop="onDeleteTap(item)">
                  <image src="/static/icons/delete.png" mode="aspectFit"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text v-if="isLoading">加载中...</text>
        <text v-else @tap="loadMore">加载更多</text>
      </view>
    </view>
    
    <!-- 删除确认弹窗 -->
    <view class="delete-modal" v-if="showDeleteModal">
      <view class="modal-mask" @tap="cancelDelete"></view>
      <view class="modal-content">
        <view class="modal-title">确认删除</view>
        <view class="modal-body">确定要删除这条历史记录吗？此操作不可撤销。</view>
        <view class="modal-footer">
          <button class="cancel-btn" @tap="cancelDelete">取消</button>
          <button class="confirm-btn" @tap="confirmDelete">确认删除</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HistoryList',
  props: {
    // 历史记录类型: 'all', 'text', 'image', 'music', 'video'
    type: {
      type: String,
      default: 'all',
      validator: (value) => ['all', 'text', 'image', 'music', 'video'].includes(value)
    },
    // 空状态文本
    emptyText: {
      type: String,
      default: '暂无历史记录'
    },
    // 每页加载条数
    pageSize: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      historyItems: [],
      page: 1,
      hasMore: true,
      isLoading: false,
      showDeleteModal: false,
      deleteItem: null,
      defaultThumbnails: {
        text: '/static/images/default-text.png',
        image: '/static/images/default-image.png',
        music: '/static/images/default-music.png',
        video: '/static/images/default-video.png'
      }
    }
  },
  computed: {
    // 按日期分组历史记录
    groupedItems() {
      const groups = {}
      
      this.historyItems.forEach(item => {
        // 提取日期部分 (YYYY-MM-DD)
        const date = new Date(item.createTime).toISOString().split('T')[0]
        
        if (!groups[date]) {
          groups[date] = []
        }
        
        groups[date].push(item)
      })
      
      return groups
    }
  },
  created() {
    this.loadHistoryItems()
  },
  methods: {
    // 加载历史记录数据
    loadHistoryItems() {
      this.isLoading = true
      
      // 模拟API请求，实际项目中应该调用真实接口
      setTimeout(() => {
        // 生成测试数据
        const newItems = this.generateMockData()
        
        this.historyItems = [...this.historyItems, ...newItems]
        this.hasMore = newItems.length === this.pageSize
        this.isLoading = false
        
        // 如果没有更多数据，hasMore设为false
        if (newItems.length < this.pageSize) {
          this.hasMore = false
        }
      }, 500)
    },
    
    // 加载更多
    loadMore() {
      if (this.isLoading || !this.hasMore) return
      
      this.page++
      this.loadHistoryItems()
    },
    
    // 点击历史记录项
    onItemTap(item) {
      this.$emit('itemTap', item)
      
      // 根据不同类型跳转到不同结果页
      const pageMap = {
        text: '/pages/create/text/result',
        image: '/pages/create/image/result',
        music: '/pages/create/music/result',
        video: '/pages/create/video/result'
      }
      
      if (pageMap[item.type]) {
        uni.navigateTo({
          url: `${pageMap[item.type]}?id=${item.id}`
        })
      }
    },
    
    // 点击分享按钮
    onShareTap(item) {
      this.$emit('shareTap', item)
      
      // 实现分享功能，根据平台调用不同API
      // 示例：调用分享API
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        title: item.title || this.getDefaultTitle(item),
        summary: item.prompt || '',
        imageUrl: item.thumbnail || this.defaultThumbnails[item.type],
        success: function (res) {
          console.log('分享成功：', res)
        },
        fail: function (err) {
          console.log('分享失败：', err)
        }
      })
    },
    
    // 点击删除按钮
    onDeleteTap(item) {
      this.deleteItem = item
      this.showDeleteModal = true
    },
    
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false
      this.deleteItem = null
    },
    
    // 确认删除
    confirmDelete() {
      if (!this.deleteItem) return
      
      // 在实际应用中应该调用API删除记录
      // 这里只是前端模拟删除
      const index = this.historyItems.findIndex(item => item.id === this.deleteItem.id)
      if (index !== -1) {
        this.historyItems.splice(index, 1)
      }
      
      this.$emit('deleteTap', this.deleteItem)
      
      this.showDeleteModal = false
      this.deleteItem = null
    },
    
    // 格式化日期标题
    formatDateHeader(dateStr) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      const date = new Date(dateStr)
      
      if (date.toDateString() === today.toDateString()) {
        return '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
      }
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      
      return `${hours}:${minutes}`
    },
    
    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        text: '文本',
        image: '图像',
        music: '音乐',
        video: '视频'
      }
      
      return typeMap[type] || '创作'
    },
    
    // 获取默认标题
    getDefaultTitle(item) {
      const typeMap = {
        text: '文本创作',
        image: '图像创作',
        music: '音乐创作',
        video: '视频创作'
      }
      
      return typeMap[item.type] || '创作结果'
    },
    
    // 生成模拟数据（仅用于演示）
    generateMockData() {
      const types = this.type === 'all' ? ['text', 'image', 'music', 'video'] : [this.type]
      const items = []
      
      const randomDate = (start, end) => {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
      }
      
      // 生成过去30天内的随机日期
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      for (let i = 0; i < this.pageSize; i++) {
        const type = types[Math.floor(Math.random() * types.length)]
        const date = randomDate(start, end)
        
        let item = {
          id: `history_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
          type: type,
          createTime: date.getTime(),
          prompt: '使用AI生成的' + this.getTypeText(type) + '，基于提示词"' + ['未来城市', '科技发展', '自然风光', '抽象艺术', '流行音乐'][Math.floor(Math.random() * 5)] + '"'
        }
        
        switch (type) {
          case 'text':
            item.title = ['创意故事', '技术文档', '商业文案', '诗歌', '剧本对话'][Math.floor(Math.random() * 5)]
            item.content = '这是一段由AI生成的文本内容，根据用户的提示词创作而成。根据不同的提示词，可以生成各种不同风格和主题的文本作品。'
            break
          case 'image':
            item.title = ['科幻场景', '自然风光', '角色设计', '抽象艺术', '建筑设计'][Math.floor(Math.random() * 5)]
            item.thumbnail = `/static/mock/image${Math.floor(Math.random() * 5) + 1}.jpg`
            break
          case 'music':
            item.title = ['流行歌曲', '电影配乐', '钢琴曲', '电子音乐', '爵士乐'][Math.floor(Math.random() * 5)]
            break
          case 'video':
            item.title = ['自然风光', '产品展示', '抽象动画', '城市延时', '人物特写'][Math.floor(Math.random() * 5)]
            item.thumbnail = `/static/mock/video${Math.floor(Math.random() * 5) + 1}.jpg`
            break
        }
        
        items.push(item)
      }
      
      // 按时间降序排序
      return items.sort((a, b) => b.createTime - a.createTime)
    }
  }
}
</script>

<style lang="scss" scoped>
.history-list {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  
  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    
    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 30rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  // 日期分组
  .history-group {
    margin-bottom: 30rpx;
    
    .date-header {
      padding: 20rpx 0;
      
      .date-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  // 历史记录项
  .history-item {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    
    // 缩略图
    .item-thumbnail {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
      overflow: hidden;
      margin-right: 20rpx;
      background-color: #f5f7fa;
      
      image {
        width: 100%;
        height: 100%;
      }
      
      &.item-thumbnail-text {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10rpx;
        
        .text-content {
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 5;
          -webkit-box-orient: vertical;
        }
      }
      
      &.item-thumbnail-music {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #6e7fff, #4e5af4);
        
        image {
          width: 60%;
          height: 60%;
        }
      }
    }
    
    // 信息区域
    .item-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
        
        .item-title {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 70%;
        }
        
        .item-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .item-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        
        .item-tag {
          font-size: 24rpx;
          padding: 6rpx 16rpx;
          border-radius: 20rpx;
          
          &.item-tag-text {
            background-color: #e6f7ff;
            color: #1890ff;
          }
          
          &.item-tag-image {
            background-color: #f6ffed;
            color: #52c41a;
          }
          
          &.item-tag-music {
            background-color: #fff7e6;
            color: #fa8c16;
          }
          
          &.item-tag-video {
            background-color: #f9f0ff;
            color: #722ed1;
          }
        }
        
        .item-actions {
          display: flex;
          align-items: center;
          
          .action-btn {
            width: 50rpx;
            height: 50rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 20rpx;
            
            image {
              width: 36rpx;
              height: 36rpx;
            }
          }
        }
      }
    }
  }
  
  // 加载更多
  .load-more {
    text-align: center;
    padding: 30rpx 0;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
  }
  
  // 删除确认弹窗
  .delete-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    
    .modal-mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.6);
    }
    
    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      
      .modal-title {
        padding: 30rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        border-bottom: 1rpx solid #eee;
      }
      
      .modal-body {
        padding: 40rpx 30rpx;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
      
      .modal-footer {
        display: flex;
        border-top: 1rpx solid #eee;
        
        button {
          flex: 1;
          height: 90rpx;
          line-height: 90rpx;
          text-align: center;
          font-size: 30rpx;
          border: none;
          border-radius: 0;
          
          &.cancel-btn {
            color: #666;
            background-color: #f5f7fa;
          }
          
          &.confirm-btn {
            color: #ff4d4f;
            background-color: #fff;
          }
        }
      }
    }
  }
}
</style> 