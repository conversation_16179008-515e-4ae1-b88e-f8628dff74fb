/**
 * 文生视频功能工作流配置
 * 定义文生视频功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 文生视频工作流配置
 */
export const 文生视频工作流配置 = {
    // 工作流基础信息
    workflowId: 'text_to_video_workflow_001',
    workflowType: 'text_to_video',
    moduleName: '文生视频',
    
    // 工作流描述
    description: '基于文本描述生成视频的AI工作流',

    // 结构化参数定义
    structuredParams: {
        
        prompt: {
            type: 'text',
            required: true,
            placeholder: '{{prompt}}',
            description: 'prompt'
        },
        
        duration: {
            type: 'text',
            required: true,
            placeholder: '{{duration}}',
            description: 'duration'
        },
        
        style: {
            type: 'text',
            required: true,
            placeholder: '{{style}}',
            description: 'style'
        },
        
        resolution: {
            type: 'text',
            required: true,
            placeholder: '{{resolution}}',
            description: 'resolution'
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下参数执行文生视频任务：

- prompt：{{prompt}}
- duration：{{duration}}
- style：{{style}}
- resolution：{{resolution}}

请提供详细的处理结果。
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: 'object'
        }
    },

    // 费用配置
    pricing: {
        basePrice: 50,
        memberDiscount: 0.8
    },

    // 执行配置
    execution: {
        timeout: 300000,
        maxRetries: 3,
        pollInterval: 2000,
        enableCache: true
    }
};

/**
 * 文生视频参数验证规则
 */
export const 文生视频参数验证规则 = {
    required: ['prompt', 'duration', 'style', 'resolution'],
    formats: {},
    ranges: {},
    enums: {}
};

/**
 * 文生视频错误码定义
 */
export const 文生视频错误码 = {
    INVALID_PARAMS: { code: 'TEXT_TO_VIDEO_001', message: '参数格式不正确' },
    INSUFFICIENT_COINS: { code: 'TEXT_TO_VIDEO_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'TEXT_TO_VIDEO_008', message: '工作流执行超时' },
    WORKFLOW_FAILED: { code: 'TEXT_TO_VIDEO_009', message: '工作流执行失败' },
    UNKNOWN_ERROR: { code: 'TEXT_TO_VIDEO_999', message: '未知错误' }
};

/**
 * 文生视频状态定义
 */
export const 文生视频状态 = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

export default {
    文生视频工作流配置,
    文生视频参数验证规则,
    文生视频错误码,
    文生视频状态
};