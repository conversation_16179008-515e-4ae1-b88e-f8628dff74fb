/**
 * 通用工作流对接基础类
 * 为所有功能板块提供统一的工作流对接机制
 * 创建时间：2025-01-11
 */

import { apiRequest } from './request.js';

/**
 * 工作流基础类
 * 所有功能板块的工作流对接都应该继承此类
 */
export class WorkflowBase {
    constructor(moduleName, workflowConfig) {
        this.moduleName = moduleName;
        this.workflowConfig = workflowConfig;
        this.requestIdPrefix = `${moduleName}_${Date.now()}`;
    }

    /**
     * 执行工作流的通用方法
     * @param {Object} structuredParams - 结构化参数
     * @param {Object} options - 执行选项
     */
    async executeWorkflow(structuredParams, options = {}) {
        try {
            // 1. 生成请求ID
            const requestId = this.generateRequestId();

            // 2. 构建标准请求格式
            const requestData = this.buildStandardRequest(requestId, structuredParams);

            // 3. 发送工作流执行请求
            const response = await apiRequest('workflow/execute', {
                method: 'POST',
                body: requestData
            });

            if (!response.success) {
                throw new Error(response.message || '工作流启动失败');
            }

            // 4. 如果需要轮询，开始轮询状态
            if (options.enablePolling !== false) {
                return await this.pollWorkflowStatus(requestId, options);
            }

            return {
                success: true,
                data: {
                    requestId,
                    workflowId: response.data.workflowId,
                    status: 'started'
                }
            };

        } catch (error) {
            console.error(`${this.moduleName}工作流执行失败:`, error);
            throw error;
        }
    }

    /**
     * 构建标准请求格式
     * @param {string} requestId - 请求ID
     * @param {Object} structuredParams - 结构化参数
     */
    buildStandardRequest(requestId, structuredParams) {
        return {
            requestId,
            userId: uni.getStorageSync('userId') || 'anonymous',
            timestamp: Date.now(),
            moduleName: this.moduleName,
            workflowId: this.workflowConfig.workflowId,
            workflowType: this.workflowConfig.workflowType,
            structuredParams: {
                ...structuredParams,
                module: this.moduleName,
                timestamp: Date.now()
            }
        };
    }

    /**
     * 轮询工作流状态
     * @param {string} requestId - 请求ID
     * @param {Object} options - 轮询选项
     */
    async pollWorkflowStatus(requestId, options = {}) {
        const {
            maxPolls = 200,
            pollInterval = 3000,
            timeout = 600000,
            onProgress
        } = options;

        let pollCount = 0;
        const startTime = Date.now();

        return new Promise((resolve, reject) => {
            const poll = async () => {
                try {
                    pollCount++;
                    const currentTime = Date.now();

                    // 检查超时
                    if (currentTime - startTime > timeout) {
                        reject(new Error('工作流执行超时'));
                        return;
                    }

                    // 检查轮询次数
                    if (pollCount > maxPolls) {
                        reject(new Error('轮询次数超限'));
                        return;
                    }

                    // 查询状态
                    const statusResult = await this.queryWorkflowStatus(requestId);
                    const status = statusResult.data.status;

                    // 调用进度回调
                    if (onProgress && typeof onProgress === 'function') {
                        onProgress({
                            ...statusResult.data,
                            pollCount,
                            elapsedTime: currentTime - startTime
                        });
                    }

                    // 处理不同状态
                    if (status === 'completed') {
                        const result = await this.getWorkflowResult(requestId);
                        resolve(result);
                    } else if (status === 'failed') {
                        reject(new Error(statusResult.data.message || '工作流执行失败'));
                    } else if (status === 'cancelled') {
                        reject(new Error('工作流已被取消'));
                    } else {
                        // 继续轮询
                        setTimeout(poll, pollInterval);
                    }

                } catch (error) {
                    reject(error);
                }
            };

            // 开始轮询
            poll();
        });
    }

    /**
     * 查询工作流状态
     * @param {string} requestId - 请求ID
     */
    async queryWorkflowStatus(requestId) {
        return await apiRequest(`workflow/status?requestId=${requestId}`);
    }

    /**
     * 获取工作流结果
     * @param {string} requestId - 请求ID
     */
    async getWorkflowResult(requestId) {
        return await apiRequest(`workflow/result?requestId=${requestId}`);
    }

    /**
     * 取消工作流执行
     * @param {string} requestId - 请求ID
     */
    async cancelWorkflow(requestId) {
        return await apiRequest('workflow/cancel', {
            method: 'POST',
            body: { requestId }
        });
    }

    /**
     * 生成请求ID
     */
    generateRequestId() {
        return `${this.requestIdPrefix}_${Math.random().toString(36).substr(2, 6)}`;
    }

    /**
     * 验证结构化参数
     * @param {Object} params - 参数对象
     * @param {Array} requiredFields - 必需字段
     */
    validateStructuredParams(params, requiredFields = []) {
        const missingFields = requiredFields.filter(field => !params[field]);
        if (missingFields.length > 0) {
            throw new Error(`缺少必需参数: ${missingFields.join(', ')}`);
        }
        return true;
    }

    /**
     * 格式化错误信息
     * @param {Error} error - 错误对象
     */
    formatError(error) {
        return {
            success: false,
            message: error.message || '操作失败',
            code: error.code || 'UNKNOWN_ERROR',
            module: this.moduleName,
            timestamp: Date.now()
        };
    }
}

/**
 * 工作流配置验证器
 */
export class WorkflowConfigValidator {
    static validate(config) {
        const requiredFields = ['workflowId', 'workflowType'];
        const missingFields = requiredFields.filter(field => !config[field]);
        
        if (missingFields.length > 0) {
            throw new Error(`工作流配置缺少必需字段: ${missingFields.join(', ')}`);
        }

        return true;
    }
}

/**
 * 结构化参数构建器
 */
export class StructuredParamsBuilder {
    constructor() {
        this.params = {};
    }

    /**
     * 添加文本参数
     * @param {string} key - 参数键
     * @param {string} value - 参数值
     */
    addTextParam(key, value) {
        this.params[key] = {
            type: 'text',
            value: value || '',
            placeholder: `{{${key}}}`
        };
        return this;
    }

    /**
     * 添加媒体参数
     * @param {string} key - 参数键
     * @param {Object} mediaInfo - 媒体信息
     */
    addMediaParam(key, mediaInfo) {
        this.params[key] = {
            type: 'media',
            value: mediaInfo,
            placeholder: `{{${key}}}`,
            metadata: {
                size: mediaInfo.size || 'unknown',
                duration: mediaInfo.duration || 0,
                format: mediaInfo.format || 'unknown'
            }
        };
        return this;
    }

    /**
     * 添加配置参数
     * @param {string} key - 参数键
     * @param {any} value - 参数值
     */
    addConfigParam(key, value) {
        this.params[key] = {
            type: 'config',
            value: value,
            placeholder: `{{${key}}}`
        };
        return this;
    }

    /**
     * 构建最终参数对象
     */
    build() {
        return this.params;
    }

    /**
     * 重置构建器
     */
    reset() {
        this.params = {};
        return this;
    }
}

export default WorkflowBase;
