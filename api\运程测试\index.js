/**
 * 运程测试功能统一入口
 * 提供2025年运程分析和预测服务
 * 创建时间：2025-01-11
 */

// 导入工作流配置
import { 运程测试配置 } from './工作流配置.js';

// 导入工作流执行接口
import { 
    执行运程测试工作流,
    查询运程测试状态,
    取消运程测试工作流
} from './工作流执行接口.js';

/**
 * 运程测试主函数
 * @param {Object} formData - 表单数据
 * @param {string} formData.name - 姓名
 * @param {string} formData.gender - 性别 (male/female)
 * @param {string} formData.birthDate - 出生日期 (YYYY-MM-DD)
 * @param {string} formData.birthTime - 出生时间 (HH:mm)
 * @param {string} formData.testType - 测试类型
 * @param {string} formData.analysisDepth - 分析深度
 * @returns {Promise<Object>} 运程测试结果
 */
export async function 运程测试(formData) {
    try {
        console.log('🔮 开始运程测试...', formData);
        
        // 验证必需参数
        const requiredFields = ['name', 'gender', 'birthDate', 'birthTime', 'testType'];
        for (const field of requiredFields) {
            if (!formData[field]) {
                throw new Error(`缺少必需参数: ${field}`);
            }
        }
        
        // 执行工作流
        const result = await 执行运程测试工作流(formData);
        
        console.log('✅ 运程测试完成:', result);
        return result;
        
    } catch (error) {
        console.error('❌ 运程测试失败:', error);
        throw error;
    }
}

/**
 * 查询运程测试状态
 * @param {string} requestId - 请求ID
 * @returns {Promise<Object>} 状态信息
 */
export async function 查询运程测试状态(requestId) {
    return await 查询运程测试状态(requestId);
}

/**
 * 取消运程测试
 * @param {string} requestId - 请求ID
 * @returns {Promise<Object>} 取消结果
 */
export async function 取消运程测试(requestId) {
    return await 取消运程测试工作流(requestId);
}

/**
 * 获取运程测试配置
 * @returns {Object} 配置信息
 */
export function 获取运程测试配置() {
    return 运程测试配置;
}

// 默认导出
export default {
    运程测试,
    查询运程测试状态,
    取消运程测试,
    获取运程测试配置,
    配置: 运程测试配置
};
