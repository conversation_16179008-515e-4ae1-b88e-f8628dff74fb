<template>
  <view class="payment-module" v-if="visible">
    <view class="popup-mask" @tap="handleClose"></view>
    <view class="payment-content">
      <!-- 头部 -->
      <view class="module-header">
        <view class="title">确认支付</view>
        <view class="close-btn" @tap="handleClose">
          <image src="/static/icons/close.png" mode="aspectFit" />
        </view>
      </view>
      
      <!-- 订单信息 -->
      <view class="order-info">
        <view class="order-name">{{ orderInfo.name }}</view>
        <view class="order-price">￥{{ orderInfo.price.toFixed(2) }}</view>
      </view>
      
      <!-- 支付方式 -->
      <view class="payment-methods">
        <view class="method-title">选择支付方式</view>
        <view 
          v-for="method in availableMethods" 
          :key="method.id"
          class="method-item"
          :class="{ active: selectedMethod === method.id }"
          @tap="selectPaymentMethod(method.id)"
        >
          <view class="method-icon">
            <image :src="method.icon" mode="aspectFit" />
          </view>
          <view class="method-name">{{ method.name }}</view>
          <view class="check-circle" :class="{ checked: selectedMethod === method.id }"></view>
        </view>
      </view>
      
      <!-- 支付按钮 -->
      <view class="pay-button" @tap="handlePay" :class="{ loading: isProcessing }">
        <text v-if="!isProcessing">立即支付</text>
        <view class="loading-dots" v-else>
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </view>
      
      <!-- 支付协议 -->
      <view class="agreement">
        <view class="checkbox" @tap="toggleAgreement">
          <view class="check" :class="{ checked: isAgreed }"></view>
        </view>
        <view class="agreement-text">
          <text>我已阅读并同意</text>
          <text class="link" @tap="goToAgreement">《支付服务协议》</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { PAYMENT_METHOD } from '@/services/OrderService';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: () => ({})
    },
    defaultMethod: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedMethod: '',
      isAgreed: true,
      isProcessing: false,
      
      // 支付方式定义
      methods: {
        [PAYMENT_METHOD.WECHAT]: {
          id: PAYMENT_METHOD.WECHAT,
          name: '微信支付',
          icon: '/static/payment/wechat.png',
          platforms: ['h5', 'pc', 'mp-weixin', 'app']
        },
        [PAYMENT_METHOD.ALIPAY]: {
          id: PAYMENT_METHOD.ALIPAY,
          name: '支付宝',
          icon: '/static/payment/alipay.png',
          platforms: ['h5', 'pc', 'app']
        },
        [PAYMENT_METHOD.WXMP]: {
          id: PAYMENT_METHOD.WXMP,
          name: '小程序支付',
          icon: '/static/payment/wechat.png',
          platforms: ['mp-weixin']
        }
      },
      platform: ''
    };
  },
  computed: {
    // 当前平台可用的支付方式
    availableMethods() {
      const methods = [];
      for (const key in this.methods) {
        if (this.methods[key].platforms.includes(this.platform)) {
          methods.push(this.methods[key]);
        }
      }
      return methods;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initialize();
      }
    },
    defaultMethod(newVal) {
      if (newVal) {
        this.selectedMethod = newVal;
      }
    }
  },
  created() {
    this.detectPlatform();
    this.initialize();
  },
  methods: {
    // 初始化
    initialize() {
      // 优先使用传入的默认支付方式
      if (this.defaultMethod && this.methods[this.defaultMethod]) {
        this.selectedMethod = this.defaultMethod;
      } else if (this.availableMethods.length > 0) {
        // 否则选择第一个可用的支付方式
        this.selectedMethod = this.availableMethods[0].id;
      }
    },
    
    // 检测当前平台
    detectPlatform() {
      // #ifdef H5
      this.platform = 'h5';
      // #endif
      
      // #ifdef MP-WEIXIN
      this.platform = 'mp-weixin';
      // #endif
      
      // #ifdef APP-PLUS
      this.platform = 'app';
      // #endif
      
      // 判断是否为PC网页
      // #ifdef H5
      if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        this.platform = 'pc';
      }
      // #endif
    },
    
    // 选择支付方式
    selectPaymentMethod(methodId) {
      this.selectedMethod = methodId;
    },
    
    // 切换协议同意状态
    toggleAgreement() {
      this.isAgreed = !this.isAgreed;
    },
    
    // 前往支付协议页面
    goToAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/payment'
      });
    },
    
    // 关闭弹窗
    handleClose() {
      if (this.isProcessing) return;
      this.$emit('close');
    },
    
    // 处理支付
    async handlePay() {
      // 检查是否同意协议
      if (!this.isAgreed) {
        uni.showToast({
          title: '请先同意服务协议',
          icon: 'none'
        });
        return;
      }
      
      // 检查订单信息
      if (!this.orderInfo || !this.orderInfo.id) {
        uni.showToast({
          title: '订单信息无效',
          icon: 'none'
        });
        return;
      }
      
      // 检查支付方式
      if (!this.selectedMethod) {
        uni.showToast({
          title: '请选择支付方式',
          icon: 'none'
        });
        return;
      }
      
      // 设置处理中状态
      this.isProcessing = true;
      
      try {
        // 根据不同平台和支付方式调用不同的支付接口
        const payResult = await this.executePayment();
        
        // 处理支付结果
        this.$emit('payment-result', payResult);
      } catch (error) {
        console.error('支付失败:', error);
        this.$emit('payment-result', { 
          status: 'fail',
          message: '支付失败，请重试'
        });
      } finally {
        // 无论成功失败，都取消处理中状态
        this.isProcessing = false;
      }
    },
    
    // 执行支付
    async executePayment() {
      // 实际开发中，这里应该调用支付服务的接口获取支付参数
      // 由于是模拟支付过程，这里直接返回模拟结果
      
      // 模拟支付延时
      await this.simulatePayment();
      
      // 模拟成功概率为80%
      const isSuccess = Math.random() < 0.8;
      
      if (isSuccess) {
        return {
          status: 'success',
          data: {
            method: this.selectedMethod,
            time: new Date().getTime(),
            transactionId: 'PAY' + new Date().getTime()
          }
        };
      } else {
        return {
          status: 'fail',
          message: '支付失败，请重试'
        };
      }
    },
    
    // 模拟支付过程
    simulatePayment() {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve();
        }, 2000);
      });
    },
    
    // 微信支付
    async wechatPay(params) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        if (typeof WeixinJSBridge === 'undefined') {
          // H5环境下需确保WeixinJSBridge已加载
          if (document.addEventListener) {
            document.addEventListener('WeixinJSBridgeReady', this.onWeixinJSBridgeReady(params, resolve, reject), false);
          } else if (document.attachEvent) {
            document.attachEvent('WeixinJSBridgeReady', this.onWeixinJSBridgeReady(params, resolve, reject));
            document.attachEvent('onWeixinJSBridgeReady', this.onWeixinJSBridgeReady(params, resolve, reject));
          }
        } else {
          this.onWeixinJSBridgeReady(params, resolve, reject)();
        }
        // #endif
        
        // #ifdef MP-WEIXIN
        // 小程序支付
        uni.requestPayment({
          ...params,
          success: (res) => {
            resolve({
              status: 'success',
              data: res
            });
          },
          fail: (err) => {
            if (err.errMsg.indexOf('cancel') > -1) {
              resolve({
                status: 'cancel',
                message: '用户取消支付'
              });
            } else {
              reject(err);
            }
          }
        });
        // #endif
        
        // #ifdef APP-PLUS
        // APP微信支付
        uni.requestPayment({
          provider: 'wxpay',
          orderInfo: params, // APP支付需要整个支付参数对象
          success: (res) => {
            resolve({
              status: 'success',
              data: res
            });
          },
          fail: (err) => {
            if (err.errMsg.indexOf('cancel') > -1) {
              resolve({
                status: 'cancel',
                message: '用户取消支付'
              });
            } else {
              reject(err);
            }
          }
        });
        // #endif
      });
    },
    
    // 微信JSAPI支付回调
    onWeixinJSBridgeReady(params, resolve, reject) {
      return function() {
        WeixinJSBridge.invoke(
          'getBrandWCPayRequest', 
          params,
          (res) => {
            if (res.err_msg === 'get_brand_wcpay_request:ok') {
              resolve({
                status: 'success',
                data: res
              });
            } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
              resolve({
                status: 'cancel',
                message: '用户取消支付'
              });
            } else {
              reject(res);
            }
          }
        );
      };
    },
    
    // 支付宝支付
    async alipayPay(orderString) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        // H5环境下创建表单提交
        const div = document.createElement('div');
        div.style.display = 'none';
        document.body.appendChild(div);
        div.innerHTML = orderString;
        document.forms[0].setAttribute('target', '_blank');
        document.forms[0].submit();
        
        // 由于支付宝会在新窗口打开，这里无法准确获取支付结果
        // 实际应用中需要通过轮询订单状态或支付宝回调通知获取支付结果
        resolve({
          status: 'pending',
          message: '请在新开窗口完成支付'
        });
        // #endif
        
        // #ifdef APP-PLUS
        // APP支付宝支付
        uni.requestPayment({
          provider: 'alipay',
          orderInfo: orderString, // 支付宝支付需要orderString
          success: (res) => {
            resolve({
              status: 'success',
              data: res
            });
          },
          fail: (err) => {
            if (err.errMsg.indexOf('cancel') > -1) {
              resolve({
                status: 'cancel',
                message: '用户取消支付'
              });
            } else {
              reject(err);
            }
          }
        });
        // #endif
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.payment-module {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  
  .popup-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
  }
  
  .payment-content {
    position: relative;
    width: 100%;
    height: auto;
    max-height: 80vh;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-out;
    
    .module-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;
      
      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .close-btn {
        width: 50rpx;
        height: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        image {
          width: 30rpx;
          height: 30rpx;
        }
      }
    }
    
    .order-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30rpx;
      
      .order-name {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 15rpx;
      }
      
      .order-price {
        font-size: 48rpx;
        font-weight: bold;
        color: #ff6b00;
      }
    }
    
    .payment-methods {
      margin-bottom: 30rpx;
      
      .method-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 20rpx;
      }
      
      .method-item {
        display: flex;
        align-items: center;
        padding: 20rpx 10rpx;
        border-bottom: 1rpx solid #f5f5f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .method-icon {
          width: 50rpx;
          height: 50rpx;
          margin-right: 20rpx;
          
          image {
            width: 100%;
            height: 100%;
          }
        }
        
        .method-name {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .check-circle {
          width: 36rpx;
          height: 36rpx;
          border-radius: 50%;
          border: 1rpx solid #ddd;
          
          &.checked {
            border: none;
            background-color: #007aff;
            position: relative;
            
            &:after {
              content: '';
              position: absolute;
              width: 20rpx;
              height: 10rpx;
              border: 4rpx solid #fff;
              border-top: none;
              border-right: none;
              transform: rotate(-45deg);
              top: 10rpx;
              left: 8rpx;
            }
          }
        }
        
        &.active {
          background-color: #f7f8fc;
        }
      }
    }
    
    .pay-button {
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(to right, #007aff, #0056b3);
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
      border-radius: 45rpx;
      margin-bottom: 30rpx;
      
      &.loading {
        opacity: 0.8;
      }
      
      .loading-dots {
        display: flex;
        align-items: center;
        
        .dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background-color: #fff;
          margin: 0 8rpx;
          animation: loading 1.4s infinite ease-in-out both;
          
          &:nth-child(1) {
            animation-delay: -0.32s;
          }
          
          &:nth-child(2) {
            animation-delay: -0.16s;
          }
        }
      }
    }
    
    .agreement {
      display: flex;
      align-items: center;
      justify-content: center;
      
      .checkbox {
        margin-right: 10rpx;
        
        .check {
          width: 30rpx;
          height: 30rpx;
          border-radius: 4rpx;
          border: 1rpx solid #ddd;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.checked {
            background-color: #007aff;
            border-color: #007aff;
            position: relative;
            
            &:after {
              content: '';
              width: 16rpx;
              height: 8rpx;
              border: 4rpx solid #fff;
              border-top: none;
              border-right: none;
              transform: rotate(-45deg);
              position: absolute;
              top: 6rpx;
              left: 4rpx;
            }
          }
        }
      }
      
      .agreement-text {
        font-size: 24rpx;
        color: #999;
        
        .link {
          color: #007aff;
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes loading {
  0%, 80%, 100% { 
    transform: scale(0);
  } 
  40% { 
    transform: scale(1.0);
  }
}
</style> 