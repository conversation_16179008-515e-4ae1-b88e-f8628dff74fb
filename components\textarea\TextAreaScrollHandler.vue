<template>
  <view class="scroll-handler-container" :class="{'has-overflow': hasOverflow}">
    <slot></slot>
    
    <!-- H5平台的滚动指示器 -->
    <view 
      v-if="isH5 && hasOverflow" 
      class="scroll-indicator"
      :class="{'show-indicator': hasOverflow && !isTouching}"
    ></view>
  </view>
</template>

<script>
export default {
  name: 'TextAreaScrollHandler',
  props: {
    contentElement: {
      type: Object,
      default: null
    },
    isH5: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      hasOverflow: false,
      isTouching: false,
      touchStartY: 0,
      touchStartX: 0,
      touchStartTime: 0,
      isScrolling: false,
      initialScrollTop: 0,
      momentum: 0,
      animationFrameId: null,
      scrollTimers: [],
      userHasScrolled: false,
      currentScrollTop: 0,
      lastScrollAttemptTime: 0,
      scrollAttempts: 0
    };
  },
  methods: {
    checkOverflow() {
      const textarea = this.contentElement;
      if (!textarea) return false;
      
      try {
        // 检查文本区域是否有溢出内容需要滚动
        const hasOverflow = textarea.scrollHeight > textarea.clientHeight;
        if (this.hasOverflow !== hasOverflow) {
          this.hasOverflow = hasOverflow;
          this.$emit('overflow-change', hasOverflow);
        }
        return hasOverflow;
      } catch (e) {
        console.error('检查溢出失败:', e);
        return false;
      }
    },
    
    handleTouchStart(e) {
      if (!this.contentElement) return;
      
      this.isTouching = true;
      this.touchStartY = e.touches[0].clientY;
      this.touchStartX = e.touches[0].clientX;
      this.touchStartTime = Date.now();
      this.initialScrollTop = this.contentElement.scrollTop;
      
      // 停止任何正在进行的滚动动画
      this.stopScrollAnimation();
      
      // 发射事件
      this.$emit('touch-start', {
        touchStartY: this.touchStartY,
        touchStartX: this.touchStartX,
        touchStartTime: this.touchStartTime,
        initialScrollTop: this.initialScrollTop
      });
    },
    
    handleTouchMove(e) {
      if (!this.contentElement || !this.isTouching) return;
      
      const currentY = e.touches[0].clientY;
      const currentX = e.touches[0].clientX;
      
      // 计算X和Y方向的移动距离
      const deltaY = this.touchStartY - currentY;
      const deltaX = Math.abs(this.touchStartX - currentX);
      
      // 如果水平移动大于垂直移动，可能是在滑动而不是滚动
      if (deltaX > Math.abs(deltaY) * 1.5) return;
      
      // 标记为滚动状态
      this.isScrolling = true;
      
      // 计算新的滚动位置
      const newScrollTop = Math.max(0, this.initialScrollTop + deltaY);
      
      // 应用滚动
      if (this.contentElement) {
        this.contentElement.scrollTop = newScrollTop;
        this.currentScrollTop = newScrollTop;
        
        if (deltaY !== 0) {
          this.userHasScrolled = true;
        }
      }
      
      // 发射事件
      this.$emit('touch-move', {
        deltaY,
        deltaX,
        currentScrollTop: this.currentScrollTop,
        isScrolling: this.isScrolling
      });
    },
    
    handleTouchEnd(e) {
      if (!this.isTouching) return;
      
      this.isTouching = false;
      
      if (this.isScrolling) {
        const endTime = Date.now();
        const duration = endTime - this.touchStartTime;
        
        if (duration < 300) {
          // 计算滚动动量
          const deltaY = this.contentElement ? this.contentElement.scrollTop - this.initialScrollTop : 0;
          const velocity = deltaY / duration;
          this.momentum = velocity * 300; // 调整动量因子
          
          // 应用动量滚动
          this.applyMomentumScroll();
        }
        
        this.isScrolling = false;
      }
      
      // 发射事件
      this.$emit('touch-end', {
        momentum: this.momentum,
        isScrolling: this.isScrolling
      });
    },
    
    applyMomentumScroll() {
      if (!this.contentElement || Math.abs(this.momentum) < 1) return;
      
      const startTime = Date.now();
      const startScrollTop = this.contentElement.scrollTop;
      const targetScrollTop = startScrollTop + this.momentum;
      const duration = 500; // 动画持续时间
      
      // 使用requestAnimationFrame进行平滑滚动
      const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        
        if (elapsed >= duration) {
          if (this.contentElement) {
            this.contentElement.scrollTop = targetScrollTop;
          }
          return;
        }
        
        // 使用缓动函数
        const progress = this.easeOutQuad(elapsed / duration);
        const currentScroll = startScrollTop + (targetScrollTop - startScrollTop) * progress;
        
        if (this.contentElement) {
          this.contentElement.scrollTop = currentScroll;
          this.currentScrollTop = currentScroll;
        }
        
        this.animationFrameId = requestAnimationFrame(animate);
      };
      
      this.animationFrameId = requestAnimationFrame(animate);
    },
    
    easeOutQuad(t) {
      return t * (2 - t);
    },
    
    stopScrollAnimation() {
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
    },
    
    scrollToBottom() {
      if (!this.contentElement) return;
      
      const now = Date.now();
      if (now - this.lastScrollAttemptTime < 100) {
        // 防止短时间内重复调用
        return;
      }
      
      this.lastScrollAttemptTime = now;
      
      try {
        // 清除任何现有的计时器
        this.scrollTimers.forEach(timer => clearTimeout(timer));
        this.scrollTimers = [];
        
        // 立即尝试滚动一次
        this.applyScroll();
        
        // 然后在短时间内尝试多次，以确保内容更新后也能滚动到底部
        for (let i = 0; i < 5; i++) {
          const timer = setTimeout(() => {
            this.applyScroll();
          }, 50 * (i + 1));
          
          this.scrollTimers.push(timer);
        }
      } catch (e) {
        console.error('滚动到底部失败:', e);
      }
    },
    
    applyScroll() {
      if (!this.contentElement) return;
      
      try {
        const scrollHeight = this.contentElement.scrollHeight;
        this.contentElement.scrollTop = scrollHeight;
        this.currentScrollTop = scrollHeight;
      } catch (e) {
        console.error('应用滚动失败:', e);
      }
    },
    
    handleMouseWheel(e) {
      if (!this.contentElement) return;
      
      if (e.deltaY !== 0) {
        this.userHasScrolled = true;
      }
      
      this.$emit('mouse-wheel', e);
    }
  },
  created() {
    // 监听window resize事件
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.checkOverflow);
    }
  },
  beforeUnmount() {
    // 清理事件监听器和定时器
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', this.checkOverflow);
    }
    
    this.stopScrollAnimation();
    this.scrollTimers.forEach(timer => clearTimeout(timer));
  }
}
</script>

<style scoped>
.scroll-handler-container {
  position: relative;
  width: 100%;
}

.scroll-indicator {
  position: absolute;
  right: 2px;
  top: 2px;
  bottom: 2px;
  width: 3px;
  border-radius: 2px;
  background-color: rgba(150, 150, 170, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.scroll-indicator.show-indicator {
  opacity: 1;
}
</style> 