<template>
  <view class="share-module" v-if="visible">
    <view class="share-mask" @tap="closeShare"></view>
    <view class="share-content">
      <view class="share-header">
        <text class="share-title">分享</text>
        <view class="close-btn" @tap="closeShare">
          <image src="/static/icons/close.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="share-item-container">
        <view 
          class="share-item" 
          v-for="(item, index) in shareOptions" 
          :key="index"
          @tap="handleShare(item.type)"
        >
          <image :src="item.icon" mode="aspectFit" class="share-icon"></image>
          <text class="share-text">{{item.text}}</text>
        </view>
      </view>

      <view class="copy-link-container">
        <input class="link-input" type="text" :value="shareLink" disabled />
        <button class="copy-btn" @tap="copyLink">复制链接</button>
      </view>

      <button class="cancel-btn" @tap="closeShare">取消</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ShareModule',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shareTitle: {
      type: String,
      default: '查看我的AI创作'
    },
    shareImage: {
      type: String,
      default: ''
    },
    shareLink: {
      type: String,
      default: 'https://ai.example.com/share/'
    },
    sharePath: {
      type: String,
      default: '/pages/share/index'
    },
    shareParams: {
      type: Object,
      default: () => ({})
    },
    contentType: {
      type: String,
      default: 'text' // 'text', 'image', 'music', 'video'
    }
  },
  data() {
    return {
      shareOptions: [
        {
          type: 'wechat',
          text: '微信',
          icon: '/static/icons/share-wechat.png'
        },
        {
          type: 'moments',
          text: '朋友圈',
          icon: '/static/icons/share-moments.png'
        },
        {
          type: 'qq',
          text: 'QQ',
          icon: '/static/icons/share-qq.png'
        },
        {
          type: 'weibo',
          text: '微博',
          icon: '/static/icons/share-weibo.png'
        },
        {
          type: 'download',
          text: '保存到本地',
          icon: '/static/icons/share-download.png'
        }
      ]
    }
  },
  methods: {
    closeShare() {
      this.$emit('update:visible', false)
    },
    
    handleShare(type) {
      // 这里可以根据不同的分享类型做不同的处理
      if (type === 'wechat') {
        this.shareToWechat()
      } else if (type === 'moments') {
        this.shareToMoments()
      } else if (type === 'qq') {
        this.shareToQQ()
      } else if (type === 'weibo') {
        this.shareToWeibo()
      } else if (type === 'download') {
        this.downloadContent()
      }
      
      this.$emit('on-share', { type, result: 'success' })
    },
    
    shareToWechat() {
      // 调用微信分享API
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        title: this.shareTitle,
        imageUrl: this.shareImage,
        href: this.generateShareUrl()
      })
    },
    
    shareToMoments() {
      // 调用朋友圈分享API
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneTimeline',
        type: 0,
        title: this.shareTitle,
        imageUrl: this.shareImage,
        href: this.generateShareUrl()
      })
    },
    
    shareToQQ() {
      // 调用QQ分享API
      uni.share({
        provider: 'qq',
        type: 0,
        title: this.shareTitle,
        imageUrl: this.shareImage,
        href: this.generateShareUrl()
      })
    },
    
    shareToWeibo() {
      // 调用微博分享API
      uni.share({
        provider: 'sinaweibo',
        type: 0,
        title: this.shareTitle,
        imageUrl: this.shareImage,
        href: this.generateShareUrl()
      })
    },
    
    downloadContent() {
      // 根据内容类型下载
      if (this.contentType === 'image') {
        this.downloadImage()
      } else if (this.contentType === 'music') {
        this.downloadMusic()
      } else if (this.contentType === 'video') {
        this.downloadVideo()
      } else {
        this.downloadText()
      }
    },
    
    downloadImage() {
      uni.downloadFile({
        url: this.shareImage,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({
                  title: '已保存到相册',
                  icon: 'success'
                })
              },
              fail: () => {
                uni.showToast({
                  title: '保存失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },
    
    downloadMusic() {
      // 音乐文件下载逻辑
      uni.showToast({
        title: '音乐下载中...',
        icon: 'loading'
      })
      
      setTimeout(() => {
        uni.showToast({
          title: '音乐已保存',
          icon: 'success'
        })
      }, 1500)
    },
    
    downloadVideo() {
      // 视频文件下载逻辑
      uni.showToast({
        title: '视频下载中...',
        icon: 'loading'
      })
      
      setTimeout(() => {
        uni.showToast({
          title: '视频已保存',
          icon: 'success'
        })
      }, 2000)
    },
    
    downloadText() {
      // 文本内容下载逻辑
      uni.setClipboardData({
        data: this.shareTitle,
        success: () => {
          uni.showToast({
            title: '内容已复制',
            icon: 'success'
          })
        }
      })
    },
    
    copyLink() {
      uni.setClipboardData({
        data: this.shareLink,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
    },
    
    generateShareUrl() {
      // 生成分享链接
      let url = this.shareLink
      if (this.shareParams && Object.keys(this.shareParams).length > 0) {
        url += '?'
        for (const key in this.shareParams) {
          url += `${key}=${encodeURIComponent(this.shareParams[key])}&`
        }
        url = url.slice(0, -1) // 移除最后的&符号
      }
      return url
    }
  }
}
</script>

<style lang="scss" scoped>
.share-module {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  
  .share-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .share-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx;
    box-sizing: border-box;
    animation: slideUp 0.3s ease-out;
    
    .share-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      
      .share-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
      
      .close-btn {
        width: 40rpx;
        height: 40rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    
    .share-item-container {
      display: flex;
      flex-wrap: wrap;
      padding: 20rpx 0;
      
      .share-item {
        width: 20%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 30rpx;
        
        .share-icon {
          width: 80rpx;
          height: 80rpx;
          margin-bottom: 16rpx;
        }
        
        .share-text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
    
    .copy-link-container {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      padding: 16rpx;
      margin: 20rpx 0 30rpx;
      
      .link-input {
        flex: 1;
        font-size: 26rpx;
        color: #999;
        height: 60rpx;
        line-height: 60rpx;
      }
      
      .copy-btn {
        width: 160rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #fff;
        background: linear-gradient(135deg, #6e7fff, #4e5af4);
        border-radius: 30rpx;
        margin: 0;
      }
    }
    
    .cancel-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      font-size: 30rpx;
      color: #333;
      background-color: #f5f5f5;
      border-radius: 44rpx;
      margin-top: 20rpx;
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style> 