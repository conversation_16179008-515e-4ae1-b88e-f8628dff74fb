/**
 * 音乐创作板块接口统一入口
 * 整合所有音乐创作相关的接口模块
 */

// 导入各个接口模块
import 工作流配置接口 from './工作流配置接口.js';
import 用户状态接口 from './用户状态接口.js';
import 工作流执行接口 from './工作流执行接口.js';
import 付费管理接口 from './付费管理接口.js';
import 结果管理接口 from './结果管理接口.js';

// ================================
// 🎯 统一的音乐创作API
// ================================

/**
 * 音乐创作完整业务流程
 * @param {string} workflowMode - 工作流模式
 * @param {Object} formData - 表单数据
 * @param {Object} options - 选项
 */
export async function 音乐创作完整业务流程(workflowMode, formData, options = {}) {
	try {
		// 1. 检查用户状态
		const userStatus = await 用户状态接口.获取用户完整状态();
		if (!userStatus.data.登录状态.isLoggedIn) {
			throw new Error('请先登录');
		}

		// 2. 获取工作流配置
		const workflowConfig = await 工作流配置接口.获取缓存的工作流配置(workflowMode);
		
		// 3. 费用处理
		const feeResult = await 付费管理接口.创作前费用处理(workflowMode, formData);
		if (!feeResult.success) {
			return feeResult; // 返回费用不足等错误
		}

		// 4. 执行工作流
		const creationResult = await 工作流执行接口.完整音乐创作流程(
			workflowMode, 
			formData, 
			{
				...options,
				onProgress: (progress) => {
					// 合并进度回调
					if (options.onProgress) {
						options.onProgress({
							...progress,
							workflowConfig: workflowConfig.data,
							feeInfo: feeResult.data
						});
					}
				}
			}
		);

		// 5. 保存结果（如果需要）
		let saveResult = null;
		if (options.autoSave !== false) {
			saveResult = await 结果管理接口.完整保存创作流程(
				creationResult.data.requestId,
				creationResult.data.result,
				options.saveOptions || {}
			);
		}

		return {
			success: true,
			data: {
				workflowMode: workflowMode,
				userStatus: userStatus.data,
				workflowConfig: workflowConfig.data,
				feeResult: feeResult.data,
				creationResult: creationResult.data,
				saveResult: saveResult?.data || null,
				completedAt: new Date().toISOString()
			}
		};

	} catch (error) {
		console.error('音乐创作完整业务流程失败:', error);
		throw error;
	}
}

/**
 * 初始化音乐创作页面
 * @param {Array} workflowModes - 需要初始化的工作流模式
 */
export async function 初始化音乐创作页面(workflowModes = ['simple', 'advanced', 'instrumental']) {
	try {
		// 并行获取所有需要的数据
		const [
			用户状态,
			...工作流配置列表
		] = await Promise.all([
			用户状态接口.获取用户完整状态(),
			...workflowModes.map(mode => 工作流配置接口.获取缓存的工作流配置(mode))
		]);

		// 组织工作流配置数据
		const 工作流配置映射 = {};
		workflowModes.forEach((mode, index) => {
			工作流配置映射[mode] = 工作流配置列表[index].data;
		});

		return {
			success: true,
			data: {
				用户状态: 用户状态.data,
				工作流配置映射: 工作流配置映射,
				可用模式: workflowModes.filter(mode => 
					工作流配置映射[mode].基础配置.enabled
				),
				初始化时间: new Date().toISOString()
			}
		};

	} catch (error) {
		console.error('初始化音乐创作页面失败:', error);
		throw error;
	}
}

/**
 * 切换工作流模式
 * @param {string} newMode - 新的工作流模式
 * @param {string} currentMode - 当前工作流模式
 */
export async function 切换工作流模式(newMode, currentMode) {
	try {
		// 获取新模式的配置
		const newConfig = await 工作流配置接口.获取缓存的工作流配置(newMode);
		
		// 检查模式是否可用
		if (!newConfig.data.基础配置.enabled) {
			throw new Error(`${newMode}模式当前不可用`);
		}

		return {
			success: true,
			data: {
				newMode: newMode,
				currentMode: currentMode,
				newConfig: newConfig.data,
				switchTime: new Date().toISOString()
			}
		};

	} catch (error) {
		console.error('切换工作流模式失败:', error);
		throw error;
	}
}

// ================================
// 🔧 工具函数
// ================================

/**
 * 格式化工作流模式显示名称
 * @param {string} mode - 工作流模式
 */
export function 格式化工作流模式名称(mode) {
	const names = {
		'simple': '简单模式',
		'advanced': '高级模式',
		'instrumental': '纯音乐模式'
	};
	return names[mode] || mode;
}

/**
 * 获取工作流模式图标
 * @param {string} mode - 工作流模式
 */
export function 获取工作流模式图标(mode) {
	const icons = {
		'simple': '🎵',
		'advanced': '🎼',
		'instrumental': '🎹'
	};
	return icons[mode] || '🎵';
}

/**
 * 验证表单数据
 * @param {string} workflowMode - 工作流模式
 * @param {Object} formData - 表单数据
 */
export function 验证表单数据(workflowMode, formData) {
	const errors = [];

	if (workflowMode === 'simple') {
		if (!formData.simplePrompt || formData.simplePrompt.trim() === '') {
			errors.push('请输入创作提示词');
		}
	} else if (workflowMode === 'advanced') {
		if (!formData.customLyrics || formData.customLyrics.trim() === '') {
			errors.push('请输入自定义歌词');
		}
	} else if (workflowMode === 'instrumental') {
		if (!formData.instrumentalStyle || formData.instrumentalStyle.trim() === '') {
			errors.push('请选择音乐风格');
		}
	}

	// 通用验证
	if (formData.duration && (formData.duration < 30 || formData.duration > 600)) {
		errors.push('音乐时长应在30秒到10分钟之间');
	}

	return {
		isValid: errors.length === 0,
		errors: errors
	};
}

// ================================
// 📤 导出所有接口
// ================================

export {
	// 工作流配置
	工作流配置接口,
	
	// 用户状态
	用户状态接口,
	
	// 工作流执行
	工作流执行接口,
	
	// 付费管理
	付费管理接口,
	
	// 结果管理
	结果管理接口
};

// 默认导出
export default {
	// 核心业务流程
	音乐创作完整业务流程,
	初始化音乐创作页面,
	切换工作流模式,
	
	// 工具函数
	格式化工作流模式名称,
	获取工作流模式图标,
	验证表单数据,
	
	// 各个接口模块
	工作流配置接口,
	用户状态接口,
	工作流执行接口,
	付费管理接口,
	结果管理接口
};
