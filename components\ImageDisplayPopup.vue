<template>
  <div>
    <!-- 中央图片弹窗组件 -->
    <div v-if="visible" class="image-popup-overlay" @click.self="$emit('close')">
      <div class="image-popup">
        <div class="image-popup-header">
          <h3>AI生成的图片</h3>
          <button @click="$emit('close')" class="close-popup-btn">×</button>
        </div>
        <div class="image-popup-content">
          <div v-if="images.length > 0" class="image-popup-list">
            <div 
              v-for="(img, index) in images"
              :key="'popup-img-'+index"
              class="image-popup-item"
              @click="$emit('view', img.image || img)"
            >
              <img :src="img.image || img" class="popup-image" />
              <div class="popup-image-caption">
                图片 {{index + 1}} (点击放大)
              </div>
            </div>
          </div>
          <div v-else class="no-images-message">
            暂无图片
          </div>
        </div>
      </div>
    </div>
    
    <!-- 顶部通知栏 - 避开底部提示词面板 -->
    <div v-if="!visible && images.length > 0" class="image-notification" @click="$emit('open')">
      <span class="image-notification-text">
        AI已生成 {{images.length}} 张图片 (点击查看)
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageDisplayPopup',
  props: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      default: false
    },
    // 要显示的图片数组
    images: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
/* 图片弹窗遮罩层 */
.image-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗容器 */
.image-popup {
  background-color: #1e1e2e;
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 3px solid magenta;
  animation: border-flash 1.5s infinite alternate;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.image-popup-header {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.2);
}

.image-popup-header h3 {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.close-popup-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 弹窗内容区 */
.image-popup-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.image-popup-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.image-popup-item {
  background-color: rgba(30, 30, 45, 0.5);
  border-radius: 12px;
  padding: 10px;
  cursor: pointer;
  transition: transform 0.2s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-popup-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.popup-image {
  width: 100%;
  border-radius: 8px;
  display: block;
  pointer-events: none;
}

.popup-image-caption {
  color: white;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
}

.no-images-message {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  padding: 40px;
}

/* 顶部通知栏 */
.image-notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 0, 255, 0.8);
  padding: 10px 20px;
  border-radius: 30px;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(255, 0, 255, 0.5);
  z-index: 999999;
  cursor: pointer;
  animation: pulse-notification 2s infinite alternate;
}

.image-notification:hover {
  background-color: rgba(255, 0, 255, 1);
}

/* 动画效果 */
@keyframes border-flash {
  from { border-color: magenta; }
  to { border-color: cyan; }
}

@keyframes pulse-notification {
  from { box-shadow: 0 4px 15px rgba(255, 0, 255, 0.5); }
  to { box-shadow: 0 4px 25px rgba(255, 0, 255, 0.9); }
}
</style> 