<template>
  <view class="global-feedback-container">
    <!-- 全局加载状态 -->
    <view v-if="loading.show" class="loading-mask" :class="{ 'transparent': loading.transparent }">
      <view class="loading-content">
        <view class="loading-icon">
          <image :src="loading.customIcon || '/static/icons/loading.gif'" mode="aspectFit"></image>
        </view>
        <view v-if="loading.text" class="loading-text">{{ loading.text }}</view>
      </view>
    </view>
    
    <!-- 全局提示消息 -->
    <view v-if="toast.show" class="toast-container" :class="[`position-${toast.position}`, { 'with-icon': toast.icon }]">
      <view class="toast-content" :class="[`type-${toast.type}`]">
        <image v-if="toast.icon" :src="getToastIcon()" mode="aspectFit" class="toast-icon"></image>
        <text class="toast-text">{{ toast.text }}</text>
      </view>
    </view>
    
    <!-- 全局对话框 -->
    <view v-if="dialog.show" class="dialog-mask">
      <view class="dialog-container" :class="{ 'dialog-confirm-type': dialog.type === 'confirm' }">
        <view v-if="dialog.title" class="dialog-title">{{ dialog.title }}</view>
        
        <view class="dialog-content">
          <text>{{ dialog.content }}</text>
        </view>
        
        <view class="dialog-footer">
          <view v-if="dialog.type === 'confirm'" class="dialog-btn cancel-btn" @tap="onDialogCancel">
            {{ dialog.cancelText || '取消' }}
          </view>
          <view class="dialog-btn confirm-btn" @tap="onDialogConfirm">
            {{ dialog.confirmText || '确定' }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 网络错误提示 -->
    <view v-if="networkError.show" class="network-error-mask">
      <view class="network-error-content">
        <image src="/static/icons/network-error.png" mode="aspectFit" class="error-icon"></image>
        <view class="error-title">{{ networkError.title || '网络连接异常' }}</view>
        <view class="error-desc">{{ networkError.description || '请检查您的网络连接并重试' }}</view>
        <view class="error-btn" @tap="onNetworkRetry">重试</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'GlobalFeedback',
  data() {
    return {
      // 全局加载状态配置
      loading: {
        show: false,
        text: '',
        transparent: false,
        customIcon: '',
        timer: null
      },
      
      // 全局提示消息配置
      toast: {
        show: false,
        text: '',
        type: 'info', // info, success, error, warning
        position: 'center', // top, center, bottom
        icon: true,
        duration: 2000,
        timer: null
      },
      
      // 全局对话框配置
      dialog: {
        show: false,
        title: '',
        content: '',
        type: 'alert', // alert, confirm
        confirmText: '确定',
        cancelText: '取消',
        callback: null,
        cancelCallback: null
      },
      
      // 网络错误提示配置
      networkError: {
        show: false,
        title: '网络连接异常',
        description: '请检查您的网络连接并重试',
        retryCallback: null
      }
    };
  },
  created() {
    // 挂载到全局
    uni.$feedback = this;
  },
  methods: {
    /**
     * 显示加载状态
     * @param {string} text 加载提示文字
     * @param {boolean} transparent 背景是否透明
     * @param {string} customIcon 自定义加载图标
     */
    showLoading(text = '加载中...', transparent = false, customIcon = '') {
      // 清除之前的计时器
      if (this.loading.timer) {
        clearTimeout(this.loading.timer);
        this.loading.timer = null;
      }
      
      this.loading.text = text;
      this.loading.transparent = transparent;
      this.loading.customIcon = customIcon;
      this.loading.show = true;
    },
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
      this.loading.show = false;
    },
    
    /**
     * 显示带有自动隐藏的加载状态
     * @param {string} text 加载提示文字
     * @param {number} duration 显示时长（毫秒）
     * @param {boolean} transparent 背景是否透明
     * @param {string} customIcon 自定义加载图标
     */
    showLoadingTimeout(text = '加载中...', duration = 3000, transparent = false, customIcon = '') {
      this.showLoading(text, transparent, customIcon);
      
      this.loading.timer = setTimeout(() => {
        this.hideLoading();
      }, duration);
    },
    
    /**
     * 显示提示消息
     * @param {string} text 提示文字
     * @param {string} type 提示类型，可选：info, success, error, warning
     * @param {string} position 提示位置，可选：top, center, bottom
     * @param {boolean} icon 是否显示图标
     * @param {number} duration 显示时长（毫秒）
     */
    showToast(text, type = 'info', position = 'center', icon = true, duration = 2000) {
      // 清除之前的计时器
      if (this.toast.timer) {
        clearTimeout(this.toast.timer);
        this.toast.timer = null;
      }
      
      this.toast.text = text;
      this.toast.type = type;
      this.toast.position = position;
      this.toast.icon = icon;
      this.toast.duration = duration;
      this.toast.show = true;
      
      // 设置自动隐藏
      this.toast.timer = setTimeout(() => {
        this.hideToast();
      }, duration);
    },
    
    /**
     * 隐藏提示消息
     */
    hideToast() {
      this.toast.show = false;
    },
    
    /**
     * 获取提示消息的图标
     * @returns {string} 图标路径
     */
    getToastIcon() {
      const iconMap = {
        'info': '/static/icons/info.png',
        'success': '/static/icons/success.png',
        'error': '/static/icons/error.png',
        'warning': '/static/icons/warning.png'
      };
      
      return iconMap[this.toast.type] || iconMap['info'];
    },
    
    /**
     * 显示对话框（警告类型）
     * @param {string} content 对话框内容
     * @param {string} title 对话框标题
     * @param {string} confirmText 确认按钮文字
     * @param {Function} callback 确认回调函数
     */
    showAlert(content, title = '提示', confirmText = '确定', callback = null) {
      this.dialog.content = content;
      this.dialog.title = title;
      this.dialog.confirmText = confirmText;
      this.dialog.type = 'alert';
      this.dialog.callback = callback;
      this.dialog.show = true;
    },
    
    /**
     * 显示对话框（确认类型）
     * @param {string} content 对话框内容
     * @param {string} title 对话框标题
     * @param {string} confirmText 确认按钮文字
     * @param {string} cancelText 取消按钮文字
     * @param {Function} callback 确认回调函数
     * @param {Function} cancelCallback 取消回调函数
     */
    showConfirm(content, title = '确认', confirmText = '确定', cancelText = '取消', callback = null, cancelCallback = null) {
      this.dialog.content = content;
      this.dialog.title = title;
      this.dialog.confirmText = confirmText;
      this.dialog.cancelText = cancelText;
      this.dialog.type = 'confirm';
      this.dialog.callback = callback;
      this.dialog.cancelCallback = cancelCallback;
      this.dialog.show = true;
    },
    
    /**
     * 隐藏对话框
     */
    hideDialog() {
      this.dialog.show = false;
    },
    
    /**
     * 对话框确认按钮点击事件
     */
    onDialogConfirm() {
      this.hideDialog();
      
      if (typeof this.dialog.callback === 'function') {
        this.dialog.callback();
      }
    },
    
    /**
     * 对话框取消按钮点击事件
     */
    onDialogCancel() {
      this.hideDialog();
      
      if (typeof this.dialog.cancelCallback === 'function') {
        this.dialog.cancelCallback();
      }
    },
    
    /**
     * 显示网络错误提示
     * @param {string} title 错误标题
     * @param {string} description 错误描述
     * @param {Function} retryCallback 重试回调函数
     */
    showNetworkError(title = '网络连接异常', description = '请检查您的网络连接并重试', retryCallback = null) {
      this.networkError.title = title;
      this.networkError.description = description;
      this.networkError.retryCallback = retryCallback;
      this.networkError.show = true;
    },
    
    /**
     * 隐藏网络错误提示
     */
    hideNetworkError() {
      this.networkError.show = false;
    },
    
    /**
     * 网络错误重试按钮点击事件
     */
    onNetworkRetry() {
      this.hideNetworkError();
      
      if (typeof this.networkError.retryCallback === 'function') {
        this.networkError.retryCallback();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.global-feedback-container {
  // 全局加载状态
  .loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    
    &.transparent {
      background-color: transparent;
    }
    
    .loading-content {
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 16rpx;
      padding: 30rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .loading-icon {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 20rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
      
      .loading-text {
        font-size: 28rpx;
        color: #fff;
        max-width: 400rpx;
        text-align: center;
      }
    }
  }
  
  // 全局提示消息
  .toast-container {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    
    &.position-top {
      top: 100rpx;
    }
    
    &.position-center {
      top: 50%;
      transform: translate(-50%, -50%);
    }
    
    &.position-bottom {
      bottom: 100rpx;
    }
    
    .toast-content {
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 16rpx;
      padding: 20rpx 30rpx;
      display: flex;
      align-items: center;
      
      &.type-success {
        background-color: rgba(39, 174, 96, 0.9);
      }
      
      &.type-error {
        background-color: rgba(235, 87, 87, 0.9);
      }
      
      &.type-warning {
        background-color: rgba(242, 153, 74, 0.9);
      }
      
      .toast-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
      }
      
      .toast-text {
        font-size: 28rpx;
        color: #fff;
        max-width: 500rpx;
        text-align: center;
      }
    }
  }
  
  // 全局对话框
  .dialog-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    
    .dialog-container {
      width: 600rpx;
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      
      .dialog-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        padding: 30rpx;
        text-align: center;
        border-bottom: 1rpx solid #eee;
      }
      
      .dialog-content {
        padding: 30rpx;
        min-height: 100rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        text-align: center;
      }
      
      .dialog-footer {
        display: flex;
        border-top: 1rpx solid #eee;
        
        .dialog-btn {
          flex: 1;
          height: 88rpx;
          line-height: 88rpx;
          text-align: center;
          font-size: 28rpx;
          
          &.cancel-btn {
            color: #666;
            border-right: 1rpx solid #eee;
          }
          
          &.confirm-btn {
            color: #6366f1;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  // 网络错误提示
  .network-error-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    
    .network-error-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .error-icon {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 40rpx;
      }
      
      .error-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .error-desc {
        font-size: 28rpx;
        color: #999;
        text-align: center;
        margin-bottom: 40rpx;
      }
      
      .error-btn {
        width: 300rpx;
        height: 80rpx;
        background-color: #6366f1;
        color: #fff;
        font-size: 28rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style> 