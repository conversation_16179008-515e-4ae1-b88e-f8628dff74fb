# 文本工具组件

本目录包含与文本生成和处理相关的组件。

## 主要组件

- **MessageInput.vue**: 消息输入组件，支持预设词标签
- **ParamsSelector.vue**: 参数选择器组件

## 使用方法

```javascript
import MessageInput from '@/components/text-tool/MessageInput.vue';

export default {
  components: {
    MessageInput
  }
}
```

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| config | Object | {} | 组件配置对象，包含参数定义 |
| initialValues | Object | {} | 初始参数值 |
| remainingCount | Number | 0 | 剩余可用次数 |
| isPanelExpanded | Boolean | true | 参数面板是否展开 |
| isGenerating | Boolean | false | 是否正在生成内容 |
| coinCost | Number | 20 | 金币消耗数量 |

### 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| submit | {prompt, params, tags, finalPrompt, quantity} | 提交生成请求 |
| param-change | {key, value, type} | 参数值变化 |
| tag-change | {key, tags} | 标签选择变化 |
| prompt-change | promptText | 提示词文本变化 |
| use-suggestion | suggestion | 使用建议提示词 |
| toggle-panel | - | 切换参数面板显示状态 |
| quantity-change | quantity | 数量变化 |
| stop-generation | - | 停止生成请求 |

## 版本说明

当前组件有多个版本：

- **TextPromptTool.vue.simplified-fixed**: 修复后的简化版（推荐）
- **TextPromptTool.vue.clean-fixed**: 修复后的清理版
- **TextPromptTool.vue.simplified**: 简化版
- **TextPromptTool.vue**: 完整版

建议优先使用 simplified-fixed 版本，该版本修复了所有已知问题并优化了性能。

## 注意事项

1. 确保传入的config对象包含必要的参数定义。
2. 组件内部会处理错误情况，但建议在父组件中添加额外的错误处理逻辑。
3. 如果需要定制样式，可以通过CSS变量进行调整。
4. 在移动端使用时，请注意测试滚动和触摸交互的表现。 