<template>
  <view class="article-display">
    <view class="article-header">
      <text class="article-title">{{ content.articleTitle || '未命名文章' }}</text>
      <view class="article-keywords" v-if="hasKeywords">
        <text class="keywords-label">关键词: </text>
        <text class="keyword-item" v-for="(keyword, index) in content.keywords" :key="index">#{{ keyword }}</text>
      </view>
    </view>
    
    <view class="article-content">
      <text class="content-text" :class="{'selectable-text': true}">{{ content.articleContent || content.content }}</text>
    </view>
    
    <view class="sections-container" v-if="hasSections">
      <text class="sections-title">文章结构</text>
      <view class="section-item" v-for="(section, index) in content.sections" :key="index">
        <text class="section-title">{{ section.title || `第${index+1}部分` }}</text>
        <text class="section-summary" :class="{'selectable-text': true}">{{ section.summary }}</text>
      </view>
    </view>
    
    <view class="article-actions">
      <view class="action-btn edit-btn" @tap="handleAction('edit')">
        <text class="action-icon">✏️</text>
        <text class="action-text">编辑</text>
      </view>
      <view class="action-btn publish-btn" @tap="handleAction('publish')">
        <text class="action-icon">📢</text>
        <text class="action-text">发布</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ArticleDisplay',
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  computed: {
    hasKeywords() {
      return this.content.keywords && this.content.keywords.length > 0;
    },
    hasSections() {
      return this.content.sections && this.content.sections.length > 0;
    }
  },
  methods: {
    handleAction(action, data) {
      // 将动作向上传递
      this.$emit('action', action, data || this.content);
    }
  }
}
</script>

<style scoped>
.article-display {
  width: 100%;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.article-header {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(to right, #f0f4c3, #f8f9fa);
  border-radius: 12rpx 12rpx 0 0;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #33691e;
  display: block;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.05);
}

.article-keywords {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10rpx;
}

.keywords-label {
  font-size: 24rpx;
  color: #689f38;
  margin-right: 8rpx;
  font-weight: 500;
}

.keyword-item {
  font-size: 24rpx;
  color: #689f38;
  margin-right: 16rpx;
  background-color: #f1f8e9;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-bottom: 8rpx;
}

.article-content {
  width: 100%;
  line-height: 1.8;
  text-align: justify;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content-text {
  font-size: 30rpx;
  color: #424242;
  white-space: pre-wrap;
  line-height: 1.8;
}

.sections-container {
  width: 100%;
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f1f8e9;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sections-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #33691e;
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #c5e1a5;
  display: block;
}

.section-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #ffffff;
  border-left: 6rpx solid #aed581;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #558b2f;
  margin-bottom: 8rpx;
  display: block;
}

.section-summary {
  font-size: 26rpx;
  color: #616161;
  display: block;
  line-height: 1.6;
}

.article-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
  padding: 10rpx 0;
}

.action-btn {
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex: 1;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.edit-btn {
  background-color: #e8f5e9;
  color: #388e3c;
}

.publish-btn {
  background-color: #dcedc8;
  color: #558b2f;
}

.action-text {
  font-size: 26rpx;
  font-weight: 500;
}

.selectable-text {
  user-select: text;
}
</style> 