<template>
  <view class="follow-menu-container">
    <!-- 触发按钮 -->
    <view 
      class="trigger-btn" 
      :id="triggerId"
      @tap.stop="toggleMenu"
      ref="triggerRef"
    >
      <slot name="trigger">
        <text class="default-trigger">⋯</text>
      </slot>
    </view>
    
    <!-- 跟随菜单 -->
    <view 
      v-if="showMenu" 
      class="menu-overlay"
      @tap="hideMenu"
    >
      <view 
        class="menu-popup"
        :style="menuStyle"
        @tap.stop
      >
        <view 
          v-for="(item, index) in menuItems" 
          :key="index"
          class="menu-item"
          :class="{ 'disabled': item.disabled }"
          @tap="handleMenuClick(item, index)"
        >
          <view v-if="item.icon" class="menu-icon">
            <text>{{ item.icon }}</text>
          </view>
          <text class="menu-text">{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FollowMenu',
  props: {
    // 菜单项列表
    menuItems: {
      type: Array,
      default: () => []
    },
    // 菜单位置偏移
    offset: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    },
    // 菜单显示位置 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
    placement: {
      type: String,
      default: 'bottom-right'
    }
  },
  
  data() {
    return {
      showMenu: false,
      menuStyle: {},
      triggerId: `trigger_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      triggerRect: null,
      scrollTimer: null,
      isScrolling: false
    };
  },
  
  methods: {
    // 切换菜单显示状态
    toggleMenu() {
      if (this.showMenu) {
        this.hideMenu();
      } else {
        this.showMenuWithPosition();
      }
    },
    
    // 显示菜单并计算位置
    async showMenuWithPosition() {
      try {
        // 获取触发按钮的位置信息
        const rect = await this.getTriggerRect();
        if (!rect) {
          console.warn('无法获取触发按钮位置');
          return;
        }

        this.triggerRect = rect;
        this.showMenu = true;

        // 立即计算菜单位置
        this.$nextTick(() => {
          this.calculateMenuPosition();
          // 开始监听滚动事件
          this.startScrollListener();
        });

      } catch (error) {
        console.error('显示菜单时出错:', error);
      }
    },

    // 开始监听滚动事件
    startScrollListener() {
      // 监听页面滚动
      uni.onPageScroll((e) => {
        if (this.showMenu) {
          this.handleScroll();
        }
      });

      // 也监听窗口滚动（兼容性）
      if (typeof window !== 'undefined') {
        window.addEventListener('scroll', this.handleScroll, { passive: true });
        window.addEventListener('resize', this.handleScroll, { passive: true });
      }
    },

    // 处理滚动事件
    handleScroll() {
      if (!this.showMenu) return;

      // 防抖处理，避免频繁计算
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }

      this.isScrolling = true;

      this.scrollTimer = setTimeout(async () => {
        // 重新获取触发按钮位置
        const newRect = await this.getTriggerRect();
        if (newRect) {
          this.triggerRect = newRect;
          this.calculateMenuPosition();
        }
        this.isScrolling = false;
      }, 16); // 约60fps的更新频率
    },

    // 停止监听滚动事件
    stopScrollListener() {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = null;
      }

      if (typeof window !== 'undefined') {
        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleScroll);
      }
    },
    
    // 获取触发按钮的位置
    getTriggerRect() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this);
        query.select(`#${this.triggerId}`).boundingClientRect((rect) => {
          if (rect) {
            // 确保位置信息准确
            const correctedRect = {
              ...rect,
              left: rect.left,
              top: rect.top,
              right: rect.right,
              bottom: rect.bottom,
              width: rect.width,
              height: rect.height
            };
            resolve(correctedRect);
          } else {
            console.warn('无法获取触发按钮位置信息');
            resolve(null);
          }
        }).exec();
      });
    },
    
    // 计算菜单位置
    calculateMenuPosition() {
      if (!this.triggerRect) return;

      const { left, top, width, height } = this.triggerRect;
      const { x: offsetX, y: offsetY } = this.offset;

      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth = systemInfo.windowWidth;
      const screenHeight = systemInfo.windowHeight;

      // 菜单尺寸估算
      const menuWidth = 120; // 菜单宽度
      const menuHeight = this.menuItems.length * 44 + 20; // 菜单高度

      let menuLeft = left + offsetX;
      let menuTop = top + height + offsetY;

      // 根据placement调整基础位置
      switch (this.placement) {
        case 'bottom-right':
          // 菜单在按钮右下方
          menuLeft = left + width + offsetX;
          menuTop = top + offsetY;
          break;
        case 'bottom-left':
          // 菜单在按钮左下方
          menuLeft = left - menuWidth + offsetX;
          menuTop = top + offsetY;
          break;
        case 'top-right':
          // 菜单在按钮右上方
          menuLeft = left + width + offsetX;
          menuTop = top - menuHeight + offsetY;
          break;
        case 'top-left':
          // 菜单在按钮左上方
          menuLeft = left - menuWidth + offsetX;
          menuTop = top - menuHeight + offsetY;
          break;
      }

      // 智能边界调整
      // 水平边界检查
      if (menuLeft + menuWidth > screenWidth - 10) {
        // 如果右侧超出，尝试放到左侧
        menuLeft = left - menuWidth + offsetX;
      }
      if (menuLeft < 10) {
        // 如果左侧超出，贴近左边界
        menuLeft = 10;
      }

      // 垂直边界检查
      if (menuTop + menuHeight > screenHeight - 10) {
        // 如果下方超出，放到按钮上方
        menuTop = top - menuHeight + offsetY;
      }
      if (menuTop < 10) {
        // 如果上方超出，贴近上边界
        menuTop = 10;
      }

      // 最终位置确保在屏幕内
      menuLeft = Math.max(10, Math.min(menuLeft, screenWidth - menuWidth - 10));
      menuTop = Math.max(10, Math.min(menuTop, screenHeight - menuHeight - 10));

      this.menuStyle = {
        position: 'fixed',
        left: `${menuLeft}px`,
        top: `${menuTop}px`,
        zIndex: 99999,
        transition: this.isScrolling ? 'none' : 'all 0.2s ease'
      };
    },
    
    // 隐藏菜单
    hideMenu() {
      this.showMenu = false;
      this.menuStyle = {};
      this.stopScrollListener();
    },
    
    // 处理菜单项点击
    handleMenuClick(item, index) {
      if (item.disabled) return;
      
      this.hideMenu();
      this.$emit('menu-click', { item, index });
    }
  },
  
  // 组件销毁时清理
  beforeDestroy() {
    this.hideMenu();
    this.stopScrollListener();
  },

  // Vue3兼容
  beforeUnmount() {
    this.hideMenu();
    this.stopScrollListener();
  }
};
</script>

<style scoped>
.follow-menu-container {
  position: relative;
  display: inline-block;
}

.trigger-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  min-height: 40rpx;
  cursor: pointer;
}

.default-trigger {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  background-color: transparent;
}

.menu-popup {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid #e5e5e5;
  overflow: hidden;
  min-width: 240rpx;
  transform-origin: top left;
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  /* 确保在滚动时位置固定 */
  position: fixed;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.menu-icon {
  margin-right: 16rpx;
  font-size: 28rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
</style>
