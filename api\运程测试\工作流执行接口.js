/**
 * 运程测试功能工作流执行接口
 * 基于通用工作流基础类的运程测试功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 运程测试工作流配置, 运程测试参数验证规则, 运程测试错误码, 运程测试状态 } from './工作流配置.js';

/**
 * 运程测试工作流执行类
 */
class FortuneTestWorkflow extends WorkflowBase {
    constructor() {
        super('运程测试', 运程测试工作流配置);
        this.validationRules = 运程测试参数验证规则;
        this.errorCodes = 运程测试错误码;
        this.statusCodes = 运程测试状态;
    }

    /**
     * 执行运程测试工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async executeFortuneTest(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateFortuneTestParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildFortuneTestParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`运程测试进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '运程测试',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('运程测试工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证运程测试参数
     * @param {Object} formData - 表单数据
     */
    validateFortuneTestParams(formData) {
        // 验证必需参数
        this.validateStructuredParams(formData, this.validationRules.required);

        // 验证格式
        Object.entries(this.validationRules.formats).forEach(([field, pattern]) => {
            if (formData[field] && !pattern.test(formData[field])) {
                throw new Error(this.errorCodes[`INVALID_${field.toUpperCase()}`]?.message || `${field}格式不正确`);
            }
        });

        // 验证范围
        Object.entries(this.validationRules.ranges).forEach(([field, range]) => {
            if (formData[field] !== undefined) {
                if (range.min !== undefined && formData[field] < range.min) {
                    throw new Error(`${field}不能小于${range.min}`);
                }
                if (range.max !== undefined && formData[field] > range.max) {
                    throw new Error(`${field}不能大于${range.max}`);
                }
                if (range.maxLength !== undefined && formData[field].length > range.maxLength) {
                    throw new Error(`${field}长度不能超过${range.maxLength}个字符`);
                }
            }
        });

        // 验证枚举值
        Object.entries(this.validationRules.enums).forEach(([field, allowedValues]) => {
            if (formData[field] && !allowedValues.includes(formData[field])) {
                throw new Error(`${field}的值必须是: ${allowedValues.join(', ')}中的一个`);
            }
        });

        return true;
    }

    /**
     * 构建运程测试结构化参数
     * @param {Object} formData - 表单数据
     */
    buildFortuneTestParams(formData) {
        const builder = new StructuredParamsBuilder();

        // 添加基础信息参数
        builder
            .addTextParam('name', formData.name)
            .addTextParam('gender', formData.gender)
            .addTextParam('birthDate', formData.birthDate)
            .addTextParam('birthTime', formData.birthTime);

        // 添加可选的出生地点
        if (formData.birthPlace) {
            builder.addTextParam('birthPlace', formData.birthPlace);
        }

        // 添加测试配置参数
        builder.addTextParam('testType', formData.testType || 'yearly');

        if (formData.testYear) {
            builder.addConfigParam('testYear', formData.testYear);
        }

        if (formData.testMonth) {
            builder.addConfigParam('testMonth', formData.testMonth);
        }

        if (formData.analysisDepth) {
            builder.addConfigParam('analysisDepth', formData.analysisDepth);
        }

        if (formData.focusAreas) {
            builder.addConfigParam('focusAreas', formData.focusAreas);
        }

        // 添加输出格式参数
        builder
            .addConfigParam('includeAdvice', formData.includeAdvice !== false)
            .addConfigParam('includeLuckyItems', formData.includeLuckyItems !== false);

        return builder.build();
    }

    /**
     * 批量运程测试
     * @param {Array} testRequests - 测试请求列表
     */
    async batchFortuneTest(testRequests) {
        const results = [];

        for (const request of testRequests) {
            try {
                const result = await this.executeFortuneTest(request.formData, request.options);
                results.push({
                    ...result,
                    requestId: request.id
                });
            } catch (error) {
                results.push({
                    success: false,
                    error: error.message,
                    requestId: request.id
                });
            }
        }

        return {
            success: true,
            data: {
                results,
                total: testRequests.length,
                successful: results.filter(r => r.success).length,
                failed: results.filter(r => !r.success).length
            }
        };
    }

    /**
     * 获取运程测试历史
     * @param {Object} params - 查询参数
     */
    async getFortuneTestHistory(params = {}) {
        try {
            const queryParams = new URLSearchParams({
                userId: uni.getStorageSync('userId') || '',
                module: '运程测试',
                ...params
            }).toString();

            return await this.apiRequest(`workflow/history?${queryParams}`);
        } catch (error) {
            console.error('获取运程测试历史失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 收藏运程测试结果
     * @param {Object} fortuneData - 运程数据
     */
    async favoriteFortuneResult(fortuneData) {
        try {
            return await this.apiRequest('fortune-test/favorite', {
                method: 'POST',
                body: {
                    userId: uni.getStorageSync('userId'),
                    fortuneData,
                    module: '运程测试',
                    timestamp: Date.now()
                }
            });
        } catch (error) {
            console.error('收藏运程结果失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 分享运程测试结果
     * @param {Object} shareData - 分享数据
     */
    async shareFortuneResult(shareData) {
        try {
            return await this.apiRequest('fortune-test/share', {
                method: 'POST',
                body: {
                    ...shareData,
                    module: '运程测试',
                    timestamp: Date.now()
                }
            });
        } catch (error) {
            console.error('分享运程结果失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 获取运程建议
     * @param {Object} fortuneResult - 运程结果
     */
    async getFortuneAdvice(fortuneResult) {
        try {
            return await this.apiRequest('fortune-test/advice', {
                method: 'POST',
                body: {
                    fortuneResult,
                    module: '运程测试',
                    timestamp: Date.now()
                }
            });
        } catch (error) {
            console.error('获取运程建议失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 对比不同时期的运程
     * @param {Array} fortuneResults - 运程结果列表
     */
    async compareFortuneResults(fortuneResults) {
        try {
            return await this.apiRequest('fortune-test/compare', {
                method: 'POST',
                body: {
                    fortuneResults,
                    module: '运程测试',
                    timestamp: Date.now()
                }
            });
        } catch (error) {
            console.error('对比运程结果失败:', error);
            return this.formatError(error);
        }
    }
}

// 创建运程测试工作流实例
const fortuneTestWorkflow = new FortuneTestWorkflow();

// ================================
// 🎯 导出的接口方法
// ================================

/**
 * 执行运程测试工作流
 * @param {Object} formData - 表单数据
 * @param {Object} options - 执行选项
 */
export async function 执行运程测试工作流(formData, options = {}) {
    return await fortuneTestWorkflow.executeFortuneTest(formData, options);
}

/**
 * 批量运程测试
 * @param {Array} testRequests - 测试请求列表
 */
export async function 批量运程测试(testRequests) {
    return await fortuneTestWorkflow.batchFortuneTest(testRequests);
}

/**
 * 获取运程测试历史
 * @param {Object} params - 查询参数
 */
export async function 获取运程测试历史(params = {}) {
    return await fortuneTestWorkflow.getFortuneTestHistory(params);
}

/**
 * 收藏运程结果
 * @param {Object} fortuneData - 运程数据
 */
export async function 收藏运程结果(fortuneData) {
    return await fortuneTestWorkflow.favoriteFortuneResult(fortuneData);
}

/**
 * 分享运程结果
 * @param {Object} shareData - 分享数据
 */
export async function 分享运程结果(shareData) {
    return await fortuneTestWorkflow.shareFortuneResult(shareData);
}

/**
 * 获取运程建议
 * @param {Object} fortuneResult - 运程结果
 */
export async function 获取运程建议(fortuneResult) {
    return await fortuneTestWorkflow.getFortuneAdvice(fortuneResult);
}

/**
 * 对比运程结果
 * @param {Array} fortuneResults - 运程结果列表
 */
export async function 对比运程结果(fortuneResults) {
    return await fortuneTestWorkflow.compareFortuneResults(fortuneResults);
}

/**
 * 查询运程测试状态
 * @param {string} requestId - 请求ID
 */
export async function 查询运程测试状态(requestId) {
    return await fortuneTestWorkflow.queryWorkflowStatus(requestId);
}

/**
 * 取消运程测试工作流
 * @param {string} requestId - 请求ID
 */
export async function 取消运程测试工作流(requestId) {
    return await fortuneTestWorkflow.cancelWorkflow(requestId);
}

export default {
    执行运程测试工作流,
    批量运程测试,
    获取运程测试历史,
    收藏运程结果,
    分享运程结果,
    获取运程建议,
    对比运程结果,
    查询运程测试状态,
    取消运程测试工作流
};
