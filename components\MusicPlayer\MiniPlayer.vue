<template>
	<view 
		class="mini-player"
		:class="{ 
			collapsed: miniState === 'collapsed',
			expanded: miniState === 'expanded'
		}"
		:style="{ display: show ? 'flex' : 'none' }"
		@click="handleMiniPlayerClick"
	>
		<!-- 封面容器 -->
		<view class="mini-cover-container">
			<!-- 封面(圆形) -->
			<view class="mini-cover" :class="{ rotating: isPlaying }">
				<image 
					:src="currentMusic.cover || '/static/images/default-music-cover.jpg'" 
					mode="aspectFill"
					class="cover-image"
				/>
			</view>
			
			<!-- 播放进度环(可选) -->
			<view class="progress-ring" v-if="miniState === 'collapsed'">
				<view class="progress-circle" :style="{ strokeDashoffset: progressOffset }"></view>
			</view>
		</view>
		
		<!-- 扩展区域(只在expanded状态显示) -->
		<view v-if="miniState === 'expanded'" class="mini-controls">
			<!-- 播放/暂停按钮 - 现代化图标 -->
			<view class="mini-play-btn" @click.stop="togglePlay">
				<view v-if="isPlaying" class="pause-icon">
					<view class="pause-bar"></view>
					<view class="pause-bar"></view>
				</view>
				<view v-else class="play-triangle"></view>
			</view>

			<!-- 关闭按钮 -->
			<view class="mini-close-btn" @click.stop="closePlayer">
				<text class="close-icon">✕</text>
			</view>
		</view>
		
		<!-- 音乐信息提示(可选,鼠标悬停显示) -->
		<view v-if="miniState === 'collapsed'" class="mini-tooltip">
			<text class="tooltip-title">{{ currentMusic.title }}</text>
			<text class="tooltip-artist">{{ currentMusic.artist }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'MiniPlayer',
	props: {
		// 是否显示迷你播放器
		show: {
			type: Boolean,
			default: false
		},
		// 当前播放的音乐
		currentMusic: {
			type: Object,
			default: () => ({
				title: '未知歌曲',
				artist: '未知艺术家',
				cover: '/static/images/default-music-cover.jpg'
			})
		},
		// 是否正在播放
		isPlaying: {
			type: Boolean,
			default: false
		},
		// 播放进度(0-100)
		progress: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			miniState: 'collapsed', // collapsed/expanded
			clickCount: 0,
			clickTimer: null,
			autoCollapseTimer: null // 自动收回定时器
		};
	},
	mounted() {
		// 监听全局点击事件
		document.addEventListener('click', this.handleGlobalClick);
	},
	computed: {
		// 进度环偏移量
		progressOffset() {
			const circumference = 2 * Math.PI * 54; // 半径54
			return circumference - (this.progress / 100) * circumference;
		}
	},
	methods: {
		// 处理全局点击(点击空白处收回)
		handleGlobalClick(e) {
			// 如果点击的不是迷你播放器内部,且当前是展开状态,则收回
			const miniPlayer = this.$el;
			if (miniPlayer && !miniPlayer.contains(e.target) && this.miniState === 'expanded') {
				this.miniState = 'collapsed';
				this.clickCount = 0;
			}
		},

		// 处理迷你播放器点击
		handleMiniPlayerClick(e) {
			// 阻止事件冒泡到全局点击
			e.stopPropagation();

			this.clickCount++;

			if (this.clickCount === 1) {
				// 第1次点击: 拉出
				this.miniState = 'expanded';

				// 300ms内如果没有第2次点击,重置计数
				this.clickTimer = setTimeout(() => {
					this.clickCount = 0;
				}, 300);
			} else if (this.clickCount === 2) {
				// 第2次点击: 全屏展开
				clearTimeout(this.clickTimer);
				this.clickCount = 0;
				this.expandToFullscreen();
			}
		},
		
		// 展开为全屏播放器
		expandToFullscreen() {
			this.$emit('expand');
			// 重置状态
			this.miniState = 'collapsed';
		},
		
		// 切换播放/暂停
		togglePlay() {
			this.$emit('toggle-play');
		},
		
		// 完全关闭播放器
		closePlayer() {
			this.$emit('close');
		},
		
		// 重置状态(外部调用)
		reset() {
			this.miniState = 'collapsed';
			this.clickCount = 0;
			if (this.clickTimer) {
				clearTimeout(this.clickTimer);
			}
		}
	},
	beforeDestroy() {
		if (this.clickTimer) {
			clearTimeout(this.clickTimer);
		}
		if (this.autoCollapseTimer) {
			clearTimeout(this.autoCollapseTimer);
		}
		// 移除全局点击监听
		document.removeEventListener('click', this.handleGlobalClick);
	}
};
</script>

<style lang="scss" scoped>
.mini-player {
	position: fixed;
	z-index: 99999; /* 提高层级,不被文字覆盖 */
	display: flex;
	align-items: center;
	transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
	cursor: pointer;

	/* 橘黄色放射状光晕背景 */
	background: radial-gradient(
		circle at center,
		rgba(255, 165, 0, 0.18) 0%,
		rgba(255, 140, 0, 0.15) 30%,
		rgba(255, 120, 0, 0.12) 60%,
		rgba(255, 100, 0, 0.06) 100%
	);
	backdrop-filter: blur(12px);
	border: 2px solid rgba(255, 140, 0, 0.4);
	box-shadow:
		0 4rpx 20rpx rgba(255, 140, 0, 0.25),
		0 0 40rpx rgba(255, 140, 0, 0.15),
		inset 0 0 20rpx rgba(255, 165, 0, 0.08);

	/* 添加放射状动画 */
	&::before {
		content: '';
		position: absolute;
		top: -10rpx;
		left: -10rpx;
		right: -10rpx;
		bottom: -10rpx;
		background: radial-gradient(
			circle at center,
			rgba(255, 165, 0, 0.3) 0%,
			rgba(255, 140, 0, 0.2) 40%,
			rgba(255, 120, 0, 0.1) 70%,
			transparent 100%
		);
		border-radius: inherit;
		animation: radiateGlow 2s ease-in-out infinite alternate;
		pointer-events: none;
		z-index: -1;
	}

	&.collapsed {
		right: 0;
		top: 50%;
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		transform: translateY(-50%) translateX(60rpx);

		&:hover {
			transform: translateY(-50%) translateX(40rpx);
			box-shadow:
				0 6rpx 30rpx rgba(255, 140, 0, 0.3),
				0 0 60rpx rgba(255, 140, 0, 0.15);
		}
	}

	&.expanded {
		right: 20rpx;
		top: 50%;
		width: 280rpx;
		height: 120rpx;
		transform: translateY(-50%);
		border-radius: 60rpx;
		padding: 10rpx 20rpx;
		background: radial-gradient(
			ellipse at center,
			rgba(255, 180, 0, 0.65) 0%,
			rgba(255, 165, 0, 0.55) 40%,
			rgba(255, 140, 0, 0.45) 70%,
			rgba(255, 120, 0, 0.35) 100%
		);
		border: 2px solid rgba(255, 165, 0, 0.7);
		box-shadow:
			0 6rpx 30rpx rgba(255, 165, 0, 0.4),
			0 0 50rpx rgba(255, 140, 0, 0.3),
			inset 0 0 30rpx rgba(255, 180, 0, 0.2);
	}
}

.mini-cover-container {
	position: relative;
	width: 100rpx;
	height: 100rpx;
	flex-shrink: 0;
}

.mini-cover {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
	
	&.rotating {
		animation: rotate 20s linear infinite;
	}
}

.cover-image {
	width: 100%;
	height: 100%;
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.progress-ring {
	position: absolute;
	top: -5rpx;
	left: -5rpx;
	width: 110rpx;
	height: 110rpx;
	pointer-events: none;
}

.progress-circle {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 4rpx solid #667eea;
	border-top-color: transparent;
	border-right-color: transparent;
	transform: rotate(-90deg);
	transition: stroke-dashoffset 0.3s;
}

.mini-controls {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-left: 20rpx;
}

.mini-play-btn,
.mini-close-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.9);
	}
}

.mini-play-btn {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
	box-shadow: 0 4rpx 15rpx rgba(255, 255, 255, 0.3);
	position: relative;
	border: 1px solid rgba(255, 255, 255, 0.2);

	&:hover {
		background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.9) 100%);
		box-shadow: 0 6rpx 20rpx rgba(255, 255, 255, 0.4);
		transform: scale(1.05);
	}
}

/* 现代化播放三角形 */
.play-triangle {
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 12rpx 0 12rpx 20rpx;
	border-color: transparent transparent transparent #333;
	margin-left: 4rpx; /* 视觉居中 */
}

/* 现代化暂停图标 */
.pause-icon {
	display: flex;
	gap: 6rpx;
	align-items: center;
}

.pause-bar {
	width: 6rpx;
	height: 24rpx;
	background: #333;
	border-radius: 3rpx;
}

.play-icon {
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
}

.mini-close-btn {
	background: rgba(255, 59, 48, 0.1);

	&:hover {
		background: rgba(255, 59, 48, 0.2);
	}

	&:active {
		background: rgba(255, 59, 48, 0.3);
	}
}

.close-icon {
	color: #ff3b30;
	font-size: 32rpx;
	font-weight: bold;
}

.mini-tooltip {
	position: absolute;
	right: 140rpx;
	top: 50%;
	transform: translateY(-50%);
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(10rpx);
	padding: 15rpx 20rpx;
	border-radius: 10rpx;
	white-space: nowrap;
	opacity: 0;
	pointer-events: none;
	transition: opacity 0.3s;
	
	.collapsed:hover & {
		opacity: 1;
	}
}

.tooltip-title {
	display: block;
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
	margin-bottom: 5rpx;
}

.tooltip-artist {
	display: block;
	color: rgba(255, 255, 255, 0.7);
	font-size: 20rpx;
}

/* H5端优化 */
/* #ifdef H5 */
.mini-player {
	&.collapsed {
		transform: translateY(-50%) translateX(30px);
		
		&:hover {
			transform: translateY(-50%) translateX(20px);
		}
	}
}
/* #endif */

/* 小程序端优化 */
/* #ifdef MP */
.mini-player {
	&.collapsed {
		transform: translateY(-50%) translateX(60rpx);
	}
}
/* #endif */

/* 放射状光晕动画 */
@keyframes radiateGlow {
	0% {
		opacity: 0.3;
		transform: scale(1);
	}
	100% {
		opacity: 0.6;
		transform: scale(1.1);
	}
}

/* 旋转动画优化 */
@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>

