/**
 * 起名功能工作流配置
 * 定义起名功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 起名功能工作流配置
 */
export const 起名工作流配置 = {
    // 工作流基础信息
    workflowId: 'name_generator_workflow_001',
    workflowType: 'name_generation',
    moduleName: '起名功能',
    
    // 工作流描述
    description: '基于生辰八字、五行理论和传统文化的智能起名工作流',
    
    // 支持的起名模式
    supportedModes: [
        'traditional',  // 传统起名
        'modern',      // 现代起名
        'poetic',      // 诗词起名
        'custom'       // 自定义起名
    ],

    // 结构化参数定义
    structuredParams: {
        // 基础信息参数
        surname: {
            type: 'text',
            required: true,
            placeholder: '{{surname}}',
            description: '姓氏',
            validation: {
                minLength: 1,
                maxLength: 2,
                pattern: '^[\u4e00-\u9fa5]+$'
            }
        },
        
        gender: {
            type: 'text',
            required: true,
            placeholder: '{{gender}}',
            description: '性别',
            allowedValues: ['male', 'female']
        },

        // 生辰八字参数
        birthDate: {
            type: 'text',
            required: true,
            placeholder: '{{birthDate}}',
            description: '出生日期',
            format: 'YYYY-MM-DD'
        },

        birthTime: {
            type: 'text',
            required: true,
            placeholder: '{{birthTime}}',
            description: '出生时间',
            format: 'HH:MM'
        },

        // 起名偏好参数
        nameLength: {
            type: 'config',
            required: false,
            placeholder: '{{nameLength}}',
            description: '名字长度',
            defaultValue: 2,
            allowedValues: [2, 3]
        },

        nameStyle: {
            type: 'text',
            required: false,
            placeholder: '{{nameStyle}}',
            description: '起名风格',
            defaultValue: 'traditional',
            allowedValues: ['traditional', 'modern', 'poetic', 'elegant', 'strong', 'cute']
        },

        expectedMeaning: {
            type: 'text',
            required: false,
            placeholder: '{{expectedMeaning}}',
            description: '期望寓意',
            maxLength: 100
        },

        // 五行偏好参数
        wuxingPreference: {
            type: 'config',
            required: false,
            placeholder: '{{wuxingPreference}}',
            description: '五行偏好',
            allowedValues: ['auto', '金', '木', '水', '火', '土']
        },

        // 生成数量参数
        generateCount: {
            type: 'config',
            required: false,
            placeholder: '{{generateCount}}',
            description: '生成名字数量',
            defaultValue: 8,
            range: { min: 1, max: 20 }
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下信息为用户起名：

基础信息：
- 姓氏：{{surname}}
- 性别：{{gender}}
- 出生日期：{{birthDate}}
- 出生时间：{{birthTime}}

起名要求：
- 名字长度：{{nameLength}}字
- 起名风格：{{nameStyle}}
- 期望寓意：{{expectedMeaning}}
- 五行偏好：{{wuxingPreference}}
- 生成数量：{{generateCount}}个

请生成符合要求的名字推荐，包含：
1. 推荐的名字列表
2. 每个名字的五行分析
3. 每个名字的寓意解释
4. 每个名字的评分
5. 整体的五行分析报告
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: {
                recommendations: 'array',
                wuxingAnalysis: 'object',
                totalCandidates: 'number',
                generatedAt: 'string'
            }
        }
    },

    // 费用配置
    pricing: {
        basePrice: 10,  // 基础费用（金币）
        pricePerName: 1, // 每个额外名字的费用
        memberDiscount: 0.8 // 会员折扣
    },

    // 执行配置
    execution: {
        timeout: 300000,      // 5分钟超时
        maxRetries: 3,        // 最大重试次数
        pollInterval: 2000,   // 轮询间隔
        enableCache: true     // 启用缓存
    }
};

/**
 * 起名功能参数验证规则
 */
export const 起名参数验证规则 = {
    // 必需参数验证
    required: ['surname', 'gender', 'birthDate', 'birthTime'],
    
    // 参数格式验证
    formats: {
        birthDate: /^\d{4}-\d{2}-\d{2}$/,
        birthTime: /^\d{2}:\d{2}$/,
        surname: /^[\u4e00-\u9fa5]{1,2}$/
    },

    // 参数范围验证
    ranges: {
        nameLength: { min: 2, max: 3 },
        generateCount: { min: 1, max: 20 },
        expectedMeaning: { maxLength: 100 }
    },

    // 枚举值验证
    enums: {
        gender: ['male', 'female'],
        nameStyle: ['traditional', 'modern', 'poetic', 'elegant', 'strong', 'cute'],
        wuxingPreference: ['auto', '金', '木', '水', '火', '土']
    }
};

/**
 * 起名功能错误码定义
 */
export const 起名错误码 = {
    INVALID_SURNAME: { code: 'NAME_001', message: '姓氏格式不正确' },
    INVALID_GENDER: { code: 'NAME_002', message: '性别参数无效' },
    INVALID_BIRTH_DATE: { code: 'NAME_003', message: '出生日期格式不正确' },
    INVALID_BIRTH_TIME: { code: 'NAME_004', message: '出生时间格式不正确' },
    INVALID_NAME_LENGTH: { code: 'NAME_005', message: '名字长度参数无效' },
    INVALID_NAME_STYLE: { code: 'NAME_006', message: '起名风格参数无效' },
    INSUFFICIENT_COINS: { code: 'NAME_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'NAME_008', message: '起名工作流执行超时' },
    WORKFLOW_FAILED: { code: 'NAME_009', message: '起名工作流执行失败' },
    UNKNOWN_ERROR: { code: 'NAME_999', message: '未知错误' }
};

/**
 * 起名功能状态定义
 */
export const 起名状态 = {
    PENDING: 'pending',           // 等待中
    PROCESSING: 'processing',     // 处理中
    COMPLETED: 'completed',       // 已完成
    FAILED: 'failed',            // 失败
    CANCELLED: 'cancelled',       // 已取消
    TIMEOUT: 'timeout'           // 超时
};

export default {
    起名工作流配置,
    起名参数验证规则,
    起名错误码,
    起名状态
};
