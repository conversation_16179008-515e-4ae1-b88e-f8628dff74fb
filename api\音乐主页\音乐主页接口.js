/**
 * 音乐主页接口
 * 管理音乐创作中心的主页展示和导航
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🎵 音乐主页数据接口
// ================================

/**
 * 获取音乐主页数据
 */
export async function 获取音乐主页数据() {
	return await apiRequest('music/home-data');
}

/**
 * 获取平台统计数据
 */
export async function 获取平台统计数据() {
	return await apiRequest('music/platform-stats');
}

/**
 * 获取精选音乐
 */
export async function 获取精选音乐() {
	return await apiRequest('music/featured-music');
}

/**
 * 获取创作模式列表
 */
export async function 获取创作模式列表() {
	return await apiRequest('music/creation-modes');
}

/**
 * 获取音乐平台信息
 */
export async function 获取音乐平台信息() {
	return await apiRequest('music/platform-info');
}

// ================================
// 📈 数据统计接口
// ================================

/**
 * 获取实时统计
 */
export async function 获取实时统计() {
	return await apiRequest('music/real-time-stats');
}

/**
 * 获取用户创作统计
 */
export async function 获取用户创作统计() {
	return await apiRequest('music/user-creation-stats');
}

/**
 * 记录页面访问
 * @param {Object} params - 访问参数
 */
export async function 记录音乐主页访问(params) {
	return await apiRequest('music/home-visit', {
		method: 'POST',
		body: params
	});
}

export default {
	获取音乐主页数据,
	获取平台统计数据,
	获取精选音乐,
	获取创作模式列表,
	获取音乐平台信息,
	获取实时统计,
	获取用户创作统计,
	记录音乐主页访问
};
