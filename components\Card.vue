<template>
  <view class="card-container" :class="[shadow ? 'card-shadow' : '', customClass]">
    <view v-if="title" class="card-header">
      <text class="card-title">{{ title }}</text>
      <slot name="header-right"></slot>
    </view>
    <view class="card-content">
      <slot></slot>
    </view>
    <view v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Card',
  props: {
    title: {
      type: String,
      default: ''
    },
    shadow: {
      type: Boolean,
      default: true
    },
    customClass: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss">
.card-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  
  &.card-shadow {
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .card-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }
  
  .card-content {
    padding: 20rpx 30rpx;
  }
  
  .card-footer {
    padding: 20rpx 30rpx;
    border-top: 1px solid #f0f0f0;
  }
}
</style> 