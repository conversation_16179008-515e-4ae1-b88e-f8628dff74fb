/**
 * 统一工作流API接口
 * 对接后端的工具类型路由系统
 * 创建时间：2025-01-16
 */

import { apiRequest } from './common/request.js';

/**
 * 执行统一工作流
 * @param {string} toolType - 工具类型 ('text', 'image', 'video', 'audio')
 * @param {Object} params - 参数对象
 * @param {string} scenario - 场景标识
 * @param {Object} options - 额外选项
 * @returns {Promise} 执行结果
 */
export async function executeUnifiedWorkflow(toolType, params, scenario = 'general', options = {}) {
  try {
    // 准备请求数据
    const requestData = {
      toolType,
      scenario,
      ...params,
      userId: options.userId || getCurrentUserId(),
      requestId: options.requestId || generateRequestId(),
      timestamp: Date.now()
    };

    console.log(`🚀 执行${toolType}工具，场景：${scenario}`, requestData);

    // 调用后端统一工作流API
    const response = await apiRequest('workflows/execute', {
      method: 'POST',
      body: requestData,
      timeout: options.timeout || 60000
    });

    if (response.success) {
      console.log(`✅ ${toolType}工作流执行成功`, response);
      return {
        success: true,
        data: response.data,
        scenario,
        toolType,
        workflowId: response.workflowId,
        requestId: requestData.requestId
      };
    } else {
      throw new Error(response.error || '工作流执行失败');
    }

  } catch (error) {
    console.error(`❌ ${toolType}工作流执行失败:`, error);
    
    // 如果是开发环境或网络错误，降级到模拟执行
    if (shouldFallbackToMock(error, options)) {
      console.log('🔄 降级到模拟执行');
      return await executeMockWorkflow(toolType, params, scenario);
    }
    
    throw error;
  }
}

/**
 * 文本工具专用接口
 * @param {Object} params - 文本参数
 * @param {string} scenario - 文本场景
 * @param {Object} options - 额外选项
 * @returns {Promise} 执行结果
 */
export async function executeTextWorkflow(params, scenario = 'general', options = {}) {
  // 确保工具类型正确传递给后端
  return await executeUnifiedWorkflow('text_generation', params, scenario, {
    ...options,
    toolType: 'text_generation' // 明确指定工具类型
  });
}

/**
 * 后端工作流编辑器调用接口
 * @param {string} workflowId - 工作流ID
 * @param {Object} executionParams - 执行参数
 * @param {Object} options - 额外选项
 * @returns {Promise} 执行结果
 */
export async function executeWorkflowById(workflowId, executionParams, options = {}) {
  try {
    console.log(`🚀 执行工作流 ${workflowId}`, executionParams);

    // 调用后端工作流执行API
    const response = await apiRequest(`workflow-editor/execute-workflow/${workflowId}`, {
      method: 'POST',
      body: executionParams,
      timeout: options.timeout || 60000
    });

    if (response.success) {
      console.log(`✅ 工作流 ${workflowId} 执行成功`, response);
      return {
        success: true,
        data: response.result,
        workflowId: workflowId,
        requestId: generateRequestId()
      };
    } else {
      throw new Error(response.error || '工作流执行失败');
    }

  } catch (error) {
    console.error(`❌ 工作流 ${workflowId} 执行失败:`, error);

    // 如果是开发环境，降级到模拟执行
    if (shouldFallbackToMock(error, options)) {
      console.log('🔄 降级到模拟执行');
      return await executeMockWorkflowById(workflowId, executionParams);
    }

    throw error;
  }
}

/**
 * 获取可用工具类型
 * @returns {Promise} 工具类型列表
 */
export async function getAvailableTools() {
  try {
    const response = await apiRequest('workflow-editor/tools', {
      method: 'GET'
    });

    return response;
  } catch (error) {
    console.error('获取工具类型失败:', error);

    // 返回默认工具类型
    return {
      success: true,
      tools: [
        {
          type: 'text_generation',
          name: '文本生成',
          description: '智能文本创作工具，支持剧本、歌词、小说等多种场景',
          scenarios: ['script', 'lyrics', 'novel', 'article', 'general']
        },
        {
          type: 'image_generation',
          name: '图像生成',
          description: '基于文本描述生成图片',
          scenarios: ['realistic', 'artistic', 'cartoon']
        },
        {
          type: 'video_generation',
          name: '视频生成',
          description: '文本转视频或图片转视频',
          scenarios: ['text_to_video', 'image_to_video']
        },
        {
          type: 'audio_generation',
          name: '音频生成',
          description: '音乐创作和语音合成',
          scenarios: ['music', 'voice', 'sound_effect']
        }
      ]
    };
  }
}

/**
 * 获取工具配置
 * @param {string} toolType - 工具类型
 * @returns {Promise} 工具配置
 */
export async function getToolConfig(toolType) {
  try {
    const response = await apiRequest(`workflow-editor/tool-config/${toolType}`, {
      method: 'GET'
    });

    return response;
  } catch (error) {
    console.error('获取工具配置失败:', error);

    // 返回默认配置
    return {
      success: true,
      config: {
        name: '默认工具',
        description: '默认工具配置',
        config_schema: {}
      }
    };
  }
}

/**
 * 图片工具专用接口
 * @param {Object} params - 图片参数
 * @param {Object} options - 额外选项
 * @returns {Promise} 执行结果
 */
export async function executeImageWorkflow(params, options = {}) {
  return await executeUnifiedWorkflow('image', params, 'generate', options);
}

/**
 * 视频工具专用接口
 * @param {Object} params - 视频参数
 * @param {Object} options - 额外选项
 * @returns {Promise} 执行结果
 */
export async function executeVideoWorkflow(params, options = {}) {
  return await executeUnifiedWorkflow('video', params, 'generate', options);
}

/**
 * 音频工具专用接口
 * @param {Object} params - 音频参数
 * @param {Object} options - 额外选项
 * @returns {Promise} 执行结果
 */
export async function executeAudioWorkflow(params, options = {}) {
  return await executeUnifiedWorkflow('audio', params, 'generate', options);
}

/**
 * 查询工作流状态
 * @param {string} requestId - 请求ID
 * @param {string} toolType - 工具类型
 * @returns {Promise} 状态结果
 */
export async function queryWorkflowStatus(requestId, toolType) {
  try {
    const response = await apiRequest(`workflows/status/${requestId}`, {
      method: 'GET',
      params: { toolType }
    });

    return response;
  } catch (error) {
    console.error('查询工作流状态失败:', error);
    throw error;
  }
}

/**
 * 取消工作流执行
 * @param {string} requestId - 请求ID
 * @param {string} toolType - 工具类型
 * @returns {Promise} 取消结果
 */
export async function cancelWorkflow(requestId, toolType) {
  try {
    const response = await apiRequest(`workflows/cancel/${requestId}`, {
      method: 'POST',
      body: { toolType }
    });

    return response;
  } catch (error) {
    console.error('取消工作流失败:', error);
    throw error;
  }
}

/**
 * 模拟工作流执行（通过工作流ID）
 */
async function executeMockWorkflowById(workflowId, executionParams) {
  // 模拟网络延迟
  const delay = Math.random() * 2000 + 1000;
  await new Promise(resolve => setTimeout(resolve, delay));

  // 根据工作流ID推断工具类型
  let toolType = 'text_generation';
  if (workflowId.includes('image')) toolType = 'image_generation';
  else if (workflowId.includes('video')) toolType = 'video_generation';
  else if (workflowId.includes('audio')) toolType = 'audio_generation';

  const scenario = executionParams.scenario || 'general';
  const mockData = generateMockResponse(toolType, executionParams, scenario);

  return {
    success: true,
    data: mockData,
    workflowId,
    requestId: generateRequestId(),
    isMock: true
  };
}

/**
 * 生成模拟响应
 */
function generateMockResponse(toolType, params, scenario) {
  switch (toolType) {
    case 'text_generation':
      return generateMockTextResponse(params, scenario);
    case 'image_generation':
      return generateMockImageResponse(params);
    case 'video_generation':
      return generateMockVideoResponse(params);
    case 'audio_generation':
      return generateMockAudioResponse(params);
    default:
      return { content: '模拟生成的内容' };
  }
}

/**
 * 模拟工作流执行（降级方案）
 */
async function executeMockWorkflow(toolType, params, scenario) {
  // 模拟网络延迟
  const delay = Math.random() * 2000 + 1000; // 1-3秒
  await new Promise(resolve => setTimeout(resolve, delay));
  
  const mockResponses = {
    text: generateMockTextResponse(params, scenario),
    image: generateMockImageResponse(params),
    video: generateMockVideoResponse(params),
    audio: generateMockAudioResponse(params)
  };
  
  const mockData = mockResponses[toolType] || { content: '模拟生成的内容' };
  
  return {
    success: true,
    data: mockData,
    scenario,
    toolType,
    workflowId: `mock_${toolType}_workflow_001`,
    requestId: generateRequestId(),
    isMock: true
  };
}

/**
 * 生成模拟文本响应
 */
function generateMockTextResponse(params, scenario) {
  const scenarioTemplates = {
    script: {
      content: `# 剧本：${params.prompt || '未命名剧本'}

## 角色设定
- 主角：${params.character || '神秘主角'}
- 场景：${params.setting || '现代都市'}

## 第一幕
**场景：${params.setting || '室内'}**

主角：（${params.emotion || '沉思'}）${params.prompt}...

（这是根据您的要求"${params.prompt}"创作的剧本片段）`,
      type: 'script',
      format: 'screenplay'
    },
    
    lyrics: {
      content: `# 歌曲：${params.prompt || '未命名歌曲'}

**主歌：**
${params.prompt}的故事在心中回响
每一个音符都充满力量
在这个世界里寻找方向

**副歌：**
让我们一起歌唱
让梦想自由飞翔
这就是属于我们的时光

**桥段：**
无论风雨多么猛烈
我们的心永远坚定

（根据您的要求"${params.prompt}"创作的歌词）`,
      type: 'lyrics',
      structure: 'verse-chorus-bridge'
    },
    
    general: {
      content: `根据您的要求"${params.prompt}"，我为您生成了以下内容：

这是一个专业的文本生成结果，结合了您指定的参数和场景需求。内容经过精心设计，符合${scenario}场景的特点。

生成参数：
- 风格：${params.style || '通用'}
- 长度：${params.length || '中等'}
- 创意度：${params.creativity || '平衡'}

内容已根据您的需求进行优化，希望能够满足您的期望。`,
      type: 'general'
    }
  };
  
  return scenarioTemplates[scenario] || scenarioTemplates.general;
}

/**
 * 生成模拟图片响应
 */
function generateMockImageResponse(params) {
  return {
    imageUrl: 'https://via.placeholder.com/1024x1024?text=Generated+Image',
    prompt: params.prompt,
    style: params.style || 'realistic',
    size: params.size || '1024x1024',
    type: 'image'
  };
}

/**
 * 生成模拟视频响应
 */
function generateMockVideoResponse(params) {
  return {
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    prompt: params.prompt,
    duration: params.duration || '10s',
    type: 'video'
  };
}

/**
 * 生成模拟音频响应
 */
function generateMockAudioResponse(params) {
  return {
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    prompt: params.prompt,
    duration: params.duration || '30s',
    type: 'audio'
  };
}

/**
 * 判断是否应该降级到模拟执行
 */
function shouldFallbackToMock(error, options) {
  // 开发环境总是降级
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // 网络错误时降级
  if (error.message.includes('网络') || error.message.includes('timeout')) {
    return true;
  }
  
  // 用户明确要求降级
  if (options.fallbackToMock === true) {
    return true;
  }
  
  return false;
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId() {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    return userInfo?.id || 'anonymous';
  } catch (error) {
    return 'anonymous';
  }
}

/**
 * 生成请求ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
}

export default {
  executeUnifiedWorkflow,
  executeTextWorkflow,
  executeImageWorkflow,
  executeVideoWorkflow,
  executeAudioWorkflow,
  executeWorkflowById,
  getAvailableTools,
  getToolConfig,
  queryWorkflowStatus,
  cancelWorkflow
};
