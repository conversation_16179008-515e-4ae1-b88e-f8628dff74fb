<template>
	<view class="date-picker" v-if="visible" @touchmove.stop.prevent>
		<view class="picker-mask" @click="close" @touchmove.stop.prevent></view>
		<view class="picker-content" @touchmove.stop.prevent>
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
			</view>

			<!-- 列标签 -->
			<view class="column-labels">
				<text class="column-label">年</text>
				<text class="column-label">月</text>
				<text class="column-label">日</text>
			</view>

			<!-- 日期选择器 -->
			<view class="picker-container">
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
					@pickend="onPickerEnd"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
					@touchmove.stop.prevent
				>
					<picker-view-column>
						<view v-for="(year, index) in years" :key="`year-${index}`" class="picker-item">
							{{ year }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(month, index) in months" :key="`month-${index}`" class="picker-item">
							{{ String(month).padStart(2, '0') }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(day, index) in days" :key="`day-${index}`" class="picker-item">
							{{ String(day).padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DatePicker',
	props: {
		visible: { type: Boolean, default: false }
	},
	data() {
		const now = new Date();
		const currentYear = new Date().getFullYear();
		const years = [];
		for (let i = 1900; i <= currentYear + 20; i++) {
			years.push(i);
		}
		const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
		const selectedYear = now.getFullYear();
		const selectedMonth = now.getMonth() + 1;
		const selectedDay = now.getDate();

		// 计算正确的初始picker-value
		const yearIndex = years.findIndex(year => year === selectedYear);
		const monthIndex = months.findIndex(month => month === selectedMonth);
		const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
		const days = Array.from({length: daysInMonth}, (_, i) => i + 1);
		const dayIndex = days.findIndex(day => day === selectedDay);

		return {
			selectedYear,
			selectedMonth,
			selectedDay,
			pickerValue: [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0
			],
			years,
			months,
			// picker-view样式配置，实现完美的数字居中和吸附效果
			indicatorStyle: `
				height: 80rpx;
				background: rgba(139, 69, 19, 0.15);
				border: 3rpx solid #8B4513;
				border-radius: 15rpx;
				box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
				margin: 0 5rpx;
				left: 5rpx;
				right: 5rpx;
			`,
			maskStyle: `
				background: linear-gradient(180deg,
					rgba(245, 230, 211, 0.95) 0%,
					rgba(245, 230, 211, 0.3) 40%,
					transparent 50%,
					rgba(245, 230, 211, 0.3) 60%,
					rgba(245, 230, 211, 0.95) 100%
				);
			`
		};
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.$nextTick(() => {
					this.initPicker();
					// 强制更新picker-view以确保正确渲染
					this.$forceUpdate();
				});
			}
		}
	},
	methods: {
		generateYears() {
			const years = [];
			const currentYear = new Date().getFullYear();
			for (let i = 1900; i <= currentYear + 20; i++) {
				years.push(i);
			}
			return years;
		},
		initPicker() {
			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.months.findIndex(month => month === this.selectedMonth);
			const dayIndex = this.days.findIndex(day => day === this.selectedDay);
			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0
			];
		},
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex] = e.detail.value;
			this.selectedYear = this.years[yearIndex];
			this.selectedMonth = this.months[monthIndex];
			this.selectedDay = this.days[dayIndex];
			this.pickerValue = [yearIndex, monthIndex, dayIndex];

			// 添加自动吸附效果 - 确保数字停留在中心位置
			this.$nextTick(() => {
				setTimeout(() => {
					// 强制重新设置值，确保吸附到中心
					this.pickerValue = [yearIndex, monthIndex, dayIndex];
				}, 150);
			});
		},
		onPickerEnd(e) {
			// 滚动结束时强制吸附到中心
			const [yearIndex, monthIndex, dayIndex] = e.detail.value;
			setTimeout(() => {
				this.pickerValue = [yearIndex, monthIndex, dayIndex];
			}, 50);
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const year = String(this.selectedYear);
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateStr = `${year}-${month}-${day}`;
			this.$emit('confirm', dateStr);
		}
	}
};
</script>

<style scoped>
.date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	width: 90%;
	max-width: 600rpx;
	background: linear-gradient(135deg, #F4E4BC, #E6D7A3);
	border-radius: 20rpx 20rpx 0 0;
	box-shadow: 0 -10rpx 30rpx rgba(139, 69, 19, 0.3);
	padding: 30rpx 20rpx 20rpx;
	border: 2rpx solid #8B4513;
}

.picker-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #8B4513;
}

.column-labels {
	display: flex;
	justify-content: space-around;
	margin-bottom: 15rpx;
	padding: 0 50rpx;
}

.column-label {
	font-size: 26rpx;
	color: #8B4513;
	font-weight: 600;
}

.picker-container {
	position: relative;
	height: 320rpx;
	margin-bottom: 30rpx;
}

/* picker-indicator 已移除，使用 indicatorStyle 代替 */

.picker-view {
	width: 100%;
	height: 100%;
}

.picker-item {
	height: 80rpx;
	line-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #8B4513;
	font-weight: 600;
	transition: all 0.3s ease;
	user-select: none;
	text-align: center;
	/* 确保数字完美居中 */
	box-sizing: border-box;
	padding: 0;
	margin: 0;
}

.picker-footer {
	display: flex;
	gap: 15rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 70rpx;
	border-radius: 35rpx;
	border: none;
	font-size: 30rpx;
	font-weight: 600;
}

.btn-cancel {
	background: #F5F5DC;
	color: #8B4513;
	border: 2rpx solid #8B4513;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}
</style>
