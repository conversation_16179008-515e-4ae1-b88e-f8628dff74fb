<template>
	<view class="simple-datetime-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<!-- 简单的头部 -->
			<view class="picker-header">
				<text class="header-title">选择出生日期时间</text>
			</view>

			<!-- 日期时间选择器 -->
			<view class="picker-container">
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
				>
					<!-- 年份列 -->
					<picker-view-column>
						<view
							v-for="(year, index) in years"
							:key="index"
							class="picker-item"
						>
							{{ year }}
						</view>
					</picker-view-column>

					<!-- 月份列 -->
					<picker-view-column>
						<view
							v-for="(month, index) in months"
							:key="index"
							class="picker-item"
						>
							{{ String(month).padStart(2, '0') }}
						</view>
					</picker-view-column>

					<!-- 日期列 -->
					<picker-view-column>
						<view
							v-for="(day, index) in days"
							:key="index"
							class="picker-item"
						>
							{{ String(day).padStart(2, '0') }}
						</view>
					</picker-view-column>

					<!-- 小时列 -->
					<picker-view-column>
						<view
							v-for="(hour, index) in hours"
							:key="index"
							class="picker-item"
						>
							{{ String(hour).padStart(2, '0') }}
						</view>
					</picker-view-column>

					<!-- 分钟列 -->
					<picker-view-column>
						<view
							v-for="(minute, index) in minutes"
							:key="index"
							class="picker-item"
						>
							{{ String(minute).padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>

				<!-- 列标签 -->
				<view class="column-labels">
					<text class="column-label">年</text>
					<text class="column-label">月</text>
					<text class="column-label">日</text>
					<text class="column-label">时</text>
					<text class="column-label">分</text>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PerfectDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		const now = new Date();
		const currentYear = now.getFullYear();

		return {
			// 当前选中的年月日时分
			selectedYear: currentYear,
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate(),
			selectedHour: now.getHours(),
			selectedMinute: now.getMinutes(),

			// picker-view的索引值 [年, 月, 日, 时, 分]
			pickerValue: [0, 0, 0, 0, 0],

			// 年份数组（1900-2035）
			years: this.generateYears(),

			// 月份数组（1-12）
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],

			// 小时数组（0-23）
			hours: this.generateHours(),

			// 分钟数组（0-59）
			minutes: this.generateMinutes(),

			// 快速选择预设
			quickPresets: [
				{
					key: 'today',
					label: '今天',
					icon: '📅',
					getValue: () => {
						const now = new Date();
						return {
							year: now.getFullYear(),
							month: now.getMonth() + 1,
							day: now.getDate(),
							hour: now.getHours(),
							minute: Math.floor(now.getMinutes() / 5) * 5
						};
					}
				},
				{
					key: 'tomorrow',
					label: '明天',
					icon: '🌅',
					getValue: () => {
						const tomorrow = new Date();
						tomorrow.setDate(tomorrow.getDate() + 1);
						return {
							year: tomorrow.getFullYear(),
							month: tomorrow.getMonth() + 1,
							day: tomorrow.getDate(),
							hour: 9,
							minute: 0
						};
					}
				},
				{
					key: 'yesterday',
					label: '昨天',
					icon: '🌄',
					getValue: () => {
						const yesterday = new Date();
						yesterday.setDate(yesterday.getDate() - 1);
						return {
							year: yesterday.getFullYear(),
							month: yesterday.getMonth() + 1,
							day: yesterday.getDate(),
							hour: 12,
							minute: 0
						};
					}
				},
				{
					key: 'week_ago',
					label: '一周前',
					icon: '📆',
					getValue: () => {
						const weekAgo = new Date();
						weekAgo.setDate(weekAgo.getDate() - 7);
						return {
							year: weekAgo.getFullYear(),
							month: weekAgo.getMonth() + 1,
							day: weekAgo.getDate(),
							hour: 10,
							minute: 0
						};
					}
				},
				{
					key: 'month_ago',
					label: '一月前',
					icon: '🗓️',
					getValue: () => {
						const monthAgo = new Date();
						monthAgo.setMonth(monthAgo.getMonth() - 1);
						return {
							year: monthAgo.getFullYear(),
							month: monthAgo.getMonth() + 1,
							day: monthAgo.getDate(),
							hour: 14,
							minute: 0
						};
					}
				},
				{
					key: 'year_ago',
					label: '一年前',
					icon: '📋',
					getValue: () => {
						const yearAgo = new Date();
						yearAgo.setFullYear(yearAgo.getFullYear() - 1);
						return {
							year: yearAgo.getFullYear(),
							month: yearAgo.getMonth() + 1,
							day: yearAgo.getDate(),
							hour: 12,
							minute: 0
						};
					}
				}
			]
		}
	},
	computed: {
		// 动态计算当月天数
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initializePicker();
			}
		},
		
		// 监听月份变化，调整日期
		selectedMonth() {
			this.adjustDayIfNeeded();
		},
		
		selectedYear() {
			this.adjustDayIfNeeded();
		}
	},
	created() {
		this.initializePicker();
	},
	methods: {
		// 生成年份数组
		generateYears() {
			const currentYear = new Date().getFullYear();
			const years = [];
			// 扩展年份范围从1800年到当前年份+10年，满足更广泛的出生年份需求
			for (let i = 1800; i <= currentYear + 10; i++) {
				years.push(i);
			}
			return years;
		},

		// 生成小时数组
		generateHours() {
			const hours = [];
			for (let i = 0; i <= 23; i++) {
				hours.push(i);
			}
			return hours;
		},

		// 生成分钟数组（每5分钟一个选项）
		generateMinutes() {
			const minutes = [];
			for (let i = 0; i <= 59; i += 5) {
				minutes.push(i);
			}
			return minutes;
		},
		
		// 初始化picker位置
		initializePicker() {
			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.selectedMonth - 1;
			const dayIndex = this.selectedDay - 1;
			const hourIndex = this.hours.findIndex(hour => hour === this.selectedHour);
			const minuteIndex = this.minutes.findIndex(minute => minute === this.selectedMinute);

			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0,
				hourIndex >= 0 ? hourIndex : 0,
				minuteIndex >= 0 ? minuteIndex : 0
			];

			console.log('初始化picker:', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				hour: this.selectedHour,
				minute: this.selectedMinute,
				pickerValue: this.pickerValue
			});
		},
		
		// picker开始滚动
		onPickStart() {
			this.isScrolling = true;
			console.log('开始滚动');
		},

		// picker值变化处理
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value;

			// 更新选中值
			this.selectedYear = this.years[yearIndex] || this.selectedYear;
			this.selectedMonth = monthIndex + 1; // monthIndex是从0开始的索引
			this.selectedDay = this.days[dayIndex] || this.selectedDay;
			this.selectedHour = this.hours[hourIndex] || this.selectedHour;
			this.selectedMinute = this.minutes[minuteIndex] || this.selectedMinute;

			// 更新picker值
			this.pickerValue = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex];

			console.log('picker变化:', {
				indexes: [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex],
				values: [this.selectedYear, this.selectedMonth, this.selectedDay, this.selectedHour, this.selectedMinute]
			});
		},
		
		// 滚动结束处理
		onPickerEnd(e) {
			console.log('滚动结束，开始磁性吸附');

			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}

			// 延迟执行吸附，确保滚动完全停止
			this.scrollTimer = setTimeout(() => {
				this.isScrolling = false;
				this.performPreciseSnap();
			}, 50); // 减少延迟，提升响应速度
		},

		// 执行精确的磁性吸附
		performPreciseSnap() {
			const currentValue = [...this.pickerValue];
			console.log('当前picker值:', currentValue);

			// 计算精确的吸附位置
			const targetValue = this.calculatePreciseSnapPosition(currentValue);

			console.log('目标吸附位置:', targetValue);

			// 检查是否需要吸附
			const needSnap = this.needsSnap(currentValue, targetValue);

			if (needSnap) {
				console.log('执行精确吸附动画');
				this.animateToTarget(currentValue, targetValue);
			} else {
				console.log('位置已完美对齐，无需吸附');
				// 确保最终值是精确的整数
				this.pickerValue = [...targetValue];
				this.updateSelectedValues(targetValue);
			}
		},
		
		// 计算精确的吸附位置
		calculatePreciseSnapPosition(currentValue) {
			const targetValue = [...currentValue];

			// 为每一列计算精确的吸附位置
			for (let i = 0; i < targetValue.length; i++) {
				targetValue[i] = this.snapToNearestIndex(currentValue[i], i);
			}

			return targetValue;
		},

		// 将索引吸附到最近的整数位置
		snapToNearestIndex(currentIndex, columnIndex) {
			// 获取当前列的最大索引
			let maxIndex;
			switch(columnIndex) {
				case 0: maxIndex = this.years.length - 1; break;
				case 1: maxIndex = this.months.length - 1; break;
				case 2: maxIndex = this.days.length - 1; break;
				case 3: maxIndex = this.hours.length - 1; break;
				case 4: maxIndex = this.minutes.length - 1; break;
				default: maxIndex = 0;
			}

			// 确保索引在有效范围内
			if (currentIndex < 0) return 0;
			if (currentIndex >= maxIndex) return maxIndex;

			// 强化的磁性吸附算法
			const decimal = currentIndex - Math.floor(currentIndex);
			const intPart = Math.floor(currentIndex);

			// 更严格的吸附阈值，确保选项完美居中
			if (decimal < 0.1) {
				// 非常接近下边界，强制向下吸附
				return intPart;
			} else if (decimal > 0.9) {
				// 非常接近上边界，强制向上吸附
				return Math.min(intPart + 1, maxIndex);
			} else if (decimal >= 0.3 && decimal <= 0.7) {
				// 扩大中心区域，强制居中对齐
				return Math.round(currentIndex);
			} else {
				// 其他区域，选择最近的整数位置
				return decimal < 0.5 ? intPart : Math.min(intPart + 1, maxIndex);
			}
		},

		// 检查是否需要吸附
		needsSnap(currentValue, targetValue) {
			return currentValue.some((val, index) =>
				Math.abs(val - targetValue[index]) > 0.05
			);
		},
		
		// 平滑动画到目标位置
		animateToTarget(startValue, targetValue) {
			const startTime = Date.now();
			const duration = 280; // 优化动画时长，更接近原生体验

			const animate = () => {
				const elapsed = Date.now() - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// 使用iOS风格的缓动函数：cubic-bezier(0.25, 0.46, 0.45, 0.94)
				let easeProgress;
				if (progress < 0.5) {
					easeProgress = 4 * progress * progress * progress;
				} else {
					easeProgress = 1 - Math.pow(-2 * progress + 2, 3) / 2;
				}

				// 计算当前位置
				const currentPos = startValue.map((start, index) => {
					const target = targetValue[index];
					return start + (target - start) * easeProgress;
				});

				this.pickerValue = currentPos;

				if (progress < 1) {
					requestAnimationFrame(animate);
				} else {
					// 动画结束，设置精确值并更新选中值
					this.pickerValue = [...targetValue];
					this.updateSelectedValues(targetValue);
				}
			};

			requestAnimationFrame(animate);
		},
		
		// 更新选中的年月日
		updateSelectedValues(pickerValue) {
			this.selectedYear = this.years[pickerValue[0]] || this.selectedYear;
			// 修复月份计算：pickerValue[1]是从0开始的索引，实际月份应该是索引 + 1
			this.selectedMonth = pickerValue[1] + 1;
			this.selectedDay = this.days[pickerValue[2]] || this.selectedDay;

			console.log('最终选中值:', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay
			});
		},
		
		// 调整日期（处理无效日期）
		adjustDayIfNeeded() {
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
				this.initializePicker(); // 重新初始化picker位置
			}
		},
		
		// 处理鼠标滚轮事件 - 增强版
		onWheel(e) {
			try {
				e.preventDefault();
				e.stopPropagation();

				// 获取滚动方向
				const delta = e.deltaY > 0 ? 1 : -1;

				// 智能列选择：根据修饰键选择不同的列
				let columnIndex = 0; // 默认年份列

				if (e.shiftKey) {
					columnIndex = 1; // 月份列
				} else if (e.ctrlKey || e.metaKey) {
					columnIndex = 2; // 日期列
				} else if (e.altKey) {
					columnIndex = 3; // 小时列
				} else if (e.shiftKey && e.altKey) {
					columnIndex = 4; // 分钟列
				}

				let newValue = [...this.pickerValue];

				// 根据列索引更新对应的值
				switch(columnIndex) {
					case 0: // 年份列
						const maxYearIndex = this.years.length - 1;
						newValue[0] = Math.max(0, Math.min(maxYearIndex, newValue[0] + delta));
						break;
					case 1: // 月份列
						newValue[1] = Math.max(0, Math.min(11, newValue[1] + delta));
						break;
					case 2: // 日期列
						const maxDayIndex = this.days.length - 1;
						newValue[2] = Math.max(0, Math.min(maxDayIndex, newValue[2] + delta));
						break;
					case 3: // 小时列
						const maxHourIndex = this.hours.length - 1;
						newValue[3] = Math.max(0, Math.min(maxHourIndex, newValue[3] + delta));
						break;
					case 4: // 分钟列
						const maxMinuteIndex = this.minutes.length - 1;
						newValue[4] = Math.max(0, Math.min(maxMinuteIndex, newValue[4] + delta));
						break;
				}

				// 更新picker值
				this.pickerValue = newValue;
				this.updateSelectedValues(newValue);

				// 延迟执行精确吸附
				clearTimeout(this.wheelTimer);
				this.wheelTimer = setTimeout(() => {
					this.performPreciseSnap();
				}, 150);
			} catch (error) {
				console.warn('滚轮事件处理出错:', error);
			}
		},

		// 关闭选择器
		close() {
			this.$emit('close');
		},

		// 选择时间建议
		selectTimeSuggestion(suggestion) {
			console.log('选择时间建议:', suggestion.label);

			try {
				// 只更新时间部分，保持日期不变
				this.selectedHour = suggestion.hour;
				this.selectedMinute = suggestion.minute;

				// 计算对应的picker索引
				const hourIndex = this.hours.findIndex(hour => hour === suggestion.hour);
				const minuteIndex = this.minutes.findIndex(minute => minute === suggestion.minute);

				// 更新picker值（只更新时间部分）
				this.pickerValue = [
					this.pickerValue[0], // 保持年份
					this.pickerValue[1], // 保持月份
					this.pickerValue[2], // 保持日期
					Math.max(0, hourIndex),
					Math.max(0, minuteIndex)
				];

				console.log('时间建议已应用:', {
					hour: suggestion.hour,
					minute: suggestion.minute,
					pickerValue: this.pickerValue
				});

			} catch (error) {
				console.error('应用时间建议时出错:', error);
			}
		},

		// 选择快速预设
		selectPreset(preset) {
			console.log('选择预设:', preset.label);

			try {
				const presetValue = preset.getValue();

				// 更新选中的值
				this.selectedYear = presetValue.year;
				this.selectedMonth = presetValue.month;
				this.selectedDay = presetValue.day;
				this.selectedHour = presetValue.hour;
				this.selectedMinute = presetValue.minute;

				// 计算对应的picker索引
				const yearIndex = this.years.findIndex(year => year === presetValue.year);
				const monthIndex = presetValue.month - 1;
				const dayIndex = presetValue.day - 1;
				const hourIndex = this.hours.findIndex(hour => hour === presetValue.hour);
				const minuteIndex = this.minutes.findIndex(minute => minute === presetValue.minute);

				// 更新picker值
				this.pickerValue = [
					Math.max(0, yearIndex),
					Math.max(0, monthIndex),
					Math.max(0, dayIndex),
					Math.max(0, hourIndex),
					Math.max(0, minuteIndex)
				];

				console.log('预设值已应用:', {
					selected: presetValue,
					pickerValue: this.pickerValue
				});

			} catch (error) {
				console.error('应用预设值时出错:', error);
			}
		},

		// 确认选择
		confirm() {
			const year = String(this.selectedYear);
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');

			const dateStr = `${year}-${month}-${day}`;
			const timeStr = `${hour}:${minute}`;
			const dateTimeStr = `${dateStr} ${timeStr}`;

			const result = {
				date: dateStr,
				time: timeStr,
				datetime: dateTimeStr,
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				hour: this.selectedHour,
				minute: this.selectedMinute
			};

			console.log('选择的日期时间:', result);
			this.$emit('confirm', result);
		}
	}
}
</script>

<style scoped>
/* 简单日期时间选择器容器 */
.simple-datetime-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

/* 遮罩层 */
.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(8rpx);
}

/* 选择器内容 */
.picker-content {
	position: relative;
	background: white;
	border-radius: 40rpx 40rpx 0 0;
	width: 100%;
	max-width: 750rpx;
	min-width: 600rpx;
	margin: 0 auto;
	overflow: hidden;
	box-shadow:
		0 -16rpx 64rpx rgba(0, 0, 0, 0.2),
		0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
	animation: slideUpIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideUpIn {
	from {
		transform: translateY(100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 优化的头部设计 */
.picker-header {
	text-align: center;
	padding: 48rpx 40rpx 32rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border-bottom: 1rpx solid rgba(220, 20, 60, 0.1);
}

.header-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.header-title {
	font-size: 44rpx;
	font-weight: 700;
	color: #333;
	letter-spacing: 1rpx;
}

.header-subtitle {
	font-size: 28rpx;
	color: #666;
	opacity: 0.8;
}

/* 实时预览区域 */
.datetime-preview {
	padding: 32rpx 40rpx;
	background: linear-gradient(135deg,
		rgba(220, 20, 60, 0.05) 0%,
		rgba(255, 182, 193, 0.08) 50%,
		rgba(220, 20, 60, 0.05) 100%
	);
	border-bottom: 1rpx solid rgba(220, 20, 60, 0.15);
	text-align: center;
}

.preview-main {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 24rpx;
	margin-bottom: 16rpx;
}

.preview-date {
	font-size: 40rpx;
	font-weight: 600;
	color: #dc143c;
	letter-spacing: 1rpx;
}

.preview-time {
	font-size: 40rpx;
	font-weight: 600;
	color: #dc143c;
	font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.preview-weekday {
	font-size: 28rpx;
	color: #666;
	opacity: 0.9;
}

/* 快速选择预设样式 */
.quick-presets {
	padding: 24rpx 40rpx;
	background: linear-gradient(135deg,
		rgba(220, 20, 60, 0.03) 0%,
		rgba(255, 182, 193, 0.06) 50%,
		rgba(220, 20, 60, 0.03) 100%
	);
	border-bottom: 1rpx solid rgba(220, 20, 60, 0.1);
}

.presets-title {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 16rpx;
	text-align: center;
	font-weight: 500;
}

.presets-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 12rpx;
}

.preset-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx 8rpx;
	background: rgba(255, 255, 255, 0.8);
	border: 1rpx solid rgba(220, 20, 60, 0.15);
	border-radius: 12rpx;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	cursor: pointer;
	min-height: 80rpx;
}

.preset-item:hover {
	background: rgba(255, 255, 255, 1);
	border-color: rgba(220, 20, 60, 0.3);
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(220, 20, 60, 0.1);
}

.preset-item:active {
	transform: translateY(0);
	box-shadow: 0 2rpx 6rpx rgba(220, 20, 60, 0.15);
}

.preset-icon {
	font-size: 24rpx;
	margin-bottom: 4rpx;
	display: block;
}

.preset-text {
	font-size: 20rpx;
	color: #666;
	font-weight: 500;
	text-align: center;
	line-height: 1.2;
}

/* 智能时间建议样式 */
.time-suggestions {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg,
		rgba(255, 165, 0, 0.03) 0%,
		rgba(255, 215, 0, 0.06) 50%,
		rgba(255, 165, 0, 0.03) 100%
	);
	border-bottom: 1rpx solid rgba(255, 165, 0, 0.15);
}

.suggestions-title {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 16rpx;
	text-align: center;
	font-weight: 500;
}

.suggestions-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 10rpx;
}

.suggestion-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 12rpx 6rpx;
	background: rgba(255, 255, 255, 0.8);
	border: 1rpx solid rgba(255, 165, 0, 0.2);
	border-radius: 10rpx;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	cursor: pointer;
	min-height: 70rpx;
}

.suggestion-item:hover {
	background: rgba(255, 255, 255, 1);
	border-color: rgba(255, 165, 0, 0.4);
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.15);
}

.suggestion-item:active {
	transform: translateY(0);
	box-shadow: 0 2rpx 6rpx rgba(255, 165, 0, 0.2);
}

.suggestion-time {
	font-size: 22rpx;
	font-weight: 600;
	color: #FF8C00;
	font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
	margin-bottom: 2rpx;
}

.suggestion-label {
	font-size: 18rpx;
	color: #999;
	font-weight: 500;
}

/* picker容器 */
.picker-container {
	position: relative;
	height: 520rpx;
	overflow: hidden;
	background: #ffffff;
}

/* picker-view样式 */
.picker-view {
	height: 100%;
	width: 100%;
	padding: 0 20rpx;
}

/* 列标签 */
.column-labels {
	position: absolute;
	bottom: 20rpx;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 0 40rpx;
	pointer-events: none;
	z-index: 20;
}

.column-label {
	font-size: 24rpx;
	color: #999;
	font-weight: 500;
	opacity: 0.8;
	text-align: center;
	flex: 1;
}

/* picker项目基础样式 */
.picker-item {
	height: 104rpx;
	line-height: 104rpx;
	text-align: center;
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

/* 不同列的特殊样式 */
.year-item {
	font-weight: 600;
}

.month-item, .day-item {
	font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.hour-item, .minute-item {
	font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
	font-weight: 600;
}

/* 渐变遮罩效果 - 增强版 */
.picker-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(255, 255, 255, 0.8) 25%,
		rgba(255, 255, 255, 0.3) 40%,
		transparent 48%,
		transparent 52%,
		rgba(255, 255, 255, 0.3) 60%,
		rgba(255, 255, 255, 0.8) 75%,
		rgba(255, 255, 255, 0.95) 100%
	);
	pointer-events: none;
	z-index: 15;
}

/* 中心选择指示器 - 重新设计 */
.picker-container::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 40rpx;
	right: 40rpx;
	height: 104rpx;
	transform: translateY(-50%);

	/* 精美的背景渐变 */
	background: linear-gradient(135deg,
		rgba(220, 20, 60, 0.06) 0%,
		rgba(255, 182, 193, 0.12) 25%,
		rgba(255, 255, 255, 0.25) 50%,
		rgba(255, 182, 193, 0.12) 75%,
		rgba(220, 20, 60, 0.06) 100%
	);

	/* 精致的边框 */
	border: 2rpx solid rgba(220, 20, 60, 0.3);
	border-radius: 24rpx;

	/* 多层阴影效果 */
	box-shadow:
		0 4rpx 16rpx rgba(220, 20, 60, 0.1),
		0 8rpx 32rpx rgba(220, 20, 60, 0.05),
		inset 0 2rpx 8rpx rgba(255, 255, 255, 0.6),
		inset 0 -1rpx 4rpx rgba(220, 20, 60, 0.05);

	/* 禁用点击事件 */
	pointer-events: none;
	z-index: 10;
}

/* 底部按钮区域 */
.picker-footer {
	display: flex;
	justify-content: space-between;
	gap: 32rpx;
	padding: 40rpx 40rpx 48rpx;
	background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
	border-top: 1rpx solid rgba(220, 20, 60, 0.1);
}

.btn-cancel, .btn-confirm {
	flex: 1;
	height: 96rpx;
	border-radius: 24rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 600;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
}

.btn-text {
	position: relative;
	z-index: 2;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
	border: 2rpx solid #e0e0e0;
}

.btn-cancel:hover {
	background: #eeeeee;
	border-color: #d0d0d0;
	transform: translateY(-2rpx);
}

.btn-cancel:active {
	background: #e8e8e8;
	transform: translateY(0);
}

.btn-confirm {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	box-shadow:
		0 6rpx 20rpx rgba(220, 20, 60, 0.3),
		0 2rpx 8rpx rgba(220, 20, 60, 0.2);
}

.btn-confirm::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.btn-confirm:hover {
	transform: translateY(-2rpx);
	box-shadow:
		0 8rpx 24rpx rgba(220, 20, 60, 0.4),
		0 4rpx 12rpx rgba(220, 20, 60, 0.3);
}

.btn-confirm:hover::before {
	opacity: 1;
}

.btn-confirm:active {
	background: linear-gradient(135deg, #b91c3c 0%, #991b3c 100%);
	transform: translateY(0);
	box-shadow:
		0 4rpx 12rpx rgba(220, 20, 60, 0.3),
		0 2rpx 6rpx rgba(220, 20, 60, 0.2);
}

/* 多端响应式优化 */

/* 小屏设备 (手机竖屏) */
@media (max-width: 750rpx) {
	.picker-content {
		max-width: 680rpx;
		min-width: 500rpx;
	}

	.picker-header {
		padding: 40rpx 32rpx 24rpx;
	}

	.header-title {
		font-size: 40rpx;
	}

	.header-subtitle {
		font-size: 26rpx;
	}

	.datetime-preview {
		padding: 24rpx 32rpx;
	}

	.preview-date, .preview-time {
		font-size: 36rpx;
	}

	.preview-weekday {
		font-size: 26rpx;
	}

	.quick-presets {
		padding: 20rpx 32rpx;
	}

	.presets-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 10rpx;
	}

	.preset-item {
		padding: 12rpx 6rpx;
		min-height: 70rpx;
	}

	.preset-icon {
		font-size: 20rpx;
	}

	.preset-text {
		font-size: 18rpx;
	}

	.time-suggestions {
		padding: 16rpx 32rpx;
	}

	.suggestions-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 8rpx;
	}

	.suggestion-item {
		padding: 10rpx 4rpx;
		min-height: 60rpx;
	}

	.suggestion-time {
		font-size: 20rpx;
	}

	.suggestion-label {
		font-size: 16rpx;
	}

	.picker-container {
		height: 480rpx;
	}

	.picker-item {
		height: 96rpx;
		line-height: 96rpx;
		font-size: 32rpx;
	}

	.column-label {
		font-size: 22rpx;
	}

	.picker-footer {
		padding: 32rpx 32rpx 40rpx;
		gap: 24rpx;
	}

	.btn-cancel, .btn-confirm {
		height: 88rpx;
		font-size: 30rpx;
	}
}

/* 中等屏幕 (平板) */
@media (min-width: 751rpx) and (max-width: 1200rpx) {
	.picker-content {
		max-width: 800rpx;
	}

	.picker-container {
		height: 560rpx;
	}

	.picker-item {
		height: 112rpx;
		line-height: 112rpx;
		font-size: 38rpx;
	}

	.column-label {
		font-size: 26rpx;
	}
}

/* 大屏设备 (桌面端) */
@media (min-width: 1201rpx) {
	.picker-content {
		max-width: 900rpx;
	}

	.picker-container {
		height: 600rpx;
	}

	.picker-item {
		height: 120rpx;
		line-height: 120rpx;
		font-size: 42rpx;
	}

	.column-label {
		font-size: 28rpx;
	}

	.btn-cancel, .btn-confirm {
		height: 104rpx;
		font-size: 34rpx;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.picker-content {
		background: #1a1a1a;
		color: #ffffff;
	}

	.picker-header {
		background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
		border-bottom-color: rgba(220, 20, 60, 0.2);
	}

	.header-title {
		color: #ffffff;
	}

	.header-subtitle {
		color: #cccccc;
	}

	.datetime-preview {
		background: linear-gradient(135deg,
			rgba(220, 20, 60, 0.1) 0%,
			rgba(255, 182, 193, 0.15) 50%,
			rgba(220, 20, 60, 0.1) 100%
		);
		border-bottom-color: rgba(220, 20, 60, 0.25);
	}

	.quick-presets {
		background: linear-gradient(135deg,
			rgba(220, 20, 60, 0.08) 0%,
			rgba(255, 182, 193, 0.12) 50%,
			rgba(220, 20, 60, 0.08) 100%
		);
		border-bottom-color: rgba(220, 20, 60, 0.2);
	}

	.presets-title {
		color: #cccccc;
	}

	.preset-item {
		background: rgba(42, 42, 42, 0.8);
		border-color: rgba(220, 20, 60, 0.25);
	}

	.preset-item:hover {
		background: rgba(42, 42, 42, 1);
		border-color: rgba(220, 20, 60, 0.4);
	}

	.preset-text {
		color: #cccccc;
	}

	.time-suggestions {
		background: linear-gradient(135deg,
			rgba(255, 165, 0, 0.08) 0%,
			rgba(255, 215, 0, 0.12) 50%,
			rgba(255, 165, 0, 0.08) 100%
		);
		border-bottom-color: rgba(255, 165, 0, 0.2);
	}

	.suggestions-title {
		color: #cccccc;
	}

	.suggestion-item {
		background: rgba(42, 42, 42, 0.8);
		border-color: rgba(255, 165, 0, 0.25);
	}

	.suggestion-item:hover {
		background: rgba(42, 42, 42, 1);
		border-color: rgba(255, 165, 0, 0.4);
	}

	.suggestion-time {
		color: #FFB347;
	}

	.suggestion-label {
		color: #cccccc;
	}

	.picker-item {
		color: #ffffff;
	}

	.column-label {
		color: #999999;
	}

	.btn-cancel {
		background: #333333;
		color: #cccccc;
		border-color: #555555;
	}

	.picker-footer {
		background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
		border-top-color: rgba(220, 20, 60, 0.2);
	}
}

</style>
