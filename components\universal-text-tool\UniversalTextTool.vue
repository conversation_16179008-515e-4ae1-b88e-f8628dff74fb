<template>
  <view class="universal-text-tool">
    <!-- 动态标题和描述 -->
    <view v-if="config.showHeader" class="tool-header">
      <view class="tool-title">{{ config.title }}</view>
      <view class="tool-description">{{ config.description }}</view>
    </view>

    <!-- 参数配置区域 -->
    <view v-if="config.showParams" class="params-section" :class="{ collapsed: isParamsCollapsed }">
      <view class="params-header" @click="toggleParams">
        <text class="params-title">{{ config.paramsTitle || '参数设置' }}</text>
        <text class="toggle-icon">{{ isParamsCollapsed ? '▼' : '▲' }}</text>
      </view>
      
      <view v-if="!isParamsCollapsed" class="params-content">
        <view v-for="param in config.params" :key="param.key" class="param-item">
          <!-- 风格选择器 -->
          <template v-if="param.type === 'style'">
            <text class="param-label">{{ param.label }}</text>
            <view class="style-options">
              <text 
                v-for="option in param.presets" 
                :key="option.value"
                class="style-tag"
                :class="{ active: paramValues[param.key] === option.value }"
                @click="selectParam(param.key, option.value)"
              >
                {{ option.label }}
              </text>
            </view>
          </template>

          <!-- 下拉选择器 -->
          <template v-if="param.type === 'select'">
            <text class="param-label">{{ param.label }}</text>
            <picker 
              :value="paramValues[param.key]" 
              :range="param.options"
              range-key="label"
              @change="onPickerChange($event, param.key, param.options)"
            >
              <view class="picker-display">
                {{ getSelectedLabel(param.key, param.options) || param.placeholder }}
              </view>
            </picker>
          </template>

          <!-- 标签多选 -->
          <template v-if="param.type === 'tags'">
            <text class="param-label">{{ param.label }}</text>
            <view class="tags-container">
              <text 
                v-for="tag in param.options" 
                :key="tag.value"
                class="tag-item"
                :class="{ selected: isTagSelected(param.key, tag.value) }"
                @click="toggleTag(param.key, tag.value)"
              >
                {{ tag.label }}
              </text>
            </view>
          </template>
        </view>
      </view>
    </view>

    <!-- 文本输入区域 -->
    <view class="input-section">
      <textarea
        ref="textInput"
        class="text-input"
        v-model="inputText"
        :placeholder="config.inputPlaceholder || '请输入内容...'"
        :maxlength="config.maxLength || 2000"
        :auto-height="true"
        @input="onTextInput"
        @focus="onInputFocus"
        @blur="onInputBlur"
      ></textarea>
      
      <!-- 字数统计 -->
      <view class="text-stats">
        {{ inputText.length }}/{{ config.maxLength || 2000 }}
      </view>
    </view>

    <!-- 媒体功能区域 -->
    <view v-if="config.enableMedia" class="media-section">
      <!-- 媒体功能按钮 -->
      <view class="media-actions">
        <view 
          v-for="mediaType in enabledMediaTypes" 
          :key="mediaType.id"
          class="media-btn"
          @click="handleMediaAction(mediaType)"
        >
          <text class="media-icon">{{ mediaType.icon }}</text>
          <text class="media-label">{{ mediaType.label }}</text>
        </view>
      </view>

      <!-- 媒体预览区域 -->
      <view v-if="mediaItems.length > 0" class="media-preview">
        <view v-for="(item, index) in mediaItems" :key="index" class="media-item">
          <image v-if="item.type === 'image'" :src="item.src" class="media-thumb" @click="previewMedia(item)" />
          <video v-if="item.type === 'video'" :src="item.src" class="media-thumb" />
          <view v-if="item.type === 'document'" class="document-thumb">
            <text class="doc-icon">📄</text>
            <text class="doc-name">{{ item.name }}</text>
          </view>
          <text class="media-remove" @click="removeMedia(index)">×</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <button 
        class="submit-btn"
        :class="{ loading: isGenerating }"
        :disabled="!canSubmit"
        @click="handleSubmit"
      >
        {{ isGenerating ? '生成中...' : (config.submitText || '生成内容') }}
      </button>
      
      <button 
        v-if="isGenerating" 
        class="stop-btn"
        @click="handleStop"
      >
        停止生成
      </button>
    </view>

    <!-- 建议词区域 -->
    <view v-if="config.showSuggestions && config.suggestions?.length" class="suggestions-section">
      <text class="suggestions-title">{{ config.suggestionTitle || '建议词' }}</text>
      <view class="suggestions-list">
        <text 
          v-for="suggestion in config.suggestions" 
          :key="suggestion"
          class="suggestion-item"
          @click="useSuggestion(suggestion)"
        >
          {{ suggestion }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniversalTextTool',
  props: {
    // 工具配置
    config: {
      type: Object,
      required: true
    },
    // 初始值
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inputText: '',
      paramValues: {},
      mediaItems: [],
      isParamsCollapsed: false,
      isGenerating: false
    };
  },
  computed: {
    // 启用的媒体类型
    enabledMediaTypes() {
      if (!this.config.mediaTypes) return [];
      return this.config.mediaTypes.filter(type => type.enabled);
    },
    
    // 是否可以提交
    canSubmit() {
      return this.inputText.trim().length > 0 && !this.isGenerating;
    }
  },
  mounted() {
    this.initializeValues();
  },
  methods: {
    // 初始化值
    initializeValues() {
      // 初始化参数默认值
      if (this.config.params) {
        this.config.params.forEach(param => {
          if (param.defaultValue) {
            this.$set(this.paramValues, param.key, param.defaultValue);
          }
        });
      }
      
      // 应用初始值
      Object.assign(this.paramValues, this.initialValues);
    },

    // 参数选择
    selectParam(key, value) {
      this.$set(this.paramValues, key, value);
      this.emitChange();
    },

    // 下拉选择变化
    onPickerChange(e, key, options) {
      const selectedOption = options[e.detail.value];
      this.$set(this.paramValues, key, selectedOption.value);
      this.emitChange();
    },

    // 获取选中的标签
    getSelectedLabel(key, options) {
      const value = this.paramValues[key];
      const option = options.find(opt => opt.value === value);
      return option ? option.label : '';
    },

    // 标签选择
    toggleTag(key, value) {
      let tags = this.paramValues[key] || [];
      if (typeof tags === 'string') {
        tags = tags.split(',').filter(t => t.trim());
      }
      
      const index = tags.indexOf(value);
      if (index > -1) {
        tags.splice(index, 1);
      } else {
        tags.push(value);
      }
      
      this.$set(this.paramValues, key, tags.join(','));
      this.emitChange();
    },

    // 检查标签是否选中
    isTagSelected(key, value) {
      const tags = this.paramValues[key] || '';
      return tags.split(',').includes(value);
    },

    // 文本输入
    onTextInput(e) {
      this.inputText = e.detail.value;
      this.emitChange();
    },

    // 输入框焦点
    onInputFocus() {
      this.$emit('input-focus');
    },

    onInputBlur() {
      this.$emit('input-blur');
    },

    // 切换参数面板
    toggleParams() {
      this.isParamsCollapsed = !this.isParamsCollapsed;
    },

    // 媒体操作
    handleMediaAction(mediaType) {
      switch (mediaType.id) {
        case 'image':
          this.handleImageUpload();
          break;
        case 'video':
          this.handleVideoUpload();
          break;
        case 'document':
          this.handleDocumentUpload();
          break;
      }
    },

    // 图片上传
    handleImageUpload() {
      uni.chooseImage({
        count: 9,
        success: (res) => {
          res.tempFilePaths.forEach(path => {
            this.mediaItems.push({
              type: 'image',
              src: path,
              name: this.getFileName(path)
            });
          });
          this.emitChange();
        }
      });
    },

    // 视频上传
    handleVideoUpload() {
      uni.chooseVideo({
        count: 1,
        success: (res) => {
          this.mediaItems.push({
            type: 'video',
            src: res.tempFilePath,
            name: this.getFileName(res.tempFilePath)
          });
          this.emitChange();
        }
      });
    },

    // 文档上传
    handleDocumentUpload() {
      // 实现文档上传逻辑
      console.log('文档上传功能');
    },

    // 移除媒体
    removeMedia(index) {
      this.mediaItems.splice(index, 1);
      this.emitChange();
    },

    // 预览媒体
    previewMedia(item) {
      if (item.type === 'image') {
        uni.previewImage({
          urls: [item.src],
          current: item.src
        });
      }
    },

    // 获取文件名
    getFileName(path) {
      return path.split('/').pop() || path.split('\\').pop() || 'unknown';
    },

    // 使用建议词
    useSuggestion(suggestion) {
      this.inputText = suggestion;
      this.emitChange();
    },

    // 提交
    handleSubmit() {
      if (!this.canSubmit) return;
      
      this.isGenerating = true;
      
      const submitData = {
        text: this.inputText,
        params: this.paramValues,
        media: this.mediaItems,
        config: this.config
      };
      
      this.$emit('submit', submitData);
    },

    // 停止生成
    handleStop() {
      this.isGenerating = false;
      this.$emit('stop');
    },

    // 发出变化事件
    emitChange() {
      this.$emit('change', {
        text: this.inputText,
        params: this.paramValues,
        media: this.mediaItems
      });
    },

    // 外部调用：设置生成状态
    setGenerating(status) {
      this.isGenerating = status;
    },

    // 外部调用：清空内容
    clear() {
      this.inputText = '';
      this.mediaItems = [];
      this.initializeValues();
      this.emitChange();
    }
  }
};
</script>

<style scoped>
.universal-text-tool {
  padding: 20px;
  background: #1a1a2e;
  border-radius: 12px;
}

.tool-header {
  margin-bottom: 20px;
  text-align: center;
}

.tool-title {
  font-size: 18px;
  font-weight: bold;
  color: #e8e8e8;
  margin-bottom: 8px;
}

.tool-description {
  font-size: 14px;
  color: #999;
}

.params-section {
  margin-bottom: 20px;
  background: rgba(40, 40, 60, 0.6);
  border-radius: 8px;
  overflow: hidden;
}

.params-header {
  padding: 12px 16px;
  background: rgba(60, 60, 80, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.params-title {
  color: #e8e8e8;
  font-weight: bold;
}

.toggle-icon {
  color: #999;
}

.params-content {
  padding: 16px;
}

.param-item {
  margin-bottom: 16px;
}

.param-label {
  display: block;
  color: #e8e8e8;
  font-size: 14px;
  margin-bottom: 8px;
}

.style-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.style-tag {
  padding: 6px 12px;
  background: rgba(60, 60, 80, 0.6);
  color: #ccc;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.style-tag.active {
  background: rgba(120, 100, 220, 0.8);
  color: #fff;
}

.text-input {
  width: 100%;
  min-height: 120px;
  background: rgba(40, 40, 60, 0.6);
  color: #e8e8e8;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
}

.text-stats {
  text-align: right;
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.media-section {
  margin: 20px 0;
}

.media-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.media-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(60, 60, 80, 0.6);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.media-btn:hover {
  background: rgba(80, 80, 100, 0.8);
}

.media-icon {
  font-size: 16px;
}

.media-label {
  font-size: 12px;
  color: #ccc;
}

.media-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.media-item {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
}

.media-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-remove {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
}

.action-section {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.submit-btn {
  flex: 1;
  padding: 12px;
  background: linear-gradient(135deg, rgba(120, 100, 220, 0.8), rgba(100, 80, 200, 0.8));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stop-btn {
  padding: 12px 20px;
  background: rgba(255, 68, 68, 0.8);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.suggestions-section {
  margin-top: 20px;
}

.suggestions-title {
  display: block;
  color: #e8e8e8;
  font-size: 14px;
  margin-bottom: 12px;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-item {
  padding: 6px 12px;
  background: rgba(40, 40, 60, 0.6);
  color: #ccc;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.suggestion-item:hover {
  background: rgba(120, 100, 220, 0.6);
  color: #fff;
}
</style>
