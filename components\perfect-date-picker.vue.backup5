<template>
	<view class="date-picker-modal" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
			</view>

			<view class="picker-container">
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
					@pickend="onPickerEnd"
					@pickstart="onPickerStart"
					@wheel.prevent="onWheel"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
					:immediate-change="false"
				>
					<!-- 年份列 -->
					<picker-view-column>
						<view
							v-for="(year, index) in years"
							:key="index"
							class="picker-item"
						>
							{{ year }}年
						</view>
					</picker-view-column>

					<!-- 月份列 -->
					<picker-view-column>
						<view
							v-for="(month, index) in months"
							:key="index"
							class="picker-item"
						>
							{{ month }}月
						</view>
					</picker-view-column>

					<!-- 日期列 -->
					<picker-view-column>
						<view
							v-for="(day, index) in days"
							:key="index"
							class="picker-item"
						>
							{{ day }}日
						</view>
					</picker-view-column>
				</picker-view>

				<!-- 选择框指示器 -->
				<view class="selection-indicator"></view>
			</view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PerfectDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		const now = new Date();
		const currentYear = now.getFullYear();

		return {
			// 当前选中的年月日
			selectedYear: currentYear,
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate(),

			// picker-view的索引值
			pickerValue: [0, 0, 0],

			// 年份数组（1900-2030）- 扩展到2030年确保包含2025年
			years: this.generateYears(),

			// 月份数组（1-12）
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],

			// 滚动状态
			isScrolling: false,
			scrollTimer: null,
			wheelTimer: null, // 鼠标滚轮定时器

			// 滚动惯性控制
			scrollVelocity: 0,
			lastScrollTime: 0,
			momentumTimer: null,

			// picker-view样式配置 - 关键的对齐配置
			indicatorStyle: `
				height: 88rpx;
				background: transparent;
				border: none;
			`,
			maskStyle: `
				background: linear-gradient(180deg,
					rgba(255, 255, 255, 0.95) 0%,
					rgba(255, 255, 255, 0.6) 35%,
					rgba(255, 255, 255, 0.1) 45%,
					transparent 50%,
					rgba(255, 255, 255, 0.1) 55%,
					rgba(255, 255, 255, 0.6) 65%,
					rgba(255, 255, 255, 0.95) 100%
				);
			`
		}
	},
	computed: {
		// 动态计算当月天数
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initializePicker();
			}
		},
		
		// 监听月份变化，调整日期
		selectedMonth() {
			this.adjustDayIfNeeded();
		},
		
		selectedYear() {
			this.adjustDayIfNeeded();
		}
	},
	created() {
		this.initializePicker();
	},
	methods: {
		// 生成年份数组
		generateYears() {
			const currentYear = new Date().getFullYear();
			const years = [];
			// 优化年份范围：1900年到当前年份+10年，确保包含2025年及未来几年
			for (let i = 1900; i <= currentYear + 10; i++) {
				years.push(i);
			}
			console.log('生成年份数组:', {
				startYear: 1900,
				endYear: currentYear + 10,
				currentYear: currentYear,
				totalYears: years.length,
				includes2025: years.includes(2025)
			});
			return years;
		},
		
		// 初始化picker位置
		initializePicker() {
			// 解析传入的日期值
			if (this.value) {
				const [year, month, day] = this.value.split('-').map(Number);
				if (year && month && day) {
					this.selectedYear = year;
					this.selectedMonth = month;
					this.selectedDay = day;
				}
			}

			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.selectedMonth - 1;
			const dayIndex = this.selectedDay - 1;

			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : this.years.findIndex(year => year === new Date().getFullYear()),
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0
			];

			console.log('初始化picker:', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				yearIndex: yearIndex,
				pickerValue: this.pickerValue,
				years: this.years.slice(0, 10) // 显示前10年用于调试
			});
		},
		
		// picker值变化处理
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex] = e.detail.value;

			// 更新选中值，添加边界检查
			this.selectedYear = this.years[yearIndex] || this.selectedYear;
			this.selectedMonth = Math.max(1, Math.min(12, monthIndex + 1)); // 确保月份在1-12范围内
			this.selectedDay = this.days[dayIndex] || this.selectedDay;

			// 更新picker值
			this.pickerValue = [yearIndex, monthIndex, dayIndex];

			// 标记正在滚动
			this.isScrolling = true;

			// 记录滚动时间用于惯性计算
			this.lastScrollTime = Date.now();

			console.log('picker变化:', {
				indexes: [yearIndex, monthIndex, dayIndex],
				values: [this.selectedYear, this.selectedMonth, this.selectedDay],
				yearFromArray: this.years[yearIndex]
			});
		},
		
		// 滚动开始处理
		onPickerStart(e) {
			console.log('开始滚动');
			this.isScrolling = true;

			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}
			if (this.momentumTimer) {
				cancelAnimationFrame(this.momentumTimer);
			}
		},

		// 滚动结束处理 - 关键的磁性吸附逻辑
		onPickerEnd(e) {
			console.log('滚动结束，开始磁性吸附处理');

			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}

			// 立即执行吸附，确保选项居中对齐
			this.scrollTimer = setTimeout(() => {
				this.isScrolling = false;
				this.performMagneticSnap();
			}, 50); // 减少延迟，更快响应
		},
		
		// 执行磁性吸附 - 核心对齐逻辑
		performMagneticSnap() {
			const currentValue = [...this.pickerValue];
			console.log('当前picker值:', currentValue);

			// 计算精确的目标吸附位置
			const targetValue = currentValue.map((value, index) => {
				return this.calculatePreciseSnapPosition(value, index);
			});

			console.log('目标磁性吸附位置:', targetValue);

			// 检查是否需要吸附 - 更严格的阈值
			const needSnap = currentValue.some((val, index) =>
				Math.abs(val - targetValue[index]) > 0.01
			);

			if (needSnap) {
				console.log('执行磁性吸附动画');
				this.animateToTarget(targetValue);
			} else {
				console.log('位置已精确对齐，无需吸附');
				// 确保最终值是整数
				this.pickerValue = targetValue;
				this.updateSelectedValues(targetValue);
			}
		},
		
		// 计算精确吸附位置 - 强化的磁性吸附算法
		calculatePreciseSnapPosition(value, columnIndex) {
			// 获取当前列的最大索引
			let maxIndex;
			switch(columnIndex) {
				case 0: maxIndex = this.years.length - 1; break;
				case 1: maxIndex = this.months.length - 1; break;
				case 2: maxIndex = this.days.length - 1; break;
				default: maxIndex = 0;
			}

			// 计算整数部分和小数部分
			const intPart = Math.floor(value);
			const decPart = value - intPart;

			let targetIndex;

			// 强化的磁性吸附算法 - 确保完美居中
			if (decPart < 0.1) {
				// 非常接近下边界，强制向下吸附
				targetIndex = intPart;
			} else if (decPart > 0.9) {
				// 非常接近上边界，强制向上吸附
				targetIndex = intPart + 1;
			} else if (decPart >= 0.3 && decPart <= 0.7) {
				// 扩大中心区域，强制居中对齐
				targetIndex = Math.round(value);
			} else {
				// 其他区域，选择最近的整数，确保精确对齐
				targetIndex = decPart < 0.5 ? intPart : intPart + 1;
			}

			// 确保在有效范围内，并返回精确的整数值
			const finalIndex = Math.max(0, Math.min(targetIndex, maxIndex));
			console.log(`列${columnIndex}: ${value} -> ${finalIndex} (小数部分: ${decPart.toFixed(3)})`);
			return finalIndex;
		},
		
		// 磁性吸附动画 - 确保完美居中对齐
		animateToTarget(targetValue) {
			const startValue = [...this.pickerValue];
			const startTime = Date.now();
			const duration = 250; // 优化动画时长，快速而流畅

			// 清除之前的动画
			if (this.momentumTimer) {
				cancelAnimationFrame(this.momentumTimer);
			}

			console.log('开始磁性吸附动画:', startValue, '->', targetValue);

			const animate = () => {
				const elapsed = Date.now() - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// 使用强化的缓动函数，确保最终精确对齐
				let easeProgress;
				if (progress < 0.5) {
					// 前半段：快速接近
					easeProgress = 2 * progress * progress;
				} else {
					// 后半段：精确对齐
					easeProgress = 1 - 2 * Math.pow(1 - progress, 2);
				}

				// 计算当前位置
				const currentPos = startValue.map((start, index) => {
					const target = targetValue[index];
					const diff = target - start;
					return start + diff * easeProgress;
				});

				this.pickerValue = currentPos;

				if (progress < 1) {
					this.momentumTimer = requestAnimationFrame(animate);
				} else {
					// 动画结束，强制设置为精确的整数值
					this.pickerValue = [...targetValue];
					this.updateSelectedValues(targetValue);
					this.momentumTimer = null;
					console.log('磁性吸附完成，最终值:', targetValue);
				}
			};

			this.momentumTimer = requestAnimationFrame(animate);
		},
		
		// 更新选中的年月日
		updateSelectedValues(pickerValue) {
			this.selectedYear = this.years[pickerValue[0]] || this.selectedYear;
			// 修复月份计算：pickerValue[1]是从0开始的索引，实际月份应该是索引 + 1
			this.selectedMonth = pickerValue[1] + 1;
			this.selectedDay = this.days[pickerValue[2]] || this.selectedDay;

			console.log('最终选中值:', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay
			});
		},
		
		// 调整日期（处理无效日期）
		adjustDayIfNeeded() {
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
				this.initializePicker(); // 重新初始化picker位置
			}
		},
		
		// 处理鼠标滚轮事件
		onWheel(e) {
			try {
				e.preventDefault();
				e.stopPropagation();

				// 获取滚动方向
				const delta = e.deltaY > 0 ? 1 : -1;

				// 简化的列选择逻辑，避免使用getBoundingClientRect
				// 默认滚动年份列，可以通过键盘修饰键切换
				let columnIndex = 0; // 默认年份列

				// 使用键盘修饰键来选择不同的列
				if (e.shiftKey) {
					columnIndex = 1; // 月份列
				} else if (e.ctrlKey || e.metaKey) {
					columnIndex = 2; // 日期列
				}

				let newValue = [...this.pickerValue];

				switch(columnIndex) {
					case 0:
						// 滚动年份列
						const maxYearIndex = this.years.length - 1;
						newValue[0] = Math.max(0, Math.min(maxYearIndex, newValue[0] + delta));
						break;
					case 1:
						// 滚动月份列
						newValue[1] = Math.max(0, Math.min(11, newValue[1] + delta));
						break;
					case 2:
						// 滚动日期列
						const maxDayIndex = this.days.length - 1;
						newValue[2] = Math.max(0, Math.min(maxDayIndex, newValue[2] + delta));
						break;
				}

				// 更新picker值
				this.pickerValue = newValue;
				this.updateSelectedValues(newValue);

				// 延迟执行磁性吸附
				clearTimeout(this.wheelTimer);
				this.wheelTimer = setTimeout(() => {
					this.performMagneticSnap();
				}, 150);
			} catch (error) {
				console.warn('滚轮事件处理出错:', error);
			}
		},

		// 关闭选择器
		close() {
			this.$emit('close');
		},

		// 确认选择
		confirm() {
			const dateStr = `${this.selectedYear}-${String(this.selectedMonth).padStart(2, '0')}-${String(this.selectedDay).padStart(2, '0')}`;
			console.log('确认选择日期:', dateStr);
			this.$emit('confirm', dateStr);
		}
	}
}
</script>

<style scoped>
/* 模态框容器 */
.date-picker-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: flex-end;
}

/* 遮罩层 */
.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

/* 选择器内容 - 多端适配优化 */
.picker-content {
	position: relative;
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	width: 100%;
	max-width: 750rpx; /* 增加最大宽度，适配更大屏幕 */
	margin: 0 auto;
	padding: 0;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);

	/* 多端适配 */
	min-width: 600rpx; /* 确保最小宽度 */
}

/* 头部 */
.picker-header {
	text-align: center;
	padding: 24rpx 30rpx;
	background: #f8f9fa;
	border-bottom: 1rpx solid #e0e0e0;
}

.header-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* picker容器 - 多端适配优化 */
.picker-container {
	position: relative;
	height: 480rpx; /* 增加高度，提供更好的视觉体验 */
	overflow: hidden;
	padding: 0 20rpx; /* 添加左右内边距 */
}

/* picker-view样式 - 关键的对齐配置 */
.picker-view {
	height: 100%;
	width: 100%;
	/* 确保picker-view内部对齐 */
	display: flex;
	align-items: center;
}

/* 强制picker-view内部元素对齐 */
.picker-view ::v-deep .uni-picker-view-group {
	height: 88rpx !important;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-view ::v-deep .uni-picker-view-content {
	padding: 0 !important;
	margin: 0 !important;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-view ::v-deep .uni-picker-view-indicator {
	height: 88rpx !important;
	background: transparent !important;
	border: none !important;
}

.picker-view ::v-deep .uni-picker-view-mask {
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(255, 255, 255, 0.6) 35%,
		rgba(255, 255, 255, 0.1) 45%,
		transparent 50%,
		rgba(255, 255, 255, 0.1) 55%,
		rgba(255, 255, 255, 0.6) 65%,
		rgba(255, 255, 255, 0.95) 100%
	) !important;
}

/* picker项目样式 - 多端适配优化 */
.picker-item {
	height: 88rpx; /* 增加高度，提升触摸体验 */
	line-height: 88rpx;
	text-align: center;
	font-size: 36rpx; /* 增大字体，提升可读性 */
	font-weight: 500;
	color: #333;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;

	/* 添加微妙的文字阴影，增强可读性 */
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);

	/* 优化字体渲染 */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;

	/* 多端适配 */
	min-height: 80rpx;
	padding: 0 10rpx;
}

/* 选中状态的项目样式 - 增强视觉效果 */
.picker-item:nth-child(3) {
	color: #dc143c;
	font-weight: 600;
	font-size: 40rpx; /* 选中项字体更大 */
	transform: scale(1.08);
	text-shadow: 0 2rpx 6rpx rgba(220, 20, 60, 0.3);
	background: rgba(220, 20, 60, 0.02); /* 添加背景色 */
	border-radius: 8rpx;
}

/* 相邻项目的渐变效果 */
.picker-item:nth-child(2),
.picker-item:nth-child(4) {
	color: #666;
	font-size: 32rpx;
	opacity: 0.85;
}

.picker-item:nth-child(1),
.picker-item:nth-child(5) {
	color: #999;
	font-size: 28rpx;
	opacity: 0.6;
}

/* 选择框指示器 - 多端适配优化 */
.selection-indicator {
	position: absolute;
	top: 50%;
	left: 30rpx; /* 增加左右边距 */
	right: 30rpx;
	height: 88rpx; /* 与picker-item高度保持一致 */
	transform: translateY(-50%);

	/* 优化的背景渐变 - 更明显的视觉效果 */
	background: linear-gradient(135deg,
		rgba(220, 20, 60, 0.06) 0%,
		rgba(255, 182, 193, 0.12) 25%,
		rgba(255, 255, 255, 0.20) 50%,
		rgba(255, 182, 193, 0.12) 75%,
		rgba(220, 20, 60, 0.06) 100%
	);

	/* 更明显的边框设计 */
	border: 3rpx solid rgba(220, 20, 60, 0.4);
	border-radius: 16rpx;

	/* 增强的阴影效果 */
	box-shadow:
		0 4rpx 12rpx rgba(220, 20, 60, 0.15),
		0 12rpx 32rpx rgba(220, 20, 60, 0.12),
		inset 0 3rpx 6rpx rgba(255, 255, 255, 0.7),
		inset 0 -2rpx 4rpx rgba(220, 20, 60, 0.15);

	/* 添加微妙的动画效果 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	/* 禁用点击事件 */
	pointer-events: none;
	z-index: 10;
}

/* 选择器激活时的指示器效果 */
.picker-container:active .selection-indicator {
	transform: translateY(-50%) scale(1.03);
	border-color: rgba(220, 20, 60, 0.6);
	box-shadow:
		0 6rpx 16rpx rgba(220, 20, 60, 0.2),
		0 16rpx 40rpx rgba(220, 20, 60, 0.15),
		inset 0 4rpx 8rpx rgba(255, 255, 255, 0.8),
		inset 0 -2rpx 6rpx rgba(220, 20, 60, 0.2);
}

/* 渐变遮罩效果 */
.picker-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.9) 0%,
		rgba(255, 255, 255, 0.6) 20%,
		rgba(255, 255, 255, 0.2) 35%,
		transparent 45%,
		transparent 55%,
		rgba(255, 255, 255, 0.2) 65%,
		rgba(255, 255, 255, 0.6) 80%,
		rgba(255, 255, 255, 0.9) 100%
	);
	pointer-events: none;
	z-index: 5;
}

/* 底部按钮 - 多端适配优化 */
.picker-footer {
	display: flex;
	justify-content: space-between;
	padding: 30rpx 40rpx 40rpx; /* 增加内边距 */
	gap: 40rpx; /* 增加按钮间距 */
	background: #f8f9fa;
	border-top: 1rpx solid #e0e0e0;
}

.btn-cancel, .btn-confirm {
	flex: 1;
	height: 96rpx; /* 增加按钮高度，提升触摸体验 */
	line-height: 96rpx;
	text-align: center;
	border-radius: 20rpx; /* 增加圆角 */
	font-size: 34rpx; /* 增大字体 */
	font-weight: 600; /* 增加字重 */
	border: none;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	cursor: pointer;

	/* 多端适配 */
	min-height: 88rpx;
	min-width: 200rpx;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
	border: 2rpx solid #e0e0e0;
}

.btn-cancel:hover {
	background: #eeeeee;
	border-color: #d0d0d0;
}

.btn-cancel:active {
	background: #e8e8e8;
	transform: scale(0.98);
}

.btn-confirm {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	box-shadow: 0 6rpx 20rpx rgba(220, 20, 60, 0.3);
	border: 2rpx solid transparent;
}

.btn-confirm:hover {
	box-shadow: 0 8rpx 24rpx rgba(220, 20, 60, 0.4);
	transform: translateY(-1rpx);
}

.btn-confirm:active {
	background: linear-gradient(135deg, #b91c3c 0%, #991b3c 100%);
	transform: scale(0.98);
	box-shadow: 0 4rpx 12rpx rgba(220, 20, 60, 0.4);
}

/* 多端响应式优化 */

/* 小屏设备 (手机竖屏) */
@media (max-width: 750rpx) {
	.picker-content {
		max-width: 680rpx;
		min-width: 500rpx;
	}

	.picker-container {
		height: 440rpx;
		padding: 0 15rpx;
	}

	.picker-item {
		font-size: 32rpx;
		height: 80rpx;
		line-height: 80rpx;
	}

	.picker-item:nth-child(3) {
		font-size: 36rpx;
	}

	.selection-indicator {
		left: 20rpx;
		right: 20rpx;
		height: 80rpx;
	}

	.picker-footer {
		padding: 25rpx 30rpx 35rpx;
		gap: 30rpx;
	}

	.btn-cancel, .btn-confirm {
		height: 88rpx;
		line-height: 88rpx;
		font-size: 32rpx;
	}
}

/* 中等屏幕 (平板) */
@media (min-width: 751rpx) and (max-width: 1200rpx) {
	.picker-content {
		max-width: 800rpx;
	}

	.picker-container {
		height: 500rpx;
	}

	.picker-item {
		font-size: 38rpx;
		height: 90rpx;
		line-height: 90rpx;
	}

	.picker-item:nth-child(3) {
		font-size: 42rpx;
	}

	.selection-indicator {
		height: 90rpx;
	}
}

/* 大屏设备 (桌面端) */
@media (min-width: 1201rpx) {
	.picker-content {
		max-width: 900rpx;
	}

	.picker-container {
		height: 520rpx;
	}

	.picker-item {
		font-size: 40rpx;
		height: 95rpx;
		line-height: 95rpx;
	}

	.picker-item:nth-child(3) {
		font-size: 44rpx;
	}

	.selection-indicator {
		height: 95rpx;
	}

	.btn-cancel, .btn-confirm {
		height: 100rpx;
		line-height: 100rpx;
		font-size: 36rpx;
	}
}
</style>
