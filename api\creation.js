/**
 * 创作相关API
 */

import { request } from './request';

/**
 * 创建文本内容
 * @param {Object} data - 创建参数
 * @returns {Promise} 创建结果
 */
export function createText(data) {
  return request({
    url: '/creation/text',
    method: 'POST',
    data
  });
}

/**
 * 创建文本内容 - 增强版
 * @param {Object} data - 创建参数
 * @param {String} scenario - 使用场景 (story, homework, article, etc.)
 * @param {Object} scenarioConfig - 场景配置
 * @returns {Promise} 创建结果
 */
export function createTextEnhanced(data, scenario = 'general', scenarioConfig = null) {
  // 根据不同场景调整参数
  const enhancedData = {
    ...data,
    scenario: scenario // 将场景信息传递给后端
  };
  
  // 如果有场景配置，合并到请求数据中
  if (scenarioConfig) {
    enhancedData.scenarioConfig = scenarioConfig;
  }
  
  return request({
    url: '/creation/text',
    method: 'POST',
    data: enhancedData
  });
}



/**
 * 创建音乐内容
 * @param {Object} data - 创建参数
 * @returns {Promise} 创建结果
 */
export function createMusic(data) {
  return request({
    url: '/creation/music',
    method: 'POST',
    data
  });
}

/**
 * 创建视频内容
 * @param {Object} data - 创建参数
 * @returns {Promise} 创建结果
 */
export function createVideo(data) {
  return request({
    url: '/creation/video',
    method: 'POST',
    data
  });
}

/**
 * 获取创作结果
 * @param {String} id - 创作ID
 * @param {String} type - 创作类型
 * @returns {Promise} 创作结果
 */
export function getCreationResult(id, type) {
  return request({
    url: `/creation/${type}/result/${id}`
  });
}

/**
 * 获取创作历史
 * @param {Object} params - 查询参数
 * @returns {Promise} 创作历史列表
 */
export function getCreationHistory(params) {
  return request({
    url: '/creation/history',
    data: params
  });
}

/**
 * 获取推荐提示词
 * @param {String} type - 创作类型
 * @returns {Promise} 推荐提示词列表
 */
export function getRecommendedPrompts(type) {
  return request({
    url: `/creation/${type}/prompts`
  });
}

/**
 * 删除创作记录
 * @param {String} id - 创作ID
 * @returns {Promise} 删除结果
 */
export function deleteCreation(id) {
  return request({
    url: `/creation/${id}`,
    method: 'DELETE'
  });
}

/**
 * 分享创作
 * @param {String} id - 创作ID
 * @returns {Promise} 分享信息
 */
export function shareCreation(id) {
  return request({
    url: `/creation/share/${id}`,
    method: 'POST'
  });
}

/**
 * 获取创作详情
 * @param {String} id - 创作ID
 * @returns {Promise} 创作详情
 */
export function getCreationDetail(id) {
  return request({
    url: `/creation/${id}`
  });
}

export default {
  createText,
  createTextEnhanced,
  createMusic,
  createVideo,
  getCreationResult,
  getCreationHistory,
  getRecommendedPrompts,
  deleteCreation,
  shareCreation,
  getCreationDetail
}; 