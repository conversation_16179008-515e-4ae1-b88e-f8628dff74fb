# 姓名配对功能 API 文档

## 📋 概述

基于传统易经学文化的姓名配对分析工作流

## 🚀 快速开始

```javascript
import { 姓名配对 } from '@/api/姓名配对/index.js';

const result = await 姓名配对({
    name1: '示例值',
    gender1: '示例值',
    birthDate1: '示例值',
    birthTime1: '示例值',
    name2: '示例值',
    gender2: '示例值',
    birthDate2: '示例值',
    birthTime2: '示例值'
});
```

## 📝 API 接口

### 主要接口

#### `姓名配对(formData, options)`
执行姓名配对功能的主接口。

**参数：**
- `name1` (string): name1 *必需*
- `gender1` (string): gender1 *必需*
- `birthDate1` (string): birthDate1 *必需*
- `birthTime1` (string): birthTime1 *必需*
- `name2` (string): name2 *必需*
- `gender2` (string): gender2 *必需*
- `birthDate2` (string): birthDate2 *必需*
- `birthTime2` (string): birthTime2 *必需*

**返回：**
```javascript
{
    success: true,
    data: {
        // 处理结果
    }
}
```

## ⚙️ 配置说明

### 工作流配置
- 工作流ID: `name_matching_workflow_001`
- 工作流类型: `name_compatibility`
- 基础费用: 12金币

## 🔧 工作流对接

### 结构化参数格式
```javascript
{
    requestId: "req_id",
    workflowId: "name_matching_workflow_001",
    workflowType: "name_compatibility",
    structuredParams: {
        name1: { type: "text", value: "值", placeholder: "{{name1}}" },
        gender1: { type: "text", value: "值", placeholder: "{{gender1}}" },
        birthDate1: { type: "text", value: "值", placeholder: "{{birthDate1}}" },
        birthTime1: { type: "text", value: "值", placeholder: "{{birthTime1}}" },
        name2: { type: "text", value: "值", placeholder: "{{name2}}" },
        gender2: { type: "text", value: "值", placeholder: "{{gender2}}" },
        birthDate2: { type: "text", value: "值", placeholder: "{{birthDate2}}" },
        birthTime2: { type: "text", value: "值", placeholder: "{{birthTime2}}" }
    }
}
```

## 🚨 错误处理

常见错误码：
- `NAME_COMPATIBILITY_001`: 参数格式不正确
- `NAME_COMPATIBILITY_007`: 金币余额不足
- `NAME_COMPATIBILITY_008`: 工作流执行超时

## 📞 技术支持

如有问题，请联系开发团队。