/**
 * 文本区域相关工具函数
 */

/**
 * 检测文本区域是否有溢出内容
 * @param {HTMLElement} element - 文本区域元素
 * @returns {boolean} - 是否有溢出内容
 */
export function checkTextareaOverflow(element) {
  if (!element) return false;
  
  try {
    return element.scrollHeight > element.clientHeight;
  } catch (e) {
    console.error('检查文本区域溢出失败:', e);
    return false;
  }
}

/**
 * 计算文本行数
 * @param {string} text - 文本内容
 * @returns {number} - 行数
 */
export function countLines(text) {
  if (!text) return 1;
  return (text.match(/\n/g) || []).length + 1;
}

/**
 * 获取文本选择区域
 * @param {HTMLElement} element - 文本区域元素
 * @returns {Object} - 选择区域的开始和结束位置
 */
export function getTextSelection(element) {
  if (!element) return { start: 0, end: 0 };
  
  try {
    return {
      start: element.selectionStart,
      end: element.selectionEnd
    };
  } catch (e) {
    console.error('获取文本选择区域失败:', e);
    return { start: 0, end: 0 };
  }
}

/**
 * 设置文本选择区域
 * @param {HTMLElement} element - 文本区域元素
 * @param {number} start - 开始位置
 * @param {number} end - 结束位置
 */
export function setTextSelection(element, start, end) {
  if (!element) return;
  
  try {
    element.setSelectionRange(start, end);
  } catch (e) {
    console.error('设置文本选择区域失败:', e);
  }
}

/**
 * 在指定位置插入文本
 * @param {string} originalText - 原始文本
 * @param {string} insertText - 要插入的文本
 * @param {number} position - 插入位置
 * @returns {string} - 插入后的文本
 */
export function insertTextAtPosition(originalText, insertText, position) {
  if (!originalText) return insertText;
  if (!insertText) return originalText;
  
  const before = originalText.slice(0, position);
  const after = originalText.slice(position);
  
  return before + insertText + after;
}

/**
 * 替换选中的文本
 * @param {string} originalText - 原始文本
 * @param {string} replaceText - 替换文本
 * @param {number} start - 开始位置
 * @param {number} end - 结束位置
 * @returns {string} - 替换后的文本
 */
export function replaceSelectedText(originalText, replaceText, start, end) {
  if (!originalText) return replaceText;
  
  const before = originalText.slice(0, start);
  const after = originalText.slice(end);
  
  return before + replaceText + after;
}

/**
 * 滚动到文本区域底部
 * @param {HTMLElement} element - 文本区域元素
 */
export function scrollToBottom(element) {
  if (!element) return;
  
  try {
    setTimeout(() => {
      element.scrollTop = element.scrollHeight;
    }, 0);
  } catch (e) {
    console.error('滚动到底部失败:', e);
  }
}

/**
 * 检测文本选择模式是否激活
 * @returns {boolean} - 是否激活了文本选择模式
 */
export function isTextSelectionActive() {
  if (typeof window === 'undefined' || !window.getSelection) return false;
  
  const selection = window.getSelection();
  return selection && selection.toString().length > 0;
}

/**
 * 应用触摸优化
 * @param {HTMLElement} element - 要优化的元素
 */
export function applyTouchOptimizations(element) {
  if (!element || typeof element !== 'object') return;
  
  try {
    // 移除默认的触摸滚动行为
    element.style.overscrollBehavior = 'none';
    element.style.webkitOverflowScrolling = 'touch';
    
    // 添加CSS类
    element.classList.add('touch-enhanced');
    
    // 针对iOS设备优化
    if (typeof navigator !== 'undefined' && 
        navigator.userAgent && 
        /iPad|iPhone|iPod/.test(navigator.userAgent)) {
      element.style.webkitAppearance = 'none';
    }
  } catch (e) {
    console.error('应用触摸优化失败:', e);
  }
} 