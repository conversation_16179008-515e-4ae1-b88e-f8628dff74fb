.quality-options {
  display: flex;
  
  .quality-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 600;
    background-color: #0f3460;
    color: #ffffff;
    margin-right: 10rpx;
    border-radius: 8rpx;
    border: 2rpx solid #3a6299;
    transition: all 0.2s ease;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
    letter-spacing: 1rpx;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
    
    &:last-child {
      margin-right: 0;
    }
    
    &.active {
      background-color: #1e3799;
      color: #ffffff;
      border-color: #4a69bd;
      box-shadow: 0 0 12rpx rgba(74, 105, 189, 0.6);
      transform: translateY(-2rpx);
    }
    
    &:active {
      transform: translateY(1rpx);
      box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
    }
  }
} 