/**
 * 起名功能业务逻辑接口
 * 处理起名功能的业务逻辑，包括费用计算、用户状态检查等
 * 创建时间：2025-01-11
 */

import { apiRequest } from '../common/request.js';
import { 执行起名工作流, 获取起名历史 } from './工作流执行接口.js';
import { 起名工作流配置 } from './工作流配置.js';
import consumptionRecordService from '@/services/ConsumptionRecordService.js';

/**
 * 起名功能业务逻辑类
 */
class NameGeneratorBusiness {
    constructor() {
        this.config = 起名工作流配置;
        this.moduleName = '起名功能';
    }

    /**
     * 完整的起名业务流程
     * @param {Object} formData - 表单数据
     * @param {Object} options - 选项
     */
    async completeNameGenerationFlow(formData, options = {}) {
        try {
            // 1. 检查用户登录状态
            const userInfo = await this.checkUserLoginStatus();
            if (!userInfo.isLoggedIn) {
                throw new Error('请先登录后再使用起名功能');
            }

            // 2. 计算费用
            const costInfo = this.calculateNameGenerationCost(formData, userInfo.userInfo);

            // 3. 检查用户余额
            const balanceCheck = await this.checkUserBalance(costInfo.totalCost);
            if (!balanceCheck.sufficient) {
                return {
                    success: false,
                    code: 'INSUFFICIENT_BALANCE',
                    message: '金币余额不足，请先充值',
                    data: {
                        required: costInfo.totalCost,
                        current: balanceCheck.currentBalance,
                        shortfall: costInfo.totalCost - balanceCheck.currentBalance
                    }
                };
            }

            // 4. 扣费
            const paymentResult = await this.deductCoins(costInfo);
            if (!paymentResult.success) {
                throw new Error('扣费失败：' + paymentResult.message);
            }

            // 4.1 记录消费
            await this.recordConsumption(formData, costInfo, paymentResult);

            // 5. 执行起名工作流
            const nameResult = await 执行起名工作流(formData, {
                ...options,
                transactionId: paymentResult.data.transactionId,
                onProgress: (progress) => {
                    console.log(`起名进度: ${progress.status}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            if (!nameResult.success) {
                // 如果起名失败，退还金币
                await this.refundCoins(paymentResult.data.transactionId, '起名失败退款');
                throw new Error('起名失败：' + nameResult.message);
            }

            // 6. 保存起名记录
            await this.saveNameGenerationRecord({
                ...nameResult.data,
                formData,
                costInfo,
                transactionId: paymentResult.data.transactionId
            });

            return {
                success: true,
                data: {
                    ...nameResult.data,
                    costInfo,
                    transactionId: paymentResult.data.transactionId,
                    completedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('起名业务流程失败:', error);
            throw error;
        }
    }

    /**
     * 检查用户登录状态
     */
    async checkUserLoginStatus() {
        try {
            const userInfo = await apiRequest('user/info');
            return {
                isLoggedIn: userInfo.success,
                userInfo: userInfo.success ? userInfo.data : null
            };
        } catch (error) {
            return {
                isLoggedIn: false,
                userInfo: null
            };
        }
    }

    /**
     * 计算起名费用
     * @param {Object} formData - 表单数据
     * @param {Object} userInfo - 用户信息
     */
    calculateNameGenerationCost(formData, userInfo) {
        const pricing = this.config.pricing;
        let baseCost = pricing.basePrice;
        
        // 计算额外名字费用
        const extraNames = Math.max(0, (formData.generateCount || 8) - 8);
        const extraCost = extraNames * pricing.pricePerName;
        
        // 计算总费用
        let totalCost = baseCost + extraCost;
        
        // 应用会员折扣
        if (userInfo && userInfo.membershipLevel && userInfo.membershipLevel !== 'free') {
            totalCost = Math.ceil(totalCost * pricing.memberDiscount);
        }

        return {
            baseCost,
            extraCost,
            totalCost,
            discount: userInfo?.membershipLevel !== 'free' ? pricing.memberDiscount : 1,
            breakdown: {
                baseNames: 8,
                extraNames,
                pricePerExtraName: pricing.pricePerName
            }
        };
    }

    /**
     * 检查用户余额
     * @param {number} requiredAmount - 需要的金币数量
     */
    async checkUserBalance(requiredAmount) {
        try {
            const balanceResult = await apiRequest('payment/balance');
            if (!balanceResult.success) {
                throw new Error('获取余额失败');
            }

            const currentBalance = balanceResult.data.coins || 0;
            return {
                sufficient: currentBalance >= requiredAmount,
                currentBalance,
                requiredAmount
            };
        } catch (error) {
            console.error('检查余额失败:', error);
            throw error;
        }
    }

    /**
     * 扣除金币
     * @param {Object} costInfo - 费用信息
     */
    async deductCoins(costInfo) {
        try {
            return await apiRequest('payment/deduct', {
                method: 'POST',
                body: {
                    amount: costInfo.totalCost,
                    module: this.moduleName,
                    description: '起名功能费用',
                    breakdown: costInfo
                }
            });
        } catch (error) {
            console.error('扣费失败:', error);
            throw error;
        }
    }

    /**
     * 退还金币
     * @param {string} transactionId - 交易ID
     * @param {string} reason - 退款原因
     */
    async refundCoins(transactionId, reason) {
        try {
            return await apiRequest('payment/refund', {
                method: 'POST',
                body: {
                    transactionId,
                    reason,
                    module: this.moduleName
                }
            });
        } catch (error) {
            console.error('退款失败:', error);
            // 退款失败不抛出错误，避免影响主流程
            return { success: false, message: error.message };
        }
    }

    /**
     * 记录消费
     * @param {Object} formData - 表单数据
     * @param {Object} costInfo - 费用信息
     * @param {Object} paymentResult - 支付结果
     */
    async recordConsumption(formData, costInfo, paymentResult) {
        try {
            await consumptionRecordService.recordNameGenerationConsumption({
                nameType: this.getNameTypeDescription(formData),
                gender: formData.gender,
                surname: formData.surname,
                cost: costInfo.totalCost,
                resultCount: formData.generateCount || 8,
                transactionId: paymentResult.data?.transactionId
            });
            console.log('✅ 起名消费记录已保存');
        } catch (error) {
            console.error('❌ 记录起名消费失败:', error);
            // 记录失败不影响主流程
        }
    }

    /**
     * 获取起名类型描述
     */
    getNameTypeDescription(formData) {
        if (formData.surname) {
            return `${formData.surname}姓${formData.gender === 'male' ? '男' : '女'}孩起名`;
        }
        return `${formData.gender === 'male' ? '男' : '女'}孩起名`;
    }

    /**
     * 保存起名记录
     * @param {Object} recordData - 记录数据
     */
    async saveNameGenerationRecord(recordData) {
        try {
            return await apiRequest('name-generator/save-record', {
                method: 'POST',
                body: {
                    userId: uni.getStorageSync('userId'),
                    module: this.moduleName,
                    ...recordData,
                    createdAt: new Date().toISOString()
                }
            });
        } catch (error) {
            console.error('保存起名记录失败:', error);
            // 保存记录失败不影响主流程
            return { success: false, message: error.message };
        }
    }

    /**
     * 获取用户起名历史
     * @param {Object} params - 查询参数
     */
    async getUserNameHistory(params = {}) {
        try {
            return await 获取起名历史({
                userId: uni.getStorageSync('userId'),
                ...params
            });
        } catch (error) {
            console.error('获取起名历史失败:', error);
            throw error;
        }
    }

    /**
     * 获取起名统计信息
     */
    async getNameGenerationStats() {
        try {
            return await apiRequest(`name-generator/stats?userId=${uni.getStorageSync('userId')}`);
        } catch (error) {
            console.error('获取起名统计失败:', error);
            throw error;
        }
    }

    /**
     * 验证起名结果
     * @param {string} requestId - 请求ID
     */
    async validateNameResult(requestId) {
        try {
            return await apiRequest(`name-generator/validate?requestId=${requestId}`);
        } catch (error) {
            console.error('验证起名结果失败:', error);
            throw error;
        }
    }

    /**
     * 重新生成名字
     * @param {Object} originalFormData - 原始表单数据
     * @param {Object} adjustments - 调整参数
     */
    async regenerateNames(originalFormData, adjustments = {}) {
        const newFormData = {
            ...originalFormData,
            ...adjustments
        };

        return await this.completeNameGenerationFlow(newFormData);
    }
}

// 创建业务逻辑实例
const nameGeneratorBusiness = new NameGeneratorBusiness();

// ================================
// 🎯 导出的业务接口
// ================================

/**
 * 完整起名业务流程
 * @param {Object} formData - 表单数据
 * @param {Object} options - 选项
 */
export async function 完整起名业务流程(formData, options = {}) {
    return await nameGeneratorBusiness.completeNameGenerationFlow(formData, options);
}

/**
 * 获取用户起名历史
 * @param {Object} params - 查询参数
 */
export async function 获取用户起名历史(params = {}) {
    return await nameGeneratorBusiness.getUserNameHistory(params);
}

/**
 * 获取起名统计信息
 */
export async function 获取起名统计信息() {
    return await nameGeneratorBusiness.getNameGenerationStats();
}

/**
 * 计算起名费用
 * @param {Object} formData - 表单数据
 */
export async function 计算起名费用(formData) {
    const userInfo = await nameGeneratorBusiness.checkUserLoginStatus();
    return nameGeneratorBusiness.calculateNameGenerationCost(formData, userInfo.userInfo);
}

/**
 * 验证起名结果
 * @param {string} requestId - 请求ID
 */
export async function 验证起名结果(requestId) {
    return await nameGeneratorBusiness.validateNameResult(requestId);
}

/**
 * 重新生成名字
 * @param {Object} originalFormData - 原始表单数据
 * @param {Object} adjustments - 调整参数
 */
export async function 重新生成名字(originalFormData, adjustments = {}) {
    return await nameGeneratorBusiness.regenerateNames(originalFormData, adjustments);
}

export default {
    完整起名业务流程,
    获取用户起名历史,
    获取起名统计信息,
    计算起名费用,
    验证起名结果,
    重新生成名字
};
