/**
 * 姓名配对功能工作流配置
 * 定义姓名配对功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 姓名配对工作流配置
 */
export const 姓名配对工作流配置 = {
    // 工作流基础信息
    workflowId: 'name_matching_workflow_001',
    workflowType: 'name_compatibility',
    moduleName: '姓名配对',
    
    // 工作流描述
    description: '基于传统易经学文化的姓名配对分析工作流',

    // 结构化参数定义
    structuredParams: {
        
        name1: {
            type: 'text',
            required: true,
            placeholder: '{{name1}}',
            description: 'name1'
        },
        
        gender1: {
            type: 'text',
            required: true,
            placeholder: '{{gender1}}',
            description: 'gender1'
        },
        
        birthDate1: {
            type: 'text',
            required: true,
            placeholder: '{{birthDate1}}',
            description: 'birthDate1'
        },
        
        birthTime1: {
            type: 'text',
            required: true,
            placeholder: '{{birthTime1}}',
            description: 'birthTime1'
        },
        
        name2: {
            type: 'text',
            required: true,
            placeholder: '{{name2}}',
            description: 'name2'
        },
        
        gender2: {
            type: 'text',
            required: true,
            placeholder: '{{gender2}}',
            description: 'gender2'
        },
        
        birthDate2: {
            type: 'text',
            required: true,
            placeholder: '{{birthDate2}}',
            description: 'birthDate2'
        },
        
        birthTime2: {
            type: 'text',
            required: true,
            placeholder: '{{birthTime2}}',
            description: 'birthTime2'
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下参数执行姓名配对任务：

- name1：{{name1}}
- gender1：{{gender1}}
- birthDate1：{{birthDate1}}
- birthTime1：{{birthTime1}}
- name2：{{name2}}
- gender2：{{gender2}}
- birthDate2：{{birthDate2}}
- birthTime2：{{birthTime2}}

请提供详细的处理结果。
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: 'object'
        }
    },

    // 费用配置
    pricing: {
        basePrice: 12,
        memberDiscount: 0.8
    },

    // 执行配置
    execution: {
        timeout: 300000,
        maxRetries: 3,
        pollInterval: 2000,
        enableCache: true
    }
};

/**
 * 姓名配对参数验证规则
 */
export const 姓名配对参数验证规则 = {
    required: ['name1', 'gender1', 'birthDate1', 'birthTime1', 'name2', 'gender2', 'birthDate2', 'birthTime2'],
    formats: {},
    ranges: {},
    enums: {}
};

/**
 * 姓名配对错误码定义
 */
export const 姓名配对错误码 = {
    INVALID_PARAMS: { code: 'NAME_COMPATIBILITY_001', message: '参数格式不正确' },
    INSUFFICIENT_COINS: { code: 'NAME_COMPATIBILITY_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'NAME_COMPATIBILITY_008', message: '工作流执行超时' },
    WORKFLOW_FAILED: { code: 'NAME_COMPATIBILITY_009', message: '工作流执行失败' },
    UNKNOWN_ERROR: { code: 'NAME_COMPATIBILITY_999', message: '未知错误' }
};

/**
 * 姓名配对状态定义
 */
export const 姓名配对状态 = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

export default {
    姓名配对工作流配置,
    姓名配对参数验证规则,
    姓名配对错误码,
    姓名配对状态
};