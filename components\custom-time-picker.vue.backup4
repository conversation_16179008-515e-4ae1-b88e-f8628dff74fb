<template>
	<view class="custom-time-picker" v-if="visible" @touchmove.stop.prevent @wheel.stop.prevent>
		<view class="picker-mask" @click="close" @touchmove.stop.prevent @wheel.stop.prevent></view>
		<view class="picker-content" @touchmove.stop.prevent @wheel.stop.prevent>
			<view class="picker-header">
				<text class="header-title">选择出生时间</text>
				<view class="header-actions">
					<text class="action-btn cancel" @click="close">取消</text>
					<text class="action-btn confirm" @click="confirm">确定</text>
				</view>
			</view>
			
			<view class="time-selector" @touchmove.stop.prevent @wheel.stop.prevent>
				<!-- 小时选择 -->
				<view class="selector-column">
					<text class="column-title">时</text>
					<view 
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="hourScroll"
					>
						<view 
							v-for="hour in hours" 
							:key="hour"
							:id="'hour-' + hour"
							class="scroll-item"
							:class="{ active: hour === selectedHour }"
							@click="selectHour(hour)"
						>
							{{ String(hour).padStart(2, '0') }}
						</view>
					</view>
				</view>
				
				<!-- 分钟选择 -->
				<view class="selector-column">
					<text class="column-title">分</text>
					<view 
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="minuteScroll"
					>
						<view 
							v-for="minute in minutes" 
							:key="minute"
							:id="'minute-' + minute"
							class="scroll-item"
							:class="{ active: minute === selectedMinute }"
							@click="selectMinute(minute)"
						>
							{{ String(minute).padStart(2, '0') }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedHour: new Date().getHours(),
			selectedMinute: new Date().getMinutes(),
			hours: Array.from({ length: 24 }, (_, i) => i),
			minutes: Array.from({ length: 60 }, (_, i) => i),
			touchStartY: 0,
			isScrolling: false
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [hour, minute] = newVal.split(':');
					this.selectedHour = parseInt(hour);
					this.selectedMinute = parseInt(minute);
				}
			},
			immediate: true
		}
	},
	methods: {
		selectHour(hour) {
			this.selectedHour = hour;
		},
		selectMinute(minute) {
			this.selectedMinute = minute;
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');
			const timeStr = `${hour}:${minute}`;
			this.$emit('confirm', timeStr);
		},
		onTouchStart(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.touchStartY = e.touches[0].clientY;
			this.isScrolling = true;
		},
		onTouchMove(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			if (!this.isScrolling) return;

			const deltaY = e.touches[0].clientY - this.touchStartY;
			const target = e.currentTarget;
			target.scrollTop -= deltaY * 0.5; // 减慢滚动速度
			this.touchStartY = e.touches[0].clientY;
		},
		onTouchEnd(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.isScrolling = false;
		},
		onWheel(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			const target = e.currentTarget;
			target.scrollTop += e.deltaY * 0.5; // 减慢滚动速度
		}
	}
}
</script>

<style scoped>
.custom-time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 500rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #F5E6D3, #E8D5B7);
	border-radius: 20rpx;
	overflow: hidden;
	animation: scaleIn 0.3s ease;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
	z-index: 2;
}

@keyframes scaleIn {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

.picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #8B4513;
	color: white;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
}

.header-actions {
	display: flex;
	gap: 30rpx;
}

.action-btn {
	font-size: 28rpx;
	padding: 8rpx 16rpx;
	border-radius: 6rpx;
	cursor: pointer;
}

.cancel {
	color: rgba(255, 255, 255, 0.7);
}

.confirm {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	font-weight: bold;
}

.time-selector {
	display: flex;
	height: 400rpx;
	padding: 20rpx 15rpx;
	overflow: hidden;
}

.selector-column {
	flex: 1;
	display: flex;
	flex-direction: column;
	margin: 0 10rpx;
}

.column-title {
	text-align: center;
	font-size: 24rpx;
	color: #8B4513;
	font-weight: bold;
	margin-bottom: 15rpx;
	background: rgba(139, 69, 19, 0.1);
	padding: 8rpx;
	border-radius: 8rpx;
}

.scroll-list {
	flex: 1;
	height: 320rpx;
	overflow-y: auto;
	overflow-x: hidden;
	scroll-behavior: smooth;
}

.scroll-list::-webkit-scrollbar {
	width: 6rpx;
}

.scroll-list::-webkit-scrollbar-track {
	background: rgba(139, 69, 19, 0.1);
	border-radius: 3rpx;
}

.scroll-list::-webkit-scrollbar-thumb {
	background: #D2B48C;
	border-radius: 3rpx;
}

.scroll-item {
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #8B4513;
	margin: 3rpx 8rpx;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.2s ease;
}

.scroll-item:hover {
	background: rgba(139, 69, 19, 0.1);
	transform: scale(1.02);
}

.scroll-item.active {
	background: #8B4513;
	color: white;
	font-weight: bold;
	transform: scale(1.05);
	box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.3);
}
</style>
