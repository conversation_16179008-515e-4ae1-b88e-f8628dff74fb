<template>
  <view class="homework-display">
    <view class="homework-header">
      <text class="homework-subject">学科：{{ content.subject || '未指定学科' }}</text>
    </view>
    
    <view class="homework-content">
      <text class="content-title">答案</text>
      <text class="content-text" :class="{'selectable-text': true}">{{ content.answer || content.content }}</text>
    </view>
    
    <view class="explanation-section" v-if="hasExplanations">
      <text class="explanation-title">解析</text>
      <view class="explanation-item" v-for="(explanation, index) in content.explanations" :key="index">
        <text class="explanation-text" :class="{'selectable-text': true}">{{ explanation }}</text>
      </view>
    </view>
    
    <view class="references-section" v-if="hasReferences">
      <text class="references-title">参考资料</text>
      <view class="reference-item" v-for="(reference, index) in content.references" :key="index">
        <text class="reference-text" :class="{'selectable-text': true}">{{ reference }}</text>
      </view>
    </view>
    
    <view class="homework-actions">
      <view class="action-btn practice-btn" @tap="handleAction('practice')">
        <text class="action-icon">📝</text>
        <text class="action-text">练习</text>
      </view>
      <view class="action-btn save-btn" @tap="handleAction('save')">
        <text class="action-icon">💾</text>
        <text class="action-text">保存</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HomeworkDisplay',
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  computed: {
    hasExplanations() {
      return this.content.explanations && this.content.explanations.length > 0;
    },
    hasReferences() {
      return this.content.references && this.content.references.length > 0;
    }
  },
  methods: {
    handleAction(action, data) {
      // 将动作向上传递
      this.$emit('action', action, data || this.content);
    }
  }
}
</script>

<style scoped>
.homework-display {
  width: 100%;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.homework-header {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(to right, #e3f2fd, #f5f7fa);
  border-radius: 12rpx 12rpx 0 0;
}

.homework-subject {
  font-size: 32rpx;
  font-weight: bold;
  color: #1565c0;
  display: block;
}

.homework-content,
.explanation-section,
.references-section {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content-title,
.explanation-title,
.references-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #424242;
  margin-bottom: 10rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: block;
}

.content-text,
.explanation-text,
.reference-text {
  font-size: 28rpx;
  color: #424242;
  line-height: 1.7;
  white-space: pre-wrap;
  display: block;
}

.explanation-item,
.reference-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  border-radius: 8rpx;
}

.explanation-item {
  background-color: #e3f2fd;
  border-left: 6rpx solid #2196f3;
}

.reference-item {
  background-color: #fff8e1;
  border-left: 6rpx solid #ffb300;
}

.homework-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
  padding: 10rpx 0;
}

.action-btn {
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex: 1;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.practice-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.save-btn {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.action-text {
  font-size: 26rpx;
  font-weight: 500;
}

.selectable-text {
  user-select: text;
}
</style> 