<template>
  <view :class="['platform-container', platformClass]">
    <slot></slot>
  </view>
</template>

<script>
import { platformAdapter } from '../utils/platform.js';

export default {
  name: 'PlatformStyle',
  data() {
    return {
      platformClass: ''
    };
  },
  created() {
    // 检测平台并添加相应的类名
    const platform = platformAdapter.getPlatformType();
    const screenSize = platformAdapter.getScreenSize();
    const orientation = platformAdapter.getDeviceOrientation();
    
    this.platformClass = `${platform} ${screenSize} ${orientation}`;
    
    // 监听窗口大小变化（仅在H5环境）
    if (platformAdapter.isH5()) {
      window.addEventListener('resize', this.handleResize);
    }
  },
  beforeDestroy() {
    // 移除监听器
    if (platformAdapter.isH5()) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    handleResize() {
      const screenSize = platformAdapter.getScreenSize();
      const orientation = platformAdapter.getDeviceOrientation();
      
      // 更新类名
      this.platformClass = `${this.platformClass.split(' ')[0]} ${screenSize} ${orientation}`;
    }
  }
};
</script>

<style>
.platform-container {
  /* 通用样式 */
  width: 100%;
  box-sizing: border-box;
}

/* 小程序样式 */
.mp-weixin, .mp-alipay {
  /* 小程序特有样式 */
}

/* APP样式 */
.app {
  /* APP特有样式 */
}

/* 移动端H5样式 */
.mobile-h5 {
  /* 移动端H5特有样式 */
}

/* PC网页样式 */
.pc {
  /* PC端特有样式 */
}

/* 屏幕尺寸响应式样式 */
.xs {
  /* 超小屏幕样式 */
}

.sm {
  /* 小屏幕样式 */
}

.md {
  /* 中屏幕样式 */
}

.lg {
  /* 大屏幕样式 */
}

.xl {
  /* 超大屏幕样式 */
}

/* 设备方向样式 */
.portrait {
  /* 竖屏样式 */
}

.landscape {
  /* 横屏样式 */
}
</style> 