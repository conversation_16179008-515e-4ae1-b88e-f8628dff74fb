<template>
  <view class="story-display">
    <view class="story-header">
      <text class="story-title">{{ content.storyTitle || '未命名故事' }}</text>
      <text class="story-author">作者：{{ content.author || '匿名' }}</text>
      <text class="story-genre">类型：{{ content.genre || '未分类' }}</text>
    </view>
    
    <view class="story-content">
      <text class="content-text" :class="{'selectable-text': true}">{{ content.storyContent || content.content }}</text>
    </view>
    
    <view class="story-actions">
      <view class="action-btn edit-btn" @tap="handleAction('edit')">
        <text class="action-icon">✏️</text>
        <text class="action-text">编辑故事</text>
      </view>
      <view class="action-btn share-btn" @tap="handleAction('share')">
        <text class="action-icon">📤</text>
        <text class="action-text">分享</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StoryDisplay',
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  methods: {
    handleAction(action, data) {
      // 将动作向上传递
      this.$emit('action', action, data || this.content);
    }
  }
}
</script>

<style scoped>
.story-display {
  width: 100%;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.story-header {
  width: 100%;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(to right, #f5f7fa, #ffffff);
  padding: 20rpx;
  border-radius: 12rpx 12rpx 0 0;
}

.story-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #343a40;
  display: block;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.05);
}

.story-author, .story-genre {
  font-size: 26rpx;
  color: #6c757d;
  margin: 6rpx 0;
  display: block;
}

.story-content {
  width: 100%;
  line-height: 1.8;
  text-align: justify;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: inset 0 0 6rpx rgba(0,0,0,0.05);
}

.content-text {
  font-size: 30rpx;
  color: #495057;
  white-space: pre-wrap;
  line-height: 1.8;
}

.story-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 15rpx;
  padding: 10rpx 0;
}

.action-btn {
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.edit-btn {
  background-color: #e3f2fd;
  color: #1976d2;
  flex: 1;
}

.share-btn {
  background-color: #f3e5f5;
  color: #7b1fa2;
  flex: 1;
}

.action-text {
  font-size: 26rpx;
  font-weight: 500;
}

.selectable-text {
  user-select: text;
}
</style> 