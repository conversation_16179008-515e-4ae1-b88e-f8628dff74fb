import { request } from '@/utils/request'

/**
 * 运程测试API
 */
export const fortuneTestAPI = {
  /**
   * 综合运程分析
   * @param {Object} data - 测试数据
   * @param {string} data.name - 姓名
   * @param {string} data.gender - 性别 ('male' 或 'female')
   * @param {string} data.birthDate - 出生日期 (YYYY-MM-DD)
   * @param {string} data.birthTime - 出生时间 (HH:MM)
   * @param {Array} data.focusAreas - 关注领域数组
   */
  async comprehensiveAnalyze(data) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/fortune-comprehensive-analysis',
        method: 'POST',
        data: {
          name: data.name,
          gender: data.gender,
          birthDate: data.birthDate,
          birthTime: data.birthTime,
          focusAreas: data.focusAreas,
          analysisType: 'comprehensive'
        }
      });

      return response;
    } catch (error) {
      console.error('综合运程分析失败:', error);
      return this.getDefaultComprehensiveData(data);
    }
  },

  /**
   * 个人运势分析
   * @param {Object} data - 测试数据
   * @param {string} data.name - 姓名
   * @param {string} data.gender - 性别 ('male' 或 'female')
   * @param {string} data.birthDate - 出生日期 (YYYY-MM-DD)
   * @param {string} data.birthTime - 出生时间 (HH:MM)
   * @param {string} data.birthPlace - 出生地点 (可选)
   */
  async personalAnalyze(data) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/fortune-personal-analysis',
        method: 'POST',
        data: {
          name: data.name,
          gender: data.gender,
          birthDate: data.birthDate,
          birthTime: data.birthTime,
          birthPlace: data.birthPlace || '',
          analysisType: 'personal'
        }
      });

      return response;
    } catch (error) {
      console.error('个人运势分析失败:', error);
      return this.getDefaultPersonalData(data);
    }
  },

  /**
   * 支付解锁完整报告
   * @param {Object} paymentData - 支付数据
   * @param {string} paymentData.method - 支付方式 ('wechat', 'alipay', 'coins')
   * @param {string} paymentData.amount - 支付金额
   * @param {string} paymentData.reportId - 报告ID
   */
  async unlockReport(paymentData) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/fortune-unlock-report',
        method: 'POST',
        data: paymentData
      });

      return response;
    } catch (error) {
      console.error('解锁报告失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户金币余额
   */
  async getUserCoins() {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/user/coins',
        method: 'GET'
      });

      return response;
    } catch (error) {
      console.error('获取金币余额失败:', error);
      return { success: true, data: { coins: 0 } };
    }
  },

  /**
   * 获取默认综合运程数据
   * @param {Object} data - 用户数据
   */
  getDefaultComprehensiveData(data) {
    const genderText = data.gender === 'male' ? '先生' : '女士';
    const focusAreaNames = {
      career: '事业运',
      love: '感情运',
      wealth: '财运',
      health: '健康运',
      study: '学业运',
      family: '家庭运'
    };

    const analysisResults = {};
    
    // 为每个关注领域生成分析
    data.focusAreas.forEach(areaId => {
      const areaName = focusAreaNames[areaId];
      switch (areaId) {
        case 'career':
          analysisResults[areaId] = `${data.name}${genderText}，您的事业运势在2025年表现出色，有望在职场上取得重大突破。建议您在春季和秋季重点发力，这两个时期是您事业发展的黄金期。您的领导能力和创新思维将得到充分发挥，有机会获得重要的晋升机会。`;
          break;
        case 'love':
          analysisResults[areaId] = `您的感情运势在2025年呈现积极态势，单身者有望在春夏之交遇到心仪对象，已有伴侣者感情会更加稳定甜蜜。建议您在感情中保持真诚和耐心，用心经营这份珍贵的感情。桃花运在夏季最为旺盛。`;
          break;
        case 'wealth':
          analysisResults[areaId] = `您的财运在2025年稳中有升，正财运较为稳定，偏财运在夏季会有不错的表现。建议您在理财方面保持谨慎，避免高风险投资。可以考虑一些稳健的理财产品，同时要注意开源节流。`;
          break;
        case 'health':
          analysisResults[areaId] = `您的健康运势整体良好，但需要注意在季节交替时加强身体保养。建议您保持规律作息，适当运动，注意饮食均衡。特别要关注心血管和消化系统的健康，定期体检很重要。`;
          break;
        case 'study':
          analysisResults[areaId] = `您的学业运势在2025年表现优异，考试运特别旺盛。无论是学历提升还是技能学习，都会有很好的成果。建议您制定详细的学习计划，保持持续的学习热情，知识的积累将为您带来更多机遇。`;
          break;
        case 'family':
          analysisResults[areaId] = `您的家庭运势和谐美满，家人关系会更加融洽。可能会有喜事临门，如添丁、搬迁新居等。建议您多花时间陪伴家人，维护好家庭关系，家和万事兴。长辈的健康需要特别关注。`;
          break;
        default:
          analysisResults[areaId] = `您在${areaName}方面的2025年运势整体向好，会有不错的发展机遇。`;
      }
    });

    // 综合建议
    analysisResults.advice = `${data.name}${genderText}，2025年是您人生的重要转折年，建议您保持积极乐观的心态，勇于尝试新事物。在关键时刻要相信自己的直觉，同时也要听取他人的建议。您的运势整体呈上升趋势，把握好机遇，必将收获满满。`;

    return {
      success: true,
      data: {
        overallScore: Math.floor(Math.random() * 20) + 75, // 75-95分随机
        focusAreaScores: data.focusAreas.reduce((scores, areaId) => {
          scores[areaId] = Math.floor(Math.random() * 25) + 70; // 70-95分随机
          return scores;
        }, {}),
        analysis: analysisResults,
        luckyElements: {
          color: ['紫色', '金色', '银色'],
          number: [3, 7, 9],
          direction: ['东南', '正南'],
          month: ['3月', '7月', '10月']
        }
      }
    };
  },

  /**
   * 获取默认个人运势数据
   * @param {Object} data - 用户数据
   */
  getDefaultPersonalData(data) {
    const genderText = data.gender === 'male' ? '先生' : '女士';
    const oppositeGender = data.gender === 'male' ? '女性' : '男性';

    return {
      success: true,
      data: {
        overallScore: Math.floor(Math.random() * 20) + 80, // 80-100分随机
        analysis: {
          overallFortune: `${data.name}${genderText}，您的2025年整体运势呈上升趋势，特别是在下半年会有显著的改善。这一年对您来说充满机遇，建议您把握好每一个机会，积极面对挑战。您的人生将迎来重要的转折点，各方面都会有不错的发展。`,
          
          careerFortune: `您的事业运势在2025年表现出色，有望在职场上取得重大突破。建议您在春季和秋季重点发力，这两个时期是您事业发展的黄金期。您的专业能力将得到认可，有机会承担更重要的职责，薪资待遇也会有所提升。`,
          
          wealthFortune: `您的财运在2025年稳中有升，正财运较为稳定，偏财运在夏季会有不错的表现。建议您在理财方面保持谨慎，避免高风险投资。可以考虑一些稳健的投资项目，同时要注意控制不必要的开支，积累财富。`,
          
          loveFortune: `您的感情运势在2025年呈现积极态势，单身者有望在春夏之交遇到心仪的${oppositeGender}，已有伴侣者感情会更加稳定甜蜜。建议您在感情中保持真诚和包容，用心经营这份珍贵的感情。婚姻运势也很不错。`,
          
          healthFortune: `您的健康运势整体良好，但需要注意在季节交替时加强身体保养。建议您保持规律作息，适当运动，注意饮食均衡。特别要关注肠胃和呼吸系统的健康，避免过度劳累，保持良好的心态。`,
          
          studyFortune: `您的学习运势在2025年非常旺盛，无论是专业技能提升还是兴趣爱好的学习，都会有很好的成果。建议您制定详细的学习计划，保持持续的学习热情，知识的积累将为您的事业发展提供强有力的支撑。`,
          
          familyFortune: `您的家庭运势和谐美满，与家人的关系会更加融洽。可能会有家庭聚会或喜事发生，为家庭带来欢乐。建议您多关心家人的健康和需求，维护好家庭关系，家庭的支持将是您前进的动力。`
        },
        luckyElements: {
          color: ['深紫色', '宝石蓝', '珍珠白'],
          number: [6, 8, 9],
          direction: ['正东', '东南'],
          month: ['4月', '8月', '11月'],
          stone: ['紫水晶', '蓝宝石', '珍珠']
        },
        monthlyFortune: this.generateMonthlyFortune(data.name)
      }
    };
  },

  /**
   * 生成月度运势
   * @param {string} name - 姓名
   */
  generateMonthlyFortune(name) {
    const months = [
      '1月', '2月', '3月', '4月', '5月', '6月',
      '7月', '8月', '9月', '10月', '11月', '12月'
    ];
    
    const fortuneTemplates = [
      '运势平稳，适合稳扎稳打',
      '运势上升，把握机遇',
      '运势旺盛，大展宏图',
      '运势波动，谨慎行事',
      '运势回升，积极进取'
    ];

    return months.map(month => ({
      month,
      score: Math.floor(Math.random() * 30) + 70, // 70-100分随机
      description: fortuneTemplates[Math.floor(Math.random() * fortuneTemplates.length)]
    }));
  },

  /**
   * 获取运势统计数据
   */
  async getStatistics() {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/fortune-test/statistics',
        method: 'GET'
      });
      
      return response;
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return {
        success: true,
        data: {
          totalTests: Math.floor(Math.random() * 10000) + 50000,
          todayTests: Math.floor(Math.random() * 500) + 200,
          averageScore: Math.floor(Math.random() * 10) + 80,
          popularAreas: ['事业运', '感情运', '财运']
        }
      };
    }
  }
}

export default fortuneTestAPI
