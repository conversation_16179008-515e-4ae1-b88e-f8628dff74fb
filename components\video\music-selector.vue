.music-options {
  .music-selection {
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #0f3460;
    border-radius: 8rpx;
    padding: 0 20rpx;
    border: 2rpx solid #3a6299;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.4);
    transition: all 0.2s ease;
    
    &:active {
      background-color: #0c2c50;
      transform: translateY(2rpx);
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
    }
    
    text {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
      
      &.arrow-icon {
        font-size: 36rpx;
        color: #48dbfb;
        transform: rotate(90deg);
      }
    }
  }
} 