<template>
	<view v-if="visible" class="date-picker-overlay">
		<view class="date-picker-modal">
			<view class="modal-header">
				<text class="modal-title">选择出生日期</text>
			</view>

			<!-- 公历/农历选择 -->
			<view class="calendar-type-selector">
				<view
					class="type-option"
					:class="{ active: calendarType === 'solar' }"
					@click="switchCalendarType('solar')"
				>
					<text class="type-text">公历</text>
				</view>
				<view
					class="type-option"
					:class="{ active: calendarType === 'lunar' }"
					@click="switchCalendarType('lunar')"
				>
					<text class="type-text">农历</text>
				</view>
			</view>

			<view class="date-form">
				<view class="form-row">
					<view class="form-group">
						<text class="form-label">年份</text>
						<input
							class="form-input"
							type="number"
							v-model="year"
							:placeholder="calendarType === 'solar' ? '2025' : '2025'"
							@input="validateYear"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">月份</text>
						<input
							class="form-input"
							type="number"
							v-model="month"
							:placeholder="calendarType === 'solar' ? '01' : '正月'"
							@input="validateMonth"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">日期</text>
						<input
							class="form-input"
							type="number"
							v-model="day"
							:placeholder="calendarType === 'solar' ? '15' : '初一'"
							@input="validateDay"
						/>
					</view>

					<view v-if="calendarType === 'lunar'" class="form-group leap-group">
						<text class="form-label">闰月</text>
						<view class="leap-checkbox" @click="toggleLeap">
							<text class="checkbox" :class="{ checked: isLeap }">{{ isLeap ? '✓' : '' }}</text>
							<text class="leap-text">闰月</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 转换结果显示 -->
			<view v-if="convertedDate" class="converted-info">
				<text class="converted-label">{{ calendarType === 'solar' ? '对应农历：' : '对应公历：' }}</text>
				<text class="converted-text">{{ convertedDate }}</text>
			</view>

			<view class="modal-footer">
				<button class="btn btn-cancel" @click="cancel">取消</button>
				<button class="btn btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
// import { solarToLunar, lunarToSolar, formatLunarDate } from '@/utils/lunar-calendar.js';

export default {
	name: 'FinalDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const now = new Date();
		return {
			calendarType: 'solar', // 'solar' 公历, 'lunar' 农历
			year: now.getFullYear(),
			month: now.getMonth() + 1,
			day: now.getDate(),
			isLeap: false, // 是否闰月
			convertedDate: '' // 转换后的日期显示
		}
	},
	computed: {
		maxDay() {
			if (this.calendarType === 'solar') {
				if (this.year && this.month) {
					return new Date(this.year, this.month, 0).getDate();
				}
				return 31;
			} else {
				// 农历月份天数通常是29或30天
				return 30;
			}
		}
	},
	watch: {
		year() {
			this.updateConvertedDate();
		},
		month() {
			this.updateConvertedDate();
		},
		day() {
			this.updateConvertedDate();
		},
		isLeap() {
			this.updateConvertedDate();
		},
		calendarType() {
			this.updateConvertedDate();
		}
	},
	methods: {
		switchCalendarType(type) {
			this.calendarType = type;
			if (type === 'solar') {
				this.isLeap = false;
			}
		},
		toggleLeap() {
			this.isLeap = !this.isLeap;
		},
		validateYear() {
			if (this.year < 1900) this.year = 1900;
			if (this.year > 2100) this.year = 2100;
		},
		validateMonth() {
			if (this.month < 1) this.month = 1;
			if (this.month > 12) this.month = 12;
		},
		validateDay() {
			if (this.day < 1) this.day = 1;
			if (this.day > this.maxDay) this.day = this.maxDay;
		},
		updateConvertedDate() {
			if (!this.year || !this.month || !this.day) {
				this.convertedDate = '';
				return;
			}

			// 暂时禁用农历转换，先测试基本功能
			if (this.calendarType === 'solar') {
				this.convertedDate = `公历：${this.year}年${this.month}月${this.day}日`;
			} else {
				this.convertedDate = `农历：${this.year}年${this.month}月${this.day}日${this.isLeap ? '(闰月)' : ''}`;
			}
		},
		cancel() {
			this.$emit('close');
		},
		confirm() {
			let finalDate;
			let displayText;

			if (this.calendarType === 'solar') {
				// 公历日期
				const dateStr = `${this.year}-${String(this.month).padStart(2, '0')}-${String(this.day).padStart(2, '0')}`;
				displayText = dateStr;

				finalDate = {
					type: 'solar',
					solar: {
						year: this.year,
						month: this.month,
						day: this.day,
						dateString: dateStr
					},
					displayText: displayText
				};
			} else {
				// 农历日期
				displayText = `农历${this.year}年${this.month}月${this.day}日${this.isLeap ? '(闰月)' : ''}`;

				finalDate = {
					type: 'lunar',
					lunar: {
						year: this.year,
						month: this.month,
						day: this.day,
						isLeap: this.isLeap
					},
					displayText: displayText
				};
			}

			this.$emit('confirm', finalDate);
		}
	}
}
</script>

<style scoped>
.date-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99999;
}

.date-picker-modal {
	background: #F5E6D3;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	margin: 40rpx;
	width: 90%;
	max-width: 700rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
	text-align: center;
	margin-bottom: 50rpx;
}

.modal-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #8B4513;
}

.date-form {
	margin-bottom: 50rpx;
}

.form-row {
	display: flex;
	gap: 30rpx;
}

.form-group {
	flex: 1;
	text-align: center;
}

.form-label {
	display: block;
	font-size: 30rpx;
	color: #8B4513;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	height: 100rpx;
	border: 3rpx solid #8B4513;
	border-radius: 15rpx;
	text-align: center;
	font-size: 36rpx;
	color: #8B4513;
	background: white;
	font-weight: bold;
}

.modal-footer {
	display: flex;
	gap: 40rpx;
}

.btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	font-size: 36rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
}

.btn-cancel {
	background: white;
	color: #8B4513;
	border: 3rpx solid #8B4513;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}

/* 公历/农历选择器样式 */
.calendar-type-selector {
	display: flex;
	background: white;
	border-radius: 15rpx;
	padding: 8rpx;
	margin-bottom: 40rpx;
	border: 2rpx solid #8B4513;
}

.type-option {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 10rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.type-option.active {
	background: #8B4513;
}

.type-text {
	font-size: 30rpx;
	font-weight: bold;
	color: #8B4513;
}

.type-option.active .type-text {
	color: white;
}

/* 闰月选择样式 */
.leap-group {
	flex: 0.8;
}

.leap-checkbox {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	cursor: pointer;
	margin-top: 20rpx;
}

.checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #8B4513;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: white;
	background: white;
	transition: all 0.3s ease;
}

.checkbox.checked {
	background: #8B4513;
}

.leap-text {
	font-size: 24rpx;
	color: #8B4513;
	font-weight: bold;
}

/* 转换结果显示 */
.converted-info {
	background: rgba(139, 69, 19, 0.1);
	border: 1rpx solid #8B4513;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
	text-align: center;
}

.converted-label {
	font-size: 24rpx;
	color: #8B4513;
	display: block;
	margin-bottom: 8rpx;
}

.converted-text {
	font-size: 28rpx;
	color: #8B4513;
	font-weight: bold;
}
</style>
