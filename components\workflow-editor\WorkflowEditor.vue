<template>
  <view class="workflow-editor">
    <!-- 工作流基本信息 -->
    <view class="editor-section">
      <view class="section-title">基本信息</view>
      <view class="form-group">
        <text class="form-label">工作流名称</text>
        <input 
          class="form-input"
          v-model="workflowConfig.name"
          placeholder="请输入工作流名称"
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">工作流描述</text>
        <textarea 
          class="form-textarea"
          v-model="workflowConfig.description"
          placeholder="请输入工作流描述"
        />
      </view>
    </view>

    <!-- 工具类型选择 -->
    <view class="editor-section">
      <view class="section-title">工具配置</view>
      <view class="form-group">
        <text class="form-label">工具类型</text>
        <view class="tool-selector">
          <picker 
            :value="selectedToolIndex"
            :range="toolOptions"
            range-key="label"
            @change="onToolTypeChange"
          >
            <view class="picker-display">
              <text class="picker-text">
                {{ selectedTool ? selectedTool.label : '请选择工具类型' }}
              </text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 工具描述 -->
      <view v-if="selectedTool" class="tool-description">
        <text class="tool-icon">{{ selectedTool.icon }}</text>
        <text class="tool-desc">{{ selectedTool.description }}</text>
      </view>
    </view>

    <!-- 文本生成场景选择（仅当选择文本生成时显示） -->
    <view v-if="workflowConfig.toolType === 'text_generation'" class="editor-section">
      <view class="section-title">文本生成配置</view>
      <view class="form-group">
        <text class="form-label">生成场景</text>
        <view class="scenario-grid">
          <view 
            v-for="scenario in textScenarios"
            :key="scenario.value"
            class="scenario-card"
            :class="{ active: workflowConfig.scenario === scenario.value }"
            @click="selectScenario(scenario.value)"
          >
            <text class="scenario-icon">{{ scenario.icon }}</text>
            <text class="scenario-name">{{ scenario.label }}</text>
            <text class="scenario-desc">{{ scenario.description }}</text>
          </view>
        </view>
      </view>

      <!-- 文本生成参数配置 -->
      <view class="form-group">
        <text class="form-label">参数配置</text>
        
        <view class="param-item">
          <text class="param-label">创作风格</text>
          <input 
            class="param-input"
            v-model="workflowConfig.style"
            placeholder="如：悬疑、浪漫、科幻等"
          />
        </view>
        
        <view class="param-item">
          <text class="param-label">内容长度</text>
          <picker 
            :value="lengthIndex"
            :range="lengthOptions"
            range-key="label"
            @change="onLengthChange"
          >
            <view class="param-picker">
              {{ lengthOptions[lengthIndex].label }}
            </view>
          </picker>
        </view>
        
        <view class="param-item">
          <text class="param-label">创意度：{{ workflowConfig.creativity }}</text>
          <slider 
            class="param-slider"
            :value="workflowConfig.creativity * 100"
            @change="onCreativityChange"
            min="0"
            max="100"
            step="10"
            activeColor="#007AFF"
          />
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="editor-actions">
      <button class="btn btn-secondary" @click="testWorkflow">测试工作流</button>
      <button class="btn btn-primary" @click="saveWorkflow">保存工作流</button>
    </view>

    <!-- 测试结果显示 -->
    <view v-if="testResult" class="test-result">
      <view class="result-title">测试结果</view>
      <view class="result-content">{{ testResult }}</view>
    </view>
  </view>
</template>

<script>
import { 
  TOOL_TYPES, 
  TEXT_SCENARIOS, 
  getToolTypeOptions, 
  getTextScenarioOptions,
  validateWorkflowConfig,
  buildExecutionParams
} from '@/config/workflow-editor-config.js';

export default {
  name: 'WorkflowEditor',
  data() {
    return {
      workflowConfig: {
        name: '',
        description: '',
        toolType: '',
        scenario: '',
        style: '',
        length: 'medium',
        creativity: 0.7
      },
      
      toolOptions: getToolTypeOptions(),
      textScenarios: getTextScenarioOptions(),
      
      lengthOptions: [
        { value: 'short', label: '短篇' },
        { value: 'medium', label: '中篇' },
        { value: 'long', label: '长篇' }
      ],
      
      selectedToolIndex: -1,
      lengthIndex: 1, // 默认中篇
      
      testResult: '',
      isLoading: false
    };
  },
  
  computed: {
    selectedTool() {
      return this.selectedToolIndex >= 0 ? this.toolOptions[this.selectedToolIndex] : null;
    }
  },
  
  methods: {
    onToolTypeChange(e) {
      const index = e.detail.value;
      this.selectedToolIndex = index;
      this.workflowConfig.toolType = this.toolOptions[index].value;
      
      // 重置场景选择
      this.workflowConfig.scenario = '';
      
      console.log('选择工具类型:', this.workflowConfig.toolType);
    },
    
    selectScenario(scenarioValue) {
      this.workflowConfig.scenario = scenarioValue;
      console.log('选择场景:', scenarioValue);
    },
    
    onLengthChange(e) {
      const index = e.detail.value;
      this.lengthIndex = index;
      this.workflowConfig.length = this.lengthOptions[index].value;
    },
    
    onCreativityChange(e) {
      this.workflowConfig.creativity = e.detail.value / 100;
    },
    
    async testWorkflow() {
      try {
        // 验证配置
        const validation = validateWorkflowConfig(this.workflowConfig);
        if (!validation.isValid) {
          uni.showToast({
            title: validation.errors[0],
            icon: 'none'
          });
          return;
        }
        
        this.isLoading = true;
        
        // 构建测试参数
        const testParams = buildExecutionParams(this.workflowConfig, {
          prompt: '这是一个测试请求，请生成示例内容'
        });
        
        console.log('测试工作流参数:', testParams);
        
        // 调用测试API
        const response = await this.callWorkflowAPI(testParams);
        
        this.testResult = response.content || '测试成功';
        
        uni.showToast({
          title: '测试成功',
          icon: 'success'
        });
        
      } catch (error) {
        console.error('测试工作流失败:', error);
        uni.showToast({
          title: '测试失败: ' + error.message,
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    async saveWorkflow() {
      try {
        // 验证配置
        const validation = validateWorkflowConfig(this.workflowConfig);
        if (!validation.isValid) {
          uni.showToast({
            title: validation.errors[0],
            icon: 'none'
          });
          return;
        }
        
        this.isLoading = true;
        
        // 保存工作流配置
        const response = await uni.request({
          url: '/api/workflow-editor/create-workflow',
          method: 'POST',
          data: {
            workflowName: this.workflowConfig.name,
            description: this.workflowConfig.description,
            toolType: this.workflowConfig.toolType,
            toolConfig: {
              scenario: this.workflowConfig.scenario,
              style: this.workflowConfig.style,
              length: this.workflowConfig.length,
              creativity: this.workflowConfig.creativity
            }
          }
        });
        
        if (response.data.success) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 触发保存成功事件
          this.$emit('workflow-saved', {
            id: response.data.workflow_id,
            config: this.workflowConfig
          });
        } else {
          throw new Error(response.data.error);
        }
        
      } catch (error) {
        console.error('保存工作流失败:', error);
        uni.showToast({
          title: '保存失败: ' + error.message,
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    async callWorkflowAPI(params) {
      // 根据工具类型调用对应的API
      const apiMap = {
        'text_generation': '/api/unified-workflow/text',
        'image_generation': '/api/unified-workflow/image',
        'video_generation': '/api/unified-workflow/video',
        'audio_generation': '/api/unified-workflow/audio'
      };
      
      const apiUrl = apiMap[params.toolType];
      if (!apiUrl) {
        throw new Error(`不支持的工具类型: ${params.toolType}`);
      }
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        data: params
      });
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error);
      }
    }
  }
};
</script>

<style scoped>
.workflow-editor {
  padding: 20px;
  background: #f5f5f5;
}

.editor-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.form-textarea {
  height: 80px;
  resize: vertical;
}

.tool-selector {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
}

.picker-text {
  font-size: 14px;
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 12px;
}

.tool-description {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 10px;
}

.tool-icon {
  font-size: 20px;
}

.tool-desc {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.scenario-card {
  padding: 15px;
  border: 2px solid #eee;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scenario-card.active {
  border-color: #007AFF;
  background: #f0f8ff;
}

.scenario-icon {
  display: block;
  font-size: 24px;
  margin-bottom: 8px;
}

.scenario-name {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.scenario-desc {
  display: block;
  font-size: 11px;
  color: #666;
}

.param-item {
  margin-bottom: 15px;
}

.param-label {
  display: block;
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.param-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 13px;
}

.param-picker {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 13px;
}

.param-slider {
  width: 100%;
  margin-top: 10px;
}

.editor-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007AFF;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn:hover {
  opacity: 0.8;
}

.test-result {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.result-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}
</style>
