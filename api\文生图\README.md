# 文生图功能 API 文档

## 📋 概述

基于文本描述生成图片的AI工作流

## 🚀 快速开始

```javascript
import { 文生图 } from '@/api/文生图/index.js';

const result = await 文生图({
    prompt: '示例值',
    style: '示例值',
    size: '示例值',
    quality: '示例值'
});
```

## 📝 API 接口

### 主要接口

#### `文生图(formData, options)`
执行文生图功能的主接口。

**参数：**
- `prompt` (string): prompt *必需*
- `style` (string): style *必需*
- `size` (string): size *必需*
- `quality` (string): quality *必需*

**返回：**
```javascript
{
    success: true,
    data: {
        // 处理结果
    }
}
```

## ⚙️ 配置说明

### 工作流配置
- 工作流ID: `text_to_image_workflow_001`
- 工作流类型: `text_to_image`
- 基础费用: 20金币

## 🔧 工作流对接

### 结构化参数格式
```javascript
{
    requestId: "req_id",
    workflowId: "text_to_image_workflow_001",
    workflowType: "text_to_image",
    structuredParams: {
        prompt: { type: "text", value: "值", placeholder: "{{prompt}}" },
        style: { type: "text", value: "值", placeholder: "{{style}}" },
        size: { type: "text", value: "值", placeholder: "{{size}}" },
        quality: { type: "text", value: "值", placeholder: "{{quality}}" }
    }
}
```

## 🚨 错误处理

常见错误码：
- `TEXT_TO_IMAGE_001`: 参数格式不正确
- `TEXT_TO_IMAGE_007`: 金币余额不足
- `TEXT_TO_IMAGE_008`: 工作流执行超时

## 📞 技术支持

如有问题，请联系开发团队。