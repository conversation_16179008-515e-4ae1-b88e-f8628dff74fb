# 文本处理功能 API 文档

## 📋 概述

多功能文本处理工作流，支持写作、翻译、总结等

## 🚀 快速开始

```javascript
import { 文本处理 } from '@/api/文本处理/index.js';

const result = await 文本处理({
    textInput: '示例值',
    processType: '示例值',
    outputFormat: '示例值'
});
```

## 📝 API 接口

### 主要接口

#### `文本处理(formData, options)`
执行文本处理功能的主接口。

**参数：**
- `textInput` (string): textInput *必需*
- `processType` (string): processType *必需*
- `outputFormat` (string): outputFormat *必需*

**返回：**
```javascript
{
    success: true,
    data: {
        // 处理结果
    }
}
```

## ⚙️ 配置说明

### 工作流配置
- 工作流ID: `text_processing_workflow_001`
- 工作流类型: `text_processing`
- 基础费用: 8金币

## 🔧 工作流对接

### 结构化参数格式
```javascript
{
    requestId: "req_id",
    workflowId: "text_processing_workflow_001",
    workflowType: "text_processing",
    structuredParams: {
        textInput: { type: "text", value: "值", placeholder: "{{textInput}}" },
        processType: { type: "text", value: "值", placeholder: "{{processType}}" },
        outputFormat: { type: "text", value: "值", placeholder: "{{outputFormat}}" }
    }
}
```

## 🚨 错误处理

常见错误码：
- `TEXT_PROCESSING_001`: 参数格式不正确
- `TEXT_PROCESSING_007`: 金币余额不足
- `TEXT_PROCESSING_008`: 工作流执行超时

## 📞 技术支持

如有问题，请联系开发团队。

## 📚 详细文档

**注意**: AI智能写作助手的完整接口规范请查看：
- [接口文件\文本处理\接口规范.md](../../接口文件/文本处理/接口规范.md) - 完整的API接口规范
- [接口文件\文本处理\后端实现指导.md](../../接口文件/文本处理/后端实现指导.md) - 后端开发实现指导
- [接口文件\文本处理\前端使用示例.md](../../接口文件/文本处理/前端使用示例.md) - 前端调用示例和组件集成