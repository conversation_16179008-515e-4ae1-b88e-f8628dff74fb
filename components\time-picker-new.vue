<template>
	<view class="time-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content" @click.stop>
			<view class="picker-header">
				<text class="header-title">选择出生时间</text>
			</view>

			<!-- 时间选择器组合 -->
			<view class="time-selector-container">
				<!-- 小时选择 -->
				<view class="time-input-group">
					<view class="input-with-dropdown">
						<input 
							class="time-input" 
							type="number" 
							:value="selectedHour" 
							@input="onHourInput"
							placeholder="小时"
							min="0" 
							max="23"
						/>
						<text class="unit-label">时</text>
						<view class="dropdown-arrow" @click="toggleHourDropdown">▼</view>
					</view>
					<scroll-view
						v-if="showHourDropdown"
						class="dropdown-list"
						scroll-y
						@scroll="onHourScroll"
						@click.stop
						@touchmove.stop
					>
						<view
							v-for="hour in hours"
							:key="hour"
							class="dropdown-item"
							:class="{ active: hour === selectedHour }"
							@click.stop="selectHour(hour)"
							@tap.stop="selectHour(hour)"
						>
							{{ hour.toString().padStart(2, '0') }}时
						</view>
					</scroll-view>
				</view>

				<!-- 分钟选择 -->
				<view class="time-input-group">
					<view class="input-with-dropdown">
						<input 
							class="time-input" 
							type="number" 
							:value="selectedMinute" 
							@input="onMinuteInput"
							placeholder="分钟"
							min="0" 
							max="59"
						/>
						<text class="unit-label">分</text>
						<view class="dropdown-arrow" @click="toggleMinuteDropdown">▼</view>
					</view>
					<scroll-view
						v-if="showMinuteDropdown"
						class="dropdown-list"
						scroll-y
						@scroll="onMinuteScroll"
						@click.stop
						@touchmove.stop
					>
						<view
							v-for="minute in minutes"
							:key="minute"
							class="dropdown-item"
							:class="{ active: minute === selectedMinute }"
							@click.stop="selectMinute(minute)"
							@tap.stop="selectMinute(minute)"
						>
							{{ minute.toString().padStart(2, '0') }}分
						</view>
					</scroll-view>
				</view>
			</view>
			
			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const currentHour = new Date().getHours();
		const currentMinute = new Date().getMinutes();

		// 生成小时选项（0-23）
		const hours = [];
		for (let i = 0; i <= 23; i++) {
			hours.push(i);
		}

		// 生成分钟选项（0-59）
		const minutes = [];
		for (let i = 0; i <= 59; i++) {
			minutes.push(i);
		}

		return {
			selectedHour: currentHour,
			selectedMinute: currentMinute,
			hours,
			minutes,
			showHourDropdown: false,
			showMinuteDropdown: false
		}
	},
	methods: {
		// 小时输入
		onHourInput(e) {
			const value = parseInt(e.detail.value);
			if (value >= 0 && value <= 23) {
				this.selectedHour = value;
			}
		},
		// 分钟输入
		onMinuteInput(e) {
			const value = parseInt(e.detail.value);
			if (value >= 0 && value <= 59) {
				this.selectedMinute = value;
			}
		},
		// 切换小时下拉框
		toggleHourDropdown() {
			this.showHourDropdown = !this.showHourDropdown;
			this.showMinuteDropdown = false;
		},
		// 切换分钟下拉框
		toggleMinuteDropdown() {
			this.showMinuteDropdown = !this.showMinuteDropdown;
			this.showHourDropdown = false;
		},
		// 选择小时
		selectHour(hour) {
			this.selectedHour = hour;
			this.showHourDropdown = false;
		},
		// 选择分钟
		selectMinute(minute) {
			this.selectedMinute = minute;
			this.showMinuteDropdown = false;
		},
		// 滚动事件（使用官方滚动）
		onHourScroll() {
			// 使用官方滚动，无需自定义处理
		},
		onMinuteScroll() {
			// 使用官方滚动，无需自定义处理
		},
		// 关闭选择器
		close() {
			this.showHourDropdown = false;
			this.showMinuteDropdown = false;
			this.$emit('close');
		},
		// 确认选择
		confirm() {
			const timeStr = `${this.selectedHour.toString().padStart(2, '0')}:${this.selectedMinute.toString().padStart(2, '0')}`;
			this.$emit('confirm', {
				hour: this.selectedHour,
				minute: this.selectedMinute,
				timeString: timeStr
			});
			this.close();
		}
	}
}
</script>

<style scoped>
.time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	background: #F5E6D3;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 40rpx;
	max-width: 500rpx;
	width: 90%;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.picker-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #8B4513;
}

.time-selector-container {
	display: flex;
	justify-content: space-between;
	gap: 30rpx;
	margin-bottom: 40rpx;
}

.time-input-group {
	flex: 1;
	position: relative;
}

.input-with-dropdown {
	position: relative;
	display: flex;
	align-items: center;
	background: white;
	border: 2rpx solid #8B4513;
	border-radius: 12rpx;
	padding: 0 10rpx;
	height: 80rpx;
}

.time-input {
	flex: 1;
	border: none;
	outline: none;
	font-size: 28rpx;
	color: #8B4513;
	text-align: center;
	background: transparent;
}

.unit-label {
	font-size: 24rpx;
	color: #8B4513;
	margin-left: 5rpx;
}

.dropdown-arrow {
	font-size: 20rpx;
	color: #8B4513;
	margin-left: 10rpx;
	cursor: pointer;
	transition: transform 0.3s ease;
}

.dropdown-list {
	position: absolute;
	top: 85rpx;
	left: 0;
	right: 0;
	background: white;
	border: 2rpx solid #8B4513;
	border-radius: 12rpx;
	max-height: 300rpx;
	z-index: 99999;
	box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

.dropdown-item {
	padding: 20rpx;
	text-align: center;
	font-size: 28rpx;
	color: #8B4513;
	border-bottom: 1rpx solid #E8E8E8;
	cursor: pointer;
	transition: background-color 0.2s ease;
	user-select: none;
	-webkit-user-select: none;
	position: relative;
}

.dropdown-item:last-child {
	border-bottom: none;
}

.dropdown-item:hover {
	background: #F5E6D3;
}

.dropdown-item.active {
	background: #8B4513;
	color: white;
	font-weight: bold;
}

.picker-footer {
	display: flex;
	justify-content: space-between;
	gap: 30rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: #F5E6D3;
	color: #8B4513;
	border: 2rpx solid #8B4513;
}

.btn-cancel:hover {
	background: #E8D5C4;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}

.btn-confirm:hover {
	background: #654321;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
	.time-selector-container {
		gap: 20rpx;
	}
}
</style>
