# Project Architecture Rules (Non-Obvious Only)

## Hidden Architectural Constraints
- **Platform Adapter Coupling**: All components depend on auto-injected `platform-adaptive.js` mixin - cannot be removed without breaking everything
- **Global State Pollution**: Multiple window-level flags prevent duplicate initialization but create hidden dependencies
- **Router Override**: `utils/router.js` globally overwrites uni.navigateTo - affects all navigation throughout app
- **Scroll System**: `simple-scroll-fix.js` auto-executes on import, modifying global scroll behavior for all elements

## Non-Standard Dependency Patterns
- **Chinese API Structure**: Business logic organized by Chinese directory names - not typical for international projects
- **Workflow-Based Architecture**: All new features must follow standardized workflow pattern (see `api/起名功能/index.js`)
- **Payment-First Design**: All creation features architecturally require payment check before execution
- **Emergency Recovery System**: H5 environment includes automatic error detection and page recovery mechanisms

## Critical Architectural Decisions
- **Multi-Platform Single Codebase**: Uses uni-app conditional compilation (`#ifdef`) instead of separate builds
- **Runtime Platform Detection**: Platform-specific behavior determined at runtime, not build time
- **Global Mixin Injection**: `platform-adaptive.js` provides platform state to ALL components automatically
- **CSS Custom Property Runtime**: Platform adapter sets CSS variables at runtime (--status-bar-height, etc.)

## Performance Bottlenecks (By Design)
- **Route Hijacking Detection**: Runs every 3 seconds to detect and restore navigation methods
- **Global Error Monitoring**: Multiple window event listeners for error recovery in H5 environment
- **Scroll State Management**: Maintains scroll state maps for all elements with touch directive
- **Platform Detection Overhead**: Runtime platform checks in every component via global mixin

## Scalability Constraints
- **Chinese Function Names**: Core business logic uses Chinese naming - limits international developer onboarding
- **Tightly Coupled Platform Logic**: Platform-specific code scattered throughout via conditional compilation
- **Global State Dependencies**: Many components implicitly depend on window-level initialization flags
- **Testing Architecture**: E2E tests tightly coupled to specific DOM structure and data expectations

## Integration Patterns
- **Vuex with Chinese Modules**: State management uses Chinese module names matching business domains
- **API Workflow Standardization**: All new features must implement standardized workflow interface
- **Error Handling Centralization**: Uses `统一错误处理` function for consistent user experience
- **Component Adaptation**: Custom adaptive components (`AdaptiveScrollView`) wrap uni-app primitives

## Hidden Coupling Points
- **Touch Directive Dependency**: Components may reference disabled `v-touch-scroll` directive
- **Platform Mixin Assumptions**: Components assume `$platform`, `$isMobile` properties exist
- **Router Enhancement Dependency**: Navigation code assumes enhanced router methods are available
- **CSS Variable Dependency**: Styles assume runtime-set custom properties exist