/**
 * 文本工具配置管理系统
 * 支持后端动态配置和本地默认配置
 */

// 基础配置模板
const baseConfig = {
  id: '',
  title: '',
  description: '',
  showHeader: true,
  showParams: true,
  paramsTitle: '参数设置',
  inputPlaceholder: '请输入内容...',
  maxLength: 2000,
  submitText: '生成内容',
  showSuggestions: false,
  suggestionTitle: '建议词',
  enableMedia: false,
  params: [],
  suggestions: [],
  mediaTypes: []
};

// 通用参数选项
const commonParams = {
  style: {
    key: 'style',
    label: '风格',
    type: 'style',
    presets: [
      { label: '标准', value: 'standard' },
      { label: '专业', value: 'professional' },
      { label: '创意', value: 'creative' },
      { label: '幽默', value: 'humorous' },
      { label: '正式', value: 'formal' },
      { label: '简洁', value: 'concise' },
      { label: '详细', value: 'detailed' },
      { label: '诗意', value: 'poetic' }
    ],
    defaultValue: 'standard'
  },
  
  length: {
    key: 'length',
    label: '长度',
    type: 'select',
    placeholder: '请选择长度',
    options: [
      { label: '短 (150字以内)', value: 'short' },
      { label: '中 (300-500字)', value: 'medium' },
      { label: '长 (800字以上)', value: 'long' }
    ],
    defaultValue: 'medium'
  },
  
  tone: {
    key: 'tone',
    label: '语调',
    type: 'style',
    presets: [
      { label: '友好', value: 'friendly' },
      { label: '严肃', value: 'serious' },
      { label: '轻松', value: 'casual' },
      { label: '激励', value: 'motivational' },
      { label: '温暖', value: 'warm' }
    ],
    defaultValue: 'friendly'
  },
  
  tags: {
    key: 'tags',
    label: '标签',
    type: 'tags',
    options: [
      { label: '商业', value: 'business' },
      { label: '技术', value: 'tech' },
      { label: '教育', value: 'education' },
      { label: '营销', value: 'marketing' },
      { label: '创意', value: 'creative' },
      { label: '科学', value: 'science' },
      { label: '健康', value: 'health' }
    ]
  }
};

// 媒体类型配置
const mediaTypes = {
  image: {
    id: 'image',
    label: '图片',
    icon: '🖼️',
    enabled: true,
    maxCount: 9,
    formats: ['jpg', 'jpeg', 'png', 'gif']
  },
  video: {
    id: 'video',
    label: '视频',
    icon: '🎥',
    enabled: true,
    maxCount: 1,
    formats: ['mp4', 'avi', 'mov']
  },
  document: {
    id: 'document',
    label: '文档',
    icon: '📄',
    enabled: true,
    maxCount: 5,
    formats: ['pdf', 'doc', 'docx', 'txt']
  }
};

// 预定义配置
export const predefinedConfigs = {
  // 通用文本创作
  general: {
    ...baseConfig,
    id: 'general',
    title: 'AI智能写作助手',
    description: '智能AI助手可以帮您创作各类文本内容',
    inputPlaceholder: '请输入您想要生成的内容主题...',
    enableMedia: true,
    params: [
      commonParams.style,
      commonParams.length,
      commonParams.tone,
      commonParams.tags
    ],
    mediaTypes: [mediaTypes.image, mediaTypes.document],
    suggestions: [
      '写一篇产品介绍',
      '创作营销文案',
      '撰写技术文档',
      '编写新闻稿',
      '制作社交媒体内容'
    ]
  },

  // 剧本创作
  script: {
    ...baseConfig,
    id: 'script',
    title: '剧本创作助手',
    description: '专业的剧本创作工具，支持多种剧本类型',
    inputPlaceholder: '请描述剧本的主题、情节或角色设定...',
    enableMedia: true,
    params: [
      {
        key: 'scriptType',
        label: '剧本类型',
        type: 'select',
        placeholder: '请选择剧本类型',
        options: [
          { label: '电影剧本', value: 'movie' },
          { label: '电视剧本', value: 'tv' },
          { label: '舞台剧本', value: 'stage' },
          { label: '短剧剧本', value: 'short' },
          { label: '微电影剧本', value: 'micro' }
        ],
        defaultValue: 'movie'
      },
      {
        key: 'genre',
        label: '题材',
        type: 'style',
        presets: [
          { label: '爱情', value: 'romance' },
          { label: '悬疑', value: 'mystery' },
          { label: '喜剧', value: 'comedy' },
          { label: '动作', value: 'action' },
          { label: '科幻', value: 'scifi' },
          { label: '历史', value: 'historical' },
          { label: '现实', value: 'realistic' }
        ],
        defaultValue: 'realistic'
      },
      commonParams.length,
      commonParams.tone
    ],
    mediaTypes: [mediaTypes.image, mediaTypes.video, mediaTypes.document],
    suggestions: [
      '都市爱情故事',
      '悬疑推理剧情',
      '家庭温情故事',
      '职场励志剧本',
      '青春校园故事'
    ]
  },

  // 歌词创作
  lyrics: {
    ...baseConfig,
    id: 'lyrics',
    title: '歌词创作助手',
    description: '创作各种风格的歌词，支持多种音乐类型',
    inputPlaceholder: '请描述歌曲的主题、情感或故事...',
    enableMedia: true,
    params: [
      {
        key: 'musicStyle',
        label: '音乐风格',
        type: 'select',
        placeholder: '请选择音乐风格',
        options: [
          { label: '流行歌曲', value: 'pop' },
          { label: '民谣', value: 'folk' },
          { label: '摇滚', value: 'rock' },
          { label: '说唱', value: 'rap' },
          { label: '古风', value: 'ancient' },
          { label: '山歌', value: 'mountain' },
          { label: '花灯', value: 'lantern' }
        ],
        defaultValue: 'pop'
      },
      {
        key: 'emotion',
        label: '情感基调',
        type: 'style',
        presets: [
          { label: '快乐', value: 'happy' },
          { label: '忧伤', value: 'sad' },
          { label: '激昂', value: 'passionate' },
          { label: '温柔', value: 'gentle' },
          { label: '怀念', value: 'nostalgic' },
          { label: '励志', value: 'inspiring' }
        ],
        defaultValue: 'happy'
      },
      {
        key: 'structure',
        label: '歌曲结构',
        type: 'select',
        placeholder: '请选择结构',
        options: [
          { label: '完整歌曲', value: 'full' },
          { label: '只要主歌', value: 'verse' },
          { label: '只要副歌', value: 'chorus' },
          { label: '自定义', value: 'custom' }
        ],
        defaultValue: 'full'
      },
      commonParams.tags
    ],
    mediaTypes: [mediaTypes.image, mediaTypes.video],
    suggestions: [
      '爱情主题歌词',
      '励志正能量歌词',
      '思乡怀念歌词',
      '青春回忆歌词',
      '友情主题歌词'
    ]
  },

  // 营销文案
  marketing: {
    ...baseConfig,
    id: 'marketing',
    title: '营销文案助手',
    description: '创作高转化率的营销文案和广告内容',
    inputPlaceholder: '请描述产品特点、目标用户或营销目标...',
    enableMedia: true,
    params: [
      {
        key: 'platform',
        label: '投放平台',
        type: 'style',
        presets: [
          { label: '微信朋友圈', value: 'wechat' },
          { label: '微博', value: 'weibo' },
          { label: '抖音', value: 'douyin' },
          { label: '小红书', value: 'xiaohongshu' },
          { label: '淘宝', value: 'taobao' },
          { label: '官网', value: 'website' }
        ],
        defaultValue: 'wechat'
      },
      {
        key: 'target',
        label: '目标用户',
        type: 'tags',
        options: [
          { label: '年轻人', value: 'young' },
          { label: '中年人', value: 'middle' },
          { label: '女性', value: 'female' },
          { label: '男性', value: 'male' },
          { label: '学生', value: 'student' },
          { label: '白领', value: 'office' },
          { label: '家长', value: 'parent' }
        ]
      },
      commonParams.style,
      commonParams.length
    ],
    mediaTypes: [mediaTypes.image, mediaTypes.video],
    suggestions: [
      '产品促销文案',
      '品牌故事文案',
      '活动宣传文案',
      '用户见证文案',
      '节日营销文案'
    ]
  }
};

// 配置管理类
class TextToolConfigManager {
  constructor() {
    this.configs = { ...predefinedConfigs };
    this.remoteConfigs = {};
  }

  // 从后端获取配置
  async fetchRemoteConfigs() {
    try {
      const response = await uni.request({
        url: '/api/text-tool/configs',
        method: 'GET'
      });
      
      if (response.data && response.data.success) {
        this.remoteConfigs = response.data.data;
        // 合并远程配置
        Object.assign(this.configs, this.remoteConfigs);
      }
    } catch (error) {
      console.warn('获取远程配置失败，使用本地配置:', error);
    }
  }

  // 获取配置
  getConfig(configId) {
    return this.configs[configId] || this.configs.general;
  }

  // 获取所有配置
  getAllConfigs() {
    return this.configs;
  }

  // 动态更新配置
  updateConfig(configId, updates) {
    if (this.configs[configId]) {
      this.configs[configId] = {
        ...this.configs[configId],
        ...updates
      };
    }
  }

  // 根据后端工作流状态动态调整媒体功能
  adjustMediaCapabilities(configId, workflowCapabilities) {
    const config = this.configs[configId];
    if (!config) return;

    // 根据工作流能力调整媒体类型
    config.mediaTypes = config.mediaTypes.map(mediaType => ({
      ...mediaType,
      enabled: workflowCapabilities.includes(mediaType.id)
    }));

    // 如果没有启用的媒体类型，禁用媒体功能
    config.enableMedia = config.mediaTypes.some(type => type.enabled);
  }
}

// 创建全局配置管理器实例
export const configManager = new TextToolConfigManager();

// 初始化配置
export async function initializeConfigs() {
  await configManager.fetchRemoteConfigs();
  return configManager;
}

// 后端API接口
export const textToolAPI = {
  // 获取工具配置
  async getToolConfig(toolId) {
    try {
      const response = await uni.request({
        url: `/api/text-tool/config/${toolId}`,
        method: 'GET'
      });
      return response.data;
    } catch (error) {
      console.error('获取工具配置失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 获取工作流能力
  async getWorkflowCapabilities(workflowId) {
    try {
      const response = await uni.request({
        url: `/api/workflow/capabilities/${workflowId}`,
        method: 'GET'
      });
      return response.data;
    } catch (error) {
      console.error('获取工作流能力失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 提交内容生成请求
  async generateContent(data) {
    try {
      const response = await uni.request({
        url: '/api/text-tool/generate',
        method: 'POST',
        data: {
          toolId: data.config.id,
          text: data.text,
          params: data.params,
          media: data.media,
          timestamp: Date.now()
        }
      });
      return response.data;
    } catch (error) {
      console.error('内容生成失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 上传媒体文件
  async uploadMedia(filePath, mediaType) {
    try {
      const response = await uni.uploadFile({
        url: '/api/media/upload',
        filePath: filePath,
        name: 'file',
        formData: {
          type: mediaType,
          timestamp: Date.now()
        }
      });
      return JSON.parse(response.data);
    } catch (error) {
      console.error('媒体上传失败:', error);
      return { success: false, error: error.message };
    }
  }
};

export default {
  predefinedConfigs,
  configManager,
  initializeConfigs,
  commonParams,
  mediaTypes,
  textToolAPI
};
