<template>
	<view v-if="visible" class="datetime-picker-overlay">
		<view class="datetime-picker-modal">
			<view class="modal-header">
				<text class="modal-title">选择出生日期时间</text>
			</view>

			<!-- 公历/农历选择 -->
			<view class="calendar-type-selector">
				<view
					class="type-option"
					:class="{ active: calendarType === 'solar' }"
					@click="switchCalendarType('solar')"
				>
					<text class="type-text">公历</text>
				</view>
				<view
					class="type-option"
					:class="{ active: calendarType === 'lunar' }"
					@click="switchCalendarType('lunar')"
				>
					<text class="type-text">农历</text>
				</view>
			</view>

			<!-- 日期输入区域 -->
			<view class="date-form">
				<view class="form-section">
					<text class="section-title">出生日期</text>
					<view class="form-row">
						<view class="form-group">
							<text class="form-label">年份</text>
							<input
								class="form-input"
								type="number"
								v-model="year"
								:placeholder="calendarType === 'solar' ? '2025' : '2025'"
								@input="validateYear"
							/>
						</view>

						<view class="form-group">
							<text class="form-label">月份</text>
							<input
								class="form-input"
								type="number"
								v-model="month"
								:placeholder="calendarType === 'solar' ? '01' : '正月'"
								@input="validateMonth"
							/>
						</view>

						<view class="form-group">
							<text class="form-label">日期</text>
							<input
								class="form-input"
								type="number"
								v-model="day"
								:placeholder="calendarType === 'solar' ? '15' : '初一'"
								@input="validateDay"
							/>
						</view>

						<view v-if="calendarType === 'lunar'" class="form-group leap-group">
							<text class="form-label">闰月</text>
							<view class="leap-checkbox" @click="toggleLeap">
								<text class="checkbox" :class="{ checked: isLeap }">{{ isLeap ? '✓' : '' }}</text>
								<text class="leap-text">闰月</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 时间输入区域 -->
				<view class="time-section">
					<text class="section-title">出生时间</text>
					<view class="form-row">
						<view class="form-group">
							<text class="form-label">小时</text>
							<input 
								class="form-input" 
								type="number" 
								v-model="hour" 
								placeholder="14"
								@input="validateHour"
							/>
						</view>
						
						<view class="form-group">
							<text class="form-label">分钟</text>
							<input 
								class="form-input" 
								type="number" 
								v-model="minute" 
								placeholder="30"
								@input="validateMinute"
							/>
						</view>
					</view>
				</view>
			</view>

			<!-- 转换结果显示 -->
			<view v-if="convertedDate" class="converted-info">
				<text class="converted-label">{{ calendarType === 'solar' ? '对应农历：' : '对应公历：' }}</text>
				<text class="converted-text">{{ convertedDate }}</text>
			</view>

			<!-- 预览显示 -->
			<view class="preview-section">
				<text class="preview-label">选择的日期时间：</text>
				<text class="preview-text">{{ previewText }}</text>
			</view>

			<view class="modal-footer">
				<button class="btn btn-cancel" @click="cancel">取消</button>
				<button class="btn btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'FortuneDatetimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const now = new Date();
		return {
			calendarType: 'solar', // 'solar' 公历, 'lunar' 农历
			year: now.getFullYear(),
			month: now.getMonth() + 1,
			day: now.getDate(),
			hour: now.getHours(),
			minute: now.getMinutes(),
			isLeap: false, // 是否闰月
			convertedDate: '' // 转换后的日期显示
		}
	},
	computed: {
		maxDay() {
			if (this.calendarType === 'solar') {
				if (this.year && this.month) {
					return new Date(this.year, this.month, 0).getDate();
				}
				return 31;
			} else {
				// 农历月份天数通常是29或30天
				return 30;
			}
		},
		previewText() {
			if (!this.year || !this.month || !this.day) {
				return '请填写完整的日期时间';
			}
			
			const dateStr = `${this.year}-${String(this.month).padStart(2, '0')}-${String(this.day).padStart(2, '0')}`;
			const timeStr = `${String(this.hour).padStart(2, '0')}:${String(this.minute).padStart(2, '0')}`;
			
			// 计算星期几
			const selectedDate = new Date(this.year, this.month - 1, this.day);
			const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
			const weekdayText = weekdays[selectedDate.getDay()];
			
			return `${dateStr} ${timeStr} ${weekdayText}`;
		}
	},
	watch: {
		year() {
			this.updateConvertedDate();
		},
		month() {
			this.updateConvertedDate();
		},
		day() {
			this.updateConvertedDate();
		},
		isLeap() {
			this.updateConvertedDate();
		},
		calendarType() {
			this.updateConvertedDate();
		}
	},
	methods: {
		switchCalendarType(type) {
			this.calendarType = type;
			if (type === 'solar') {
				this.isLeap = false;
			}
		},
		toggleLeap() {
			this.isLeap = !this.isLeap;
		},
		validateYear() {
			if (this.year < 1900) this.year = 1900;
			if (this.year > 2100) this.year = 2100;
		},
		validateMonth() {
			if (this.month < 1) this.month = 1;
			if (this.month > 12) this.month = 12;
		},
		validateDay() {
			if (this.day < 1) this.day = 1;
			if (this.day > this.maxDay) this.day = this.maxDay;
		},
		validateHour() {
			if (this.hour < 0) this.hour = 0;
			if (this.hour > 23) this.hour = 23;
		},
		validateMinute() {
			if (this.minute < 0) this.minute = 0;
			if (this.minute > 59) this.minute = 59;
		},
		updateConvertedDate() {
			if (!this.year || !this.month || !this.day) {
				this.convertedDate = '';
				return;
			}

			// 暂时禁用农历转换，先测试基本功能
			if (this.calendarType === 'solar') {
				this.convertedDate = `公历：${this.year}年${this.month}月${this.day}日`;
			} else {
				this.convertedDate = `农历：${this.year}年${this.month}月${this.day}日${this.isLeap ? '(闰月)' : ''}`;
			}
		},
		cancel() {
			this.$emit('close');
		},
		confirm() {
			const dateStr = `${this.year}-${String(this.month).padStart(2, '0')}-${String(this.day).padStart(2, '0')}`;
			const timeStr = `${String(this.hour).padStart(2, '0')}:${String(this.minute).padStart(2, '0')}`;
			const dateTimeStr = `${dateStr} ${timeStr}`;

			// 计算星期几
			const selectedDate = new Date(this.year, this.month - 1, this.day);
			const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
			const weekdayText = weekdays[selectedDate.getDay()];

			let finalData;

			if (this.calendarType === 'solar') {
				// 公历日期
				finalData = {
					type: 'solar',
					date: dateStr,
					time: timeStr,
					datetime: dateTimeStr,
					year: this.year,
					month: this.month,
					day: this.day,
					hour: this.hour,
					minute: this.minute,
					weekdayText: weekdayText,
					solar: {
						year: this.year,
						month: this.month,
						day: this.day,
						dateString: dateStr
					},
					displayText: dateStr
				};
			} else {
				// 农历日期
				const displayText = `农历${this.year}年${this.month}月${this.day}日${this.isLeap ? '(闰月)' : ''}`;
				
				finalData = {
					type: 'lunar',
					date: dateStr,
					time: timeStr,
					datetime: dateTimeStr,
					year: this.year,
					month: this.month,
					day: this.day,
					hour: this.hour,
					minute: this.minute,
					weekdayText: weekdayText,
					lunar: {
						year: this.year,
						month: this.month,
						day: this.day,
						isLeap: this.isLeap
					},
					displayText: displayText
				};
			}

			this.$emit('confirm', finalData);
		}
	}
}
</script>

<style scoped>
.datetime-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99999;
}

.datetime-picker-modal {
	background: linear-gradient(135deg, #6A0DAD 0%, #8A2BE2 100%);
	border-radius: 25rpx;
	padding: 50rpx 40rpx;
	margin: 40rpx;
	width: 90%;
	max-width: 750rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
	border: 3rpx solid #DDA0DD;
}

.modal-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.modal-title {
	font-size: 42rpx;
	font-weight: 700;
	color: #FFFFFF;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 公历/农历选择器样式 */
.calendar-type-selector {
	display: flex;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 15rpx;
	padding: 8rpx;
	margin-bottom: 40rpx;
	border: 2rpx solid #DDA0DD;
}

.type-option {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 10rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.type-option.active {
	background: #8A2BE2;
}

.type-text {
	font-size: 30rpx;
	font-weight: 600;
	color: #6A0DAD;
}

.type-option.active .type-text {
	color: white;
}

/* 表单区域 */
.date-form {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
	border: 2rpx solid #DDA0DD;
}

.form-section, .time-section {
	margin-bottom: 40rpx;
}

.time-section {
	margin-bottom: 0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #6A0DAD;
	margin-bottom: 25rpx;
	text-align: center;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.form-row {
	display: flex;
	gap: 20rpx;
}

.form-group {
	flex: 1;
	text-align: center;
}

.form-label {
	display: block;
	font-size: 26rpx;
	color: #6A0DAD;
	font-weight: 600;
	margin-bottom: 15rpx;
}

.form-input {
	width: 100%;
	height: 90rpx;
	border: 3rpx solid #8A2BE2;
	border-radius: 15rpx;
	text-align: center;
	font-size: 32rpx;
	color: #6A0DAD;
	background: white;
	font-weight: 600;
	transition: all 0.3s ease;
}

.form-input:focus {
	border-color: #DDA0DD;
	box-shadow: 0 0 15rpx rgba(138, 43, 226, 0.3);
}

/* 闰月选择样式 */
.leap-group {
	flex: 0.8;
}

.leap-checkbox {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	cursor: pointer;
	margin-top: 15rpx;
}

.checkbox {
	width: 35rpx;
	height: 35rpx;
	border: 2rpx solid #8A2BE2;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	color: white;
	background: white;
	transition: all 0.3s ease;
}

.checkbox.checked {
	background: #8A2BE2;
}

.leap-text {
	font-size: 22rpx;
	color: #6A0DAD;
	font-weight: 600;
}

/* 转换结果显示 */
.converted-info {
	background: rgba(255, 255, 255, 0.9);
	border: 2rpx solid #DDA0DD;
	border-radius: 15rpx;
	padding: 20rpx;
	margin-bottom: 25rpx;
	text-align: center;
}

.converted-label {
	font-size: 24rpx;
	color: #6A0DAD;
	display: block;
	margin-bottom: 8rpx;
}

.converted-text {
	font-size: 28rpx;
	color: #8A2BE2;
	font-weight: 600;
}

/* 预览区域 */
.preview-section {
	background: rgba(255, 255, 255, 0.9);
	border: 2rpx solid #DDA0DD;
	border-radius: 15rpx;
	padding: 25rpx;
	margin-bottom: 30rpx;
	text-align: center;
}

.preview-label {
	font-size: 26rpx;
	color: #6A0DAD;
	display: block;
	margin-bottom: 12rpx;
	font-weight: 600;
}

.preview-text {
	font-size: 30rpx;
	color: #8A2BE2;
	font-weight: 700;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 按钮区域 */
.modal-footer {
	display: flex;
	gap: 30rpx;
}

.btn {
	flex: 1;
	height: 90rpx;
	border-radius: 25rpx;
	font-size: 32rpx;
	font-weight: 700;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: rgba(255, 255, 255, 0.9);
	color: #6A0DAD;
	border: 3rpx solid #8A2BE2;
}

.btn-cancel:hover {
	background: rgba(255, 255, 255, 1);
	transform: translateY(-2rpx);
}

.btn-confirm {
	background: linear-gradient(135deg, #DDA0DD, #9370DB);
	color: white;
	border: 3rpx solid #FFFFFF;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.btn-confirm:hover {
	background: linear-gradient(135deg, #9370DB, #8A2BE2);
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 20rpx rgba(138, 43, 226, 0.4);
}

.btn-confirm:active {
	transform: translateY(0);
}
</style>
