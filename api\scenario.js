/**
 * 场景相关API
 */

import { request } from './request';
import { textScenarioAdapter } from '@/utils/text-scenario';

// 本地模拟数据，当API请求失败时使用
const mockScenarios = {
  scenarios: []
};

// 存储动态加载的场景适配器
let dynamicScenarioAdapters = {};

/**
 * 获取所有可用场景
 * @param {Object} params - 查询参数
 * @returns {Promise} 场景列表
 */
export function getScenarios(params) {
  return request({
    url: '/scenarios',
    method: 'GET',
    data: params
  }).catch(error => {
    console.warn('使用本地模拟场景数据', error);
    return { code: 200, data: mockScenarios.scenarios };
  });
}

/**
 * 获取场景详情
 * @param {String} id - 场景ID
 * @returns {Promise} 场景详情
 */
export function getScenarioDetail(id) {
  return request({
    url: `/scenarios/${id}`,
    method: 'GET'
  }).catch(error => {
    console.warn(`使用本地模拟场景详情数据: ${id}`, error);
    const scenario = mockScenarios.scenarios.find(s => s.id === id);
    return { code: 200, data: scenario || null };
  });
}

/**
 * 获取场景配置
 * @param {String} id - 场景ID
 * @returns {Promise} 场景配置
 */
export function getScenarioConfig(id) {
  return request({
    url: `/scenarios/${id}/config`,
    method: 'GET'
  }).catch(error => {
    console.warn(`使用本地模拟场景配置数据: ${id}`, error);
    return { code: 200, data: { id, prompts: [], welcomeMessage: '欢迎使用AI创作助手' } };
  });
}

/**
 * 获取场景推荐提示词
 * @param {String} id - 场景ID
 * @returns {Promise} 推荐提示词
 */
export function getScenarioPrompts(id) {
  return request({
    url: `/scenarios/${id}/prompts`,
    method: 'GET'
  }).catch(error => {
    console.warn(`使用本地模拟场景提示词数据: ${id}`, error);
    return { code: 200, data: [] };
  });
}

/**
 * 获取场景封面图片
 * @param {String} id - 场景ID
 * @param {String} size - 图片尺寸(small, medium, large)
 * @returns {Promise} 封面图片URL
 */
export function getScenarioCover(id, size = 'medium') {
  return request({
    url: `/scenarios/${id}/cover`,
    method: 'GET',
    data: { size }
  }).catch(error => {
    console.warn(`使用本地模拟场景封面数据: ${id}`, error);
    return { code: 200, data: { url: null } };
  });
}

/**
 * 获取场景工具配置
 * @param {String} id - 场景ID
 * @param {String} toolType - 工具类型(text, image, video, etc.)
 * @returns {Promise} 工具配置
 */
export function getScenarioToolConfig(id, toolType = 'text') {
  return request({
    url: `/scenarios/${id}/tools/${toolType}`,
    method: 'GET'
  }).catch(error => {
    console.warn(`使用本地模拟场景工具配置数据: ${id}/${toolType}`, error);
    return { 
      code: 200, 
      data: { 
        id, 
        toolType,
        config: {
          maxLength: 2000,
          supportedFormats: ['text', 'html']
        }
      } 
    };
  });
}

/**
 * 获取场景适配器配置
 * @param {String} id - 场景ID
 * @param {String} toolType - 工具类型(text, image, video, etc.)
 * @returns {Promise} 适配器配置
 */
export function getScenarioAdapter(id, toolType = 'text') {
  return request({
    url: `/scenarios/${id}/adapters/${toolType}`,
    method: 'GET'
  }).catch(error => {
    console.warn(`使用本地模拟场景适配器数据: ${id}/${toolType}`, error);
    return { 
      code: 200, 
      data: null
    };
  });
}

/**
 * 动态注册场景适配器
 * @param {String} scenarioId - 场景ID
 * @param {Object} adapterConfig - 适配器配置
 * @returns {Boolean} 是否注册成功
 */
export function registerScenarioAdapter(scenarioId, adapterConfig) {
  if (!scenarioId || !adapterConfig) {
    console.error('注册场景适配器失败: 缺少必要参数');
    return false;
  }

  try {
    // 创建适配器对象
    const adapter = {
      prepareParams: new Function('baseParams', adapterConfig.prepareParamsCode || 'return { ...baseParams };'),
      formatResult: new Function('result', adapterConfig.formatResultCode || 'return result;'),
      displayComponent: adapterConfig.displayComponent || 'DefaultTextDisplay'
    };

    // 存储适配器
    dynamicScenarioAdapters[scenarioId] = adapter;
    
    // 如果是文本适配器，则添加到文本场景适配器中
    if (adapterConfig.toolType === 'text') {
      textScenarioAdapter[scenarioId] = adapter;
    }
    
    console.log(`成功注册场景适配器: ${scenarioId}`);
    return true;
  } catch (error) {
    console.error(`注册场景适配器失败: ${scenarioId}`, error);
    return false;
  }
}

/**
 * 获取动态注册的场景适配器
 * @param {String} scenarioId - 场景ID
 * @returns {Object|null} 适配器对象或null
 */
export function getDynamicAdapter(scenarioId) {
  return dynamicScenarioAdapters[scenarioId] || null;
}

/**
 * 批量加载场景适配器
 * @param {Array} scenarioIds - 场景ID数组
 * @param {String} toolType - 工具类型
 * @returns {Promise} 加载结果
 */
export async function loadScenarioAdapters(scenarioIds, toolType = 'text') {
  if (!Array.isArray(scenarioIds) || scenarioIds.length === 0) {
    return { success: false, message: '没有指定要加载的场景' };
  }
  
  const results = {
    success: 0,
    failed: 0,
    adapters: []
  };
  
  for (const id of scenarioIds) {
    try {
      const response = await getScenarioAdapter(id, toolType);
      if (response.code === 200 && response.data) {
        const success = registerScenarioAdapter(id, response.data);
        if (success) {
          results.success++;
          results.adapters.push(id);
        } else {
          results.failed++;
        }
      } else {
        results.failed++;
      }
    } catch (error) {
      console.error(`加载场景适配器失败: ${id}`, error);
      results.failed++;
    }
  }
  
  return results;
}

export default {
  getScenarios,
  getScenarioDetail,
  getScenarioConfig,
  getScenarioPrompts,
  getScenarioCover,
  getScenarioToolConfig,
  getScenarioAdapter,
  registerScenarioAdapter,
  getDynamicAdapter,
  loadScenarioAdapters
}; 