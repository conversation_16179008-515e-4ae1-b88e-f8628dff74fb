/**
 * 其他功能接口
 * 管理算命、姓名生成、客服、帮助等其他功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🔮 算命功能接口
// ================================

/**
 * 获取算命分类
 */
export async function 获取算命分类() {
	return await apiRequest('fortune/categories');
}

/**
 * 提交算命请求
 * @param {Object} fortuneData - 算命数据
 */
export async function 提交算命请求(fortuneData) {
	return await apiRequest('fortune/submit', {
		method: 'POST',
		body: fortuneData
	});
}

/**
 * 获取算命结果
 * @param {string} fortuneId - 算命ID
 */
export async function 获取算命结果(fortuneId) {
	return await apiRequest(`fortune/result?fortuneId=${fortuneId}`);
}

/**
 * 获取算命历史
 * @param {Object} params - 查询参数
 */
export async function 获取算命历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`fortune/history?${queryParams}`);
}

/**
 * 婚姻测试
 * @param {Object} testData - 测试数据
 */
export async function 婚姻测试(testData) {
	return await apiRequest('fortune/marriage-test', {
		method: 'POST',
		body: testData
	});
}

/**
 * 运势测试
 * @param {Object} testData - 测试数据
 */
export async function 运势测试(testData) {
	return await apiRequest('fortune/fortune-test', {
		method: 'POST',
		body: testData
	});
}

// ================================
// 📝 姓名生成接口
// ================================

/**
 * 生成姓名
 * @param {Object} nameData - 姓名生成数据
 */
export async function 生成姓名(nameData) {
	return await apiRequest('name-generator/generate', {
		method: 'POST',
		body: nameData
	});
}

/**
 * 获取姓名生成历史
 * @param {Object} params - 查询参数
 */
export async function 获取姓名生成历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`name-generator/history?${queryParams}`);
}

/**
 * 姓名评分
 * @param {Object} scoreData - 评分数据
 */
export async function 姓名评分(scoreData) {
	return await apiRequest('name-generator/score', {
		method: 'POST',
		body: scoreData
	});
}

// ================================
// 🎤 语音克隆接口
// ================================

/**
 * 上传语音样本
 * @param {File} audioFile - 音频文件
 * @param {Object} metadata - 元数据
 */
export async function 上传语音样本(audioFile, metadata) {
	const formData = new FormData();
	formData.append('audio', audioFile);
	formData.append('metadata', JSON.stringify(metadata));
	
	return await apiRequest('voice-clone/upload-sample', {
		method: 'POST',
		body: formData
	});
}

/**
 * 创建语音克隆任务
 * @param {Object} cloneData - 克隆数据
 */
export async function 创建语音克隆任务(cloneData) {
	return await apiRequest('voice-clone/create-task', {
		method: 'POST',
		body: cloneData
	});
}

/**
 * 获取语音克隆结果
 * @param {string} taskId - 任务ID
 */
export async function 获取语音克隆结果(taskId) {
	return await apiRequest(`voice-clone/result?taskId=${taskId}`);
}

/**
 * 获取语音克隆历史
 * @param {Object} params - 查询参数
 */
export async function 获取语音克隆历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`voice-clone/history?${queryParams}`);
}

// ================================
// 💬 客服系统接口
// ================================

/**
 * 获取客服信息
 */
export async function 获取客服信息() {
	return await apiRequest('customer-service/info');
}

/**
 * 创建客服会话
 * @param {Object} sessionData - 会话数据
 */
export async function 创建客服会话(sessionData) {
	return await apiRequest('customer-service/create-session', {
		method: 'POST',
		body: sessionData
	});
}

/**
 * 发送客服消息
 * @param {Object} messageData - 消息数据
 */
export async function 发送客服消息(messageData) {
	return await apiRequest('customer-service/send-message', {
		method: 'POST',
		body: messageData
	});
}

/**
 * 获取客服消息历史
 * @param {string} sessionId - 会话ID
 * @param {Object} params - 查询参数
 */
export async function 获取客服消息历史(sessionId, params = {}) {
	const queryParams = new URLSearchParams({ sessionId, ...params }).toString();
	return await apiRequest(`customer-service/message-history?${queryParams}`);
}

/**
 * 结束客服会话
 * @param {string} sessionId - 会话ID
 */
export async function 结束客服会话(sessionId) {
	return await apiRequest('customer-service/end-session', {
		method: 'POST',
		body: { sessionId }
	});
}

// ================================
// 📚 帮助中心接口
// ================================

/**
 * 获取帮助分类
 */
export async function 获取帮助分类() {
	return await apiRequest('help/categories');
}

/**
 * 获取帮助文章列表
 * @param {Object} params - 查询参数
 */
export async function 获取帮助文章列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`help/articles?${queryParams}`);
}

/**
 * 获取帮助文章详情
 * @param {string} articleId - 文章ID
 */
export async function 获取帮助文章详情(articleId) {
	return await apiRequest(`help/article-detail?articleId=${articleId}`);
}

/**
 * 搜索帮助文章
 * @param {string} keyword - 搜索关键词
 * @param {Object} params - 查询参数
 */
export async function 搜索帮助文章(keyword, params = {}) {
	const queryParams = new URLSearchParams({ keyword, ...params }).toString();
	return await apiRequest(`help/search?${queryParams}`);
}

/**
 * 获取常见问题
 */
export async function 获取常见问题() {
	return await apiRequest('help/faq');
}

// ================================
// 📖 教程系统接口
// ================================

/**
 * 获取教程列表
 * @param {Object} params - 查询参数
 */
export async function 获取教程列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`tutorial/list?${queryParams}`);
}

/**
 * 获取教程详情
 * @param {string} tutorialId - 教程ID
 */
export async function 获取教程详情(tutorialId) {
	return await apiRequest(`tutorial/detail?tutorialId=${tutorialId}`);
}

/**
 * 记录教程学习进度
 * @param {Object} progressData - 进度数据
 */
export async function 记录教程学习进度(progressData) {
	return await apiRequest('tutorial/record-progress', {
		method: 'POST',
		body: progressData
	});
}

/**
 * 获取用户学习进度
 * @param {string} tutorialId - 教程ID
 */
export async function 获取用户学习进度(tutorialId) {
	return await apiRequest(`tutorial/user-progress?tutorialId=${tutorialId}`);
}

// ================================
// 📄 协议文档接口
// ================================

/**
 * 获取用户协议
 */
export async function 获取用户协议() {
	return await apiRequest('agreement/user-agreement');
}

/**
 * 获取隐私政策
 */
export async function 获取隐私政策() {
	return await apiRequest('agreement/privacy-policy');
}

/**
 * 获取支付协议
 */
export async function 获取支付协议() {
	return await apiRequest('agreement/payment-agreement');
}

/**
 * 获取服务条款
 */
export async function 获取服务条款() {
	return await apiRequest('agreement/terms-of-service');
}

// ================================
// 🔧 系统工具接口
// ================================

/**
 * 获取系统配置
 */
export async function 获取系统配置() {
	return await apiRequest('system/config');
}

/**
 * 获取版本信息
 */
export async function 获取版本信息() {
	return await apiRequest('system/version');
}

/**
 * 检查更新
 */
export async function 检查更新() {
	return await apiRequest('system/check-update');
}

/**
 * 上报错误
 * @param {Object} errorData - 错误数据
 */
export async function 上报错误(errorData) {
	return await apiRequest('system/report-error', {
		method: 'POST',
		body: errorData
	});
}

/**
 * 获取热门工具
 */
export async function 获取热门工具() {
	return await apiRequest('system/hot-tools');
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整算命流程
 * @param {string} type - 算命类型
 * @param {Object} data - 算命数据
 */
export async function 完整算命流程(type, data) {
	try {
		// 1. 提交算命请求
		const submitResult = await 提交算命请求({
			type,
			...data
		});
		
		if (!submitResult.success) {
			throw new Error(submitResult.message || '提交失败');
		}
		
		// 2. 轮询获取结果
		const fortuneId = submitResult.data.fortuneId;
		let attempts = 0;
		const maxAttempts = 30;
		
		while (attempts < maxAttempts) {
			const result = await 获取算命结果(fortuneId);
			
			if (result.success && result.data.status === 'completed') {
				return {
					success: true,
					data: result.data
				};
			}
			
			if (result.data.status === 'failed') {
				throw new Error('算命失败');
			}
			
			// 等待2秒后重试
			await new Promise(resolve => setTimeout(resolve, 2000));
			attempts++;
		}
		
		throw new Error('算命超时');
		
	} catch (error) {
		console.error('完整算命流程失败:', error);
		throw error;
	}
}

/**
 * 完整姓名生成流程
 * @param {Object} nameData - 姓名数据
 */
export async function 完整姓名生成流程(nameData) {
	try {
		// 1. 生成姓名
		const generateResult = await 生成姓名(nameData);
		
		if (!generateResult.success) {
			throw new Error(generateResult.message || '生成失败');
		}
		
		// 2. 如果需要评分，进行评分
		if (nameData.needScore && generateResult.data.names) {
			const scorePromises = generateResult.data.names.map(name => 
				姓名评分({ name: name.fullName })
			);
			
			const scoreResults = await Promise.all(scorePromises);
			
			// 将评分结果合并到姓名数据中
			generateResult.data.names.forEach((name, index) => {
				if (scoreResults[index].success) {
					name.score = scoreResults[index].data.score;
					name.analysis = scoreResults[index].data.analysis;
				}
			});
		}
		
		return generateResult;
		
	} catch (error) {
		console.error('完整姓名生成流程失败:', error);
		throw error;
	}
}

export default {
	获取算命分类,
	提交算命请求,
	获取算命结果,
	获取算命历史,
	婚姻测试,
	运势测试,
	生成姓名,
	获取姓名生成历史,
	姓名评分,
	上传语音样本,
	创建语音克隆任务,
	获取语音克隆结果,
	获取语音克隆历史,
	获取客服信息,
	创建客服会话,
	发送客服消息,
	获取客服消息历史,
	结束客服会话,
	获取帮助分类,
	获取帮助文章列表,
	获取帮助文章详情,
	搜索帮助文章,
	获取常见问题,
	获取教程列表,
	获取教程详情,
	记录教程学习进度,
	获取用户学习进度,
	获取用户协议,
	获取隐私政策,
	获取支付协议,
	获取服务条款,
	获取系统配置,
	获取版本信息,
	检查更新,
	上报错误,
	获取热门工具,
	完整算命流程,
	完整姓名生成流程
};
