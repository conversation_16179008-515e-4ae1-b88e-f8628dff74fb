/**
 * 姓名配对功能统一入口文件
 * 整合姓名配对功能的所有接口和业务逻辑
 * 创建时间：2025-01-11
 */

import { 执行姓名配对工作流, 查询姓名配对状态, 取消姓名配对工作流 } from './工作流执行接口.js';
import { 姓名配对工作流配置, 姓名配对参数验证规则, 姓名配对错误码, 姓名配对状态 } from './工作流配置.js';

// 姓名配对功能统一API
export const 姓名配对功能API = {
    执行姓名配对工作流,
    查询姓名配对状态,
    取消姓名配对工作流,
    
    配置: 姓名配对工作流配置,
    验证规则: 姓名配对参数验证规则,
    错误码: 姓名配对错误码,
    状态码: 姓名配对状态
};

// 快捷方法
export async function 姓名配对(formData, options = {}) {
    return await 执行姓名配对工作流(formData, options);
}

export default 姓名配对功能API;