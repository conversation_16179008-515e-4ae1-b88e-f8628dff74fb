<template>
  <view v-if="visible" class="adaptive-popup-overlay" @click="handleOverlayClick">
    <view class="adaptive-popup-container" :style="popupStyle" @click.stop>
      <!-- 头部 -->
      <view v-if="showHeader" class="popup-header">
        <text class="popup-title">{{ title }}</text>
        <view v-if="showClose" class="close-btn" @click="close">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <!-- 内容区域 -->
      <view class="popup-content" :style="contentStyle">
        <slot></slot>
      </view>
      
      <!-- 底部操作区 -->
      <view v-if="showFooter" class="popup-footer">
        <slot name="footer">
          <button v-if="showCancel" class="popup-btn btn-cancel" @click="cancel">
            {{ cancelText }}
          </button>
          <button v-if="showConfirm" class="popup-btn btn-confirm" @click="confirm">
            {{ confirmText }}
          </button>
        </slot>
      </view>
    </view>
  </view>
</template>

<script>
import { StyleAdapter, ApiAdapter, DebugAdapter } from '@/utils/platform-adapter'

export default {
  name: 'AdaptivePopup',
  
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    
    // 标题
    title: {
      type: String,
      default: ''
    },
    
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: true
    },
    
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: true
    },
    
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: true
    },
    
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    
    // 是否显示确认按钮
    showConfirm: {
      type: Boolean,
      default: true
    },
    
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确定'
    },
    
    // 点击遮罩是否关闭
    closeOnOverlay: {
      type: Boolean,
      default: true
    },
    
    // 自定义宽度
    width: {
      type: String,
      default: ''
    },
    
    // 自定义高度
    height: {
      type: String,
      default: ''
    }
  },
  
  computed: {
    // 弹窗样式 - 使用适配器
    popupStyle() {
      const baseStyle = StyleAdapter.getPopupStyle()
      
      const customStyle = {}
      if (this.width) {
        customStyle.width = this.width
      }
      if (this.height) {
        customStyle.height = this.height
      }
      
      return {
        ...baseStyle,
        ...customStyle
      }
    },
    
    // 内容区域样式
    contentStyle() {
      const style = {}
      
      // 如果有固定高度，内容区域需要滚动
      if (this.height) {
        style.overflowY = 'auto'
        style.flex = '1'
      }
      
      return style
    }
  },
  
  watch: {
    visible(newVal) {
      if (newVal) {
        this.onOpen()
      } else {
        this.onClose()
      }
    }
  },
  
  methods: {
    // 打开弹窗
    open() {
      this.$emit('update:visible', true)
      this.$emit('open')
    },
    
    // 关闭弹窗
    close() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    
    // 确认
    confirm() {
      this.$emit('confirm')
      // 默认关闭弹窗，可以在父组件中阻止
      this.close()
    },
    
    // 取消
    cancel() {
      this.$emit('cancel')
      this.close()
    },
    
    // 点击遮罩
    handleOverlayClick() {
      if (this.closeOnOverlay) {
        this.close()
      }
    },
    
    // 弹窗打开时
    onOpen() {
      // 禁用页面滚动
      // #ifdef H5
      if (typeof document !== 'undefined') {
        document.body.style.overflow = 'hidden'
      }
      // #endif
      
      DebugAdapter.log('弹窗打开')
    },
    
    // 弹窗关闭时
    onClose() {
      // 恢复页面滚动
      // #ifdef H5
      if (typeof document !== 'undefined') {
        document.body.style.overflow = ''
      }
      // #endif
      
      DebugAdapter.log('弹窗关闭')
    }
  }
}
</script>

<style lang="scss" scoped>
.adaptive-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
  box-sizing: border-box;
}

.adaptive-popup-container {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-width: 90vw;
  max-height: 90vh;
  
  /* 平台特定样式 */
  /* #ifdef APP-PLUS */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  /* #endif */
  
  /* #ifdef H5 */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
  /* #endif */
  
  /* #ifndef H5 */
  /* #ifndef APP-PLUS */
  box-shadow: 0 6px 28px rgba(0, 0, 0, 0.2);
  /* #endif */
  /* #endif */
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  
  /* #ifdef H5 */
  &:hover {
    background-color: #f5f5f5;
  }
  /* #endif */
  
  &:active {
    background-color: #e9ecef;
  }
}

.close-icon {
  font-size: 24px;
  color: #666666;
  line-height: 1;
}

.popup-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.popup-footer {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.popup-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.btn-cancel {
    background-color: #f8f9fa;
    color: #666666;
    border: 1px solid #e0e0e0;
    
    /* #ifdef H5 */
    &:hover {
      background-color: #e9ecef;
    }
    /* #endif */
    
    &:active {
      background-color: #dee2e6;
    }
  }
  
  &.btn-confirm {
    background-color: var(--primary-color, #007aff);
    color: #ffffff;
    
    /* #ifdef H5 */
    &:hover {
      background-color: var(--primary-dark, #0056cc);
    }
    /* #endif */
    
    &:active {
      background-color: var(--primary-darker, #004499);
    }
  }
}

/* 响应式适配 */
/* #ifdef H5 */
@media (max-width: 768px) {
  .adaptive-popup-container {
    width: 95vw;
    max-height: 85vh;
  }
  
  .popup-content {
    padding: 16px;
  }
  
  .popup-footer {
    padding: 12px 16px;
  }
}
/* #endif */
</style>
