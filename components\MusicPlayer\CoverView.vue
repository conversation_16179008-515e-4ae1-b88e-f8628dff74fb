<template>
	<view class="cover-view" :style="themeStyles">
		<!-- 封面图片 -->
		<view class="cover-container">
			<image 
				:src="music.cover || '/static/images/default-music-cover.jpg'" 
				mode="aspectFill"
				class="cover-image"
			/>
			
			<!-- 付费标识 -->
			<view v-if="music.isPaid && !music.isPurchased" class="paid-badge">
				<text class="paid-icon">💰</text>
				<text class="paid-text">{{ music.price }}金币</text>
			</view>
			
			<!-- 试听标识 -->
			<view v-if="music.isPaid && !music.isPurchased && music.allowTrial" class="trial-badge">
				<text class="trial-text">可试听30秒</text>
			</view>
		</view>
		
		<!-- 推荐歌曲列表 -->
		<view class="recommend-section">
			<view class="section-title">
				<text class="title-text">推荐歌曲</text>
				<text class="title-subtitle">为你精选</text>
			</view>
			
			<scroll-view class="recommend-list" scroll-y>
				<view 
					v-for="(item, index) in recommendList" 
					:key="item.id"
					class="recommend-item"
					@click="handleRecommendClick(item)"
				>
					<!-- 封面 -->
					<view class="item-cover">
						<image :src="item.cover" mode="aspectFill" class="item-cover-img" />
						<view v-if="item.isPaid && !item.isPurchased" class="item-paid-icon">
							<text>💰</text>
						</view>
					</view>
					
					<!-- 信息 -->
					<view class="item-info">
						<text class="item-title">{{ item.title }}</text>
						<view class="item-meta">
							<text class="item-artist">{{ item.artist }}</text>
							<text class="item-separator">•</text>
							<text class="item-plays">{{ formatPlays(item.plays) }}播放</text>
						</view>
					</view>
					
					<!-- 播放按钮 -->
					<view class="item-play-btn">
						<svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
							<polygon points="5 3 19 12 5 21 5 3"></polygon>
						</svg>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { getTheme } from '@/utils/playerThemes.js';

export default {
	name: 'CoverView',
	props: {
		// 当前音乐
		music: {
			type: Object,
			required: true,
			default: () => ({
				cover: '',
				title: '',
				artist: '',
				isPaid: false,
				isPurchased: false,
				price: 0,
				allowTrial: false
			})
		},
		// 推荐列表
		recommendList: {
			type: Array,
			default: () => []
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	computed: {
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--text-primary': theme.colors.textPrimary,
				'--text-secondary': theme.colors.textSecondary,
				'--text-tertiary': theme.colors.textTertiary,
				'--card-bg': theme.colors.cardBg,
				'--card-bg-hover': theme.colors.cardBgHover,
				'--icon-color': theme.colors.iconColor,
				'--icon-color-active': theme.colors.iconColorActive,
				'--shadow-color': theme.colors.shadowColor
			};
		}
	},
	methods: {
		// 点击推荐歌曲
		handleRecommendClick(item) {
			this.$emit('recommend-click', item);
		},
		
		// 格式化播放量
		formatPlays(plays) {
			if (!plays) return '0';
			if (plays >= 10000) {
				return (plays / 10000).toFixed(1) + 'w';
			}
			if (plays >= 1000) {
				return (plays / 1000).toFixed(1) + 'k';
			}
			return plays.toString();
		}
	}
};
</script>

<style lang="scss" scoped>
.cover-view {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 40rpx;
}

.cover-container {
	position: relative;
	width: 100%;
	aspect-ratio: 1;
	max-width: 600rpx;
	margin: 0 auto 40rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx var(--shadow-color, rgba(0, 0, 0, 0.3));
}

.cover-image {
	width: 100%;
	height: 100%;
}

.paid-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 10rpx 20rpx;
	background: rgba(255, 215, 0, 0.9);
	backdrop-filter: blur(10rpx);
	border-radius: 30rpx;
	box-shadow: 0 4rpx 15rpx rgba(255, 215, 0, 0.4);
}

.paid-icon {
	font-size: 28rpx;
}

.paid-text {
	color: #333;
	font-size: 24rpx;
	font-weight: bold;
}

.trial-badge {
	position: absolute;
	bottom: 20rpx;
	left: 50%;
	transform: translateX(-50%);
	padding: 10rpx 20rpx;
	background: rgba(0, 0, 0, 0.7);
	backdrop-filter: blur(10rpx);
	border-radius: 30rpx;
}

.trial-text {
	color: #FFD700;
	font-size: 22rpx;
}

.recommend-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.section-title {
	display: flex;
	align-items: baseline;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.title-text {
	color: var(--text-primary, #FFFFFF);
	font-size: 32rpx;
	font-weight: bold;
}

.title-subtitle {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	font-size: 24rpx;
}

.recommend-list {
	flex: 1;
	min-height: 0;
}

.recommend-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: var(--card-bg, rgba(255, 255, 255, 0.1));
	backdrop-filter: blur(10rpx);
	border-radius: 15rpx;
	margin-bottom: 15rpx;
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.98);
		background: var(--card-bg-hover, rgba(255, 255, 255, 0.15));
	}
}

.item-cover {
	position: relative;
	width: 100rpx;
	height: 100rpx;
	border-radius: 10rpx;
	overflow: hidden;
	flex-shrink: 0;
}

.item-cover-img {
	width: 100%;
	height: 100%;
}

.item-paid-icon {
	position: absolute;
	top: 5rpx;
	right: 5rpx;
	width: 30rpx;
	height: 30rpx;
	background: rgba(255, 215, 0, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18rpx;
}

.item-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	min-width: 0;
}

.item-title {
	color: var(--text-primary, #FFFFFF);
	font-size: 28rpx;
	font-weight: 500;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.item-meta {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.item-artist,
.item-plays {
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
	font-size: 22rpx;
}

.item-separator {
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	font-size: 22rpx;
}

.item-play-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: var(--icon-color-active, #50E3C2);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	transition: all 0.3s;
}

.play-icon {
	width: 24rpx;
	height: 24rpx;
	color: #FFFFFF;
	margin-left: 3rpx;
}

/* H5端优化 */
/* #ifdef H5 */
.recommend-item {
	cursor: pointer;
	
	&:hover {
		background: var(--card-bg-hover, rgba(255, 255, 255, 0.15));
		
		.item-play-btn {
			transform: scale(1.1);
		}
	}
}
/* #endif */
</style>

