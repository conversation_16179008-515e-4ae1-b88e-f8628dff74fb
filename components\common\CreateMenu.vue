<template>
	<view class="create-menu" :class="{ 'visible': visible }" @click="hideMenu">
		<view class="menu-mask" @click.stop="hideMenu"></view>
		<view class="menu-content" @click.stop>
			<view class="menu-title">创作中心</view>
			<view class="menu-grid">
				<!-- 文本创作 -->
				<view class="menu-item" @click="navigate('/pages/create/text/index')">
					<view class="menu-icon text-icon">
						<text class="icon-text">✎</text>
					</view>
					<text class="menu-label">文本创作</text>
				</view>
				
				<!-- 图像创作 -->
				<view class="menu-item" @click="navigate('/pages/create/image/index')">
					<view class="menu-icon image-icon">
						<text class="icon-text">🖼️</text>
					</view>
					<text class="menu-label">图像创作</text>
				</view>
				
				<!-- 视频创作 -->
				<view class="menu-item" @click="navigate('/pages/create/video/index')">
					<view class="menu-icon video-icon">
						<text class="icon-text">🎬</text>
					</view>
					<text class="menu-label">视频创作</text>
				</view>
				
				<!-- 音乐创作 -->
				<view class="menu-item" @click="navigate('/pages/create/music/index')">
					<view class="menu-icon music-icon">
						<text class="icon-text">🎵</text>
					</view>
					<text class="menu-label">音乐创作</text>
				</view>
				
				<!-- 口型同步 -->
				<view class="menu-item" @click="navigate('/pages/create/lipsync/index')">
					<view class="menu-icon lipsync-icon">
						<text class="icon-text">👄</text>
					</view>
					<text class="menu-label">口型同步</text>
				</view>
			</view>
			<view class="close-btn" @click="hideMenu">关闭</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CreateMenu',
	data() {
		return {
			visible: false
		}
	},
	created() {
		console.log('CreateMenu: 组件已创建');
		// 监听显示创建菜单事件
		uni.$on('show-create-menu', this.showMenu);
		console.log('CreateMenu: 已监听show-create-menu事件');
		
		// 添加全局方法，让外部可以直接调用
		if (typeof getApp === 'function') {
			const app = getApp();
			if (app && app.globalData) {
				app.globalData.showCreateMenu = this.showMenu;
				console.log('CreateMenu: 已在globalData中添加showCreateMenu方法');
			}
		}
	},
	mounted() {
		console.log('CreateMenu: 组件已挂载');
		// 临时测试 - 5秒后自动显示菜单（仅用于调试）
		/* setTimeout(() => {
			console.log('CreateMenu: 自动显示菜单（调试用）');
			this.showMenu();
		}, 5000); */
	},
	beforeDestroy() {
		// 移除事件监听
		console.log('CreateMenu: 组件销毁前，移除事件监听');
		uni.$off('show-create-menu', this.showMenu);
		
		// 移除全局方法
		if (typeof getApp === 'function') {
			const app = getApp();
			if (app && app.globalData && app.globalData.showCreateMenu) {
				app.globalData.showCreateMenu = null;
				console.log('CreateMenu: 已移除globalData中的showCreateMenu方法');
			}
		}
	},
	methods: {
		// 显示菜单
		showMenu() {
			console.log('CreateMenu: 显示菜单方法被调用');
			
			this.visible = true;
			console.log('CreateMenu: visible设置为true');
			
			// 添加测试代码 - 确保DOM更新
			this.$nextTick(() => {
				console.log('CreateMenu: DOM已更新，菜单应该已显示');
			});
			
			// 显示toast通知用户
			if (typeof uni.showToast === 'function') {
				uni.showToast({
					title: '创作菜单已打开',
					icon: 'none',
					duration: 1000
				});
			}
		},
		
		// 强制显示菜单 - 备用方法
		forceShowMenu() {
			console.log('CreateMenu: 强制显示菜单');
			// 直接修改DOM
			this.visible = true;
			// 强制立即更新视图
			this.$forceUpdate();
		},
		
		// 隐藏菜单
		hideMenu() {
			console.log('CreateMenu: 隐藏菜单');
			this.visible = false;
		},
		
		// 导航到创作页面
		navigate(url) {
			this.hideMenu();
			console.log('CreateMenu: 导航到', url);
			
			// 延迟导航，等待菜单动画完成
			setTimeout(() => {
				uni.navigateTo({
					url: url,
					success: () => {
						console.log('CreateMenu: 导航成功');
					},
					fail: (err) => {
						console.error('CreateMenu: 导航失败', err);
					}
				});
			}, 200);
		}
	}
}
</script>

<style scoped>
.create-menu {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s ease;
}

.create-menu.visible {
	opacity: 1;
	visibility: visible;
}

.menu-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(3px);
}

.menu-content {
	width: 600rpx;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	position: relative;
	z-index: 1;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.2);
	transform: translateY(20px);
	transition: transform 0.3s ease;
}

.create-menu.visible .menu-content {
	transform: translateY(0);
}

.menu-title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 40rpx;
	color: #333333;
}

.menu-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.menu-item {
	width: 160rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 40rpx;
}

.menu-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 16rpx;
}

.icon-text {
	font-size: 48rpx;
	color: #FFFFFF;
}

.text-icon {
	background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.image-icon {
	background: linear-gradient(135deg, #FF69B4, #FF8C69);
}

.video-icon {
	background: linear-gradient(135deg, #FF416C, #FF4B2B);
}

.music-icon {
	background: linear-gradient(135deg, #FFD700, #FFA500);
}

.lipsync-icon {
	background: linear-gradient(135deg, #8A2BE2, #6A1B9A);
}

.menu-label {
	font-size: 26rpx;
	color: #333333;
}

.close-btn {
	width: 80%;
	height: 80rpx;
	background-color: #F0F0F0;
	border-radius: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 20rpx auto 0;
	font-size: 28rpx;
	color: #333333;
}
</style> 