/**
 * 起名功能工作流执行接口
 * 基于通用工作流基础类的起名功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 起名工作流配置, 起名参数验证规则, 起名错误码, 起名状态 } from './工作流配置.js';

/**
 * 起名功能工作流执行类
 */
class NameGeneratorWorkflow extends WorkflowBase {
    constructor() {
        super('起名功能', 起名工作流配置);
        this.validationRules = 起名参数验证规则;
        this.errorCodes = 起名错误码;
        this.statusCodes = 起名状态;
    }

    /**
     * 执行起名工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async executeNameGeneration(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateNameGenerationParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildNameGenerationParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`起名进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '起名功能',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('起名工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证起名参数
     * @param {Object} formData - 表单数据
     */
    validateNameGenerationParams(formData) {
        // 验证必需参数
        this.validateStructuredParams(formData, this.validationRules.required);

        // 验证格式
        Object.entries(this.validationRules.formats).forEach(([field, pattern]) => {
            if (formData[field] && !pattern.test(formData[field])) {
                throw new Error(this.errorCodes[`INVALID_${field.toUpperCase()}`]?.message || `${field}格式不正确`);
            }
        });

        // 验证范围
        Object.entries(this.validationRules.ranges).forEach(([field, range]) => {
            if (formData[field] !== undefined) {
                if (range.min !== undefined && formData[field] < range.min) {
                    throw new Error(`${field}不能小于${range.min}`);
                }
                if (range.max !== undefined && formData[field] > range.max) {
                    throw new Error(`${field}不能大于${range.max}`);
                }
                if (range.maxLength !== undefined && formData[field].length > range.maxLength) {
                    throw new Error(`${field}长度不能超过${range.maxLength}个字符`);
                }
            }
        });

        // 验证枚举值
        Object.entries(this.validationRules.enums).forEach(([field, allowedValues]) => {
            if (formData[field] && !allowedValues.includes(formData[field])) {
                throw new Error(`${field}的值必须是: ${allowedValues.join(', ')}中的一个`);
            }
        });

        return true;
    }

    /**
     * 构建起名结构化参数
     * @param {Object} formData - 表单数据
     */
    buildNameGenerationParams(formData) {
        const builder = new StructuredParamsBuilder();

        // 添加基础信息参数
        builder
            .addTextParam('surname', formData.surname)
            .addTextParam('gender', formData.gender)
            .addTextParam('birthDate', formData.birthDate)
            .addTextParam('birthTime', formData.birthTime);

        // 添加起名偏好参数
        if (formData.nameLength) {
            builder.addConfigParam('nameLength', formData.nameLength);
        }
        
        if (formData.nameStyle) {
            builder.addTextParam('nameStyle', formData.nameStyle);
        }

        if (formData.expectedMeaning) {
            builder.addTextParam('expectedMeaning', formData.expectedMeaning);
        }

        if (formData.wuxingPreference) {
            builder.addConfigParam('wuxingPreference', formData.wuxingPreference);
        }

        if (formData.generateCount) {
            builder.addConfigParam('generateCount', formData.generateCount);
        }

        return builder.build();
    }

    /**
     * 批量起名
     * @param {Array} nameRequests - 起名请求列表
     */
    async batchNameGeneration(nameRequests) {
        const results = [];

        for (const request of nameRequests) {
            try {
                const result = await this.executeNameGeneration(request.formData, request.options);
                results.push({
                    ...result,
                    requestId: request.id
                });
            } catch (error) {
                results.push({
                    success: false,
                    error: error.message,
                    requestId: request.id
                });
            }
        }

        return {
            success: true,
            data: {
                results,
                total: nameRequests.length,
                successful: results.filter(r => r.success).length,
                failed: results.filter(r => !r.success).length
            }
        };
    }

    /**
     * 获取起名历史
     * @param {Object} params - 查询参数
     */
    async getNameGenerationHistory(params = {}) {
        try {
            const queryParams = new URLSearchParams({
                userId: uni.getStorageSync('userId') || '',
                module: '起名功能',
                ...params
            }).toString();

            return await this.apiRequest(`workflow/history?${queryParams}`);
        } catch (error) {
            console.error('获取起名历史失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 收藏名字
     * @param {Object} nameData - 名字数据
     */
    async favoriteNameResult(nameData) {
        try {
            return await this.apiRequest('name-generator/favorite', {
                method: 'POST',
                body: {
                    userId: uni.getStorageSync('userId'),
                    nameData,
                    module: '起名功能',
                    timestamp: Date.now()
                }
            });
        } catch (error) {
            console.error('收藏名字失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 分享名字结果
     * @param {Object} shareData - 分享数据
     */
    async shareNameResult(shareData) {
        try {
            return await this.apiRequest('name-generator/share', {
                method: 'POST',
                body: {
                    ...shareData,
                    module: '起名功能',
                    timestamp: Date.now()
                }
            });
        } catch (error) {
            console.error('分享名字结果失败:', error);
            return this.formatError(error);
        }
    }
}

// 创建起名工作流实例
const nameGeneratorWorkflow = new NameGeneratorWorkflow();

// ================================
// 🎯 导出的接口方法
// ================================

/**
 * 执行起名工作流
 * @param {Object} formData - 表单数据
 * @param {Object} options - 执行选项
 */
export async function 执行起名工作流(formData, options = {}) {
    return await nameGeneratorWorkflow.executeNameGeneration(formData, options);
}

/**
 * 批量起名
 * @param {Array} nameRequests - 起名请求列表
 */
export async function 批量起名(nameRequests) {
    return await nameGeneratorWorkflow.batchNameGeneration(nameRequests);
}

/**
 * 获取起名历史
 * @param {Object} params - 查询参数
 */
export async function 获取起名历史(params = {}) {
    return await nameGeneratorWorkflow.getNameGenerationHistory(params);
}

/**
 * 收藏名字
 * @param {Object} nameData - 名字数据
 */
export async function 收藏名字(nameData) {
    return await nameGeneratorWorkflow.favoriteNameResult(nameData);
}

/**
 * 分享名字结果
 * @param {Object} shareData - 分享数据
 */
export async function 分享名字结果(shareData) {
    return await nameGeneratorWorkflow.shareNameResult(shareData);
}

/**
 * 查询起名工作流状态
 * @param {string} requestId - 请求ID
 */
export async function 查询起名状态(requestId) {
    return await nameGeneratorWorkflow.queryWorkflowStatus(requestId);
}

/**
 * 取消起名工作流
 * @param {string} requestId - 请求ID
 */
export async function 取消起名工作流(requestId) {
    return await nameGeneratorWorkflow.cancelWorkflow(requestId);
}

export default {
    执行起名工作流,
    批量起名,
    获取起名历史,
    收藏名字,
    分享名字结果,
    查询起名状态,
    取消起名工作流
};
