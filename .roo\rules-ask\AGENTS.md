# Project Documentation Rules (Non-Obvious Only)

## Counterintuitive Project Structure
- **Entry Point**: `main.js` in root (not `src/main.js`) - uni-app convention differs from typical Vue projects
- **API Organization**: Business logic uses Chinese directory names (`api/起名功能/`, `api/音乐创作/`) - not standard practice
- **Static Assets**: Located in `static/` directory (not `public/` like most Vue projects)
- **Route Definition**: `pages.json` defines all navigation (not Vue Router files)

## Hidden Documentation Context
- **Platform Conditionals**: Code uses `#ifdef H5`, `#ifdef APP-PLUS`, `#ifndef` comments for multi-platform targeting
- **Global Mixins**: `platform-adaptive.js` auto-injected into ALL components - provides undocumented global properties
- **CSS Variables**: Runtime-set custom properties (--status-bar-height, --safe-area-inset-*) not in static CSS
- **Touch Directive**: `v-touch-scroll` appears functional but is intentionally disabled (see line 139)

## Misleading File Names/Purposes
- **`utils/simple-scroll-fix.js`**: Auto-executes on import, modifies global scroll behavior (not just utility functions)
- **`utils/router.js`**: Overwrites uni.navigateTo globally (not just helper functions)
- **`mixins/platform-adaptive.js`**: Provides global state and methods (not just adaptive styling)
- **`config/platform-config.js`**: Contains runtime platform detection (not just static config)

## Business Logic Patterns
- **Payment Integration**: All creation features require `检查创作费用并扣费` before execution
- **Workflow Pattern**: New features follow standardized workflow structure (see `api/起名功能/index.js`)
- **Error Handling**: Uses `统一错误处理` function for consistent user-facing messages
- **Chinese Function Names**: Core business logic uses Chinese naming (`音乐创作完整业务流程`, `获取用户信息`)

## Testing Context
- **E2E Setup**: Tests expect dev server on port 8080 but `npm run dev:h5` starts on 5173
- **Test Data**: E2E tests require exactly 20 test items for scroll validation
- **Critical Selectors**: `.models-scroll`, `.chat-page`, `.text-create-container` are test-critical DOM markers
- **Playwright Config**: webServer command differs from standard dev command

## Architecture Decisions
- **Vue Version**: Uses Vue 2 patterns (`createStore`) with Vue 3 imports - hybrid approach
- **State Management**: Vuex store with Chinese module names and business-specific structure
- **Multi-Platform**: Single codebase targets H5, App, and multiple mini-programs simultaneously
- **Emergency Recovery**: H5 environment includes automatic error recovery and route fixing systems