<template>
  <view class="video-mode-switcher">
    <view class="mode-tabs">
      <view
        v-for="mode in availableModes"
        :key="mode.id"
        class="mode-tab"
        :class="{ active: currentMode === mode.id }"
        @tap="switchMode(mode.id)"
      >
        <image v-if="mode.icon" :src="mode.icon" class="mode-icon" mode="aspectFit" />
        <text class="mode-name">{{ mode.name }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'VideoModeSwitcher',
  props: {
    currentMode: {
      type: String,
      default: 'text'
    },
    availableModes: {
      type: Array,
      default: () => [
        { id: 'text', name: '文生视频', icon: '/static/icons/text-to-video.png' },
        { id: 'image', name: '图生视频', icon: '/static/icons/image-to-video.png' },
        { id: 'text-to-image', name: '文生图片', icon: '/static/icons/text-to-image.png' }
      ]
    }
  },
  data() {
    return {
      recommendedTemplates: [
        {
          id: 1,
          name: '旅行记录',
          type: 'text',
          cover: '/static/templates/travel.jpg',
          prompt: '穿越壮丽的自然景观，展现令人惊叹的山脉、瀑布和海滩，充满旅行的自由与探索精神'
        },
        {
          id: 2,
          name: '产品展示',
          type: 'image',
          cover: '/static/templates/product.jpg',
          images: ['/static/templates/product1.jpg', '/static/templates/product2.jpg']
        },
        {
          id: 3,
          name: '城市夜景',
          type: 'text',
          cover: '/static/templates/city.jpg',
          prompt: '繁华的现代城市夜景，高楼大厦灯火通明，霓虹灯闪烁，车流如光河流动，充满动感'
        },
        {
          id: 4,
          name: '美食制作',
          type: 'text',
          cover: '/static/templates/food.jpg',
          prompt: '美味佳肴的制作过程，展示食材的新鲜与色彩，烹饪的技巧与艺术，成品的诱人外观'
        },
        {
          id: 5,
          name: '人物特写',
          type: 'image',
          cover: '/static/templates/portrait.jpg',
          images: ['/static/templates/portrait1.jpg', '/static/templates/portrait2.jpg']
        }
      ]
    }
  },
  methods: {
    switchMode(mode) {
      if (mode !== this.currentMode) {
        this.$emit('mode-change', mode);
      }
    },
    
    goToTemplates() {
      uni.navigateTo({
        url: '/pages/create/video/templates'
      });
    },
    
    useTemplate(template) {
      // 切换到对应的模式
      this.switchMode(template.type);
      
      // 向父组件传递模板信息
      this.$emit('use-template', template);
      
      // 显示提示
      uni.showToast({
        title: `已选择"${template.name}"模板`,
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.video-mode-switcher {
  margin: 0;
  
  .mode-tabs {
    display: flex;
    overflow: hidden;
    background-color: #16213e;
    border-bottom: 4rpx solid #1e3799;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
    
    .mode-tab {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 15rpx;
      position: relative;
      transition: all 0.3s;
      gap: 10rpx;

      .mode-icon {
        width: 32rpx;
        height: 32rpx;
      }
      
      &.active {
        background: linear-gradient(180deg, rgba(74, 105, 189, 0.2) 0%, rgba(15, 52, 96, 0.8) 100%);
        box-shadow: inset 0 0 15rpx rgba(74, 105, 189, 0.3);
        
        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 15%;
          width: 70%;
          height: 6rpx;
          background: linear-gradient(90deg, rgba(10, 189, 227, 0.8), rgba(74, 105, 189, 1), rgba(10, 189, 227, 0.8));
          border-radius: 3rpx 3rpx 0 0;
          box-shadow: 0 -2rpx 10rpx rgba(74, 105, 189, 0.6);
        }
        
        .mode-name {
          color: #ffffff;
          font-weight: 700;
          font-size: 34rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
          transform: scale(1.05);
          background: linear-gradient(180deg, #ffffff, #48dbfb);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      
      .mode-name {
        font-size: 32rpx;
        color: #e6e6e6;
        transition: all 0.3s;
      }
      
      &:active {
        background-color: rgba(74, 105, 189, 0.1);
      }
    }
  }
}
</style> 