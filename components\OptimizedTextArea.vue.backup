﻿<template>
  <view class="optimized-textarea-container" :class="[platformClass, {
    'has-overflow': hasOverflow,
    'selecting': isLongPress || selectionEnabled || isTextSelectionActive()
  }]">
    <!-- 涓绘枃鏈 -->
    <textarea
      ref="textareaRef"
      class="optimized-textarea"
      :value="modelValue"
      :placeholder="showAnimatedPlaceholder ? '' : placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :auto-height="true"
      :fixed="false"
      :cursor-spacing="cursorSpacing"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @paste="handlePaste"
      @keyboardheightchange="handleKeyboardHeightChange"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @click="handleClick"
    ></textarea>
    
    <!-- 鍙戦€佹寜閽彃妲斤紝鏀惧湪鍙充笅瑙?-->
    <view class="send-button-slot">
      <slot name="sendButton"></slot>
    </view>
    
    <!-- 鍔ㄦ€佹彁绀烘枃瀛楃粍浠?-->
    <view 
      v-if="showAnimatedPlaceholder && !modelValue" 
      class="animated-placeholder"
      :class="{ 'is-focused': isFocused }"
    >
      <text class="placeholder-text">{{ currentPlaceholder }}</text>
    </view>

    <!-- H5骞冲彴鐨勬粴鍔ㄦ寚绀哄櫒 -->
    <view 
      v-if="isH5 && hasOverflow" 
      class="scroll-indicator"
      :class="{'show-indicator': hasOverflow && !isTouchingTextarea}"
    ></view>
    
    <!-- 璋冭瘯鐢細婊氬姩浣嶇疆鎸囩ず鍣紝闅愯棌 -->
    <view
      v-if="false && debugMode"
      class="debug-scroll-indicator"
      :style="{
        top: `${20 + (currentScrollTop / 10)}px`, 
        height: `${Math.min(50, contentLines * 3)}px`
      }"
    ></view>
    
    <!-- 璋冭瘯鐢細鏄剧ず搴曢儴鏍囪锛岄殣钘?-->
    <text
      v-if="false && (isDebug || debugMode)" 
      class="debug-bottom-marker"
    >鈻?/text>
  </view>
</template>

<script>
export default {
  name: 'OptimizedTextArea',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '璇疯緭鍏ュ唴瀹?
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200 // 澧炲ぇ榛樿鏈€澶ч珮搴︿负200px锛屾彁渚涙洿濂界殑瑙嗛噹
    },
    minHeight: {
      type: Number,
      default: 40 // 榛樿鏈€灏忛珮搴?0px
    },
    isDebug: {
      type: Boolean,
      default: false
    },
    isSending: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'confirm', 'paste', 'keyboardheightchange', 'height-change', 'send', 'paste-image'],
  computed: {
    textareaStyle() {
      return {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`,
        overflowY: this.hasOverflow ? 'auto' : 'hidden',
        paddingRight: '60px', /* 涓哄彂閫佹寜閽暀鍑虹┖闂?*/
      };
    }
  },
  data() {
    return {
      isFocused: false,
      platformClass: '',
      currentPlaceholder: '',
      placeholderIndex: 0,
      placeholderTimer: null,
      hasOverflow: false,
      textareaHeight: 0,
      lastContentHeight: 0,
      isComposing: false,
      isTouchingTextarea: false,
      touchStartY: 0,
      touchStartX: 0,
      touchStartTime: 0,
      isScrolling: false,
      initialScrollTop: 0,
      momentum: 0,
      animationFrameId: null,
      isH5: false,
      isApp: false,
      isMiniProgram: false,
      isIOS: false,
      isAndroid: false,
      contentLines: 1,
      scrollAnimation: null,
      observerAttached: false,
      scrollTimers: [],
      mutationObserver: null,
      lastScrollAttemptTime: 0,
      autoHeightActive: true,
      isUpdatingHeight: false,
      userHasScrolled: false,
      scrollAttempts: 0,
      lastUserInteraction: 0,
      debugInterval: null,
      debugMode: this.isDebug,
      isLongPress: false,
      longPressTimer: null,
      lastClickTime: 0,  // 娣诲姞鐢ㄤ簬妫€娴嬪弻鍑荤殑鍙橀噺
      selectionEnabled: false, // 鏍囪鏄惁鍚敤浜嗘枃鏈€夋嫨妯″紡
      selectionLogShown: false, // 娣诲姞鐢ㄤ簬妫€娴嬫枃鏈€夋嫨妯″紡鐨勫彉閲?      resetScrollFlagTimer: null,
      initialTextLoaded: false, // 娣诲姞鏍囪锛岃〃绀哄垵濮嬫枃鏈槸鍚﹀凡鍔犺浇
      lastDeltaY: 0,     // 鐢ㄤ簬璁板綍涓婁竴娆杞寸殑鍙樺寲閲?      lastDeltaTime: 0,  // 鐢ㄤ簬璁＄畻閫熷害鐨勬椂闂村樊
      isHBuilder: false, // 娣诲姞HBuilder鐜妫€娴嬫爣蹇?      _hasSelectedAll: false, // 娣诲姞鏍囪锛岃〃绀烘槸鍚﹀凡缁忔墽琛岃繃鍏ㄩ€夋搷浣?    };
  },
  watch: {
    placeholders: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.currentPlaceholder = newVal[0];
          this.startPlaceholderAnimation();
        } else {
          this.currentPlaceholder = this.placeholder;
        }
      }
    },
    modelValue: {
      immediate: true,
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.checkOverflow();
          
          // 鍙湪鍐呭棣栨鍔犺浇鎴栨槑鏄惧鍔犳椂鑷姩婊氬姩
          const isSubstantialAdd = !oldVal || (newVal && oldVal && newVal.length > oldVal.length + 10);
          // 濡傛灉鐢ㄦ埛宸茬粡鎵嬪姩婊氬姩锛屼笉瑕佽嚜鍔ㄦ粴鍔?          if (isSubstantialAdd && !this.userHasScrolled) {
            if (this.scrollAttempts < 3) {
              this.scrollAttempts++;
              this.scrollToBottom();
              
              setTimeout(() => {
                this.scrollAttempts = 0;
              }, 500);
            }
          }
        });
      }
    },
    maxHeight() {
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    }
  },
  created() {
    this.detectPlatform();
    this.setupAppFeatures();
  },
  mounted() {
    this.$nextTick(() => {
      this.initTextarea();
      
      this.setupMutationObserver();
      
      // 閲嶆柊鐩戝惉婊氬姩浜嬩欢
      const textarea = this.getTextareaElement();
      if (textarea && this.isH5) {
        // 娣诲姞鏄庣‘鐨勬粴鍔ㄤ簨浠剁洃鍚?        textarea.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
        
        // 娣诲姞閿洏浜嬩欢锛屾敮鎸丆trl+A鍏ㄩ€夊拰Delete/Backspace鍒犻櫎
        textarea.addEventListener('keydown', this.handleKeyDown);
        
        // 璁剧疆鏂囨湰閫夋嫨灞炴€?        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
      }
      
      // 鍒濆鍖栧浘鐗囩矘璐村姛鑳?      console.log('鍒濆鍖栧浘鐗囩矘璐村姛鑳?);
      
      try {
        // 鍦╠ocument绾у埆娣诲姞绮樿创浜嬩欢鐩戝惉
        if (this.isH5 && typeof document !== 'undefined') {
          // 娣诲姞鍏ㄥ眬绮樿创浜嬩欢鐩戝惉锛岀‘淇濊兘鎹曡幏鎵€鏈夌矘璐翠簨浠?          document.addEventListener('paste', (e) => {
            console.log('document绾у埆绮樿创浜嬩欢琚Е鍙?);
            this.handlePaste(e);
          });
          
          // 娣诲姞棰濆鐨勭矘璐翠簨浠剁洃鍚湪window绾у埆
          if (typeof window !== 'undefined') {
            window.addEventListener('paste', (e) => {
              console.log('window绾у埆绮樿创浜嬩欢琚Е鍙?);
              this.handlePaste(e);
            });
          }
          
          // 鍒涘缓闅愯棌鐨勬枃浠惰緭鍏ュ厓绱犱綔涓哄閫夋柟妗?          this.createHiddenFileInput();
        }
        
        // 鍦℉Builder鐜涓壒鍒鐞?        if (this.isHBuilder) {
          console.log('鍦℉Builder鐜涓垵濮嬪寲鍥剧墖绮樿创鍔熻兘');
          
          // 娣诲姞鍏ㄥ眬绮樿创浜嬩欢鐩戝惉
          if (typeof window !== 'undefined') {
            window.addEventListener('paste', (e) => {
              console.log('window绾у埆绮樿创浜嬩欢琚Е鍙?);
              this.handlePaste(e);
            });
          }
          
          // 濡傛灉鏈塸lus鐜锛屾坊鍔犵壒娈婂鐞?          if (typeof plus !== 'undefined') {
            // 鍙互鍦ㄨ繖閲屾坊鍔爌lus鐜鐨勭壒娈婂垵濮嬪寲浠ｇ爜
            console.log('妫€娴嬪埌plus鐜锛屽惎鐢ㄧ壒娈婄矘璐村鐞?);
          }
        }
        
        // 鍦ˋPP鐜涓壒鍒鐞?        if (this.isApp) {
          console.log('鍦ˋPP鐜涓垵濮嬪寲鍥剧墖绮樿创鍔熻兘');
          // APP鐜鐨勭壒娈婂鐞嗗凡鍦╡nhanceAppTextSelection鏂规硶涓疄鐜?        }
        
        // 鍦ㄥ皬绋嬪簭鐜涓壒鍒鐞?        if (this.isMiniProgram) {
          console.log('鍦ㄥ皬绋嬪簭鐜涓垵濮嬪寲鍥剧墖绮樿创鍔熻兘');
          // 灏忕▼搴忕幆澧冪殑鐗规畩澶勭悊宸插湪setupMiniProgramImagePaste鏂规硶涓疄鐜?        }
      } catch (err) {
        console.error('鍒濆鍖栧浘鐗囩矘璐村姛鑳藉け璐?', err);
      }
      
      // 鍒濆鍖栨祴璇曟暟鎹?      if (this.isDebug || false) { // 鏀逛负false锛岀鐢ㄦ祴璇曟枃鏈?        this.enableDebugging(false); // 绂佺敤璋冭瘯闈㈡澘鏄剧ず
        
        setTimeout(() => {
          const testText = "杩欐槸涓€涓祴璇曟枃鏈琝n鐢ㄦ潵楠岃瘉婊氬姩鍔熻兘鏄惁姝ｅ父宸ヤ綔\n绗笁琛孿n绗洓琛孿n绗簲琛孿n绗叚琛孿n绗竷琛孿n绗叓琛孿n绗節琛孿n绗崄琛?;
          this.setText(testText);
          this.initialTextLoaded = true;
          console.log('娣诲姞娴嬭瘯鏂囨湰浠ユ祴璇曟粴鍔?);
        }, 1000);
      }
    });
  },
  
  // 鍒濆鍖栧浘鐗囩矘璐村姛鑳?  beforeUnmount() {
    this.clearPlaceholderTimer();
    this.cleanupScrollAnimation();
    this.cleanupResizeObserver();
    this.cleanupMutationObserver();
    this.clearAllTimers();
    
    const textarea = this.getTextareaElement();
    if (textarea) {
      // 绉婚櫎鎵€鏈変簨浠剁洃鍚櫒
      try {
      textarea.removeEventListener('scroll', this.handleScroll);
        textarea.removeEventListener('wheel', this.handleMouseWheel);
      textarea.removeEventListener('mousewheel', this.handleMouseWheel);
      textarea.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
      textarea.removeEventListener('keydown', this.handleKeyDown);
        textarea.removeEventListener('mouseup', this.handleMouseUp);
        textarea.removeEventListener('selectstart', this.handleSelectStart);
        textarea.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('绉婚櫎浜嬩欢鐩戝惉鍣ㄥけ璐?', e);
      }
    }
    
    // 绉婚櫎window绾у埆鐨勪簨浠剁洃鍚櫒
    if (this.isH5 && window) {
      try {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('绉婚櫎window浜嬩欢鐩戝惉鍣ㄥけ璐?', e);
      }
    }
    
    // 绉婚櫎document绾у埆鐨勪簨浠剁洃鍚櫒
    if (this.isH5 && document) {
      try {
        document.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('绉婚櫎document浜嬩欢鐩戝惉鍣ㄥけ璐?', e);
      }
    }
    
    if (this.debugMode) {
      this.disableDebugging();
    }
  },
  methods: {
    detectPlatform() {
      try {
        // 妫€娴嬫槸鍚︿负H5鐜
        this.isH5 = typeof window !== 'undefined' && typeof document !== 'undefined';
        
        // 妫€娴婬Builder鐜
        this.isHBuilder = typeof navigator !== 'undefined' && 
                         (navigator.userAgent.indexOf('HBuilder') > -1 || 
                          navigator.userAgent.indexOf('Html5Plus') > -1 ||
                          (typeof window !== 'undefined' && typeof window.plus !== 'undefined') ||
                          (typeof plus !== 'undefined'));
        
        if (this.isHBuilder) {
          console.log('妫€娴嬪埌HBuilder鐜锛屽惎鐢ㄧ壒娈婄矘璐村鐞嗗拰鏂囨湰閫夋嫨');
        }
        
        // 妫€娴婣pp鐜
        try {
          // HBuilder/uni-app鐜妫€娴?          this.isApp = (typeof uni !== 'undefined' && typeof plus !== 'undefined') || 
                       (typeof window !== 'undefined' && window.plus) ||
                       !!navigator?.userAgent?.match(/Html5Plus/i);
        } catch (e) {
          this.isApp = false;
        }
        
        // 灏忕▼搴忕幆澧冩娴?        try {
          this.isMiniProgram = typeof wx !== 'undefined' && typeof wx.getSystemInfoSync === 'function';
          
          // 棰濆妫€鏌ni-app鐜涓殑灏忕▼搴?          if (!this.isMiniProgram && typeof uni !== 'undefined' && uni.getSystemInfoSync) {
            const sysInfo = uni.getSystemInfoSync();
            this.isMiniProgram = sysInfo.mp && (sysInfo.mp.weixin || sysInfo.mp.alipay || sysInfo.mp.baidu);
          }
          
          // 濡傛灉鏄皬绋嬪簭鐜锛屽惎鐢ㄧ壒娈婂浘鐗囩矘璐村鐞?          if (this.isMiniProgram) {
            console.log('妫€娴嬪埌灏忕▼搴忕幆澧冿紝鍚敤鐗规畩鍥剧墖绮樿创澶勭悊');
            this.setupMiniProgramImagePaste();
          }
        } catch (e) {
          this.isMiniProgram = false;
        }
        
        // 璁惧绫诲瀷妫€娴?        if (this.isH5 && typeof navigator !== 'undefined') {
          this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
          this.isAndroid = /Android/i.test(navigator.userAgent);
        } else if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
          try {
            const sysInfo = uni.getSystemInfoSync();
            this.isIOS = sysInfo.platform === 'ios';
            this.isAndroid = sysInfo.platform === 'android';
          } catch (e) {
            // 榛樿妫€娴?            if (typeof navigator !== 'undefined') {
              this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
              this.isAndroid = /Android/i.test(navigator.userAgent);
            }
          }
        }
        
        // 鏍规嵁涓嶅悓骞冲彴璁剧疆绫诲悕
        if (this.isH5) {
          this.platformClass = 'h5-platform';
        } else if (this.isApp) {
          this.platformClass = 'app-platform';
        } else if (this.isMiniProgram) {
          this.platformClass = 'mp-platform';
        }
        
        if (this.debugMode) {
          console.log('褰撳墠骞冲彴:', this.platformClass, 
                      '璁惧:', this.isIOS ? 'iOS' : this.isAndroid ? 'Android' : '鍏朵粬');
        }
      } catch (e) {
        console.error('骞冲彴妫€娴嬪け璐?', e);
        // 榛樿涓篐5
        this.platformClass = 'h5-platform';
      }
    },
    
    // 璁剧疆灏忕▼搴忕幆澧冧笅鐨勫浘鐗囩矘璐村姛鑳?    setupMiniProgramImagePaste() {
      if (!this.isMiniProgram) return;
      
      try {
        // 寰俊灏忕▼搴?        if (typeof wx !== 'undefined') {
          // 鐩戝惉鍓创鏉垮彉鍖?          wx.onClipboardData && wx.onClipboardData((res) => {
            if (res && res.data) {
              // 妫€鏌ユ槸鍚︽槸鍥剧墖璺緞
              if (res.data.indexOf('wxfile://') === 0 || 
                  res.data.indexOf('http') === 0 && 
                  (res.data.indexOf('.png') > -1 || 
                   res.data.indexOf('.jpg') > -1 || 
                   res.data.indexOf('.jpeg') > -1 || 
                   res.data.indexOf('.gif') > -1)) {
                
                // 鍙兘鏄浘鐗囪矾寰勶紝瑙﹀彂鍥剧墖绮樿创浜嬩欢
                this.$emit('paste-image', {
                  path: res.data,
                  type: 'image/png' // 榛樿绫诲瀷
                });
              } else {
                // 鏅€氭枃鏈紝鎻掑叆鍒板厜鏍囦綅缃?                this.insertTextAtCursor(res.data);
              }
            }
          });
        }
        
        // uni-app鐜
        if (typeof uni !== 'undefined') {
          // 娣诲姞鑷畾涔夌殑鍥剧墖绮樿创鎸夐挳锛堜粎鍦ㄥ皬绋嬪簭鐜锛?          this.$nextTick(() => {
            // 鍙互鍦ㄨ繖閲屾坊鍔犱竴涓€夋嫨鍥剧墖鐨勬寜閽紝浣嗕负浜嗕繚鎸佺晫闈㈢畝娲侊紝鎴戜滑涓嶆坊鍔犻澶朥I
            // 鑰屾槸渚濊禆鐖剁粍浠舵彁渚涚殑鍥剧墖涓婁紶鍔熻兘
          });
        }
      } catch (e) {
        console.error('璁剧疆灏忕▼搴忓浘鐗囩矘璐村姛鑳藉け璐?', e);
      }
    },

    clearAllTimers() {
      for (const timer of this.scrollTimers) {
        clearTimeout(timer);
      }
      this.scrollTimers = [];
    },

    initTextarea() {
      this.textareaHeight = this.minHeight;
      
      // 纭繚瀹瑰櫒DOM宸茬粡鍔犺浇
      this.$nextTick(() => {
        this.updateHeightAndCheck();
        this.initEvents();
        
        if (this.debugMode) {
          this.enableDebugging();
        }
      });
    },
    
    initEvents() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 娓呯悊鍙兘鐨勬棫鐩戝惉鍣?      try {
        textarea.removeEventListener('scroll', this.handleScroll);
        textarea.removeEventListener('wheel', this.handleMouseWheel);
        textarea.removeEventListener('mousewheel', this.handleMouseWheel);
        textarea.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
        textarea.removeEventListener('keydown', this.handleKeyDown);
        textarea.removeEventListener('mouseup', this.handleMouseUp);
        textarea.removeEventListener('selectstart', this.handleSelectStart);
        textarea.removeEventListener('paste', this.handlePaste);
      } catch(e) {}
      
      // 娣诲姞鏂扮殑鐩戝惉鍣?      textarea.addEventListener('scroll', this.handleScroll);
      
      // 鐗瑰埆閽堝H5鐜娣诲姞榧犳爣婊氳疆浜嬩欢
      if (this.isH5) {
        textarea.addEventListener('wheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
        
        // 娣诲姞閿洏浜嬩欢锛屾敮鎸丆trl+A鍏ㄩ€夊拰鍏朵粬蹇嵎閿?        textarea.addEventListener('keydown', this.handleKeyDown);
        
        // 娣诲姞閫夋嫨浜嬩欢鐩戝惉
        textarea.addEventListener('selectstart', this.handleSelectStart);
        textarea.addEventListener('mouseup', this.handleMouseUp);
        
        // 璁剧疆鍩烘湰鐨勬枃鏈€夋嫨灞炴€?        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
        
        // 纭繚iOS鐨勮Е鎽镐簨浠跺搷搴旀洿鐏垫晱
        if (this.isIOS) {
          textarea.style.webkitOverflowScrolling = 'touch';
        }
        
        // 绂佺敤鑷姩婊氬姩鍒板簳閮ㄧ殑琛屼负锛岃鐢ㄦ埛鎺у埗婊氬姩
        if (typeof this.userHasScrolled === 'undefined') {
          this.userHasScrolled = false;
        }
      }
      
      // 涓篐Builder鐜娣诲姞鐗规畩鏀寔
      if (this.isHBuilder) {
        console.log('HBuilder鐜鐗规畩澶勭悊');
        
        // 澧炲己閫夋嫨鑳藉姏
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
        
        // 纭繚鑳芥帴鏀剁矘璐翠簨浠?        textarea.setAttribute('contenteditable', 'true');
        
        // 娣诲姞绮樿创浜嬩欢鐩戝惉
        window.addEventListener('paste', this.handlePaste);
        textarea.addEventListener('paste', this.handlePaste);
        
        // 娣诲姞閿洏浜嬩欢鏀寔
        textarea.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keydown', this.handleKeyDown);
        
        // 灏濊瘯鏀寔妗嗛€?        textarea.addEventListener('selectstart', this.handleSelectStart);
        textarea.addEventListener('mouseup', this.handleMouseUp);
        
        // 娣诲姞鐗规畩鏍峰紡
        textarea.classList.add('hbuilder-textarea');
      }
      
      // 搴旂敤婊氬姩浼樺寲
      this.enhanceScrolling();
    },
    
    enableDebugging(showPanel = true) {
      console.log('TextArea璋冭瘯妯″紡宸插惎鐢?);
      
      if (this.isH5 && showPanel) {
        const debugPanel = document.createElement('div');
        debugPanel.style.position = 'fixed';
        debugPanel.style.bottom = '10px';
        debugPanel.style.right = '10px';
        debugPanel.style.backgroundColor = 'rgba(0,0,0,0.7)';
        debugPanel.style.color = '#fff';
        debugPanel.style.padding = '5px 10px';
        debugPanel.style.borderRadius = '5px';
        debugPanel.style.fontSize = '12px';
        debugPanel.style.zIndex = '9999';
        debugPanel.style.maxWidth = '200px';
        debugPanel.id = 'textarea-debug-panel';
        
        const updateDebugInfo = () => {
          const textarea = this.getTextareaElement();
          if (!textarea) return;
          
          debugPanel.innerHTML = `
            <div>骞冲彴: ${this.platformClass}</div>
            <div>婊氬姩浣嶇疆: ${textarea.scrollTop || 0}px</div>
            <div>鍐呭楂樺害: ${textarea.scrollHeight || 0}px</div>
            <div>鍙楂樺害: ${textarea.clientHeight || 0}px</div>
            <div>瀹瑰櫒楂樺害: ${this.textareaHeight}px</div>
            <div>鏈€澶ч珮搴? ${this.maxHeight}px</div>
            <div>婧㈠嚭: ${this.hasOverflow ? '鏄? : '鍚?}</div>
            <div>琛屾暟: ${this.contentLines}</div>
          `;
        };
        
        document.body.appendChild(debugPanel);
        this.debugInterval = setInterval(updateDebugInfo, 500);
        updateDebugInfo();
      }
    },
    
    disableDebugging() {
      if (this.isH5) {
        clearInterval(this.debugInterval);
        const panel = document.getElementById('textarea-debug-panel');
        if (panel) {
          document.body.removeChild(panel);
        }
      }
    },

    getTextareaElement() {
      if (!this.$refs.textareaRef) return null;
      
      if (this.isH5) {
        if (this.$refs.textareaRef.$el) {
          return this.$refs.textareaRef.$el;
        }
        return this.$refs.textareaRef;
      } 
      
      return this.$refs.textareaRef;
    },
    
    updateHeightAndCheck() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      this.isUpdatingHeight = true;
      
      let contentHeight = this.minHeight;
      
      if (this.isH5) {
        try {
          const originalHeight = textarea.style.height;
          const originalMaxHeight = textarea.style.maxHeight;
          
          textarea.style.height = 'auto';
          textarea.style.maxHeight = 'none';
          
          contentHeight = Math.max(this.minHeight, textarea.scrollHeight);
          
          textarea.style.height = originalHeight;
          textarea.style.maxHeight = originalMaxHeight;
          
          if (this.debugMode) {
            console.log('H5 - 鍐呭楂樺害璁＄畻:', contentHeight, 'scrollHeight:', textarea.scrollHeight);
          }
        } catch (e) {
          console.error('璁＄畻楂樺害澶辫触:', e);
        }
      } else {
        const lineCount = this.countLines(this.modelValue);
        const lineHeight = 20;
        contentHeight = Math.max(this.minHeight, lineCount * lineHeight);
        
        if (this.debugMode) {
          console.log('闈濰5 - 琛屾暟:', lineCount, '浼扮畻楂樺害:', contentHeight);
        }
      }
      
      this.lastContentHeight = contentHeight;
      
      const newHeight = Math.min(Math.max(contentHeight, this.minHeight), this.maxHeight);
      
      if (this.textareaHeight !== newHeight) {
        this.textareaHeight = newHeight;
        this.$emit('height-change', newHeight);
        
        if (this.debugMode) {
          console.log('楂樺害鏇存柊涓?', newHeight);
        }
      }
      
      const hasOverflow = contentHeight > this.maxHeight;
      if (hasOverflow !== this.hasOverflow) {
        this.hasOverflow = hasOverflow;
        if (this.debugMode) {
          console.log('婧㈠嚭鐘舵€?', hasOverflow ? '婧㈠嚭' : '鏈孩鍑?);
        }
      }
      
      this.isUpdatingHeight = false;
      
      this.$forceUpdate();
    },
    
    countLines(text) {
      if (!text) return 1;
      
      const lines = (text.match(/\n/g) || []).length + 1;
      this.contentLines = lines;
      return lines;
    },
    
    cleanupMutationObserver() {
      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
        this.mutationObserver = null;
      }
    },
    
    cleanupResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },

    startPlaceholderAnimation() {
      this.clearPlaceholderTimer();
      
      if (!this.placeholders || this.placeholders.length <= 1) return;
      
      this.placeholderTimer = setInterval(() => {
        this.placeholderIndex = (this.placeholderIndex + 1) % this.placeholders.length;
        this.currentPlaceholder = this.placeholders[this.placeholderIndex];
      }, 3000);
    },

    clearPlaceholderTimer() {
      if (this.placeholderTimer) {
        clearInterval(this.placeholderTimer);
        this.placeholderTimer = null;
      }
    },
    
    cleanupScrollAnimation() {
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
        this.scrollAnimation = null;
      }
    },

    handleClick(e) {
      // 妫€娴嬫槸鍚︽槸鍙屽嚮琛屼负
      const now = Date.now();
      if (now - this.lastClickTime < 300) {
        // 鍙屽嚮锛屽惎鐢ㄦ枃鏈€夋嫨妯″紡
        this.selectionEnabled = true;
        
        // 纭繚鏂囨湰鍙€夋嫨
        const textarea = this.getTextareaElement();
        if (textarea) {
          textarea.style.userSelect = 'text';
          textarea.style.webkitUserSelect = 'text';
          textarea.style.MozUserSelect = 'text';
          textarea.style.msUserSelect = 'text';
          
          // 鏇村骞冲彴鐨勫睘鎬?          textarea.style.webkitTapHighlightColor = 'rgba(77, 157, 255, 0.3)';
          
          // 灏濊瘯鎵嬪姩璋冩暣鏍峰紡锛屽寮洪€夋嫨瑙嗚鏁堟灉
          if (this.isHBuilder) {
            textarea.classList.add('selecting-text');
          }
        }
        
        // 鍦ˋPP鐜涓嬶紝灏濊瘯浣跨敤鍏ㄩ€?        if (this.isApp) {
          this.$nextTick(() => {
            try {
              // 灏濊瘯鍘熺敓鍏ㄩ€?              if (textarea && typeof textarea.setSelectionRange === 'function') {
                textarea.setSelectionRange(0, textarea.value.length);
              } else if (uni && uni.createSelectorQuery) {
                // 灏濊瘯閫氳繃uni API鎿嶄綔
                uni.createSelectorQuery()
                  .in(this)
                  .select('.optimized-textarea')
                  .context((res) => {
                    if (res && res.context) {
                      res.context.setSelectionRange(0, this.modelValue.length);
                    }
                  }).exec();
              }
            } catch(e) {
              console.error('鍏ㄩ€夋枃鏈け璐?', e);
            }
          });
        }
        
        // 鎸姩鍙嶉
        try {
          if (navigator && navigator.vibrate) {
            navigator.vibrate(50);
          }
        } catch(e) {}
        
        if (this.debugMode) console.log('妫€娴嬪埌鍙屽嚮锛屽惎鐢ㄦ枃鏈€夋嫨妯″紡');
      } else {
        // 鍗曞嚮锛岃褰曟椂闂?        this.lastClickTime = now;
      }
    },

    handleInput(e) {
      const value = e.detail?.value || e.target?.value || '';
      this.$emit('update:modelValue', value);
      
      // 浣跨敤handleContent鏂规硶澶勭悊鍐呭鍙樺寲
      this.$nextTick(() => {
        this.handleContent();
      });
    },

    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
      
      // 鍙湁鍦ㄦ湭妫€娴嬪埌鐢ㄦ埛婊氬姩鏃舵墠婊氬姩鍒板簳閮?      if (!this.userHasScrolled) {
        this.$nextTick(() => {
          this.enhanceScrolling();
          // 浣跨敤鍗曟婊氬姩锛屼笉瑕佸己鍒?          this.scrollToBottom();
        });
      }
    },

    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    handleConfirm(e) {
      // 鍏堣Е鍙戝師鏈夌殑confirm浜嬩欢
      this.$emit('confirm', e);
      
      // 濡傛灉璁剧疆浜嗚嚜鍔ㄥ彂閫侊紝鍒欑洿鎺ュ彂閫佹秷鎭?      if (this.isSending) {
        this.sendMessage();
      } else {
        // 鍚﹀垯鍙粴鍔ㄥ埌搴曢儴
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    handlePaste(e) {
      console.log('绮樿创浜嬩欢琚Е鍙?, e ? '浜嬩欢瀵硅薄瀛樺湪' : '浜嬩欢瀵硅薄涓嶅瓨鍦?);
      
      try {
        // 灏濊瘯浠庡壀璐存澘浜嬩欢鑾峰彇鏁版嵁
        if (e && e.clipboardData) {
          console.log('鍓创鏉挎暟鎹瓨鍦?, 
                     'items:', e.clipboardData.items ? e.clipboardData.items.length : '鏃爄tems', 
                     'files:', e.clipboardData.files ? e.clipboardData.files.length : '鏃爁iles');
          
          // 浼樺厛妫€鏌iles
          if (e.clipboardData.files && e.clipboardData.files.length > 0) {
            const file = e.clipboardData.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('浠巆lipboardData.files鑾峰彇鍒板浘鐗?', file.type);
              e.preventDefault();
              
              // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                source: 'clipboard-files'
              });
              
              return false;
            }
          }
          
          // 妫€鏌tems
          if (e.clipboardData.items) {
            // 閬嶅巻鎵€鏈夌矘璐撮」锛屾煡鎵惧浘鐗?            for (let i = 0; i < e.clipboardData.items.length; i++) {
              const item = e.clipboardData.items[i];
              console.log('妫€鏌ュ壀璐存澘item:', item.kind, item.type);
              
              if (item.kind === 'file' && item.type.indexOf('image') !== -1) {
                try {
                  const blob = item.getAsFile();
                  if (blob) {
                    console.log('浠巆lipboardData.items鑾峰彇鍒板浘鐗?', item.type);
                    e.preventDefault();
                    
                    // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                    this.$emit('paste-image', {
                      blob: blob,
                      type: item.type,
                      source: 'clipboard-items'
                    });
                    
                    return false;
                  }
                } catch (itemErr) {
                  console.error('鑾峰彇鍓创鏉块」鏂囦欢澶辫触:', itemErr);
                }
              }
            }
            
            // 濡傛灉娌℃湁鍥剧墖锛屽皾璇曡幏鍙栨枃鏈?            let pastedText = '';
            try {
              pastedText = e.clipboardData.getData('text/plain');
              console.log('浠庡壀璐存澘鑾峰彇鍒版枃鏈?', pastedText ? '鎴愬姛' : '澶辫触');
            } catch (textErr) {
              console.error('鑾峰彇鍓创鏉挎枃鏈け璐?', textErr);
            }
            
            if (pastedText) {
              // 妫€鏌ユ枃鏈槸鍚﹀彲鑳芥槸鍥剧墖URL
              if (this.isImageUrl(pastedText)) {
                console.log('妫€娴嬪埌鍙兘鏄浘鐗嘦RL:', pastedText);
                e.preventDefault();
                
                // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                this.$emit('paste-image', {
                  path: pastedText,
                  type: 'image/url',
                  source: 'clipboard-text-url'
                });
                
                return false;
              }
              
              // 鏅€氭枃鏈紝闃绘榛樿琛屼负锛屾墜鍔ㄥ鐞嗙矘璐?              e.preventDefault();
              
              // 鑾峰彇褰撳墠鍊煎拰鍏夋爣浣嶇疆
      const textarea = this.getTextareaElement();
              let currentValue = this.modelValue || '';
              let selectionStart = 0;
              let selectionEnd = 0;
              
              try {
                if (textarea && textarea.selectionStart !== undefined) {
                  selectionStart = textarea.selectionStart;
                  selectionEnd = textarea.selectionEnd;
                }
              } catch (err) {
                console.error('鑾峰彇閫夋嫨鑼冨洿澶辫触:', err);
                // 濡傛灉鑾峰彇澶辫触锛岄粯璁よ拷鍔犲埌鏈熬
                selectionStart = currentValue.length;
                selectionEnd = currentValue.length;
              }
              
              // 鎻掑叆鏂囨湰
              const newValue = currentValue.substring(0, selectionStart) + 
                              pastedText + 
                              currentValue.substring(selectionEnd);
              
              // 鏇存柊鍊?              this.$emit('update:modelValue', newValue);
              
              // 鏇存柊鍏夋爣浣嶇疆
        this.$nextTick(() => {
                try {
                  if (textarea && typeof textarea.setSelectionRange === 'function') {
                    const newPosition = selectionStart + pastedText.length;
          textarea.focus();
          textarea.setSelectionRange(newPosition, newPosition);
                  }
                } catch (err) {
                  console.error('璁剧疆鍏夋爣浣嶇疆澶辫触:', err);
                }
          
                // 鏇存柊楂樺害鍜屾粴鍔?          this.updateHeightAndCheck();
                this.scrollToBottom();
              });
              
              return false;
            }
          }
        }
        
        // H5鐜涓嬶紝濡傛灉鏃犳硶閫氳繃鍓创鏉胯幏鍙栧浘鐗囷紝灏濊瘯浣跨敤鍏朵粬鏂规硶
        if (this.isH5 && (!e || !e.clipboardData || (!e.clipboardData.files?.length && !e.clipboardData.items?.length))) {
          console.log('H5鐜涓嬫棤娉曢€氳繃鍓创鏉夸簨浠惰幏鍙栧浘鐗囷紝灏濊瘯鍏朵粬鏂规硶');
          
          // 灏濊瘯浣跨敤navigator.clipboard API
          this.tryPasteImageWithNavigatorClipboard();
          
          // 涓嶅啀鏄剧ず鎻愮ず妗嗭紝鐩存帴杩斿洖
          return true;
        }
        
        // 鐗规畩澶勭悊HBuilder鐜
        if (this.isHBuilder) {
          console.log('HBuilder鐜鐗规畩澶勭悊绮樿创');
          
          // 鍦℉Builder鐜涓紝涓嶈灏濊瘯浣跨敤navigator.clipboard API
          // 鍥犱负瀹冧細瀵艰嚧"Read permission denied"閿欒
          
          // 涔熶笉瑕佸皾璇曚娇鐢╠ocument.execCommand('paste')
          // 鍥犱负瀹冧細瀵艰嚧"callback is not a function"閿欒
          
          // 鐩存帴璁╃郴缁熷鐞嗙矘璐翠簨浠讹紝涓嶉樆姝㈤粯璁よ涓?          return true;
        }
      } catch (err) {
        console.error('澶勭悊绮樿创浜嬩欢澶辫触:', err);
        
        // 濡傛灉鍦ㄥ鐞嗚繃绋嬩腑鍑洪敊锛屽皾璇曚娇鐢ㄦ枃浠堕€夋嫨鍣ㄤ綔涓哄閫夋柟妗?        if (this.isH5) {
          console.log('绮樿创澶勭悊鍑洪敊锛屽皾璇曚娇鐢ㄦ枃浠堕€夋嫨鍣?);
          setTimeout(() => {
            this.showFileSelector();
          }, 300);
        }
      }
      
      // 濡傛灉涓婅堪鏂规硶澶辫触锛屼笉闃绘榛樿琛屼负锛岃绯荤粺澶勭悊绮樿创
      return true;
    },
    
    // 妫€鏌ユ枃鏈槸鍚︽槸鍥剧墖URL
    isImageUrl(text) {
      if (!text) return false;
      
      // 绠€鍗曟鏌ユ槸鍚︽槸鍥剧墖URL
      const trimmed = text.trim().toLowerCase();
      return (trimmed.startsWith('http') || trimmed.startsWith('https') || 
              trimmed.startsWith('data:image') || trimmed.startsWith('file:') || 
              trimmed.startsWith('wxfile:')) && 
             (trimmed.endsWith('.png') || trimmed.endsWith('.jpg') || 
              trimmed.endsWith('.jpeg') || trimmed.endsWith('.gif') || 
              trimmed.endsWith('.webp') || trimmed.endsWith('.bmp') ||
              trimmed.indexOf('data:image/') !== -1);
    },
    
    // 灏濊瘯鍦℉Builder鐜涓嬬矘璐?    tryHBuilderPaste() {
      try {
        // 涓嶅皾璇曚娇鐢╬lus.pasteboard API锛屽洜涓哄畠鍙兘涓嶅彲鐢ㄦ垨瀵艰嚧閿欒
        console.log('HBuilder鐜涓嬬畝鍖栫矘璐村鐞?);
        
        // 鐩存帴灏濊瘯鑾峰彇鍓创鏉挎枃鏈?        if (typeof uni !== 'undefined' && uni.getClipboardData) {
          uni.getClipboardData({
            success: (res) => {
              if (res && res.data) {
                console.log('鎴愬姛閫氳繃uni.getClipboardData鑾峰彇鍐呭');
                
                // 妫€鏌ユ槸鍚︽槸鍥剧墖URL
                if (this.isImageUrl(res.data)) {
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/url',
                    source: 'hbuilder-clipboard'
                  });
                } else {
                  // 鏅€氭枃鏈紝鎻掑叆鍒板綋鍓嶄綅缃?                  this.insertTextAtCursor(res.data);
                }
              } else {
                console.log('鍓创鏉垮唴瀹逛负绌?);
              }
            },
            fail: (err) => {
              console.error('uni.getClipboardData澶辫触:', err);
              
              // 缁欑敤鎴锋彁绀?              if (typeof uni !== 'undefined' && uni.showToast) {
                uni.showToast({
                  title: '鏃犳硶璁块棶鍓创鏉匡紝璇锋墜鍔ㄧ矘璐?,
                  icon: 'none',
                  duration: 2000
                });
              }
            }
          });
        } else {
          console.log('uni.getClipboardData涓嶅彲鐢?);
        }
      } catch (err) {
        console.error('HBuilder绮樿创澶勭悊澶辫触:', err);
      }
    },

    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e);
      
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    
    handleTouchStart(e) {
      const touch = e.touches[0];
      this.touchStartY = touch.clientY;
      this.touchStartX = touch.clientX;
      this.touchStartTime = Date.now();
      this.isTouchingTextarea = true;
      
      // 濡傛灉闀挎寜鍚姩鏂囨湰閫夋嫨
      if (this.longPressTimer) clearTimeout(this.longPressTimer);
      this.longPressTimer = setTimeout(() => {
        this.isLongPress = true;
        this.selectionEnabled = true;
        
        // 鍦ˋpp鐜涓嬭Е鍙戝叏閫?        if (this.isApp) {
          this.selectAllText();
        }
        
        // 鎸姩鍙嶉
        try {
          if (navigator && navigator.vibrate) {
            navigator.vibrate(50);
          }
        } catch(e) {}
      }, 300);
    },
    
    handleTouchMove(e) {
      // 纭繚鏄崟鐐硅Е鎽?      if (e.touches.length !== 1) return;
      
      // 鏂囨湰閫夋嫨妯″紡涓嶈繘琛屾粴鍔ㄥ鐞?      if (this.isLongPress || this.selectionEnabled) return;
      
      const touch = e.touches[0];
      const currentY = touch.clientY;
      const currentX = touch.clientX;
      const deltaY = currentY - this.touchStartY;
      const deltaX = currentX - this.touchStartX;
      
      // 璁板綍鏃堕棿鍜屼綅绉伙紝鐢ㄤ簬璁＄畻鎯€?      const now = Date.now();
      const deltaTime = now - this.lastUserInteraction;
      this.lastDeltaY = deltaY;
      this.lastDeltaTime = deltaTime;
      
      // 妫€娴嬫槸鍚︽槸鏄庣‘鐨勫瀭鐩存粦鍔紝闄嶄綆鍒ゆ柇闂ㄦ
      if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 3) {
        // 鍙栨秷闀挎寜瀹氭椂鍣?        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
        
        // 鏍囪鐢ㄦ埛婊氬姩
        this.userHasScrolled = true;
        this.lastUserInteraction = now;
        
        // 鐩存帴鎵ц婊氬姩锛屾渶澶у寲鐏垫晱搴?        const textarea = this.getTextareaElement();
        if (textarea) {
          // 澶у箙鎻愰珮鐏垫晱搴︼紝鐗瑰埆鏄湪HBuilder鐜
          let sensitivity = this.isH5 ? 2.2 : 3.0;
          // APP鐜鐗瑰埆浼樺寲
          if (this.isApp) sensitivity = 3.5;
          
          // 鐩存帴鏍规嵁鎵嬫寚浣嶇疆绉诲姩锛屼娇鐢ㄦ洿楂樼殑鐏垫晱搴?          textarea.scrollTop -= deltaY * sensitivity;
          
          // 鏇存柊璧峰鐐癸紝瀹炵幇杩炵画婊氬姩鏁堟灉
          this.touchStartY = currentY;
          this.touchStartX = currentX;
        }
      } else if (Math.abs(deltaX) > 10) {
        // 姘村钩婊戝姩锛屽彲鑳芥槸鏂囨湰閫夋嫨锛屽彇娑堥暱鎸?        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      }
    },
    
    handleTouchEnd(e) {
      // 鍙栨秷闀挎寜瀹氭椂鍣?      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }
      
      // 璁＄畻鎯€ф粴鍔?      const now = Date.now();
      const timeElapsed = now - this.lastUserInteraction;
      
      // 鍙湁鍦ㄧ煭鏃堕棿鍐呯粨鏉熻Е鎽革紝涓旀湁鏄庢樉婊戝姩鎵嶅簲鐢ㄦ儻鎬?      if (timeElapsed < 100 && Math.abs(this.lastDeltaY) > 5) {
        // 璁＄畻婊戝姩鐨勯€熷害
        const velocity = this.lastDeltaY / Math.max(10, this.lastDeltaTime);
        if (Math.abs(velocity) > 0.1) {
          const textarea = this.getTextareaElement();
          if (textarea) {
            this.applyInertialScroll(textarea, velocity);
          }
        }
      }
      
      // 閲嶇疆鐘舵€?      this.isTouchingTextarea = false;
      
      // 寤惰繜閲嶇疆鏂囨湰閫夋嫨鐘舵€?      if (!this.isTextSelectionActive()) {
        setTimeout(() => {
          this.isLongPress = false;
          this.selectionEnabled = false;
        }, 1000);
      }
    },
    
    // 妫€娴嬫槸鍚︽鍦ㄨ繘琛屾枃鏈€夋嫨
    isTextSelectionActive() {
      try {
        // 浠呭湪H5鐜涓鏌?        if (this.isH5 && window) {
          // 妫€鏌indow.getSelection
          if (window.getSelection) {
            const selection = window.getSelection();
            if (selection && selection.type === 'Range' && selection.toString().length > 0) {
              return true;
            }
          }
          
          // 妫€鏌ユ枃鏈鑷韩鐨勯€夋嫨鐘舵€?          const textarea = this.getTextareaElement();
          if (textarea && 
              typeof textarea.selectionStart !== 'undefined' && 
              textarea.selectionStart !== textarea.selectionEnd) {
            return true;
          }
        }
        
        // 闈濰5鐜锛屼緷闈爏electionEnabled鏍囧織
        return this.selectionEnabled;
      } catch (e) {
        console.error('妫€鏌ユ枃鏈€夋嫨鐘舵€佹椂鍑洪敊:', e);
      }
      
      // 榛樿杩斿洖false
      return false;
    },

    applyScroll(element, scrollPosition) {
      if (!element) return;
      
      try {
        // 鐩存帴楂樻晥璁剧疆婊氬姩浣嶇疆锛屾棤闇€棰濆閫昏緫
        const maxScroll = Math.max(0, element.scrollHeight - element.clientHeight);
        const limitedScroll = Math.max(0, Math.min(maxScroll, scrollPosition));
        
        // 浼樺厛浣跨敤鐩存帴璧嬪€硷紝閫熷害鏇村揩
        element.scrollTop = limitedScroll;
        
        // 瀵圭壒瀹氬钩鍙扮殑棰濆浼樺寲
        if (this.isApp) {
          // APP鐜灏濊瘯淇鍙兘鐨勬粴鍔ㄩ棶棰?          setTimeout(() => {
            if (Math.abs(element.scrollTop - limitedScroll) > 2) {
              element.scrollTop = limitedScroll;
            }
          }, 0);
        }
      } catch (err) {
        console.error('婊氬姩搴旂敤澶辫触:', err);
      }
    },
    
    applyInertialScroll(element, velocity) {
      if (!element || Math.abs(velocity) < 0.05) return;
      
      // 璁＄畻鍒濆閫熷害锛孉PP鐜浣跨敤鏇村ぇ鐨勫€?      const initialSpeed = velocity * (this.isApp ? 100 : 80);
      // 鍑忕紦鍑忛€熺巼锛屽欢闀挎粦鍔ㄨ窛绂?      const deceleration = this.isApp ? 0.95 : 0.92;
      
      let currentSpeed = initialSpeed;
      let currentPosition = element.scrollTop;
      
      // 娓呯悊鏃х殑鍔ㄧ敾
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
      }
      
      // 鍒涘缓鎯€у姩鐢?      const animate = () => {
        // 搴旂敤褰撳墠閫熷害
        currentPosition -= currentSpeed;
        
        // 杈圭晫妫€鏌?        if (currentPosition < 0) {
          currentPosition = 0;
          currentSpeed = 0;
        } else if (currentPosition > element.scrollHeight - element.clientHeight) {
          currentPosition = element.scrollHeight - element.clientHeight;
          currentSpeed = 0;
        }
        
        // 搴旂敤浣嶇疆
        element.scrollTop = currentPosition;
        
        // 鍑忛€?        currentSpeed *= deceleration;
        
        // 缁х画鍔ㄧ敾鎴栧仠姝?        if (Math.abs(currentSpeed) > 0.5) {
          this.scrollAnimation = requestAnimationFrame(animate);
        } else {
          this.scrollAnimation = null;
        }
      };
      
      // 寮€濮嬪姩鐢?      this.scrollAnimation = requestAnimationFrame(animate);
    },

    setCursorToEnd() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      try {
        const len = this.modelValue.length;
        
        if (this.isH5) {
          textarea.focus();
          
          if (typeof textarea.selectionStart !== 'undefined') {
            textarea.selectionStart = len;
            textarea.selectionEnd = len;
          }
        } else {
          if (uni && uni.createSelectorQuery) {
            const query = uni.createSelectorQuery().in(this);
            query.select('.optimized-textarea').context((res) => {
              if (res && res.context) {
                res.context.setSelectionRange(len, len);
              }
            }).exec();
          }
        }
        
        this.forceScrollToBottom();
      } catch (error) {
        console.error('璁剧疆鍏夋爣浣嶇疆澶辫触:', error);
      }
    },

    setupMutationObserver() {
      if (this.isH5 && window.MutationObserver) {
        const textarea = this.getTextareaElement();
        if (!textarea) return;
        
        this.mutationObserver = new MutationObserver(mutations => {
          if (this.debugMode) {
            console.log('妫€娴嬪埌DOM鍙樺寲:', mutations.length);
          }
          
          setTimeout(() => {
            if (!this.userHasScrolled) {
              this.forceScrollToBottom();
            }
          }, 50);
        });
        
        const config = {
          childList: true,
          subtree: true,
          characterData: true,
          attributes: true
        };
        
        this.mutationObserver.observe(textarea, config);
      }
    },
    
    handleScroll(e) {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 璁板綍鐢ㄦ埛涓诲姩婊氬姩琛屼负
      if (Date.now() - this.lastUserInteraction < 1000) {
        this.userHasScrolled = true;
        
        // 涓€娈垫椂闂村悗閲嶇疆鐢ㄦ埛婊氬姩鏍囧織锛屼絾寤堕暱鏃堕棿
        clearTimeout(this.resetScrollFlagTimer);
        this.resetScrollFlagTimer = setTimeout(() => {
          this.userHasScrolled = false;
        }, 5000); // 澧炲姞鍒?绉掞紝缁欑敤鎴锋洿澶氱殑闃呰鍜岀紪杈戞椂闂?      }
      
      // 妫€鏌ユ槸鍚︽粴鍔ㄥ埌椤堕儴鎴栧簳閮?      const isAtTop = textarea.scrollTop <= 0;
      const isAtBottom = Math.abs(textarea.scrollTop + textarea.clientHeight - textarea.scrollHeight) <= 2;
      
      if (isAtTop) {
        textarea.classList.add('at-top');
        textarea.classList.remove('at-bottom');
      } else if (isAtBottom) {
        textarea.classList.remove('at-top');
        textarea.classList.add('at-bottom');
      } else {
        textarea.classList.remove('at-top');
        textarea.classList.remove('at-bottom');
      }
    },
    
    updateScrollIndicator(textarea, isAtBottom) {
      if (!this.hasOverflow) return;
      
      const container = this.$el;
      if (!container) return;
      
      if (isAtBottom) {
        container.classList.remove('show-bottom-indicator');
      } else {
        container.classList.add('show-bottom-indicator');
      }
    },
    
    setText(text) {
      // 璁剧疆鏂囨湰鏃朵笉搴旇閲嶇疆鐢ㄦ埛婊氬姩鐘舵€?      const wasInitialText = !this.initialTextLoaded;
      
      this.$emit('update:modelValue', text);
      
      this.$nextTick(() => {
        this.updateHeightAndCheck();
        
        // 鍙湁棣栨鍔犺浇鏂囨湰鏃舵墠鑷姩婊氬姩鍒板簳閮?        if (wasInitialText) {
          setTimeout(() => {
            this.userHasScrolled = false; // 涓存椂閲嶇疆鐘舵€?            this.forceScrollToBottom();
            this.initialTextLoaded = true;
          }, 50);
          
          setTimeout(() => {
            this.userHasScrolled = false;
            this.forceScrollToBottom();
            this.setCursorToEnd();
          }, 200);
        }
      });
    },

    clearText() {
      this.$emit('update:modelValue', '');
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    },

    focus() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.focus();
        this.forceScrollToBottom();
      }
    },

    blur() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.blur();
      }
    },
    
    updateHeightAndScroll() {
      this.updateHeightAndCheck();
      this.forceScrollToBottom();
    },

    forceScrollToBottom() {
      // 寮哄埗婊氬姩锛堝拷鐣serHasScrolled鐘舵€侊級
      this.scrollToBottom(true);
    },
    
    scrollToBottom(force = false) {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 濡傛灉鐢ㄦ埛宸茬粡鎵嬪姩婊氬姩锛屼笖涓嶆槸寮哄埗婊氬姩锛屽垯涓嶆墽琛?      if (this.userHasScrolled && !force) {
        return;
      }
      
      // 绠€鍗曠洿鎺ュ湴璁剧疆婊氬姩浣嶇疆
      try {
        textarea.scrollTop = textarea.scrollHeight;
      } catch (e) {
        console.error('婊氬姩澶辫触:', e);
      }
    },

    checkOverflow() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 鍒ゆ柇鍐呭鏄惁瓒呭嚭鏈€澶ч珮搴?      const contentHeight = this.isH5 ? 
        textarea.scrollHeight : 
        this.countLines(this.modelValue) * 20;
      
      const hasOverflow = contentHeight > this.maxHeight;
      
      if (hasOverflow !== this.hasOverflow) {
        this.hasOverflow = hasOverflow;
        if (this.debugMode) {
          console.log('婧㈠嚭鐘舵€佸彉鍖?', hasOverflow ? '鏈夋孩鍑? : '鏃犳孩鍑?);
        }
      }
    },

    // 鏇存柊妫€鏌ュ鐞嗗唴瀹瑰彉鍖?    handleContent() {
      this.updateHeightAndCheck();
      
      // 濡傛灉涓嶆槸鏂囨湰閫夋嫨鎿嶄綔涓旂敤鎴锋病鏈夋墜鍔ㄦ粴鍔紝婊氬姩鍒板簳閮?      if (!this.isTextSelectionActive() && !this.selectionEnabled && !this.userHasScrolled) {
        this.scrollToBottom();
      } else if (this.isTextSelectionActive() || this.selectionEnabled) {
        // 濡傛灉姝ｅ湪閫夋嫨鏂囨湰锛岀‘淇濅笉骞叉壈閫夋嫨
        this.$nextTick(() => {
          if (!document.activeElement || document.activeElement !== this.getTextareaElement()) {
            // 濡傛灉鏂囨湰妗嗕笉鏄椿璺冨厓绱狅紝灏濊瘯鑱氱劍
            const textarea = this.getTextareaElement();
            if (textarea) textarea.focus();
          }
        });
      }
    },

    // 閲嶅啓澧炲己婊氬姩鏂规硶锛岀Щ闄や竴浜涘彲鑳藉共鎵扮殑浼樺寲
    enhanceScrolling() {
      const textarea = this.getTextareaElement();
      if (!textarea || !this.isH5) return;
      
      // 纭繚婊氬姩琛屼负绠€鍗曠洿鎺?      textarea.style.overflowY = 'auto';
      
      if (this.isIOS) {
        // iOS鐗规畩澶勭悊
        textarea.style.webkitOverflowScrolling = 'touch';
      } else {
        // 鍏朵粬骞冲彴锛岄噸缃竴浜涙牱寮?        textarea.style.scrollBehavior = 'auto';
        textarea.style.willChange = 'auto';
      }
    },

    // 鏄惧紡澶勭悊榧犳爣婊氳疆浜嬩欢
    handleMouseWheel(e) {
      // 涓€鏃︽娴嬪埌榧犳爣婊氳疆锛岀珛鍗虫爣璁扮敤鎴峰凡婊氬姩
      this.userHasScrolled = true;
      
      if (this.debugMode) {
        console.log('妫€娴嬪埌榧犳爣婊氳疆浜嬩欢锛岀鐢ㄨ嚜鍔ㄦ粴鍔?);
      }
      
      // 寤堕暱鐢ㄦ埛婊氬姩鏍囪鐨勬寔缁椂闂?      clearTimeout(this.resetScrollFlagTimer);
      this.resetScrollFlagTimer = setTimeout(() => {
        this.userHasScrolled = false;
        if (this.debugMode) console.log('閲嶇疆鐢ㄦ埛婊氬姩鏍囪');
      }, 10000); // 10绉掑唴涓嶈嚜鍔ㄦ粴鍔?    },

    // 澧炲己澶勭悊閿洏浜嬩欢
    handleKeyDown(e) {
      // 澶勭悊Ctrl+A鍏ㄩ€?      if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault(); // 闃绘榛樿琛屼负纭繚鎴戜滑鐨勫叏閫夌敓鏁?        this.selectionEnabled = true;
        
        // 绔嬪嵆鎵ц鍏ㄩ€?        try {
          this.selectAllText();
          
          // 鐗瑰埆澶勭悊HBuilder鐜
          if (this.isHBuilder) {
            // 棰濆鐨勫叏閫夊鐞?            const textarea = this.getTextareaElement();
            if (textarea) {
              // 灏濊瘯鎵€鏈夊彲鑳界殑鍏ㄩ€夋柟娉?              if (typeof textarea.select === 'function') {
                textarea.select();
              }
              
              // 灏濊瘯璁剧疆閫夋嫨鑼冨洿鍒板叏鏂?              if (typeof textarea.setSelectionRange === 'function') {
                textarea.setSelectionRange(0, this.modelValue.length || 0);
              }
              
              // 浣跨敤document.execCommand
              try {
                document.execCommand('selectAll');
              } catch(e) {}
            }
          }
        } catch(err) {
          console.error('鍏ㄩ€夊け璐?', err);
        }
        
        return false;
      }
      
      // 澶勭悊Ctrl+V绮樿创
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        console.log('妫€娴嬪埌Ctrl+V绮樿创蹇嵎閿?);
        
        // 鐗瑰埆澶勭悊HBuilder鐜
        if (this.isHBuilder) {
          try {
            // 妫€鏌ュ壀璐存澘鏄惁鏈夊浘鐗囷紙HBuilder鐜锛?            if (typeof plus !== 'undefined' && plus.pasteboard) {
              plus.pasteboard.getImageItems(items => {
                if (items && items.length > 0) {
                  console.log('HBuilder鐜妫€娴嬪埌绮樿创鐨勫浘鐗?);
                  
                  // 鑾峰彇绗竴涓浘鐗?                  const imageItem = items[0];
                  
                  // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                  this.$emit('paste-image', {
                    path: imageItem.path,
                    type: imageItem.type || 'image/png'
                  });
                  
                  return;
                } else {
                  // 濡傛灉娌℃湁鍥剧墖锛屽皾璇曡幏鍙栨枃鏈?                  this.tryGetClipboardText();
                }
              }, err => {
                console.error('鑾峰彇鍓创鏉垮浘鐗囧け璐?', err);
                // 灏濊瘯鑾峰彇鏂囨湰
                this.tryGetClipboardText();
              });
              
              return false;
            } else {
              // 灏濊瘯浣跨敤navigator.clipboard API鑾峰彇鍓创鏉垮唴瀹?              this.tryGetClipboardText();
            }
          } catch(err) {
            console.error('灏濊瘯浣跨敤HBuilder鍓创鏉緼PI澶辫触:', err);
          }
        }
        
        // 涓嶉樆姝㈤粯璁よ涓猴紝璁╃郴缁熷鐞嗙矘璐?        return true;
      }
      
      // 澶勭悊Ctrl+Delete鎴朇trl+Backspace娓呯┖鏂囨湰妗?      if ((e.ctrlKey || e.metaKey) && (e.key === 'Delete' || e.key === 'Backspace')) {
        // 濡傛灉鏈夊唴瀹癸紝娓呯┖鏂囨湰妗?        if (this.modelValue) {
          e.preventDefault(); // 闃绘榛樿琛屼负
          
          // 鐩存帴娓呯┖鏂囨湰锛屼笉渚濊禆鍏ㄩ€?          this.$emit('update:modelValue', '');
          
          return false;
        }
      }
      
      // 澶勭悊Delete鎴朆ackspace鍒犻櫎閫変腑鍐呭
      if (e.key === 'Delete' || e.key === 'Backspace') {
        console.log('妫€娴嬪埌Delete鎴朆ackspace閿?);
        
        try {
          // 妫€鏌ユ槸鍚︽湁閫変腑鏂囨湰
          const textarea = this.getTextareaElement();
          let hasSelection = false;
          
          // 鏂规硶1锛氭鏌extarea鐨勯€夋嫨鑼冨洿
          if (textarea && 
              typeof textarea.selectionStart !== 'undefined' && 
              textarea.selectionStart !== textarea.selectionEnd) {
            hasSelection = true;
          }
          
          // 鏂规硶2锛氭鏌ocument.getSelection
          if (!hasSelection && document.getSelection) {
            const selection = document.getSelection();
            if (selection && selection.type === 'Range' && selection.toString().length > 0) {
              hasSelection = true;
            }
          }
          
          // 鏂规硶3锛氭鏌ユ槸鍚﹀彲鑳芥槸鍏ㄩ€夌姸鎬?          if (!hasSelection && document.activeElement === textarea && 
              this.selectionEnabled && this.modelValue) {
            console.log('鍙兘鏄叏閫夌姸鎬侊紝灏濊瘯鍒犻櫎鍏ㄩ儴鍐呭');
            e.preventDefault(); // 闃绘榛樿琛屼负
            this.$emit('update:modelValue', '');
            return false;
          }
          
          // 濡傛灉鏈夐€変腑鏂囨湰锛屾墜鍔ㄥ鐞嗗垹闄?          if (hasSelection) {
            console.log('妫€娴嬪埌鏈夐€変腑鏂囨湰锛屾墽琛屽垹闄?);
            e.preventDefault(); // 闃绘榛樿琛屼负
            this.deleteSelectedText();
            return false;
          }
        } catch(err) {
          console.error('澶勭悊Delete/Backspace閿け璐?', err);
        }
      }
      
      // 澶勭悊Ctrl+X鍓垏
      if ((e.ctrlKey || e.metaKey) && e.key === 'x') {
        // 纭繚鍦℉Builder鐜涓嬪壀鍒囧姛鑳芥甯稿伐浣?        if (this.isHBuilder) {
          try {
            // 鍏堝鍒堕€変腑鏂囨湰
            this.copySelectedText();
            // 鐒跺悗鍒犻櫎閫変腑鏂囨湰
            setTimeout(() => {
              this.deleteSelectedText();
            }, 100);
          } catch(err) {
            console.error('鍓垏鎿嶄綔澶辫触:', err);
          }
        }
      }
      
      // 澶勭悊Ctrl+C澶嶅埗
      if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        // 纭繚鍦℉Builder鐜涓嬪鍒跺姛鑳芥甯稿伐浣?        if (this.isHBuilder) {
          try {
            this.copySelectedText();
          } catch(err) {
            console.error('澶嶅埗鎿嶄綔澶辫触:', err);
          }
        }
      }
      
      // 澶勭悊Escape閿紝鍙栨秷閫夋嫨
      if (e.key === 'Escape') {
        this.selectionEnabled = false;
        
        // 鍙栨秷閫夋嫨
        try {
          const textarea = this.getTextareaElement();
          if (textarea && typeof textarea.selectionStart !== 'undefined') {
            const cursorPos = textarea.selectionEnd || 0;
            textarea.setSelectionRange(cursorPos, cursorPos);
          }
        } catch(err) {
          console.error('鍙栨秷閫夋嫨澶辫触:', err);
        }
      }
    },
    
    // 灏濊瘯鑾峰彇鍓创鏉挎枃鏈唴瀹?    tryGetClipboardText() {
      // 鍦℉Builder鐜涓紝閬垮厤浣跨敤navigator.clipboard API
      if (this.isHBuilder) {
        console.log('HBuilder鐜涓嬮伩鍏嶄娇鐢╪avigator.clipboard API');
        
        // 灏濊瘯浣跨敤uni API
        this.tryUniClipboardAPI();
        return;
      }
      
      // 灏濊瘯浣跨敤navigator.clipboard API
      if (navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText()
          .then(text => {
            if (text) {
              console.log('浠巒avigator.clipboard鑾峰彇鍒版枃鏈?);
              this.insertTextAtCursor(text);
            }
          })
          .catch(err => {
            console.error('navigator.clipboard.readText澶辫触:', err);
            
            // 灏濊瘯浣跨敤uni API
            this.tryUniClipboardAPI();
          });
      } else {
        // 灏濊瘯浣跨敤uni API
        this.tryUniClipboardAPI();
      }
    },
    
    // 灏濊瘯浣跨敤uni API鑾峰彇鍓创鏉垮唴瀹?    tryUniClipboardAPI() {
      if (typeof uni !== 'undefined' && uni.getClipboardData) {
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              console.log('浠巙ni.getClipboardData鑾峰彇鍒版枃鏈?);
              this.insertTextAtCursor(res.data);
            }
          },
          fail: (err) => {
            console.error('uni.getClipboardData澶辫触:', err);
          }
        });
      }
    },
    
    // 澧炲己鍒犻櫎閫変腑鏂囨湰鏂规硶
    deleteSelectedText() {
      console.log('鎵ц鍒犻櫎閫変腑鏂囨湰鏂规硶');
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 鑾峰彇褰撳墠鏂囨湰鍜岄€夋嫨鑼冨洿
        let text = this.modelValue || '';
        let start = 0;
        let end = text.length;
        
        // 灏濊瘯鑾峰彇閫夋嫨鑼冨洿
        try {
          if (textarea.selectionStart !== undefined && textarea.selectionEnd !== undefined) {
            start = textarea.selectionStart;
            end = textarea.selectionEnd;
            console.log('鑾峰彇鍒伴€夋嫨鑼冨洿:', start, end);
          }
        } catch (e) {
          console.error('鑾峰彇閫夋嫨鑼冨洿澶辫触锛屽皢鍒犻櫎鍏ㄩ儴鏂囨湰:', e);
        }
        
        // 濡傛灉娌℃湁閫夋嫨鑼冨洿鎴栭€夋嫨鑼冨洿鏃犳晥锛屾鏌ユ槸鍚︿负鍏ㄩ€夌姸鎬?        if (start === end) {
          // 妫€鏌ユ槸鍚﹀彲鑳芥槸鍏ㄩ€夌姸鎬佷絾鏈兘姝ｇ‘鑾峰彇鑼冨洿
          if (document.activeElement === textarea && 
              (document.getSelection().toString() === text || 
               document.getSelection().type === 'Range')) {
            console.log('妫€娴嬪埌鍙兘鏄叏閫夌姸鎬侊紝鎵ц鍏ㄩ儴娓呯┖');
            start = 0;
            end = text.length;
          } else {
            console.log('娌℃湁閫夋嫨鑼冨洿锛屼笉鎵ц鍒犻櫎');
            return;
          }
        }
        
        console.log('鍒犻櫎閫変腑閮ㄥ垎锛屼粠', start, '鍒?, end);
        
        // 鍒涘缓鏂版枃鏈紙鍒犻櫎閫変腑閮ㄥ垎锛?        const newText = text.substring(0, start) + text.substring(end);
        
        // 鏇存柊鏂囨湰
        this.$emit('update:modelValue', newText);
        
        // 閲嶆柊璁剧疆鍏夋爣浣嶇疆
        this.$nextTick(() => {
          try {
            textarea.focus();
            if (typeof textarea.setSelectionRange === 'function') {
              textarea.setSelectionRange(start, start);
              }
            } catch (e) {
            console.error('璁剧疆鍏夋爣浣嶇疆澶辫触:', e);
            }
          });
      } catch (e) {
        console.error('鍒犻櫎鏂囨湰澶辫触:', e);
        
        // 濡傛灉鍒犻櫎澶辫触锛屽皾璇曠洿鎺ユ竻绌?        this.$emit('update:modelValue', '');
      }
    },

    // 鍦ㄧ粍浠跺垱寤烘椂娣诲姞骞冲彴鐗瑰畾鐨勫垵濮嬪寲
    setupAppFeatures() {
      if (!this.isApp) return;
      
      // 灏濊瘯澧炲己APP鐜鐨勬枃鏈紪杈戝姛鑳?      try {
        // 鐩戝惉uni鐨剅eady浜嬩欢
        if (typeof uni !== 'undefined') {
          // 蹇呰鏃惰缃叏灞€APP鏍峰紡
          if (typeof plus !== 'undefined' && plus.webview) {
            const currentWebview = plus.webview.currentWebview();
            if (currentWebview) {
              // 鍙兘鐨凙PP鐗瑰畾浼樺寲
              currentWebview.setStyle({
                softinputMode: 'adjustResize' // 杞敭鐩樺脊鍑烘椂鑷姩璋冩暣椤甸潰澶у皬
              });
            }
          }
          
          // 鍦ˋPP鐜涓惎鐢ㄦ洿澶氶€夋嫨鍔熻兘
          this.$nextTick(() => {
            setTimeout(() => {
              this.enhanceAppTextSelection();
            }, 500);
          });
        }
      } catch (e) {
        console.error('APP鍔熻兘澧炲己澶辫触:', e);
      }
    },

    // APP鐜涓殑閫夋嫨鍜岀紪杈戝寮?    enhanceAppTextSelection() {
      if (!this.isApp) return;
      
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 1. 纭繚鏍峰紡姝ｇ‘
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.webkitTouchCallout = 'default';
        
        // 2. 娣诲姞APP鐜涓嬬殑鍥剧墖绮樿创鏀寔
        if (typeof plus !== 'undefined') {
          // 鐩戝惉plus鐜涓嬬殑绮樿创浜嬩欢
          document.addEventListener('paste', (e) => {
            this.handlePaste(e);
          });
          
          // 娣诲姞鍥剧墖绮樿创鏀寔
          this.setupAppImagePaste();
        }
        
        // 3. 灏濊瘯娣诲姞鑷畾涔夎彍鍗?        if (typeof plus !== 'undefined' && plus.nativeUI) {
          // 娣诲姞鑷畾涔夐暱鎸夋搷浣?          textarea.addEventListener('longpress', (e) => {
            // 闃绘榛樿琛屼负
            e.preventDefault();
            
            // 璁剧疆閫夋嫨妯″紡
            this.isLongPress = true;
            this.selectionEnabled = true;
            
            // 瑙﹀彂鎸姩
            if (plus.device && plus.device.vibrate) {
              plus.device.vibrate(50);
            }
            
            // 鍏ㄩ€夋枃鏈?            this.selectAllText();
            
            // 鍙互鑰冭檻鏄剧ず鑷畾涔夎彍鍗?            plus.nativeUI.actionSheet({
              title: '鏂囨湰鎿嶄綔',
              cancel: '鍙栨秷',
              buttons: [
                {title: '鍏ㄩ€?},
                {title: '澶嶅埗'},
                {title: '鍓垏'},
                {title: '绮樿创'},
                {title: '绮樿创鍥剧墖'},
                {title: '鍒犻櫎'}
              ]
            }, (e) => {
              // 澶勭悊鑿滃崟閫夋嫨
              switch(e.index) {
                case 1: // 鍏ㄩ€?                  this.selectAllText();
                  break;
                case 2: // 澶嶅埗
                  this.copySelectedText();
                  break;
                case 3: // 鍓垏
                  this.cutSelectedText();
                  break;
                case 4: // 绮樿创
                  // 灏濊瘯鑾峰彇鍓创鏉垮唴瀹瑰苟绮樿创
                  this.tryGetClipboardText();
                  break;
                case 5: // 绮樿创鍥剧墖
                  this.tryPasteImage();
                  break;
                case 6: // 鍒犻櫎
                  this.deleteSelectedText();
                  break;
              }
            });
          });
        }
      } catch (e) {
        console.error('enhanceAppTextSelection澶辫触:', e);
      }
    },
    
    // 璁剧疆APP鐜涓嬬殑鍥剧墖绮樿创鍔熻兘
    setupAppImagePaste() {
      if (!this.isApp) return;
      
      try {
        // 閽堝plus鐜
        if (typeof plus !== 'undefined' && plus.pasteboard) {
          // 鍙互鍦ㄨ繖閲屾坊鍔犵壒瀹氱殑鍒濆鍖栦唬鐮?          console.log('APP鐜涓嬪惎鐢ㄥ浘鐗囩矘璐存敮鎸?);
        }
      } catch (e) {
        console.error('璁剧疆APP鍥剧墖绮樿创鍔熻兘澶辫触:', e);
      }
    },
    
    // 灏濊瘯绮樿创鍥剧墖
    tryPasteImage() {
      console.log('灏濊瘯绮樿创鍥剧墖锛屽綋鍓嶇幆澧?', 
                 this.isH5 ? 'H5' : 
                 this.isApp ? 'APP' : 
                 this.isMiniProgram ? '灏忕▼搴? : 
                 this.isHBuilder ? 'HBuilder' : '鏈煡');
      
      try {
        // 閽堝涓嶅悓骞冲彴瀹炵幇鍥剧墖绮樿创
        if (this.isHBuilder || (this.isApp && typeof plus !== 'undefined' && plus.pasteboard)) {
          // HBuilder/APP鐜
          console.log('浣跨敤plus.pasteboard API鑾峰彇鍥剧墖');
          
          plus.pasteboard.getImageItems(items => {
            if (items && items.length > 0) {
              console.log('鑾峰彇鍒板浘鐗囬」:', items.length);
              
              // 鑾峰彇绗竴涓浘鐗?              const imageItem = items[0];
              console.log('鍥剧墖璺緞:', imageItem.path ? '鏈夋晥' : '鏃犳晥');
              
              // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
              this.$emit('paste-image', {
                path: imageItem.path,
                type: imageItem.type || 'image/png',
                source: 'tryPasteImage-plus'
              });
            } else {
              console.log('鍓创鏉夸腑娌℃湁鍥剧墖锛屽皾璇曡幏鍙栨枃鏈?);
              
              // 灏濊瘯鑾峰彇鏂囨湰骞舵鏌ユ槸鍚︽槸鍥剧墖URL
              plus.pasteboard.getString(text => {
                if (text && this.isImageUrl(text)) {
                  console.log('鍓创鏉挎枃鏈彲鑳芥槸鍥剧墖URL:', text);
                  
                  // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                  this.$emit('paste-image', {
                    path: text,
                    type: 'image/url',
                    source: 'tryPasteImage-plus-text'
                  });
                } else {
                  console.log('鍓创鏉挎枃鏈笉鏄浘鐗嘦RL');
                  
                  // 濡傛灉鍦℉5鐜涓紝鎵撳紑鏂囦欢閫夋嫨鍣?                  if (this.isH5) {
                    this.showFileSelector();
                  }
                }
              });
            }
          }, err => {
            console.error('鑾峰彇鍓创鏉垮浘鐗囧け璐?', err);
            
            // 濡傛灉鍦℉5鐜涓紝鎵撳紑鏂囦欢閫夋嫨鍣?            if (this.isH5) {
              this.showFileSelector();
            }
          });
        } else if (this.isH5) {
          // H5鐜
          console.log('H5鐜灏濊瘯鑾峰彇鍓创鏉垮浘鐗?);
          
          // 鏂规硶1: 浣跨敤navigator.clipboard API (鐜颁唬娴忚鍣?
          if (navigator.clipboard && navigator.clipboard.read) {
            console.log('灏濊瘯浣跨敤navigator.clipboard.read');
            
            navigator.clipboard.read()
              .then(clipboardItems => {
                console.log('璇诲彇鍒板壀璐存澘椤?', clipboardItems.length);
                
                let foundImage = false;
                
                for (const clipboardItem of clipboardItems) {
                  console.log('鍓创鏉块」绫诲瀷:', clipboardItem.types);
                  
                  for (const type of clipboardItem.types) {
                    if (type.startsWith('image/')) {
                      console.log('妫€娴嬪埌鍥剧墖绫诲瀷:', type);
                      foundImage = true;
                      
                      clipboardItem.getType(type)
                        .then(blob => {
                          console.log('鑾峰彇鍒板浘鐗嘼lob:', blob.size);
                          
                          // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                          this.$emit('paste-image', {
                            blob: blob,
                            type: type,
                            source: 'navigator-clipboard-read'
                          });
                        })
                        .catch(err => {
                          console.error('鑾峰彇鍥剧墖blob澶辫触:', err);
                          // 濡傛灉鑾峰彇blob澶辫触锛岀洿鎺ユ墦寮€鏂囦欢閫夋嫨鍣?                          this.showFileSelector();
                        });
                      
                      break;
                    }
                  }
                  
                  if (foundImage) break;
                }
                
                if (!foundImage) {
                  console.log('鍓创鏉夸腑娌℃湁鍥剧墖锛岀洿鎺ユ墦寮€鏂囦欢閫夋嫨鍣?);
                  this.showFileSelector();
                }
              })
              .catch(err => {
                console.error('璇诲彇鍓创鏉垮唴瀹瑰け璐?', err);
                
                // 鏉冮檺閿欒鎴栧叾浠栭敊璇紝鐩存帴鎵撳紑鏂囦欢閫夋嫨鍣?                console.log('鏃犳硶璁块棶鍓创鏉匡紝鎵撳紑鏂囦欢閫夋嫨鍣?);
                this.showFileSelector();
              });
          } else {
            // 濡傛灉navigator.clipboard.read涓嶅彲鐢紝鐩存帴鎵撳紑鏂囦欢閫夋嫨鍣?            console.log('navigator.clipboard.read涓嶅彲鐢紝鎵撳紑鏂囦欢閫夋嫨鍣?);
            this.showFileSelector();
          }
        } else if (this.isMiniProgram) {
          // 灏忕▼搴忕幆澧?          console.log('灏忕▼搴忕幆澧冨皾璇曡幏鍙栧壀璐存澘鍥剧墖');
          
          if (typeof wx !== 'undefined' && wx.getClipboardData) {
            wx.getClipboardData({
              success: (res) => {
                console.log('鑾峰彇鍒板壀璐存澘鏁版嵁:', res.data ? '鏈夊唴瀹? : '鏃犲唴瀹?);
                
                if (res.data && this.isImageUrl(res.data)) {
                  console.log('鍓创鏉垮唴瀹瑰彲鑳芥槸鍥剧墖URL:', res.data);
                  
                  // 鍙兘鏄浘鐗囪矾寰?                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/png', // 榛樿绫诲瀷
                    source: 'wx-getClipboardData'
                  });
                } else {
                  console.log('鍓创鏉夸腑娌℃湁鍥剧墖URL');
                }
              },
              fail: (err) => {
                console.error('鑾峰彇鍓创鏉挎暟鎹け璐?', err);
              }
            });
          } else if (typeof uni !== 'undefined' && uni.getClipboardData) {
            // 灏濊瘯浣跨敤uni API
            uni.getClipboardData({
              success: (res) => {
                console.log('鑾峰彇鍒皍ni鍓创鏉挎暟鎹?', res.data ? '鏈夊唴瀹? : '鏃犲唴瀹?);
                
                if (res.data && this.isImageUrl(res.data)) {
                  console.log('uni鍓创鏉垮唴瀹瑰彲鑳芥槸鍥剧墖URL:', res.data);
                  
                  // 鍙兘鏄浘鐗囪矾寰?                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/png', // 榛樿绫诲瀷
                    source: 'uni-getClipboardData'
                  });
                } else {
                  console.log('uni鍓创鏉夸腑娌℃湁鍥剧墖URL');
                }
              }
            });
          } else {
            console.log('灏忕▼搴忕幆澧冩棤娉曠洿鎺ヨ闂壀璐存澘');
          }
        } else {
          console.log('褰撳墠鐜涓嶆敮鎸佸浘鐗囩矘璐?);
        }
      } catch (e) {
        console.error('灏濊瘯绮樿创鍥剧墖澶辫触:', e);
        
        // 濡傛灉鍦℉5鐜涓嚭閿欙紝鎵撳紑鏂囦欢閫夋嫨鍣?        if (this.isH5) {
          console.log('绮樿创鍥剧墖鍑洪敊锛屾墦寮€鏂囦欢閫夋嫨鍣?);
          this.showFileSelector();
        }
      }
    },
    
    // 閫氳繃鍒涘缓涓存椂浜嬩欢灏濊瘯绮樿创鍥剧墖
    tryPasteImageViaEvent() {
      console.log('灏濊瘯閫氳繃鍒涘缓涓存椂浜嬩欢绮樿创鍥剧墖');
      
      // 鍦℉Builder鐜涓伩鍏嶄娇鐢╠ocument.execCommand
      if (this.isHBuilder) {
        console.log('HBuilder鐜涓嶄娇鐢╠ocument.execCommand锛岄伩鍏峜allback閿欒');
        return;
      }
      
      try {
        // 鍒涘缓涓€涓复鏃剁殑鍙紪杈戝厓绱?        const tempDiv = document.createElement('div');
        tempDiv.contentEditable = true;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '0';
        tempDiv.style.width = '1px';
        tempDiv.style.height = '1px';
        tempDiv.style.opacity = '0';
        tempDiv.style.pointerEvents = 'none';
        
        // 纭繚鍏冪礌鍙互鎺ユ敹绮樿创浜嬩欢
        tempDiv.setAttribute('tabindex', '-1');
        
        // 娣诲姞鍒版枃妗?        document.body.appendChild(tempDiv);
        
        // 鑱氱劍鍏冪礌
        tempDiv.focus();
        
        // 娣诲姞绮樿创浜嬩欢鐩戝惉
        let pasteDetected = false;
        const pasteHandler = (e) => {
          pasteDetected = true;
          console.log('涓存椂鍏冪礌鎹曡幏鍒扮矘璐翠簨浠?);
          
          // 妫€鏌ユ槸鍚︽湁鍥剧墖
          if (e.clipboardData && e.clipboardData.files && e.clipboardData.files.length > 0) {
            const file = e.clipboardData.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('涓存椂鍏冪礌浠巆lipboardData.files鑾峰彇鍒板浘鐗?', file.type);
              e.preventDefault();
              e.stopPropagation();
              
              // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                source: 'temp-div-paste-files'
              });
            }
          }
        };
        
        tempDiv.addEventListener('paste', pasteHandler);
        
        // 灏濊瘯瑙﹀彂绮樿创浜嬩欢
        let pasteSuccess = false;
        try {
          pasteSuccess = document.execCommand('paste');
          console.log('document.execCommand("paste")缁撴灉:', pasteSuccess);
        } catch (execErr) {
          console.error('document.execCommand("paste")澶辫触:', execErr);
        }
        
        // 妫€鏌ユ槸鍚︾矘璐翠簡鍥剧墖
        setTimeout(() => {
          // 绉婚櫎绮樿创浜嬩欢鐩戝惉鍣?          tempDiv.removeEventListener('paste', pasteHandler);
          
          // 妫€鏌ユ槸鍚︽湁鍥剧墖鍏冪礌
          const images = tempDiv.querySelectorAll('img');
          
          if (images.length > 0) {
            console.log('妫€娴嬪埌绮樿创鐨勫浘鐗囧厓绱?', images.length);
            
            // 澶勭悊绗竴涓浘鐗?            this.processImageElement(images[0]);
          } else {
            // 妫€鏌ユ槸鍚︽湁鑳屾櫙鍥剧墖
            const computedStyle = window.getComputedStyle(tempDiv);
            const backgroundImage = computedStyle.backgroundImage;
            
            if (backgroundImage && backgroundImage !== 'none' && backgroundImage.indexOf('url(') !== -1) {
              console.log('妫€娴嬪埌鑳屾櫙鍥剧墖:', backgroundImage);
              
              // 鎻愬彇URL
              const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
              if (urlMatch && urlMatch[1]) {
                const imageUrl = urlMatch[1];
                
                // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                this.$emit('paste-image', {
                  path: imageUrl,
                  type: 'image/url',
                  source: 'temp-div-background'
                });
              }
            } else if (!pasteDetected && !pasteSuccess) {
              console.log('鏈娴嬪埌绮樿创鐨勫浘鐗?);
              
              // 涓嶅啀鏄剧ず鎻愮ず妗嗭紝鐩存帴杩斿洖
            }
          }
          
          // 娓呯悊涓存椂鍏冪礌
          try {
            document.body.removeChild(tempDiv);
          } catch (cleanupErr) {
            console.error('娓呯悊涓存椂鍏冪礌澶辫触:', cleanupErr);
          }
        }, 200);
      } catch (err) {
        console.error('閫氳繃浜嬩欢绮樿创鍥剧墖澶辫触:', err);
        
        // 濡傛灉澶辫触锛屽皾璇曚娇鐢ㄦ枃浠堕€夋嫨鍣?        if (this.isH5) {
          setTimeout(() => {
            this.showFileSelector();
          }, 300);
        }
      }
    },
    
    // 澶勭悊浠庣矘璐翠簨浠朵腑鑾峰彇鐨勫浘鐗囧厓绱?    processImageElement(imgElement) {
      if (!imgElement) return;
      
      try {
        // 鑾峰彇鍥剧墖src
        const imgSrc = imgElement.src;
        
        if (imgSrc) {
          console.log('鑾峰彇鍒板浘鐗噑rc:', imgSrc.substring(0, 30) + (imgSrc.length > 30 ? '...' : ''));
          
          // 濡傛灉鏄痙ata URL锛岃浆鎹负blob
          if (imgSrc.startsWith('data:')) {
            this.dataURLToBlob(imgSrc, (blob) => {
              if (blob) {
                // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                this.$emit('paste-image', {
                  blob: blob,
                  type: blob.type || 'image/png',
                  source: 'paste-event-data-url'
                });
              } else {
                // 濡傛灉杞崲澶辫触锛岀洿鎺ヤ娇鐢╠ata URL
                this.$emit('paste-image', {
                  path: imgSrc,
                  type: imgSrc.split(';')[0].replace('data:', ''),
                  source: 'paste-event-data-url-fallback'
                });
              }
            });
          } else {
            // 鏅€歎RL
            this.$emit('paste-image', {
              path: imgSrc,
              type: 'image/url',
              source: 'paste-event-url'
            });
          }
        } else {
          console.log('鍥剧墖鍏冪礌娌℃湁src灞炴€?);
          
          // 灏濊瘯浠巗tyle涓彁鍙栬儗鏅浘鐗?          const style = imgElement.getAttribute('style');
          if (style && style.includes('background-image')) {
            const match = style.match(/background-image:\s*url\(['"]?(.*?)['"]?\)/i);
            if (match && match[1]) {
              this.$emit('paste-image', {
                path: match[1],
                type: 'image/url',
                source: 'paste-event-background'
              });
            }
          }
        }
      } catch (err) {
        console.error('澶勭悊鍥剧墖鍏冪礌澶辫触:', err);
      }
    },

    // 灏咲ata URL杞崲涓築lob
    dataURLToBlob(dataURL, callback) {
      try {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        
        const blob = new Blob([u8arr], { type: mime });
        callback(blob);
      } catch (err) {
        console.error('杞崲Data URL澶辫触:', err);
        callback(null);
      }
    },

    // 澶嶅埗閫変腑鏂囨湰
    copySelectedText() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 鑾峰彇閫夋嫨鐨勬枃鏈?        let selectedText = '';
        
        if (this.isH5) {
          // H5鐜
          if (window.getSelection) {
            selectedText = window.getSelection().toString();
          } else if (document.selection) {
            selectedText = document.selection.createRange().text;
          }
        }
        
        // 濡傛灉娌℃湁閫氳繃window閫夋嫨鑾峰彇鍒版枃鏈紝灏变粠textarea鏈韩鑾峰彇
        if (!selectedText && textarea.selectionStart !== undefined) {
          const start = textarea.selectionStart;
          const end = textarea.selectionEnd;
          selectedText = this.modelValue.substring(start, end);
        }
        
        // 濡傛灉杩樻槸娌℃湁閫変腑鐨勬枃鏈紝鍒欏皾璇曞叏閫?        if (!selectedText) {
          selectedText = this.modelValue;
        }
        
        // 澶嶅埗鍒板壀璐存澘
        if (selectedText) {
          if (uni && uni.setClipboardData) {
            uni.setClipboardData({
              data: selectedText,
              success: () => {
                uni.showToast({
                  title: '宸插鍒?,
                  icon: 'none',
                  duration: 1500
                });
              }
            });
          } else if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(selectedText)
              .then(() => {
                if (uni) {
                  uni.showToast({
                    title: '宸插鍒?,
                    icon: 'none',
                    duration: 1500
                  });
                }
              });
          }
        }
      } catch (e) {
        console.error('澶嶅埗鏂囨湰澶辫触:', e);
      }
    },

    // 鍓垏閫変腑鏂囨湰
    cutSelectedText() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 鍏堝鍒舵枃鏈?        this.copySelectedText();
        
        // 鐒跺悗鍒犻櫎閫変腑鐨勬枃鏈?        this.deleteSelectedText();
      } catch (e) {
        console.error('鍓垏鏂囨湰澶辫触:', e);
      }
    },

    // 娣诲姞鍙戦€佹柟娉曪紝澶栭儴鍙皟鐢?    sendMessage() {
      // 濡傛灉娌℃湁鍐呭锛屼笉澶勭悊
      if (!this.modelValue || this.modelValue.trim() === '') {
        return false;
      }
      
      // 鍙戦€佸墠鐨勫唴瀹?      const content = this.modelValue;
      
      // 娓呯┖杈撳叆妗?      this.$emit('update:modelValue', '');
      
      // 閲嶇疆鐢ㄦ埛婊氬姩鏍囧織
      this.userHasScrolled = false;
      
      // 鏇存柊楂樺害
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
      
      // 瑙﹀彂鍙戦€佷簨浠讹紝浼犻€掑彂閫佺殑鍐呭
      this.$emit('send', content);
      
      return true;
    },

    // 澧炲己鎻掑叆鏂囨湰鍒板厜鏍囦綅缃殑鏂规硶
    insertTextAtCursor(text) {
      if (!text) return;
      console.log('鎵ц鎻掑叆鏂囨湰鍒板厜鏍囦綅缃?', text.substring(0, 20) + (text.length > 20 ? '...' : ''));
      
      const textarea = this.getTextareaElement();
      let newText = this.modelValue || '';
      
      try {
        // 灏濊瘯鑾峰彇褰撳墠閫夋嫨浣嶇疆
        let start = 0;
        let end = 0;
        
        if (textarea && typeof textarea.selectionStart !== 'undefined') {
          start = textarea.selectionStart || 0;
          end = textarea.selectionEnd || 0;
          } else {
          // 濡傛灉鏃犳硶鑾峰彇閫夋嫨浣嶇疆锛岄粯璁よ拷鍔犲埌鏈熬
          start = end = newText.length;
        }
        
        // 鎻掑叆鏂囨湰
        newText = newText.substring(0, start) + text + newText.substring(end);
        
        // 鏇存柊鏂囨湰
        this.$emit('update:modelValue', newText);
        
        // 鏇存柊UI鍜屽厜鏍囦綅缃?        this.$nextTick(() => {
          try {
            // 灏濊瘯鑱氱劍鍜岃缃厜鏍囦綅缃?            if (textarea) {
              textarea.focus();
              
              if (typeof textarea.setSelectionRange === 'function') {
                const newPosition = start + text.length;
                textarea.setSelectionRange(newPosition, newPosition);
              }
            }
          } catch (err) {
            console.error('璁剧疆鍏夋爣浣嶇疆澶辫触:', err);
          }
          
          // 鏇存柊楂樺害鍜屾粴鍔?          this.updateHeightAndCheck();
          this.forceScrollToBottom();
          
          // 200ms鍚庡啀娆℃粴鍔ㄥ埌搴曢儴锛岀‘淇濆唴瀹瑰畬鍏ㄥ姞杞?          setTimeout(() => {
            this.forceScrollToBottom();
          }, 200);
        });
      } catch (err) {
        console.error('鎻掑叆鏂囨湰澶辫触锛屽皾璇曠洿鎺ヨ拷鍔?', err);
        
        // 鍑洪敊鏃剁殑澶囩敤鏂规锛氱洿鎺ヨ拷鍔犲埌鏈熬
        try {
          newText = newText + text;
        this.$emit('update:modelValue', newText);
        
        this.$nextTick(() => {
            this.updateHeightAndCheck();
            this.forceScrollToBottom();
          });
        } catch (backupErr) {
          console.error('澶囩敤鎻掑叆鏂规硶涔熷け璐?', backupErr);
        }
      }
      
      // 瑙﹀彂绮樿创浜嬩欢
      this.$emit('paste', { detail: { value: text } });
    },

    // 娣诲姞鍏ㄩ€夋寜閽偣鍑讳簨浠?    handleSelectAllButtonClick() {
      if (this.disabled) return;
      
      console.log('鍏ㄩ€夋寜閽鐐瑰嚮');
      this.selectAllText();
      
      // 閫夋嫨鍚庢樉绀烘彁绀?      uni.showToast({
        title: '宸插叏閫?,
        icon: 'none',
        duration: 1000
      });
    },

    // 娣诲姞鍏ㄩ€夊苟澶嶅埗鎸夐挳鐐瑰嚮浜嬩欢
    handleSelectAndCopyButtonClick() {
      if (this.disabled || !this.modelValue) return;
      
      console.log('鍏ㄩ€夊苟澶嶅埗鎸夐挳琚偣鍑?);
      
      // 鐩存帴灏嗘枃鏈鍒跺埌鍓创鏉?      if (uni && uni.setClipboardData) {
        uni.setClipboardData({
          data: this.modelValue,
          success: () => {
            // 鏄剧ず瑙嗚鍙嶉
            this.selectionEnabled = true;
            
            // 灏濊瘯鎵ц鍏ㄩ€変互鎻愪緵瑙嗚鍙嶉
            this.selectAllText();
            
            // 鎻愮ず鐢ㄦ埛
            uni.showToast({
              title: '鍐呭宸插鍒跺埌鍓创鏉?,
              icon: 'none',
              duration: 1500
            });
            
            // 1.5绉掑悗閲嶇疆閫夋嫨鐘舵€?            setTimeout(() => {
              this.selectionEnabled = false;
            }, 1500);
          },
          fail: () => {
            uni.showToast({
              title: '澶嶅埗澶辫触锛岃鎵嬪姩鎿嶄綔',
              icon: 'none',
              duration: 1500
            });
          }
        });
      } else {
        // 灏濊瘯甯歌鍏ㄩ€夋柟娉?        this.selectAllText();
        
        // 灏濊瘯澶嶅埗
        this.copySelectedText();
      }
    },
    
    // 澶勭悊閫夋嫨寮€濮嬩簨浠?    handleSelectStart(e) {
      // 璁剧疆閫夋嫨妯″紡
      this.selectionEnabled = true;
      
      if (this.debugMode) {
        console.log('閫夋嫨鏂囨湰寮€濮?);
      }
    },
    
    // 澶勭悊榧犳爣鎶捣浜嬩欢锛岀敤浜庢娴嬫枃鏈€夋嫨
    handleMouseUp(e) {
      // 鐭殏寤惰繜妫€鏌ユ槸鍚︽湁鏂囨湰琚€変腑
      setTimeout(() => {
        if (this.isTextSelectionActive()) {
          this.selectionEnabled = true;
          
          if (this.debugMode) {
            console.log('妫€娴嬪埌鏂囨湰閫夋嫨');
          }
        } else {
          // 濡傛灉娌℃湁鏂囨湰琚€変腑锛屽彲浠ヨ€冭檻閲嶇疆閫夋嫨鐘舵€?          // 浣嗕负浜嗕笉骞叉壈鐢ㄦ埛鎿嶄綔锛岃繖閲屼笉绔嬪嵆閲嶇疆
        }
      }, 100);
    },
    
    // 娣诲姞鍏ㄩ€夊苟娓呯┖鎸夐挳鐐瑰嚮浜嬩欢
    handleSelectAndClearButtonClick() {
      if (this.disabled) return;
      
      console.log('鍏ㄩ€夊苟娓呯┖鎸夐挳琚偣鍑?);
      
      // 鐩存帴娓呯┖鏂囨湰锛屼笉渚濊禆鍏ㄩ€?      this.$emit('update:modelValue', '');
      
      // 鎻愮ず鐢ㄦ埛
      uni.showToast({
        title: '鍐呭宸叉竻绌?,
        icon: 'none',
        duration: 1500
      });
      
      // 閲嶇疆閫夋嫨鐘舵€?      this.selectionEnabled = false;
      
      // 鏇存柊楂樺害
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    },

    // 澧炲己鍏ㄩ€夋柟娉?- 淇"this.selectAllText is not a function"閿欒
    selectAllText() {
      console.log('鎵ц鍏ㄩ€夋枃鏈柟娉?);
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 璁剧疆閫夋嫨鐘舵€?        this.selectionEnabled = true;
        
        // 浣跨敤澶氱鏂规硶灏濊瘯鍏ㄩ€夛紝鎻愰珮鎴愬姛鐜?        // 鏂规硶1: 鍘熺敓select鏂规硶
        if (typeof textarea.select === 'function') {
          textarea.focus();
          textarea.select();
          console.log('浣跨敤textarea.select()鏂规硶鍏ㄩ€?);
        }
        
        // 鏂规硶2: 璁剧疆閫夋嫨鑼冨洿
        if (typeof textarea.setSelectionRange === 'function') {
          textarea.focus();
          textarea.setSelectionRange(0, this.modelValue.length || 0);
          console.log('浣跨敤setSelectionRange鏂规硶鍏ㄩ€?);
        }
        
        // 鏂规硶3: 浣跨敤document.execCommand
        try {
          document.execCommand('selectAll');
          console.log('浣跨敤document.execCommand("selectAll")鏂规硶鍏ㄩ€?);
        } catch(e) {
          console.log('document.execCommand("selectAll")澶辫触');
        }
        
        // 鏂规硶4: 瀵逛簬鐗瑰畾骞冲彴鐨凙PI
        if (this.isApp && uni && uni.createSelectorQuery) {
          // 閫氳繃uni API灏濊瘯鍏ㄩ€?          uni.createSelectorQuery()
            .in(this)
            .select('.optimized-textarea')
            .context((res) => {
              if (res && res.context) {
                res.context.focus();
                res.context.setSelectionRange(0, this.modelValue.length || 0);
                console.log('浣跨敤uni API鍏ㄩ€?);
              }
            }).exec();
        }
        
        // 鎵嬪姩璁剧疆鏂囨湰閫夋嫨鏍峰紡
        textarea.classList.add('selecting-text');
        
        // 鍒涘缓涓€涓爣璁帮紝琛ㄧず宸茬粡鎵ц杩囧叏閫夋搷浣?        this._hasSelectedAll = true;
        
        // 璁剧疆涓€涓欢鏃讹紝纭繚閫夋嫨鐘舵€佽姝ｇ‘鏍囪
        setTimeout(() => {
          // 鍐嶆纭閫夋嫨鐘舵€?          if (document.activeElement === textarea) {
            this.selectionEnabled = true;
            console.log('纭鍏ㄩ€夌姸鎬佸凡璁剧疆');
          }
        }, 100);
        
      } catch (e) {
        console.error('鍏ㄩ€夋枃鏈け璐?', e);
        
        // 濡傛灉鍏ㄩ€夊け璐ワ紝鑷冲皯鏍囪閫夋嫨鐘舵€?        this.selectionEnabled = true;
      }
    },

    // 娣诲姞绮樿创鎸夐挳鐐瑰嚮浜嬩欢澶勭悊
    handlePasteButtonClick() {
      console.log('绮樿创鎸夐挳琚偣鍑?);
      
      // 鏄剧ず鍔犺浇鎻愮ず
      uni.showLoading({
        title: '鑾峰彇鍓创鏉垮唴瀹?..',
        mask: true
      });
      
      // 灏濊瘯澶氱鏂规硶鑾峰彇鍓创鏉垮唴瀹?      this.getClipboardContentMultiMethod((text) => {
        // 闅愯棌鍔犺浇鎻愮ず
        uni.hideLoading();
        
        if (text) {
          // 鐩存帴杩藉姞鏂囨湰鍒版湯灏撅紝涓嶈€冭檻鍏夋爣浣嶇疆锛岀‘淇濆唴瀹硅兘琚坊鍔?          const newText = (this.modelValue || '') + text;
          this.$emit('update:modelValue', newText);
          
          // 鏇存柊UI
          this.$nextTick(() => {
            this.updateHeightAndCheck();
            this.forceScrollToBottom();
            
            // 鎻愪緵鎴愬姛鍙嶉
            uni.showToast({
              title: '宸茬矘璐村唴瀹?,
              icon: 'success',
              duration: 1500
            });
          });
        } else {
          // 鎻愪緵澶辫触鍙嶉
          uni.showToast({
            title: '鍓创鏉夸负绌烘垨鏃犳硶璁块棶',
            icon: 'none',
            duration: 1500
          });
        }
      });
    },
    
    // 浣跨敤澶氱鏂规硶灏濊瘯鑾峰彇鍓创鏉垮唴瀹?    getClipboardContentMultiMethod(callback) {
      let contentObtained = false;
      let timeoutId = setTimeout(() => {
        if (!contentObtained) {
          console.log('鑾峰彇鍓创鏉垮唴瀹硅秴鏃?);
          callback(''); // 瓒呮椂杩斿洖绌哄瓧绗︿覆
        }
      }, 3000); // 3绉掕秴鏃?      
      // 鏂规硶1: 浣跨敤plus.clipboard API
      if (typeof plus !== 'undefined' && plus.clipboard) {
        try {
          plus.clipboard.get((text) => {
            clearTimeout(timeoutId);
            contentObtained = true;
            
            if (text) {
              console.log('鎴愬姛閫氳繃plus.clipboard鑾峰彇鍐呭');
              callback(text);
            } else {
              // 灏濊瘯涓嬩竴涓柟娉?              this.tryUniClipboardAPI(callback);
            }
          }, (err) => {
            console.error('plus.clipboard.get澶辫触:', err);
            // 灏濊瘯涓嬩竴涓柟娉?            this.tryUniClipboardAPI(callback);
          });
        } catch (err) {
          console.error('浣跨敤plus.clipboard鍑洪敊:', err);
          // 灏濊瘯涓嬩竴涓柟娉?          this.tryUniClipboardAPI(callback);
        }
      } else {
        // 灏濊瘯涓嬩竴涓柟娉?        this.tryUniClipboardAPI(callback);
      }
    },
    
    // 灏濊瘯浣跨敤uni API鑾峰彇鍓创鏉垮唴瀹?    tryUniClipboardAPI(callback) {
      if (typeof uni !== 'undefined' && uni.getClipboardData) {
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              console.log('鎴愬姛閫氳繃uni.getClipboardData鑾峰彇鍐呭');
              callback(res.data);
            } else {
              // 灏濊瘯鍏朵粬鏂规硶
              this.tryNavigatorClipboard(callback);
            }
          },
          fail: (err) => {
            console.error('uni.getClipboardData澶辫触:', err);
            // 灏濊瘯鍏朵粬鏂规硶
            this.tryNavigatorClipboard(callback);
          }
        });
      } else {
        // 灏濊瘯鍏朵粬鏂规硶
        this.tryNavigatorClipboard(callback);
      }
    },
    
    // 灏濊瘯浣跨敤navigator.clipboard API
    tryNavigatorClipboard(callback) {
      if (navigator && navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText()
          .then((text) => {
            if (text) {
              console.log('鎴愬姛閫氳繃navigator.clipboard鑾峰彇鍐呭');
              callback(text);
            } else {
              // 灏濊瘯鍏朵粬鏂规硶
              this.tryDocumentExecCommand(callback);
            }
          })
          .catch((err) => {
            console.error('navigator.clipboard.readText澶辫触:', err);
            // 灏濊瘯鍏朵粬鏂规硶
            this.tryDocumentExecCommand(callback);
          });
      } else {
        // 灏濊瘯鍏朵粬鏂规硶
        this.tryDocumentExecCommand(callback);
      }
    },
    
    // 灏濊瘯浣跨敤document.execCommand
    tryDocumentExecCommand(callback) {
      try {
        if (document && document.execCommand) {
          const textarea = this.getTextareaElement();
          if (textarea) {
            textarea.focus();
            const result = document.execCommand('paste');
            if (result) {
              console.log('鎴愬姛閫氳繃document.execCommand鑾峰彇鍐呭');
              // 杩欑鏂规硶鐩存帴绮樿创鍒颁簡鏂囨湰妗嗭紝涓嶉渶瑕侀澶栧鐞?              callback(''); // 杩斿洖绌哄瓧绗︿覆锛屽洜涓哄唴瀹瑰凡缁忚鎻掑叆
            } else {
              console.log('document.execCommand("paste")澶辫触');
              callback(''); // 鎵€鏈夋柟娉曢兘澶辫触
            }
          } else {
            callback(''); // 鎵€鏈夋柟娉曢兘澶辫触
          }
        } else {
          callback(''); // 鎵€鏈夋柟娉曢兘澶辫触
        }
      } catch (err) {
        console.error('document.execCommand("paste")鍑洪敊:', err);
        callback(''); // 鎵€鏈夋柟娉曢兘澶辫触
      }
    },

    // 澶勭悊绯荤粺绮樿创鎸夐挳鐐瑰嚮
    handleSystemPaste() {
      console.log('绯荤粺绮樿创鎸夐挳琚偣鍑?);
      
      // 鑾峰彇鏂囨湰妗嗗厓绱?      const textarea = this.getTextareaElement();
      if (!textarea) {
        console.error('鎵句笉鍒版枃鏈鍏冪礌');
        return;
      }
      
      // 鑱氱劍鏂囨湰妗?      textarea.focus();
      
      try {
        // 灏濊瘯浣跨敤document.execCommand瑙﹀彂绮樿创
        if (document && document.execCommand) {
          const result = document.execCommand('paste');
          console.log('document.execCommand("paste")缁撴灉:', result);
          
          if (result) {
            // 鎴愬姛瑙﹀彂绮樿创
            uni.showToast({
              title: '绮樿创鎴愬姛',
              icon: 'success',
              duration: 1000
            });
          } else {
            // 濡傛灉execCommand杩斿洖false锛屾彁绀虹敤鎴锋墜鍔ㄧ矘璐?            uni.showToast({
              title: '璇蜂娇鐢–trl+V绮樿创',
              icon: 'none',
              duration: 1500
            });
            
            // 灏濊瘯鏄剧ず涓€涓彁绀猴紝寮曞鐢ㄦ埛浣跨敤绯荤粺绮樿创鍔熻兘
            setTimeout(() => {
              uni.showModal({
                title: '绮樿创鎻愮ず',
                content: '璇蜂娇鐢ㄧ郴缁熺矘璐村姛鑳斤細\n1. 闀挎寜鏂囨湰妗哱n2. 閫夋嫨"绮樿创"\n鎴栦娇鐢ㄩ敭鐩樺揩鎹烽敭Ctrl+V',
                showCancel: false,
                confirmText: '鐭ラ亾浜?
              });
            }, 500);
          }
        } else {
          // 濡傛灉document.execCommand涓嶅彲鐢紝鎻愮ず鐢ㄦ埛鎵嬪姩绮樿创
          uni.showToast({
            title: '璇蜂娇鐢–trl+V绮樿创',
            icon: 'none',
            duration: 1500
          });
        }
      } catch (err) {
        console.error('瑙﹀彂绯荤粺绮樿创澶辫触:', err);
        
        // 鎻愮ず鐢ㄦ埛鎵嬪姩绮樿创
        uni.showToast({
          title: '璇蜂娇鐢ㄧ郴缁熺矘璐村姛鑳?,
          icon: 'none',
          duration: 1500
        });
      }
    },

    // 鍒涘缓闅愯棌鐨勬枃浠惰緭鍏ュ厓绱犱綔涓哄閫夋柟妗?    createHiddenFileInput() {
      if (!this.isH5 || typeof document === 'undefined') return;
      
      try {
        // 妫€鏌ユ槸鍚﹀凡缁忓瓨鍦?        let fileInput = document.getElementById('hidden-image-input');
        if (fileInput) {
          document.body.removeChild(fileInput);
        }
        
        // 鍒涘缓鏂扮殑鏂囦欢杈撳叆鍏冪礌
        fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'hidden-image-input';
        fileInput.accept = 'image/*';
        fileInput.style.position = 'absolute';
        fileInput.style.top = '-9999px';
        fileInput.style.left = '-9999px';
        fileInput.style.opacity = '0';
        fileInput.style.pointerEvents = 'none';
        fileInput.style.visibility = 'hidden';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
        
        // 娣诲姞鍙樻洿浜嬩欢鐩戝惉鍣?        fileInput.addEventListener('change', (e) => {
          console.log('鏂囦欢閫夋嫨鍣ㄥ彉鏇翠簨浠惰Е鍙?);
          if (fileInput.files && fileInput.files.length > 0) {
            const file = fileInput.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('浠庢枃浠惰緭鍏ヨ幏鍙栧埌鍥剧墖:', file.type, file.name, file.size);
              
              // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                name: file.name,
                size: file.size,
                source: 'file-input'
              });
              
              // 閲嶇疆鏂囦欢杈撳叆锛屼互渚垮彲浠ュ啀娆￠€夋嫨鐩稿悓鐨勬枃浠?              fileInput.value = '';
            }
          }
        });
        
        // 娣诲姞鍒版枃妗?        document.body.appendChild(fileInput);
        
        console.log('鍒涘缓浜嗛殣钘忕殑鏂囦欢杈撳叆鍏冪礌浣滀负澶囬€夋柟妗?);
      } catch (err) {
        console.error('鍒涘缓闅愯棌鏂囦欢杈撳叆鍏冪礌澶辫触:', err);
      }
    },
    
    // 鏄剧ず鏂囦欢閫夋嫨鍣ㄤ綔涓虹矘璐村閫夋柟妗?    showFileSelector() {
      if (!this.isH5 || typeof document === 'undefined') return;
      
      try {
        let fileInput = document.getElementById('hidden-image-input');
        if (!fileInput) {
          // 濡傛灉涓嶅瓨鍦紝鍏堝垱寤?          this.createHiddenFileInput();
          fileInput = document.getElementById('hidden-image-input');
        }
        
        if (fileInput) {
          // 閲嶇疆value纭繚鑳藉閫夋嫨鐩稿悓鐨勬枃浠?          fileInput.value = '';
          
          // 纭繚鏂囦欢杈撳叆鍏冪礌鍙涓斿彲浜や簰
          fileInput.style.position = 'fixed';
          fileInput.style.top = '50%';
          fileInput.style.left = '50%';
          fileInput.style.transform = 'translate(-50%, -50%)';
          fileInput.style.opacity = '0';
          fileInput.style.pointerEvents = 'auto';
          fileInput.style.visibility = 'visible';
          fileInput.style.width = '100%';
          fileInput.style.height = '100%';
          fileInput.style.zIndex = '9999';
          
          // 鐐瑰嚮鏂囦欢杈撳叆鍏冪礌
          console.log('瑙﹀彂鏂囦欢閫夋嫨鍣ㄧ偣鍑?);
          fileInput.click();
          
          // 鐐瑰嚮鍚庢仮澶嶉殣钘忕姸鎬?          setTimeout(() => {
            fileInput.style.position = 'absolute';
            fileInput.style.top = '-9999px';
            fileInput.style.left = '-9999px';
            fileInput.style.transform = 'none';
            fileInput.style.pointerEvents = 'none';
            fileInput.style.visibility = 'hidden';
            fileInput.style.width = '1px';
            fileInput.style.height = '1px';
            fileInput.style.zIndex = '-1';
          }, 500);
        } else {
          console.error('鎵句笉鍒版枃浠惰緭鍏ュ厓绱?);
          
          // 灏濊瘯鐩存帴鍒涘缓骞剁偣鍑讳竴涓复鏃剁殑鏂囦欢杈撳叆
          const tempInput = document.createElement('input');
          tempInput.type = 'file';
          tempInput.accept = 'image/*';
          tempInput.style.position = 'fixed';
          tempInput.style.top = '50%';
          tempInput.style.left = '50%';
          tempInput.style.transform = 'translate(-50%, -50%)';
          tempInput.style.opacity = '0';
          tempInput.style.zIndex = '9999';
          
          tempInput.addEventListener('change', (e) => {
            if (tempInput.files && tempInput.files.length > 0) {
              const file = tempInput.files[0];
              if (file && file.type.indexOf('image') !== -1) {
                console.log('浠庝复鏃舵枃浠惰緭鍏ヨ幏鍙栧埌鍥剧墖:', file.type);
                
                // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                this.$emit('paste-image', {
                  blob: file,
                  type: file.type,
                  source: 'temp-file-input'
                });
              }
              
              // 娓呯悊涓存椂鍏冪礌
              document.body.removeChild(tempInput);
            }
          });
          
          document.body.appendChild(tempInput);
          tempInput.click();
        }
      } catch (err) {
        console.error('鏄剧ず鏂囦欢閫夋嫨鍣ㄥけ璐?', err);
        
        // 鏈€鍚庣殑澶囬€夋柟妗堬細浣跨敤uni API
        if (typeof uni !== 'undefined' && uni.chooseImage) {
          uni.chooseImage({
            count: 1,
            success: (res) => {
              if (res.tempFilePaths && res.tempFilePaths.length > 0) {
                this.$emit('paste-image', {
                  path: res.tempFilePaths[0],
                  type: 'image/png',
                  source: 'uni-choose-image'
                });
              }
            }
          });
        }
      }
    },

    // 灏濊瘯浣跨敤navigator.clipboard API绮樿创鍥剧墖
    tryPasteImageWithNavigatorClipboard() {
      if (!this.isH5 || typeof navigator === 'undefined' || !navigator.clipboard) {
        return;
      }
      
      console.log('灏濊瘯浣跨敤navigator.clipboard API绮樿创鍥剧墖');
      
      try {
        // 妫€鏌ユ槸鍚︽敮鎸乺ead鏂规硶
        if (navigator.clipboard.read) {
          navigator.clipboard.read()
            .then(clipboardItems => {
              console.log('鎴愬姛璇诲彇鍓创鏉块」:', clipboardItems.length);
              
              let imagePromises = [];
              
              // 澶勭悊鎵€鏈夊壀璐存澘椤?              for (const clipboardItem of clipboardItems) {
                // 妫€鏌ュ彲鐢ㄧ殑绫诲瀷
                for (const type of clipboardItem.types) {
                  if (type.startsWith('image/')) {
                    console.log('妫€娴嬪埌鍥剧墖绫诲瀷:', type);
                    
                    // 鑾峰彇鍥剧墖blob
                    const imagePromise = clipboardItem.getType(type)
                      .then(blob => {
                        console.log('鑾峰彇鍒板浘鐗嘼lob:', blob.size);
                        
                        // 瑙﹀彂鍥剧墖绮樿创浜嬩欢
                        this.$emit('paste-image', {
                          blob: blob,
                          type: type,
                          source: 'navigator-clipboard-read'
                        });
      
      return true;
                      })
                      .catch(err => {
                        console.error('鑾峰彇鍥剧墖blob澶辫触:', err);
                        return false;
                      });
                    
                    imagePromises.push(imagePromise);
                    break;
                  }
                }
              }
              
              // 濡傛灉娌℃湁鎵惧埌鍥剧墖锛屾鏌ユ槸鍚︽湁鍏朵粬澶囬€夋柟妗?              if (imagePromises.length === 0) {
                console.log('鍓创鏉夸腑娌℃湁妫€娴嬪埌鍥剧墖锛屽皾璇曞叾浠栨柟娉?);
                this.tryPasteImageViaEvent();
              }
              
              return Promise.all(imagePromises);
            })
            .catch(err => {
              console.error('璇诲彇鍓创鏉垮け璐?', err);
              
              // 濡傛灉鏄潈闄愰敊璇紝鎻愮ず鐢ㄦ埛
              if (err.name === 'NotAllowedError' || err.message.includes('permission')) {
                console.log('鍓创鏉胯闂鎷掔粷锛屽彲鑳介渶瑕佹潈闄?);
                
                // 鏄剧ず鏉冮檺鎻愮ず
                if (typeof uni !== 'undefined' && uni.showModal) {
                  uni.showModal({
                    title: '闇€瑕佸壀璐存澘鏉冮檺',
                    content: '璇峰湪娴忚鍣ㄨ缃腑鍏佽璁块棶鍓创鏉匡紝鎴栦娇鐢ㄦ枃浠堕€夋嫨鍣ㄤ笂浼犲浘鐗?,
                    confirmText: '閫夋嫨鍥剧墖',
                    cancelText: '鍙栨秷',
                    success: (res) => {
                      if (res.confirm) {
                        this.showFileSelector();
                      }
                    }
                  });
                } else {
                  // 鏅€氭祻瑙堝櫒鐜
                  if (confirm('鏃犳硶璁块棶鍓创鏉匡紝鏄惁浣跨敤鏂囦欢閫夋嫨鍣ㄤ笂浼犲浘鐗囷紵')) {
                    this.showFileSelector();
                  }
                }
              } else {
                // 鍏朵粬閿欒锛屽皾璇曞閫夋柟妗?                this.tryPasteImageViaEvent();
              }
            });
        } else {
          // 涓嶆敮鎸乺ead鏂规硶锛屽皾璇曞閫夋柟妗?          console.log('navigator.clipboard.read涓嶅彲鐢紝灏濊瘯鍏朵粬鏂规硶');
          this.tryPasteImageViaEvent();
        }
      } catch (err) {
        console.error('浣跨敤navigator.clipboard API澶辫触:', err);
        this.tryPasteImageViaEvent();
      }
    },

    // 澶勭悊鍥剧墖绮樿创鎸夐挳鐐瑰嚮
    handleImagePasteButtonClick() {
      console.log('鍥剧墖绮樿创鎸夐挳琚偣鍑?);
      
      // 鍦℉5鐜涓嬶紝鐩存帴鎵撳紑鏂囦欢閫夋嫨鍣?      if (this.isH5) {
        this.showFileSelector();
        return;
      }
      
      // 鍦ㄥ叾浠栫幆澧冧笅锛屽皾璇曚粠鍓创鏉胯幏鍙栧浘鐗?      this.tryPasteImage();
    },
  }
};
</script>

<style>
.optimized-textarea-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: visible;
  box-sizing: border-box;
}

.optimized-textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 12px 16px;
  padding-right: 60px; /* 浠呬负鍙戦€佹寜閽暀鍑虹┖闂?*/
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid rgba(77, 157, 255, 0.3);
  border-radius: 30px;
  background-color: rgba(45, 45, 55, 0.6);
  color: rgba(255, 255, 255, 0.9);
  outline: none;
  resize: none;
  transition: border-color 0.3s, background-color 0.3s;
  overflow-y: auto !important; 
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: auto; /* 鍏佽姝ｅ父婊氬姩琛屼负 */
  touch-action: auto; /* 淇濇寔鍘熺敓瑙︽懜琛屼负 */
  position: relative;
  /* 纭繚鏂囨湰鍙€夋嫨 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  /* 鍏佽iOS闀挎寜鏄剧ず鑿滃崟 */
  -webkit-touch-callout: default !important;
  /* 璁剧疆鏂囨湰鍏夋爣 */
  cursor: text;
  /* 绉婚櫎鍙兘骞叉壈婊氬姩鐨勫睘鎬?*/
  transform: none;
  -webkit-transform: none;
  will-change: auto;
  scroll-behavior: auto;
}

.optimized-textarea:focus {
  border-color: rgba(77, 157, 255, 0.8);
  box-shadow: 0 0 0 2px rgba(77, 157, 255, 0.3), 0 2px 12px rgba(77, 157, 255, 0.4);
  background-color: rgba(40, 40, 60, 0.6);
}

.animated-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  pointer-events: none;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  transition: opacity 0.3s;
}

.is-focused .placeholder-text {
  opacity: 0.6;
}

.h5-platform .optimized-textarea::-webkit-scrollbar {
  width: 3px;
  background-color: transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  transition: background-color 0.3s;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

.h5-platform .optimized-textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 浼樺寲婊氬姩鎻愮ず鎸囩ず鍣?*/
.optimized-textarea-container.has-overflow::after {
  content: '';
  position: absolute;
  bottom: 10px;
  right: 55px;
  width: 4px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(77, 157, 255, 0.7), transparent);
  border-radius: 2px;
  pointer-events: none;
  opacity: 0.8;
  animation: scrollIndicator 1.5s infinite;
}

@keyframes scrollIndicator {
  0% { opacity: 0.4; height: 15px; }
  50% { opacity: 0.8; height: 20px; }
  100% { opacity: 0.4; height: 15px; }
}

/* iOS璁惧浼樺寲 */
@supports (-webkit-touch-callout: none) {
  .optimized-textarea {
    -webkit-overflow-scrolling: touch !important;
    padding-top: 13px; /* 寰皟iOS涓婄殑鏂囧瓧浣嶇疆 */
    -webkit-tap-highlight-color: rgba(77, 157, 255, 0.2);
    /* 鍏佽iOS鏂囨湰閫夋嫨鑿滃崟 */
    -webkit-touch-callout: default !important;
    -webkit-user-select: text !important;
    /* 绉婚櫎骞叉壈婊氬姩鐨勬牱寮?*/
    -webkit-transform: none;
    transform: none;
    backface-visibility: visible;
  }
  
  /* iOS璁惧涓婂姞寮烘枃鏈€夋嫨鏍峰紡 */
  .optimized-textarea::selection {
    background-color: rgba(77, 157, 255, 0.4);
  }
}

.debug-bottom-marker {
  position: absolute;
  right: 8px;
  bottom: 8px;
  color: rgba(0, 255, 255, 0.7);
  font-size: 16px;
  pointer-events: none;
  z-index: 100;
}

.h5-platform .optimized-textarea {
  overflow-y: auto !important;
}

.mp-platform .optimized-textarea {
  position: relative;
  z-index: 1;
}

.app-platform .optimized-textarea {
  position: relative;
  z-index: 1;
}

.has-overflow .optimized-textarea {
  box-shadow: inset 0 -8px 8px -8px rgba(77, 157, 255, 0.3);
}

@media (pointer: coarse) {
  .optimized-textarea {
    font-size: 16px;
  }
  
  .h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
    width: 6px;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.show-indicator {
  opacity: 1;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.3; width: 30px; }
  100% { opacity: 0.7; width: 40px; }
}

.debug-scroll-indicator {
  position: absolute;
  right: 5px;
  width: 3px;
  background: rgba(100, 200, 255, 0.5);
  border-radius: 3px;
  pointer-events: none;
  z-index: 100;
}

@keyframes pulse-border {
  0% { border-color: rgba(77, 157, 255, 0.3); }
  50% { border-color: rgba(77, 157, 255, 0.6); }
  100% { border-color: rgba(77, 157, 255, 0.3); }
}

.optimized-textarea.at-top,
.optimized-textarea.at-bottom {
  animation: pulse-border 1s ease infinite;
}

.optimized-textarea.at-top {
  box-shadow: inset 0 4px 8px -4px rgba(77, 157, 255, 0.3);
}

.optimized-textarea.at-bottom {
  box-shadow: inset 0 -4px 8px -4px rgba(77, 157, 255, 0.3);
}

.send-button-slot {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optimized-textarea-container.show-bottom-indicator::after {
  content: '鈫?;
  position: absolute;
  bottom: 8px;
  right: 45px;
  width: 20px;
  height: 20px;
  background-color: rgba(77, 157, 255, 0.7);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  animation: bounce 1s infinite alternate;
  pointer-events: none;
  z-index: 10;
}

@keyframes bounce {
  0% { transform: translateY(0); }
  100% { transform: translateY(-3px); }
}

.h5-platform .optimized-textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.4) transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar {
  width: 5px;
  background-color: transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 10px;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 鏂囨湰閫夋嫨鏍峰紡 */
.optimized-textarea::selection {
  background: rgba(77, 157, 255, 0.4);
  color: #ffffff;
}

/* Android璁惧浼樺寲 */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  .optimized-textarea {
    overflow-y: auto;
    scrollbar-width: thin;
    /* 纭繚Android涓婄殑婊氬姩琛屼负姝ｅ父 */
    touch-action: auto;
    -webkit-transform: none;
    transform: none;
  }
}

/* 瑙︽懜鐘舵€佷紭鍖?*/
.optimized-textarea-container.selecting .optimized-textarea {
  /* 鏂囨湰閫夋嫨妯″紡涓嬩紭鍖栨牱寮?*/
  touch-action: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -webkit-touch-callout: default !important;
  /* 娣诲姞瑙嗚鍙嶉 */
  background-color: rgba(50, 50, 70, 0.7) !important;
  border-color: rgba(77, 157, 255, 0.7) !important;
  box-shadow: inset 0 0 0 1px rgba(77, 157, 255, 0.8) !important;
}

/* APP鐜涓嬫枃鏈€夋嫨寮哄寲 */
.app-platform .optimized-textarea {
  caret-color: rgba(77, 157, 255, 0.9);
  -webkit-user-select: auto;
  user-select: auto;
  -webkit-touch-callout: default;
  touch-callout: default;
  transition-property: border-color, background-color;
  transition-duration: 0.3s;
}

/* 鍔犲己鏂囨湰閫夋嫨瑙嗚鏍峰紡 */
.optimized-textarea::selection {
  background-color: rgba(77, 157, 255, 0.5) !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* HBuilder鐜鐗规畩鏍峰紡 */
.optimized-textarea.selecting-text {
  background-color: rgba(50, 50, 70, 0.7) !important;
  border-color: rgba(77, 157, 255, 0.7) !important;
  box-shadow: inset 0 0 0 1px rgba(77, 157, 255, 0.8) !important;
}

/* 绮樿创鎸夐挳鏍峰紡 */
.paste-button {
  display: none;
}

.paste-button:active {
  display: none;
}

.paste-icon {
  display: none;
}

.paste-text {
  display: none;
}

/* 绮樿创鎸夐挳鏍峰紡 - 澶у彿 */
.paste-button-large {
  display: none;
}

.paste-button-large:active {
  display: none;
}

@keyframes pulse-button {
  0% { box-shadow: 0 0 0 0 rgba(77, 157, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(77, 157, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(77, 157, 255, 0); }
}

/* 绮樿创鎸夐挳鏍峰紡 - 灏忓彿 */
.paste-button-small {
  display: none;
}

.paste-button-small:active {
  display: none;
}

.paste-button-hbuilder {
  display: none;
}

.paste-button-hbuilder:active {
  display: none;
}

.paste-icon-text {
  display: none;
}

/* HBuilder鐜鐗规畩鏍峰紡 */
.hbuilder-textarea {
  -webkit-user-select: text !important;
  user-select: text !important;
  -webkit-touch-callout: default !important;
  cursor: text !important;
  -webkit-tap-highlight-color: rgba(77, 157, 255, 0.3) !important;
}

.image-paste-button {
  position: absolute;
  right: 60px;
  bottom: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(77, 157, 255, 0.7);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.image-paste-button:hover {
  background-color: rgba(77, 157, 255, 0.9);
  transform: scale(1.05);
}

.image-paste-button:active {
  transform: scale(0.95);
  background-color: rgba(77, 157, 255, 1);
}

.image-paste-icon {
  font-size: 20px;
  color: white;
}
</style> 

