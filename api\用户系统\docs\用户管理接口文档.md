# 用户系统 - 用户管理接口文档

## 📋 **功能概述**

用户系统负责用户注册、登录、信息管理、权限控制等核心功能。

## 🔧 **核心接口**

### **1. 用户注册**
```
POST /api/v1/user/register
Content-Type: application/json

请求体：
{
    "username": "用户名",
    "password": "密码",
    "email": "邮箱",
    "phone": "手机号",
    "verificationCode": "验证码"
}

响应：
{
    "success": true,
    "data": {
        "userId": "user_123",
        "username": "用户名",
        "email": "邮箱",
        "token": "JWT令牌",
        "expiresIn": 7200
    },
    "message": "注册成功"
}
```

### **2. 用户登录**
```
POST /api/v1/user/login
Content-Type: application/json

请求体：
{
    "loginType": "username|email|phone",
    "loginValue": "登录凭据",
    "password": "密码",
    "rememberMe": true
}

响应：
{
    "success": true,
    "data": {
        "userId": "user_123",
        "username": "用户名",
        "email": "邮箱",
        "phone": "手机号",
        "avatar": "头像URL",
        "isMember": false,
        "memberExpiry": null,
        "balance": 100,
        "token": "JWT令牌",
        "expiresIn": 7200
    },
    "message": "登录成功"
}
```

### **3. 获取用户信息**
```
GET /api/v1/user/profile
Authorization: Bearer {token}

响应：
{
    "success": true,
    "data": {
        "userId": "user_123",
        "username": "用户名",
        "email": "邮箱",
        "phone": "手机号",
        "avatar": "头像URL",
        "realName": "真实姓名",
        "gender": "male|female",
        "birthday": "1990-01-01",
        "location": "所在地",
        "isMember": false,
        "memberExpiry": null,
        "balance": 100,
        "totalSpent": 500,
        "registerTime": "2024-01-01T00:00:00.000Z",
        "lastLoginTime": "2024-01-11T10:30:00.000Z",
        "status": "active|suspended|banned"
    }
}
```

### **4. 更新用户信息**
```
PUT /api/v1/user/profile
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "username": "新用户名",
    "email": "新邮箱",
    "phone": "新手机号",
    "realName": "真实姓名",
    "gender": "male|female",
    "birthday": "1990-01-01",
    "location": "所在地",
    "avatar": "头像URL"
}

响应：
{
    "success": true,
    "data": {
        "userId": "user_123",
        "updatedFields": ["username", "email"],
        "profile": { /* 更新后的用户信息 */ }
    },
    "message": "信息更新成功"
}
```

### **5. 修改密码**
```
PUT /api/v1/user/password
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "oldPassword": "旧密码",
    "newPassword": "新密码",
    "confirmPassword": "确认新密码"
}

响应：
{
    "success": true,
    "message": "密码修改成功"
}
```

### **6. 忘记密码**
```
POST /api/v1/user/forgot-password
Content-Type: application/json

请求体：
{
    "email": "邮箱地址"
}

响应：
{
    "success": true,
    "message": "重置密码邮件已发送"
}
```

### **7. 重置密码**
```
POST /api/v1/user/reset-password
Content-Type: application/json

请求体：
{
    "token": "重置令牌",
    "newPassword": "新密码",
    "confirmPassword": "确认新密码"
}

响应：
{
    "success": true,
    "message": "密码重置成功"
}
```

### **8. 用户注销**
```
POST /api/v1/user/logout
Authorization: Bearer {token}

响应：
{
    "success": true,
    "message": "注销成功"
}
```

### **9. 发送验证码**
```
POST /api/v1/user/send-verification
Content-Type: application/json

请求体：
{
    "type": "email|sms",
    "target": "邮箱或手机号",
    "purpose": "register|login|reset|bind"
}

响应：
{
    "success": true,
    "data": {
        "expiresIn": 300,
        "canResendAfter": 60
    },
    "message": "验证码已发送"
}
```

### **10. 绑定第三方账号**
```
POST /api/v1/user/bind-oauth
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "provider": "wechat|qq|weibo",
    "code": "授权码",
    "state": "状态参数"
}

响应：
{
    "success": true,
    "data": {
        "provider": "wechat",
        "openId": "第三方用户ID",
        "nickname": "第三方昵称",
        "avatar": "第三方头像"
    },
    "message": "绑定成功"
}
```

## 📊 **数据模型**

### **用户表结构**
```sql
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    real_name VARCHAR(50),
    gender ENUM('male', 'female'),
    birthday DATE,
    location VARCHAR(100),
    avatar VARCHAR(255),
    is_member BOOLEAN DEFAULT FALSE,
    member_expiry DATETIME,
    balance INT DEFAULT 0,
    total_spent INT DEFAULT 0,
    register_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_time DATETIME,
    status ENUM('active', 'suspended', 'banned') DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### **第三方绑定表**
```sql
CREATE TABLE user_oauth_bindings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    provider ENUM('wechat', 'qq', 'weibo') NOT NULL,
    open_id VARCHAR(100) NOT NULL,
    nickname VARCHAR(100),
    avatar VARCHAR(255),
    bind_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY unique_binding (user_id, provider),
    UNIQUE KEY unique_openid (provider, open_id)
);
```

### **登录日志表**
```sql
CREATE TABLE user_login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    login_type ENUM('username', 'email', 'phone', 'oauth') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_time (user_id, login_time),
    INDEX idx_ip_time (ip_address, login_time)
);
```

## 🔐 **权限控制**

### **用户状态**
- `active`: 正常用户，可以使用所有功能
- `suspended`: 暂停用户，限制部分功能
- `banned`: 封禁用户，禁止登录

### **会员权限**
- 普通用户：基础功能，标准费用
- 会员用户：高级功能，费用8折，优先处理

### **API权限验证**
```javascript
// 中间件示例
function requireAuth(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
        return res.status(401).json({
            success: false,
            error: { code: "AUTH_001", message: "未提供认证令牌" }
        });
    }
    
    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(401).json({
            success: false,
            error: { code: "AUTH_002", message: "认证令牌无效" }
        });
    }
}
```

## 🚨 **错误码说明**

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| USER_001 | 用户名已存在 | 400 |
| USER_002 | 邮箱已存在 | 400 |
| USER_003 | 手机号已存在 | 400 |
| USER_004 | 用户名或密码错误 | 401 |
| USER_005 | 用户不存在 | 404 |
| USER_006 | 用户已被封禁 | 403 |
| USER_007 | 验证码错误或已过期 | 400 |
| USER_008 | 密码格式不符合要求 | 400 |
| USER_009 | 邮箱格式不正确 | 400 |
| USER_010 | 手机号格式不正确 | 400 |
| AUTH_001 | 未提供认证令牌 | 401 |
| AUTH_002 | 认证令牌无效 | 401 |
| AUTH_003 | 认证令牌已过期 | 401 |
| AUTH_004 | 权限不足 | 403 |

## 🔒 **安全措施**

### **密码安全**
- 密码最少8位，包含字母和数字
- 使用bcrypt加密存储
- 支持密码强度检测

### **登录安全**
- 登录失败5次锁定账户30分钟
- 记录登录日志和异常登录
- 支持双因素认证（可选）

### **数据安全**
- 敏感信息加密存储
- API接口HTTPS传输
- 定期清理过期令牌

---

**文档版本**: v1.0  
**创建时间**: 2025-01-11  
**维护团队**: 用户系统组
