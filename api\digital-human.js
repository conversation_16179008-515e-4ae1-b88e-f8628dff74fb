/**
 * 数字人相关API接口
 */

import request from './request.js'

// API基础路径
const API_BASE = '/api/digital-human'

/**
 * 数字人管理相关接口
 */
export const avatarAPI = {
  /**
   * 上传数字人视频
   * @param {File} file 视频文件
   * @param {Object} options 上传选项
   */
  upload(file, options = {}) {
    const formData = new FormData()
    formData.append('video', file)
    formData.append('name', options.name || '')
    formData.append('description', options.description || '')
    
    return request({
      url: `${API_BASE}/avatar/upload`,
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000 // 上传超时时间60秒
    })
  },

  /**
   * 获取用户的数字人列表
   * @param {Object} params 查询参数
   */
  getList(params = {}) {
    return request({
      url: `${API_BASE}/avatar/list`,
      method: 'GET',
      data: {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        type: params.type || 'all' // all, custom, preset
      }
    })
  },

  /**
   * 获取预设数字人列表
   */
  getPresetList() {
    return request({
      url: `${API_BASE}/avatar/preset`,
      method: 'GET'
    })
  },

  /**
   * 删除数字人
   * @param {String} avatarId 数字人ID
   */
  delete(avatarId) {
    return request({
      url: `${API_BASE}/avatar/${avatarId}`,
      method: 'DELETE'
    })
  },

  /**
   * 收藏/取消收藏数字人
   * @param {String} avatarId 数字人ID
   * @param {Boolean} isFavorite 是否收藏
   */
  toggleFavorite(avatarId, isFavorite) {
    return request({
      url: `${API_BASE}/avatar/${avatarId}/favorite`,
      method: 'PUT',
      data: { isFavorite }
    })
  },

  /**
   * 获取数字人详情
   * @param {String} avatarId 数字人ID
   */
  getDetail(avatarId) {
    return request({
      url: `${API_BASE}/avatar/${avatarId}`,
      method: 'GET'
    })
  }
}

/**
 * 音色管理相关接口
 */
export const voiceAPI = {
  /**
   * 获取预设音色列表
   */
  getPresetList() {
    return request({
      url: `${API_BASE}/voice/preset`,
      method: 'GET'
    })
  },

  /**
   * 上传自定义音色
   * @param {File} file 音频文件
   * @param {Object} options 上传选项
   */
  uploadCustom(file, options = {}) {
    const formData = new FormData()
    formData.append('audio', file)
    formData.append('name', options.name || '')
    formData.append('description', options.description || '')
    
    return request({
      url: `${API_BASE}/voice/upload`,
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 从视频提取音色
   * @param {String} avatarId 数字人ID
   */
  extractFromVideo(avatarId) {
    return request({
      url: `${API_BASE}/voice/extract`,
      method: 'POST',
      data: { avatarId }
    })
  },

  /**
   * 获取用户自定义音色列表
   */
  getCustomList() {
    return request({
      url: `${API_BASE}/voice/custom`,
      method: 'GET'
    })
  },

  /**
   * 删除自定义音色
   * @param {String} voiceId 音色ID
   */
  deleteCustom(voiceId) {
    return request({
      url: `${API_BASE}/voice/custom/${voiceId}`,
      method: 'DELETE'
    })
  },

  /**
   * 音色预览
   * @param {String} voiceId 音色ID
   * @param {String} text 预览文本
   */
  preview(voiceId, text = '这是音色预览测试') {
    return request({
      url: `${API_BASE}/voice/preview`,
      method: 'POST',
      data: { voiceId, text }
    })
  }
}

/**
 * 数字人生成相关接口
 */
export const generationAPI = {
  /**
   * 创建生成任务
   * @param {Object} params 生成参数
   */
  create(params) {
    return request({
      url: `${API_BASE}/generation/create`,
      method: 'POST',
      data: {
        avatarId: params.avatarId,
        text: params.text,
        voiceId: params.voiceId,
        voiceType: params.voiceType, // preset, custom, video
        settings: params.settings || {}
      }
    })
  },

  /**
   * 获取生成任务状态
   * @param {String} taskId 任务ID
   */
  getStatus(taskId) {
    return request({
      url: `${API_BASE}/generation/${taskId}/status`,
      method: 'GET'
    })
  },

  /**
   * 获取生成历史列表
   * @param {Object} params 查询参数
   */
  getHistory(params = {}) {
    return request({
      url: `${API_BASE}/generation/history`,
      method: 'GET',
      data: {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        status: params.status || 'all', // all, pending, processing, completed, failed
        startDate: params.startDate,
        endDate: params.endDate
      }
    })
  },

  /**
   * 删除生成记录
   * @param {String} taskId 任务ID
   */
  delete(taskId) {
    return request({
      url: `${API_BASE}/generation/${taskId}`,
      method: 'DELETE'
    })
  },

  /**
   * 批量删除生成记录
   * @param {Array} taskIds 任务ID数组
   */
  batchDelete(taskIds) {
    return request({
      url: `${API_BASE}/generation/batch-delete`,
      method: 'POST',
      data: { taskIds }
    })
  },

  /**
   * 下载生成结果
   * @param {String} taskId 任务ID
   */
  download(taskId) {
    return request({
      url: `${API_BASE}/generation/${taskId}/download`,
      method: 'GET',
      responseType: 'blob'
    })
  },

  /**
   * 获取生成结果详情
   * @param {String} taskId 任务ID
   */
  getResult(taskId) {
    return request({
      url: `${API_BASE}/generation/${taskId}/result`,
      method: 'GET'
    })
  }
}

/**
 * 系统配置相关接口
 */
export const configAPI = {
  /**
   * 获取系统配置
   */
  getConfig() {
    return request({
      url: `${API_BASE}/config`,
      method: 'GET'
    })
  },

  /**
   * 获取用户配额信息
   */
  getQuota() {
    return request({
      url: `${API_BASE}/quota`,
      method: 'GET'
    })
  },

  /**
   * 获取支持的文件格式
   */
  getSupportedFormats() {
    return request({
      url: `${API_BASE}/formats`,
      method: 'GET'
    })
  }
}

/**
 * WebSocket连接管理（用于实时进度更新）
 */
export class GenerationWebSocket {
  constructor() {
    this.ws = null
    this.listeners = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
  }

  /**
   * 连接WebSocket
   * @param {String} taskId 任务ID
   */
  connect(taskId) {
    const wsUrl = `${process.env.VUE_APP_WS_BASE_URL}/digital-human/progress/${taskId}`
    
    this.ws = new WebSocket(wsUrl)
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.reconnectAttempts = 0
      this.emit('connected')
    }
    
    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.emit('progress', data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }
    
    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      this.emit('disconnected')
      this.attemptReconnect(taskId)
    }
    
    this.ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      this.emit('error', error)
    }
  }

  /**
   * 尝试重连
   */
  attemptReconnect(taskId) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect(taskId)
      }, this.reconnectInterval)
    } else {
      console.log('达到最大重连次数，停止重连')
      this.emit('reconnectFailed')
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.listeners.clear()
  }

  /**
   * 添加事件监听器
   * @param {String} event 事件名
   * @param {Function} callback 回调函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   * @param {String} event 事件名
   * @param {Function} callback 回调函数
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param {String} event 事件名
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调执行失败:', error)
        }
      })
    }
  }
}

// 导出默认对象
export default {
  avatar: avatarAPI,
  voice: voiceAPI,
  generation: generationAPI,
  config: configAPI,
  WebSocket: GenerationWebSocket
}
