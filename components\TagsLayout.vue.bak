<template>
  <view class="tags-rows">
    <!-- 第一行 - 常用提示词 -->
    <scroll-view 
      class="tags-row-scroll" 
      scroll-x 
      show-scrollbar="false"
      enhanced="true"
      bounce="true"
    >
      <view class="tags-row">
        <view 
          class="preset-tag" 
          v-for="(tag, index) in presetPromptTags"
          :key="'prompt-'+index"
          @tap="emitTagClick(tag)"
          :style="{ backgroundColor: getTagColor(index) }"
        >
          {{ tag.title }}
        </view>
      </view>
    </scroll-view>
    
    <!-- 第二行 - 类型标签 -->
    <scroll-view 
      class="tags-row-scroll" 
      scroll-x 
      show-scrollbar="false"
      enhanced="true"
      bounce="true"
    >
      <view class="tags-row">
        <view 
          class="preset-tag" 
          v-for="(tag, index) in typeTags"
          :key="'type-'+index"
          @tap="emitTagClick(tag)"
          :style="{ backgroundColor: getTagColor(index + 100) }"
        >
          {{ tag.title }}
        </view>
      </view>
    </scroll-view>
    
    <!-- 第三行 - 风格标签 -->
    <scroll-view 
      class="tags-row-scroll" 
      scroll-x 
      show-scrollbar="false"
      enhanced="true"
      bounce="true"
    >
      <view class="tags-row">
        <view 
          class="preset-tag" 
          v-for="(tag, index) in styleTags"
          :key="'style-'+index"
          @tap="emitTagClick(tag)"
          :style="{ backgroundColor: getTagColor(index + 50) }"
        >
          {{ tag.title }}
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'TagsLayout',
  props: {
    presetPromptTags: {
      type: Array,
      default: () => []
    },
    typeTags: {
      type: Array,
      default: () => []
    },
    styleTags: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    emitTagClick(tag) {
      this.$emit('tag-click', tag);
    },
    getTagColor(index) {
      // 扩展颜色集合，增加更多鲜艳的颜色
      const expandedColors = [
        '#4CAF50', // 绿色
        '#2196F3', // 蓝色
        '#FF9800', // 橙色
        '#9C27B0', // 紫色
        '#F44336', // 红色
        '#009688', // 青绿色
        '#E91E63', // 粉色
        '#3F51B5', // 靛蓝色
        '#CDDC39', // 酸橙色
        '#FF5722', // 深橙色
        '#607D8B', // 蓝灰色
        '#00BCD4', // 天青色
        '#FFC107', // 琥珀色
        '#795548', // 棕色
        '#673AB7', // 深紫色
        '#8BC34A'  // 浅绿色
      ];
      
      // 为风格标签和类型标签保证不同的颜色序列
      if (index >= 100) {
        // 使用乘数为类型标签提供不同的颜色顺序
        index = ((index - 100) * 7) % expandedColors.length;
      } else if (index >= 10) {
        // 使用不同的因子为风格标签提供不同的颜色排序
        index = ((index - 10) * 5 + 3) % expandedColors.length;
      } else {
        // 使用素数19来创建伪随机序列
        index = (index * 19) % expandedColors.length;
      }
      
      return expandedColors[index];
    }
  }
}
</script>

<style>
/* 三行布局的标签样式 */
.tags-rows {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 15rpx;
}

/* 标签行滚动区域 */
.tags-row-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 10rpx;
}

.tags-row {
  display: inline-flex;
  padding: 4rpx;
}

.preset-tag {
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #ffffff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  margin-right: 16rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.preset-tag:active {
  transform: scale(0.95);
  opacity: 0.7;
}

/* H5平台特定样式 */
/* #ifdef H5 */
.tags-row-scroll::-webkit-scrollbar {
  height: 4px;
}

.tags-row-scroll::-webkit-scrollbar-track {
  background: rgba(30, 30, 50, 0.1);
  border-radius: 2px;
}

.tags-row-scroll::-webkit-scrollbar-thumb {
  background: rgba(110, 86, 207, 0.3);
  border-radius: 2px;
}

.tags-row-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(110, 86, 207, 0.5);
}
/* #endif */

/* 移动端平台特定样式 */
/* #ifdef APP-PLUS || MP */
.tags-row-scroll {
  -webkit-overflow-scrolling: touch;
}
/* #endif */
</style> 