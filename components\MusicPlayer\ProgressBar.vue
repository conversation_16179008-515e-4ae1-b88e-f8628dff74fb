<template>
	<view class="progress-bar-container" :style="themeStyles">
		<!-- 播放/暂停按钮 -->
		<view class="play-btn" :class="{ playing: isPlaying }" @click="handleTogglePlay">
			<svg v-if="!isPlaying" class="play-icon" viewBox="0 0 24 24" fill="currentColor">
				<polygon points="5 3 19 12 5 21 5 3"></polygon>
			</svg>
			<svg v-else class="pause-icon" viewBox="0 0 24 24" fill="currentColor">
				<rect x="6" y="4" width="4" height="16"></rect>
				<rect x="14" y="4" width="4" height="16"></rect>
			</svg>
		</view>
		
		<!-- 进度条区域 -->
		<view class="progress-area">
			<!-- 时间和进度条 -->
			<view class="progress-wrapper">
				<view 
					class="progress-track"
					@click="handleProgressClick"
					@touchstart="handleTouchStart"
					@touchmove="handleTouchMove"
					@touchend="handleTouchEnd"
				>
					<view class="progress-bg"></view>
					<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					<view 
						class="progress-thumb" 
						:class="{ dragging: isDragging }"
						:style="{ left: progressPercent + '%' }"
					></view>
				</view>
			</view>
			
			<!-- 时间显示 -->
			<view class="time-row">
				<text class="time-text">{{ formatTime(currentTime) }}</text>
				<text class="time-text">{{ formatTime(duration) }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getTheme } from '@/utils/playerThemes.js';

export default {
	name: 'ProgressBar',
	props: {
		// 当前播放时间(秒)
		currentTime: {
			type: Number,
			default: 0
		},
		// 总时长(秒)
		duration: {
			type: Number,
			default: 0
		},
		// 是否正在播放
		isPlaying: {
			type: Boolean,
			default: false
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	data() {
		return {
			isDragging: false,
			dragStartX: 0,
			dragStartTime: 0,
			progressBarWidth: 0
		};
	},
	computed: {
		// 进度百分比
		progressPercent() {
			if (this.duration === 0) return 0;
			return Math.min(100, (this.currentTime / this.duration) * 100);
		},
		
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--primary-color': theme.colors.primary,
				'--progress-bg': theme.colors.progressBg,
				'--progress-fill': theme.colors.progressFill,
				'--text-secondary': theme.colors.textSecondary,
				'--icon-color': theme.colors.iconColor,
				'--button-bg': theme.colors.buttonBg,
				'--shadow-color': theme.colors.shadowColor
			};
		}
	},
	methods: {
		// 切换播放/暂停
		handleTogglePlay() {
			this.$emit('toggle-play');
		},
		
		// 点击进度条
		handleProgressClick(e) {
			if (this.isDragging) return;
			
			const rect = e.currentTarget.getBoundingClientRect();
			const x = e.detail.x - rect.left;
			const percent = x / rect.width;
			const time = this.duration * percent;
			
			this.$emit('seek', time);
		},
		
		// 触摸开始
		handleTouchStart(e) {
			this.isDragging = true;
			this.dragStartX = e.touches[0].clientX;
			this.dragStartTime = this.currentTime;
			
			// 获取进度条宽度
			const query = uni.createSelectorQuery().in(this);
			query.select('.progress-track').boundingClientRect(data => {
				if (data) {
					this.progressBarWidth = data.width;
				}
			}).exec();
		},
		
		// 触摸移动
		handleTouchMove(e) {
			if (!this.isDragging || !this.progressBarWidth) return;
			
			const currentX = e.touches[0].clientX;
			const deltaX = currentX - this.dragStartX;
			const deltaPercent = deltaX / this.progressBarWidth;
			const newTime = Math.max(0, Math.min(this.duration, this.dragStartTime + (deltaPercent * this.duration)));
			
			this.$emit('seek', newTime);
		},
		
		// 触摸结束
		handleTouchEnd() {
			this.isDragging = false;
		},
		
		// 格式化时间
		formatTime(seconds) {
			if (!seconds || isNaN(seconds)) return '00:00';
			const min = Math.floor(seconds / 60);
			const sec = Math.floor(seconds % 60);
			return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`;
		}
	}
};
</script>

<style lang="scss" scoped>
.progress-bar-container {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx 40rpx;
	background: rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.play-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, var(--primary-color, #4A90E2) 0%, var(--progress-fill, #50E3C2) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 20rpx var(--shadow-color, rgba(0, 0, 0, 0.3));
	transition: all 0.3s;
	flex-shrink: 0;
	
	&:active {
		transform: scale(0.95);
	}
	
	&.playing {
		animation: pulse 2s ease-in-out infinite;
	}
}

.play-icon,
.pause-icon {
	width: 36rpx;
	height: 36rpx;
	color: #FFFFFF;
}

.play-icon {
	margin-left: 4rpx;
}

@keyframes pulse {
	0%, 100% {
		box-shadow: 0 8rpx 20rpx var(--shadow-color, rgba(0, 0, 0, 0.3));
	}
	50% {
		box-shadow: 0 8rpx 30rpx var(--progress-fill, #50E3C2);
	}
}

.progress-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.progress-wrapper {
	width: 100%;
}

.progress-track {
	position: relative;
	height: 8rpx;
	border-radius: 4rpx;
	overflow: visible;
	cursor: pointer;
}

.progress-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: var(--progress-bg, rgba(255, 255, 255, 0.2));
	border-radius: 4rpx;
}

.progress-fill {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	background: var(--progress-fill, #50E3C2);
	border-radius: 4rpx;
	transition: width 0.1s linear;
}

.progress-thumb {
	position: absolute;
	top: 50%;
	width: 20rpx;
	height: 20rpx;
	background: var(--progress-fill, #50E3C2);
	border-radius: 50%;
	transform: translate(-50%, -50%);
	box-shadow: 0 2rpx 8rpx var(--shadow-color, rgba(0, 0, 0, 0.3));
	transition: all 0.2s;
	
	&.dragging {
		width: 28rpx;
		height: 28rpx;
		box-shadow: 0 4rpx 12rpx var(--progress-fill, #50E3C2);
	}
}

.time-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.time-text {
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
	font-size: 22rpx;
}

/* H5端优化 */
/* #ifdef H5 */
.play-btn {
	cursor: pointer;
	
	&:hover {
		transform: scale(1.05);
	}
}

.progress-track {
	&:hover {
		.progress-thumb {
			width: 24rpx;
			height: 24rpx;
		}
	}
}
/* #endif */
</style>

