<template>
  <view class="video-generating-status" v-if="visible">
    <view class="mask" @touchmove.prevent></view>
    
    <!-- 生成中状态 -->
    <view class="status-container" v-if="!completed || !isImage">
      <view class="status-header">
        <text class="header-title">{{ isImage ? '图片生成中' : '视频生成中' }}</text>
        <view class="header-actions">
          <view class="history-btn" @tap="viewHistory">
            <svg class="history-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="9" stroke="white" stroke-width="2" />
              <path d="M12 7V12L15 15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M12 3C7.02944 3 3 7.02944 3 12" stroke="white" stroke-width="2" stroke-linecap="round" />
              <path d="M21 12C21 16.9706 16.9706 21 12 21" stroke="white" stroke-width="2" stroke-linecap="round" />
              <circle cx="3" cy="12" r="1" fill="white" />
              <circle cx="12" cy="21" r="1" fill="white" />
            </svg>
            <text>历史</text>
          </view>
          <text class="header-close" @tap="closeModal" v-if="showCloseButton">×</text>
        </view>
      </view>
      
      <view class="status-content">
        <!-- 显示进度信息 -->
        <view class="progress-section">
          <view class="circular-progress">
            <text class="progress-percentage">{{ Math.round(progress) }}%</text>
            <view class="progress-track">
              <view 
                class="progress-fill" 
                :style="{ transform: `rotate(${3.6 * progress}deg)` }"
              ></view>
            </view>
          </view>
          
          <view class="progress-info">
            <text class="current-step">{{ currentStepText }}</text>
            <text class="time-remaining" v-if="timeRemaining">预计剩余时间: {{ timeRemaining }}</text>
          </view>
        </view>
        
        <!-- 处理步骤列表 -->
        <view class="steps-section">
          <view 
            v-for="(step, index) in steps" 
            :key="index"
            class="step-item"
            :class="{ 
              'active': currentStep >= index, 
              'completed': currentStep > index 
            }"
          >
            <view class="step-icon"></view>
            <text class="step-text">{{ step.text }}</text>
            <view class="step-time" v-if="currentStep > index">
              {{ step.completedTime || '完成' }}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 金币消费明细 -->
      <view class="cost-section">
        <view class="section-divider"></view>
        <view class="cost-header">
          <text class="cost-title">金币消费明细</text>
          <text class="total-cost">总计: {{ cost.total }} 金币</text>
        </view>
        
        <view class="cost-details">
          <view class="cost-item" v-for="(item, index) in cost.details" :key="index">
            <text class="cost-item-name">{{ item.name }}</text>
            <text class="cost-item-value">{{ item.value }} 金币</text>
          </view>
        </view>
        
        <view class="tip-box">
          <text class="tip-text">提示：生成过程中请勿关闭页面，否则可能导致生成失败，已扣除的金币将不予退还</text>
        </view>
      </view>
      
      <!-- 底部操作按钮 -->
      <view class="button-section">
        <button class="cancel-btn" @tap="cancelGeneration" v-if="!completed">取消生成</button>
        <view class="completed-buttons" v-if="completed && !isImage">
          <button class="view-btn" @tap="viewVideo">查看视频</button>
          <button class="generate-btn" @tap="generateAgain">再次生成</button>
        </view>
      </view>
    </view>
    
    <!-- 图片结果页面 - 垂直排列所有图片 -->
    <view class="result-container" v-if="completed && isImage">
      <view class="result-header">
        <text class="result-title">生成结果</text>
        <view class="header-actions">
          <text class="result-count">共{{ generatedImages.length }}张图片</text>
          <view class="history-btn" @tap="viewHistory">
            <svg class="history-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="9" stroke="white" stroke-width="2" />
              <path d="M12 7V12L15 15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M12 3C7.02944 3 3 7.02944 3 12" stroke="white" stroke-width="2" stroke-linecap="round" />
              <path d="M21 12C21 16.9706 16.9706 21 12 21" stroke="white" stroke-width="2" stroke-linecap="round" />
              <circle cx="3" cy="12" r="1" fill="white" />
              <circle cx="12" cy="21" r="1" fill="white" />
            </svg>
            <text>历史</text>
          </view>
          <text class="header-close" @tap="closeModal">×</text>
        </view>
      </view>
      
      <scroll-view class="result-scroll" scroll-y>
        <view class="result-content">
          <!-- 图片列表 -->
          <view class="image-result-item" v-for="(image, index) in generatedImages" :key="image.id">
            <view class="image-card">
              <image class="result-image" :src="image.url" mode="widthFix" @tap="previewSingleImage(index)"></image>
              
              <view class="image-actions">
                <view class="action-btn preview-btn" @tap="previewSingleImage(index)">
                  <image src="/static/icons/preview.png" mode="aspectFit"></image>
                  <text>预览</text>
                </view>
                <view class="action-btn save-btn" @tap="saveImageSingle(index)">
                  <image src="/static/icons/download.png" mode="aspectFit"></image>
                  <text>保存</text>
                </view>
                <view class="action-btn video-btn" @tap="useImageForVideo(index)">
                  <image src="/static/icons/video.png" mode="aspectFit"></image>
                  <text>用于视频</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      
      <view class="result-footer">
        <button class="footer-btn" @tap="saveAllImages">保存全部图片</button>
        <button class="footer-btn primary" @tap="generateAgain">再次生成</button>
      </view>
    </view>
    
    <!-- 取消确认弹窗 -->
    <view class="confirm-modal" v-if="showConfirmCancel">
      <view class="confirm-content">
        <text class="confirm-title">确定取消生成?</text>
        <text class="confirm-desc">取消后已扣除的金币将不予退还</text>
        <view class="confirm-buttons">
          <button class="confirm-cancel" @tap="closeConfirm">再想想</button>
          <button class="confirm-ok" @tap="confirmCancel">确定取消</button>
        </view>
      </view>
    </view>
    
    <!-- 操作提示弹窗 -->
    <view class="toast" v-if="toast.show">
      {{ toast.message }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'VideoGeneratingStatus',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialParams: {
      type: Object,
      default: () => ({})
    },
    isImage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      progress: 0,
      currentStep: 0,
      showCloseButton: false,
      completed: false,
      timeRemaining: '',
      showConfirmCancel: false,
      toast: {
        show: false,
        message: ''
      },
      
      // 生成步骤
      steps: [
        { text: '准备资源', completedTime: '' },
        { text: '分析内容', completedTime: '' },
        { text: '创建场景', completedTime: '' },
        { text: '渲染视频', completedTime: '' },
        { text: '优化处理', completedTime: '' },
        { text: '生成完成', completedTime: '' }
      ],
      
      // 金币消费
      cost: {
        total: 0,
        details: []
      },
      
      // 计时器
      progressTimer: null,
      toastTimer: null,
      
      // 视频相关信息
      videoResult: null,
      generatedImages: []
    }
  },
  computed: {
    currentStepText() {
      if (this.currentStep < this.steps.length) {
        return this.steps[this.currentStep].text;
      }
      return '生成完成';
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initGeneration();
      } else {
        this.clearTimers();
      }
    },
    progress(val) {
      if (val >= 100) {
        this.completeGeneration();
      }
    },
    isImage: {
      immediate: true,
      handler(val) {
        // 根据是否为图片调整步骤文字
        if (val) {
          // 如果是图片生成，修改相关步骤的文字
          this.steps = [
            { text: '准备资源', completedTime: '' },
            { text: '分析内容', completedTime: '' },
            { text: '构建图像', completedTime: '' },
            { text: '细节处理', completedTime: '' },
            { text: '优化效果', completedTime: '' },
            { text: '生成完成', completedTime: '' }
          ];
        } else {
          // 视频生成步骤
          this.steps = [
            { text: '准备资源', completedTime: '' },
            { text: '分析内容', completedTime: '' },
            { text: '创建场景', completedTime: '' },
            { text: '渲染视频', completedTime: '' },
            { text: '优化处理', completedTime: '' },
            { text: '生成完成', completedTime: '' }
          ];
        }
      }
    }
  },
  methods: {
    initGeneration() {
      this.progress = 0;
      this.currentStep = 0;
      this.showCloseButton = false;
      this.completed = false;
      this.timeRemaining = this.isImage ? '约1分钟' : '约5分钟';
      this.showConfirmCancel = false;
      this.generatedImages = [];
      
      // 初始化金币消费详情
      this.initCostDetails();
      
      // 模拟进度更新
      this.simulateProgress();
    },
    
    initCostDetails() {
      // 实际项目中应从initialParams中获取参数
      const params = this.initialParams;
      
      // 计算总成本
      let totalCost = params.cost || (this.isImage ? 100 : 300);
      
      // 构建消费明细
      if (this.isImage) {
        // 图片生成消费明细
        const details = [
          { name: '基础图片生成费用', value: Math.round(totalCost * 0.7) },
          { name: '图片高清处理', value: Math.round(totalCost * 0.2) },
          { name: '细节优化渲染', value: Math.round(totalCost * 0.1) }
        ];
        
        this.cost = {
          total: totalCost,
          details: details
        };
      } else {
        // 视频生成消费明细
        const details = [
          { name: '基础视频生成费用', value: Math.round(totalCost * 0.6) },
          { name: '视频高清处理', value: Math.round(totalCost * 0.2) },
          { name: '视频优化渲染', value: Math.round(totalCost * 0.1) },
          { name: '视频转码处理', value: Math.round(totalCost * 0.1) }
        ];
        
        this.cost = {
          total: totalCost,
          details: details
        };
      }
    },
    
    simulateProgress() {
      // 根据图片或视频调整生成时间
      const totalDuration = this.isImage ? 5000 : 15000; // 图片5秒，视频15秒完成
      const stepDurations = this.isImage ? 
        [500, 1000, 1500, 1000, 500, 500] : // 图片各步骤时间
        [2000, 2000, 3000, 4000, 3000, 1000]; // 视频各步骤时间
      
      let startTime = Date.now();
      let lastStepTime = startTime;
      
      this.progressTimer = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const stepElapsed = Date.now() - lastStepTime;
        
        // 更新总进度
        this.progress = Math.min(100, (elapsed / totalDuration) * 100);
        
        // 更新当前步骤
        let currentStepEndTime = 0;
        for (let i = 0; i <= this.currentStep; i++) {
          currentStepEndTime += stepDurations[i];
        }
        
        if (elapsed >= currentStepEndTime && this.currentStep < this.steps.length - 1) {
          this.steps[this.currentStep].completedTime = this.getRandomCompletedTime();
          this.currentStep++;
          lastStepTime = Date.now();
          
          // 更新剩余时间
          if (this.isImage) {
            // 图片生成剩余时间
            const remainingSteps = this.steps.length - this.currentStep - 1;
            if (remainingSteps > 0) {
              this.timeRemaining = '几秒钟';
            } else {
              this.timeRemaining = '即将完成';
            }
          } else {
            // 视频生成剩余时间
            const remainingSteps = this.steps.length - this.currentStep - 1;
            if (remainingSteps > 1) {
              this.timeRemaining = `约${remainingSteps}分钟`;
            } else if (remainingSteps === 1) {
              this.timeRemaining = '不到1分钟';
            } else {
              this.timeRemaining = '即将完成';
            }
          }
        }
        
        if (elapsed >= totalDuration) {
          this.progress = 100;
          clearInterval(this.progressTimer);
          this.progressTimer = null;
          
          // 更新最后一个步骤
          if (this.currentStep < this.steps.length) {
            this.steps[this.currentStep].completedTime = this.getRandomCompletedTime();
          }
        }
      }, 100);
    },
    
    getRandomCompletedTime() {
      // 图片生成比视频快，时间更短
      const minSec = this.isImage ? 5 : 10;
      const maxSec = this.isImage ? 15 : 30;
      const randomSeconds = Math.floor(Math.random() * (maxSec - minSec)) + minSec;
      return `${randomSeconds}秒`;
    },
    
    completeGeneration() {
      this.completed = true;
      this.showCloseButton = true;
      
      // 根据类型生成结果
      if (this.isImage) {
        // 图片生成结果 - 支持多图
        const imageCount = this.initialParams.count || 1;
        this.generatedImages = [];
        
        // 生成多张样例图片
        const sampleImages = [
          '/static/demo/sample_image.jpg',
          '/static/demo/sample_image2.jpg',
          '/static/demo/sample_image3.jpg',
          '/static/demo/sample_image4.jpg'
        ];
        
        // 根据count生成对应数量的图片
        for (let i = 0; i < Math.min(imageCount, 4); i++) {
          this.generatedImages.push({
            url: sampleImages[i] || sampleImages[0],
            id: `img_${Date.now()}_${i}`,
            timestamp: new Date().toISOString()
          });
        }
        
        // 将第一张图片设为主图片结果
        this.videoResult = this.generatedImages[0];
        
        // 保存到历史记录
        this.saveToHistory('text_to_image_history', {
          id: `img_gen_${Date.now()}`,
          prompt: this.initialParams.prompt || '',
          timestamp: new Date().toISOString(),
          images: this.generatedImages,
          content: this.generatedImages[0]?.url || '/static/images/default-text.png', // 添加content字段，与历史记录页面匹配
          coverImage: '/static/images/default-text.png', // 添加封面图
          params: this.initialParams
        });
        
        // 显示成功提示
        this.showToast(`${this.generatedImages.length}张图片生成成功！`);
      } else {
        // 视频生成结果
        this.videoResult = {
          url: '/static/demo/sample_video.mp4',
          duration: '15秒',
          size: '8.5MB',
          timestamp: new Date().toISOString()
        };
        
        // 保存到历史记录
        const storageKey = this.initialParams.mode === 'image' ? 'image_to_video_history' : 'text_to_video_history';
        this.saveToHistory(storageKey, {
          id: `video_gen_${Date.now()}`,
          prompt: this.initialParams.prompt || '',
          timestamp: new Date().toISOString(),
          url: this.videoResult.url,
          content: this.videoResult.url, // 添加content字段，与历史记录页面匹配
          coverImage: '/static/images/default-video.png', // 添加封面图
          duration: this.videoResult.duration,
          size: this.videoResult.size,
          params: this.initialParams
        });
        
        // 显示成功提示
        this.showToast('视频生成成功！');
      }
    },
    
    cancelGeneration() {
      this.showConfirmCancel = true;
    },
    
    closeConfirm() {
      this.showConfirmCancel = false;
    },
    
    confirmCancel() {
      this.closeConfirm();
      this.closeModal();
      this.showToast(`已取消${this.isImage ? '图片' : '视频'}生成`);
    },
    
    viewVideo() {
      // 视频预览
      console.log('查看内容:', this.videoResult);
      this.$emit('view-video', this.videoResult);
    },
    
    generateAgain() {
      // 实际项目中应返回表单并保留参数
      this.$emit('generate-again');
      this.closeModal();
    },
    
    closeModal() {
      this.clearTimers();
      this.$emit('close');
    },
    
    showToast(message) {
      this.toast = {
        show: true,
        message: message
      };
      
      if (this.toastTimer) {
        clearTimeout(this.toastTimer);
      }
      
      this.toastTimer = setTimeout(() => {
        this.toast.show = false;
      }, 3000);
    },
    
    clearTimers() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
      
      if (this.toastTimer) {
        clearTimeout(this.toastTimer);
        this.toastTimer = null;
      }
    },
    
    previewSingleImage(index) {
      if (this.isImage && this.generatedImages.length > 0) {
        uni.previewImage({
          urls: this.generatedImages.map(img => img.url),
          current: index,
          longPressActions: {
            itemList: ['保存图片', '分享'],
            success: (data) => {
              if (data.tapIndex === 0) {
                // 保存当前查看的图片
                uni.saveImageToPhotosAlbum({
                  filePath: this.generatedImages[index].url,
                  success: () => {
                    this.showToast('图片已保存到相册');
                  }
                });
              } else if (data.tapIndex === 1) {
                // 分享当前查看的图片
                uni.share({
                  provider: 'weixin',
                  scene: 'WXSceneSession',
                  type: 2,
                  imageUrl: this.generatedImages[index].url,
                  success: () => {
                    this.showToast('分享成功');
                  }
                });
              }
            }
          }
        });
      }
    },
    
    saveImageSingle(index) {
      if (!this.isImage || index >= this.generatedImages.length) return;
      
      uni.saveImageToPhotosAlbum({
        filePath: this.generatedImages[index].url,
        success: () => {
          this.showToast('图片已保存到相册');
        },
        fail: (err) => {
          console.error('保存图片失败', err);
          this.showToast('保存图片失败，请重试');
        }
      });
    },
    
    saveAllImages() {
      if (!this.generatedImages.length) return;
      
      let savedCount = 0;
      const totalCount = this.generatedImages.length;
      
      // 显示保存进度
      uni.showLoading({
        title: `保存中(0/${totalCount})`,
        mask: true
      });
      
      // 依次保存每张图片
      this.generatedImages.forEach((image, index) => {
        uni.saveImageToPhotosAlbum({
          filePath: image.url,
          success: () => {
            savedCount++;
            if (savedCount === totalCount) {
              uni.hideLoading();
              this.showToast(`已成功保存${savedCount}张图片到相册`);
            } else {
              uni.showLoading({
                title: `保存中(${savedCount}/${totalCount})`,
                mask: true
              });
            }
          },
          fail: (err) => {
            console.error('保存图片失败', err);
            savedCount++;
            if (savedCount === totalCount) {
              uni.hideLoading();
              this.showToast(`部分图片保存失败，已成功保存${savedCount}张图片`);
            }
          }
        });
      });
    },
    
    useImageForVideo(index) {
      // 使用选中的图片进行视频生成
      if (this.isImage && this.generatedImages.length > 0) {
        const selectedImage = this.generatedImages[index];
        this.$emit('use-for-video', {
          mainImage: selectedImage,
          allImages: this.generatedImages
        });
        this.closeModal();
      }
    },
    
    useForVideo() {
      if (this.isImage) {
        // 发送所有生成的图片数据
        this.$emit('use-for-video', {
          mainImage: this.videoResult,
          allImages: this.generatedImages
        });
      }
      this.closeModal();
    },
    
    viewHistory() {
      // 跳转到历史记录详细页面，并通过参数指示需要添加测试数据
      const type = this.isImage ? 'text-to-image' : 'text-to-video';
      uni.navigateTo({
        url: `/pages/history/detail?type=${type}&addTestData=true`
      });
      // 不关闭当前弹窗，让用户可以返回继续操作
    },
    
    saveToHistory(storageKey, record) {
      // 添加成本信息到记录中
      record.cost = this.cost.total;
      record.costDetails = this.cost.details;
      
      // 添加createTime字段，与timestamp保持一致，确保历史记录页面可以正确显示
      record.createTime = record.timestamp;
      
      // 获取现有历史记录
      uni.getStorage({
        key: storageKey,
        success: (res) => {
          let history = res.data;
          if (!Array.isArray(history)) {
            history = [];
          }
          
          // 添加新记录到历史记录的前面
          history.unshift(record);
          
          // 限制历史记录数量，最多保存50条
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
          
          // 保存更新后的历史记录
          uni.setStorage({
            key: storageKey,
            data: history
          });
        },
        fail: () => {
          // 创建新的历史记录
          uni.setStorage({
            key: storageKey,
            data: [record]
          });
        }
      });
    }
  },
  beforeDestroy() {
    this.clearTimers();
  }
}
</script>

<style lang="scss" scoped>
.video-generating-status {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  
  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
  }
  
  .status-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-height: 90%;
    background-color: #16213e;
    border-radius: 20rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .status-header {
      padding: 30rpx;
      border-bottom: 1px solid #2c2c44;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #ffffff;
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        
        .history-btn {
          width: 55rpx;
          height: 55rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #2c3e50;
          border-radius: 50%;
          margin-right: 20rpx;
          padding: 12rpx;
          box-sizing: border-box;
          border: none;
          transition: all 0.2s ease;
          
          &:active {
            transform: scale(0.95);
            background-color: #34495e;
          }
          
          .history-icon {
            width: 34rpx;
            height: 34rpx;
            color: #ffffff;
          }
          
          text {
            display: none;
          }
        }
        
        .header-close {
          font-size: 40rpx;
          color: #e6e6e6;
          padding: 0 10rpx;
        }
      }
    }
    
    .status-content {
      padding: 30rpx;
      
      .progress-section {
        display: flex;
        align-items: center;
        margin-bottom: 40rpx;
        
        .circular-progress {
          position: relative;
          width: 160rpx;
          height: 160rpx;
          border-radius: 50%;
          background-color: #0f3460;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 30rpx;
          
          .progress-percentage {
            font-size: 40rpx;
            font-weight: 600;
            color: #ffffff;
          }
          
          .progress-track {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
            
            .progress-fill {
              position: absolute;
              top: 0;
              left: 0;
              width: 50%;
              height: 100%;
              background-color: #1e3799;
              transform-origin: right center;
            }
          }
        }
        
        .progress-info {
          flex: 1;
          
          .current-step {
            font-size: 32rpx;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 15rpx;
            display: block;
          }
          
          .time-remaining {
            font-size: 26rpx;
            color: #a0a0c0;
          }
        }
      }
      
      .steps-section {
        .step-item {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .step-icon {
            width: 20rpx;
            height: 20rpx;
            border-radius: 50%;
            background-color: #2c2c44;
            margin-right: 15rpx;
          }
          
          .step-text {
            font-size: 28rpx;
            color: #a0a0c0;
            flex: 1;
          }
          
          .step-time {
            font-size: 24rpx;
            color: #a0a0c0;
          }
          
          &.active {
            .step-icon {
              background-color: #4a69bd;
            }
            
            .step-text {
              color: #ffffff;
            }
          }
          
          &.completed {
            .step-icon {
              background-color: #1e3799;
            }
            
            .step-text {
              color: #e6e6e6;
            }
            
            .step-time {
              color: #4a69bd;
            }
          }
        }
      }
    }
    
    .cost-section {
      padding: 0 30rpx 30rpx;
      
      .section-divider {
        height: 1rpx;
        background-color: #2c2c44;
        margin-bottom: 30rpx;
      }
      
      .cost-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        
        .cost-title {
          font-size: 30rpx;
          font-weight: 600;
          color: #ffffff;
        }
        
        .total-cost {
          font-size: 30rpx;
          font-weight: 600;
          color: #ffda79;
        }
      }
      
      .cost-details {
        margin-bottom: 20rpx;
        
        .cost-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .cost-item-name {
            font-size: 26rpx;
            color: #a0a0c0;
          }
          
          .cost-item-value {
            font-size: 26rpx;
            color: #ffda79;
          }
        }
      }
      
      .tip-box {
        padding: 15rpx;
        background-color: rgba(30, 55, 153, 0.1);
        border-radius: 8rpx;
        
        .tip-text {
          font-size: 24rpx;
          color: #a0a0c0;
          line-height: 1.5;
        }
      }
    }
    
    .button-section {
      padding: 20rpx 30rpx;
      border-top: 1px solid #2c2c44;
      
      .cancel-btn {
        width: 100%;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: #2c2c44;
        color: #ffffff;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .completed-buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        
        button {
          flex: 1;
          min-width: 160rpx;
          height: 80rpx;
          border-radius: 40rpx;
          margin: 0 8rpx;
          font-size: 28rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          
          &:first-child {
            margin-left: 0;
          }
          
          &:last-child {
            margin-right: 0;
          }
        }
        
        .view-btn {
          background-color: #1e3799;
          color: #ffffff;
        }
        
        .generate-btn {
          background-color: #2c2c44;
          color: #ffffff;
        }
      }
    }
  }
  
  // 图片结果页面样式
  .result-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #16213e;
    display: flex;
    flex-direction: column;
    
    .result-header {
      padding: 30rpx;
      border-bottom: 1px solid #2c2c44;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: rgba(15, 52, 96, 0.8);
      
      .result-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #ffffff;
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        
        .result-count {
          font-size: 26rpx;
          color: #a0a0c0;
          margin-right: 20rpx;
        }
        
        .history-btn {
          width: 55rpx;
          height: 55rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #2c3e50;
          border-radius: 50%;
          margin-right: 20rpx;
          padding: 12rpx;
          box-sizing: border-box;
          border: none;
          transition: all 0.2s ease;
          
          &:active {
            transform: scale(0.95);
            background-color: #34495e;
          }
          
          .history-icon {
            width: 34rpx;
            height: 34rpx;
            color: #ffffff;
          }
          
          text {
            display: none;
          }
        }
        
        .header-close {
          font-size: 40rpx;
          color: #e6e6e6;
          padding: 0 10rpx;
        }
      }
    }
    
    .result-scroll {
      flex: 1;
      overflow-y: auto;
      
      .result-content {
        padding: 20rpx;
        
        .image-result-item {
          margin-bottom: 30rpx;
          
          .image-card {
            background-color: rgba(15, 52, 96, 0.5);
            border-radius: 16rpx;
            overflow: hidden;
            border: 1px solid rgba(74, 105, 189, 0.3);
            box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
            
            .result-image {
              width: 100%;
              border-radius: 16rpx 16rpx 0 0;
            }
            
            .image-actions {
              display: flex;
              padding: 20rpx;
              background-color: rgba(15, 52, 96, 0.6);
              
              .action-btn {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 10rpx 0;
                
                image {
                  width: 40rpx;
                  height: 40rpx;
                  margin-bottom: 10rpx;
                }
                
                text {
                  font-size: 24rpx;
                  color: #ffffff;
                }
                
                &:active {
                  opacity: 0.7;
                }
              }
              
              .preview-btn {
                color: #48dbfb;
              }
              
              .save-btn {
                color: #0abde3;
              }
              
              .video-btn {
                color: #4a69bd;
              }
            }
          }
        }
      }
    }
    
    .result-footer {
      display: flex;
      padding: 20rpx;
      border-top: 1px solid #2c2c44;
      background-color: rgba(15, 52, 96, 0.8);
      
      .footer-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        margin: 0 10rpx;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #2c2c44;
        color: #ffffff;
        
        &.primary {
          background: linear-gradient(to right, #1e3799, #4a69bd);
          color: #ffffff;
          box-shadow: 0 4rpx 15rpx rgba(30, 55, 153, 0.5);
        }
      }
    }
  }
  
  .confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .confirm-content {
      width: 80%;
      background-color: #16213e;
      border-radius: 20rpx;
      padding: 30rpx;
      
      .confirm-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #ffffff;
        text-align: center;
        margin-bottom: 20rpx;
        display: block;
      }
      
      .confirm-desc {
        font-size: 28rpx;
        color: #a0a0c0;
        text-align: center;
        margin-bottom: 30rpx;
        display: block;
      }
      
      .confirm-buttons {
        display: flex;
        
        button {
          flex: 1;
          height: 90rpx;
          border-radius: 10rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 30rpx;
        }
        
        .confirm-cancel {
          background-color: #2c2c44;
          color: #e6e6e6;
          margin-right: 15rpx;
        }
        
        .confirm-ok {
          background-color: #1e3799;
          color: #ffffff;
        }
      }
    }
  }
  
  .toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 15rpx 30rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    z-index: 1002;
    white-space: nowrap;
  }
}
</style> 