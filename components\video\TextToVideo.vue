<template>
  <view class="text-to-video">
    <view class="ai-card">
      <!-- 文本提示词输入 -->
      <view class="section">
        <view class="section-title">
          <text class="ai-text-subtitle">描述您想要的视频内容</text>
        </view>
        <textarea 
          class="ai-input text-input" 
          v-model="prompt" 
          placeholder="详细描述您想要的视频场景、角色、动作、风格等，例如：一个穿红色连衣裙的女孩在阳光明媚的海滩上奔跑，镜头从远到近，电影质感..." 
          auto-height
          :maxlength="1000"
        ></textarea>
        <view class="char-counter">
          <text>{{ prompt.length }}/1000</text>
        </view>
      </view>
      
      <!-- 视频时长 -->
      <view class="section">
        <view class="section-title">
          <text class="ai-text-subtitle">视频时长</text>
          <text class="section-value">{{ duration }}秒</text>
        </view>
        <view class="duration-slider">
          <slider 
            :value="duration" 
            :min="3" 
            :max="15" 
            :step="3" 
            :block-size="24" 
            block-color="#6e56cf"
            background-color="rgba(255,255,255,0.1)"
            active-color="#6e56cf"
            @change="onDurationChange"
          />
          <view class="duration-marks">
            <text>3秒</text>
            <text>6秒</text>
            <text>9秒</text>
            <text>12秒</text>
            <text>15秒</text>
          </view>
        </view>
      </view>
      
      <!-- 视频比例选择 -->
      <view class="section">
        <view class="section-title">
          <text class="ai-text-subtitle">视频比例</text>
        </view>
        <view class="aspect-list">
          <view 
            v-for="(aspect, index) in aspectRatios" 
            :key="index" 
            class="aspect-item" 
            :class="{ active: aspectRatio === aspect.value }"
            @click="aspectRatio = aspect.value"
          >
            <view class="aspect-preview" :style="{ 'aspect-ratio': aspect.ratio }"></view>
            <text class="aspect-name">{{ aspect.name }}</text>
          </view>
        </view>
      </view>
      
      <!-- 高级设置 -->
      <view class="section">
        <view class="collapsible-header" @click="advancedSettingsOpen = !advancedSettingsOpen">
          <text class="ai-text-subtitle">高级设置</text>
          <text class="iconfont" :class="advancedSettingsOpen ? 'icon-up' : 'icon-down'"></text>
        </view>
        <view class="collapsible-content" v-if="advancedSettingsOpen">
          <!-- 风格强度 -->
          <view class="setting-item">
            <view class="setting-header">
              <text class="setting-title">风格强度</text>
              <text class="setting-value">{{ styleStrength }}</text>
            </view>
            <slider 
              :value="styleStrength" 
              :min="1" 
              :max="10" 
              :step="1" 
              :block-size="24" 
              block-color="#6e56cf"
              background-color="rgba(255,255,255,0.1)"
              active-color="#6e56cf"
              @change="onStyleStrengthChange"
            />
          </view>
          
          <!-- 负面提示词 -->
          <view class="setting-item">
            <view class="setting-header">
              <text class="setting-title">负面提示词</text>
            </view>
            <textarea 
              class="ai-input negative-prompt-input" 
              v-model="negativePrompt" 
              placeholder="输入您不希望在视频中出现的元素，例如：模糊、变形、低质量..." 
              auto-height
              :maxlength="500"
            ></textarea>
          </view>
        </view>
      </view>
      
      <!-- 生成按钮 -->
      <view class="btn-container">
        <button 
          class="ai-btn generate-btn" 
          :disabled="!canGenerate" 
          :class="{ disabled: !canGenerate }"
          @click="generateVideo"
        >
          {{ calculatePrice() }}金币立即生成
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TextToVideo',
  props: {
    aspectRatios: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      prompt: '',
      negativePrompt: '',
      duration: 6,
      aspectRatio: '16:9',
      styleStrength: 7,
      advancedSettingsOpen: false
    }
  },
  computed: {
    canGenerate() {
      return this.prompt.length >= 20;
    }
  },
  methods: {
    onDurationChange(e) {
      this.duration = e.detail.value;
    },
    onStyleStrengthChange(e) {
      this.styleStrength = e.detail.value;
    },
    calculatePrice() {
      // 基础价格300金币
      let price = 300;
      
      // 每增加3秒，增加100金币
      if (this.duration > 3) {
        price += Math.floor((this.duration - 3) / 3) * 100;
      }
      
      return price;
    },
    generateVideo() {
      if (!this.canGenerate) return;
      
      this.$emit('generate', {
        type: 'text-to-video',
        data: {
          prompt: this.prompt,
          negativePrompt: this.negativePrompt,
          duration: this.duration,
          aspectRatio: this.aspectRatio,
          styleStrength: this.styleStrength
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.text-to-video {
  width: 100%;
}

.section {
  margin-bottom: 30rpx;
  
  .section-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15rpx;
    
    .section-value {
      color: rgba(255, 255, 255, 0.6);
      font-size: 28rpx;
    }
  }
}

.text-input {
  width: 100%;
  min-height: 200rpx;
  background-color: rgba(255,255,255,0.05);
  border-radius: 8rpx;
  padding: 20rpx;
  color: #fff;
  font-size: 28rpx;
}

.char-counter {
  text-align: right;
  margin-top: 8rpx;
  
  text {
    font-size: 24rpx;
    color: rgba(255,255,255,0.6);
  }
}

.duration-slider {
  margin-bottom: 15rpx;
  
  .duration-marks {
    display: flex;
    justify-content: space-between;
    margin-top: 5rpx;
    
    text {
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

.aspect-list {
  display: flex;
  
  .aspect-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 15rpx;
    
    &:last-child {
      margin-right: 0;
    }
    
    .aspect-preview {
      width: 80rpx;
      background-color: rgba(255, 255, 255, 0.1);
      height: 80rpx;
      border-radius: 8rpx;
      margin-bottom: 8rpx;
      border: 2px solid transparent;
      transition: all 0.3s;
    }
    
    .aspect-name {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.6);
    }
    
    &.active {
      .aspect-preview {
        border-color: #6e56cf;
        background-color: rgba(110, 86, 207, 0.2);
      }
      
      .aspect-name {
        color: #6e56cf;
      }
    }
  }
}

.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 10rpx 0;
  
  .iconfont {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
    transition: transform 0.3s;
  }
}

.collapsible-content {
  padding-top: 20rpx;
}

.setting-item {
  margin-bottom: 30rpx;
  
  .setting-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15rpx;
    
    .setting-title {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
    }
    
    .setting-value {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.6);
    }
  }
  
  .negative-prompt-input {
    width: 100%;
    min-height: 120rpx;
    background-color: rgba(255,255,255,0.05);
    border-radius: 8rpx;
    padding: 20rpx;
    color: #fff;
    font-size: 28rpx;
  }
}

.btn-container {
  .generate-btn {
    width: 100%;
    background-color: #6e56cf;
    color: #fff;
    border-radius: 10rpx;
    padding: 20rpx 0;
    font-size: 30rpx;
    
    &.disabled {
      opacity: 0.5;
      background: rgba(110, 86, 207, 0.5);
    }
  }
}
</style> 