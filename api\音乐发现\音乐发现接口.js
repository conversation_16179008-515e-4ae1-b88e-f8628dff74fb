/**
 * 音乐发现接口
 * 管理音乐浏览、推荐、分类等功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🔍 音乐发现接口
// ================================

/**
 * 获取热门音乐
 * @param {Object} params - 查询参数
 */
export async function 获取热门音乐(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/discover/hot?${queryParams}`);
}

/**
 * 获取最新音乐
 * @param {Object} params - 查询参数
 */
export async function 获取最新音乐(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/discover/latest?${queryParams}`);
}

/**
 * 获取推荐音乐
 * @param {Object} params - 查询参数
 */
export async function 获取推荐音乐(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/discover/recommended?${queryParams}`);
}

/**
 * 获取音乐分类
 */
export async function 获取音乐分类() {
	return await apiRequest('music/discover/categories');
}

/**
 * 按分类获取音乐
 * @param {string} category - 分类ID
 * @param {Object} params - 查询参数
 */
export async function 按分类获取音乐(category, params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/discover/category/${category}?${queryParams}`);
}

/**
 * 搜索音乐
 * @param {Object} params - 搜索参数
 */
export async function 搜索音乐(params) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/discover/search?${queryParams}`);
}

// ================================
// 🎧 音乐播放接口
// ================================

/**
 * 获取音乐详情
 * @param {string} musicId - 音乐ID
 */
export async function 获取音乐详情(musicId) {
	return await apiRequest(`music/player/detail?musicId=${musicId}`);
}

/**
 * 记录播放
 * @param {Object} params - 播放参数
 */
export async function 记录播放(params) {
	return await apiRequest('music/player/play', {
		method: 'POST',
		body: params
	});
}

/**
 * 点赞音乐
 * @param {string} musicId - 音乐ID
 * @param {string} action - 操作类型 (like/unlike)
 */
export async function 点赞音乐(musicId, action = 'like') {
	return await apiRequest('music/player/like', {
		method: 'POST',
		body: { musicId, action }
	});
}

/**
 * 收藏音乐
 * @param {string} musicId - 音乐ID
 * @param {string} action - 操作类型 (collect/uncollect)
 */
export async function 收藏音乐(musicId, action = 'collect') {
	return await apiRequest('music/player/collect', {
		method: 'POST',
		body: { musicId, action }
	});
}

/**
 * 分享音乐
 * @param {string} musicId - 音乐ID
 * @param {string} platform - 分享平台
 */
export async function 分享音乐(musicId, platform) {
	return await apiRequest('music/player/share', {
		method: 'POST',
		body: { musicId, platform }
	});
}

// ================================
// 💬 评论接口
// ================================

/**
 * 获取评论列表
 * @param {string} musicId - 音乐ID
 * @param {Object} params - 查询参数
 */
export async function 获取评论列表(musicId, params = {}) {
	const queryParams = new URLSearchParams({ musicId, ...params }).toString();
	return await apiRequest(`music/player/comments?${queryParams}`);
}

/**
 * 发表评论
 * @param {Object} params - 评论参数
 */
export async function 发表评论(params) {
	return await apiRequest('music/player/comment', {
		method: 'POST',
		body: params
	});
}

/**
 * 点赞评论
 * @param {string} commentId - 评论ID
 * @param {string} action - 操作类型
 */
export async function 点赞评论(commentId, action = 'like') {
	return await apiRequest('music/player/comment-like', {
		method: 'POST',
		body: { commentId, action }
	});
}

/**
 * 删除评论
 * @param {string} commentId - 评论ID
 */
export async function 删除评论(commentId) {
	return await apiRequest('music/player/comment', {
		method: 'DELETE',
		body: { commentId }
	});
}

// ================================
// 🎯 个性化推荐接口
// ================================

/**
 * 获取个性化推荐
 */
export async function 获取个性化推荐() {
	return await apiRequest('music/discover/personalized');
}

/**
 * 获取相似音乐
 * @param {string} musicId - 音乐ID
 */
export async function 获取相似音乐(musicId) {
	return await apiRequest(`music/discover/similar?musicId=${musicId}`);
}

/**
 * 获取用户可能喜欢的音乐
 */
export async function 获取用户可能喜欢的音乐() {
	return await apiRequest('music/discover/may-like');
}

/**
 * 记录用户偏好
 * @param {Object} params - 偏好参数
 */
export async function 记录用户偏好(params) {
	return await apiRequest('music/discover/preference', {
		method: 'POST',
		body: params
	});
}

export default {
	获取热门音乐,
	获取最新音乐,
	获取推荐音乐,
	获取音乐分类,
	按分类获取音乐,
	搜索音乐,
	获取音乐详情,
	记录播放,
	点赞音乐,
	收藏音乐,
	分享音乐,
	获取评论列表,
	发表评论,
	点赞评论,
	删除评论,
	获取个性化推荐,
	获取相似音乐,
	获取用户可能喜欢的音乐,
	记录用户偏好
};
