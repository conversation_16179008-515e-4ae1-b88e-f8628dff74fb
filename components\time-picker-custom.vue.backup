<template>
	<view class="time-picker" v-if="visible" @touchmove.stop.prevent>
		<view class="picker-mask" @click="close" @touchmove.stop.prevent></view>
		<view class="picker-content" @touchmove.stop.prevent>
			<view class="picker-header">
				<text class="header-title">选择出生时间</text>
			</view>

			<!-- 列标签 -->
			<view class="column-labels">
				<text class="column-label">时</text>
				<text class="column-label">分</text>
			</view>

			<!-- 时间选择器 -->
			<view class="picker-container">
				<view class="picker-indicator"></view>
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
					@touchmove.stop.prevent
				>
					<picker-view-column>
						<view v-for="(hour, index) in hours" :key="`hour-${index}`" class="picker-item">
							{{ String(hour).padStart(2, '0') }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(minute, index) in minutes" :key="`minute-${index}`" class="picker-item">
							{{ String(minute).padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TimePicker',
	props: {
		visible: { type: Boolean, default: false }
	},
	data() {
		const now = new Date();
		const hours = [];
		for (let i = 0; i < 24; i++) {
			hours.push(i);
		}
		const minutes = [];
		for (let i = 0; i < 60; i++) {
			minutes.push(i);
		}
		const selectedHour = now.getHours();
		const selectedMinute = now.getMinutes();

		// 计算正确的初始picker-value
		const hourIndex = hours.findIndex(hour => hour === selectedHour);
		const minuteIndex = minutes.findIndex(minute => minute === selectedMinute);

		return {
			selectedHour,
			selectedMinute,
			pickerValue: [
				hourIndex >= 0 ? hourIndex : 0,
				minuteIndex >= 0 ? minuteIndex : 0
			],
			hours,
			minutes,
			// picker-view样式配置，实现自动吸入效果 - 改为暗红色
			indicatorStyle: 'height: 80rpx; background: rgba(139, 0, 0, 0.1); border: 2rpx solid #8B0000; border-radius: 12rpx;',
			maskStyle: 'background: linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.95) 100%);'
		};
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.$nextTick(() => {
					this.initPicker();
					// 强制更新picker-view以确保正确渲染
					this.$forceUpdate();
				});
			}
		}
	},
	methods: {
		generateHours() {
			const hours = [];
			for (let i = 0; i < 24; i++) {
				hours.push(i);
			}
			return hours;
		},
		generateMinutes() {
			const minutes = [];
			for (let i = 0; i < 60; i++) {
				minutes.push(i);
			}
			return minutes;
		},
		initPicker() {
			const hourIndex = this.hours.findIndex(hour => hour === this.selectedHour);
			const minuteIndex = this.minutes.findIndex(minute => minute === this.selectedMinute);
			this.pickerValue = [
				hourIndex >= 0 ? hourIndex : 0,
				minuteIndex >= 0 ? minuteIndex : 0
			];
		},
		onPickerChange(e) {
			const [hourIndex, minuteIndex] = e.detail.value;
			this.selectedHour = this.hours[hourIndex];
			this.selectedMinute = this.minutes[minuteIndex];
			this.pickerValue = [hourIndex, minuteIndex];
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');
			const timeStr = `${hour}:${minute}`;
			this.$emit('confirm', timeStr);
		}
	}
};
</script>

<style scoped>
.time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	width: 90%;
	max-width: 600rpx;
	background: linear-gradient(135deg, #F4E4BC, #E6D7A3);
	border-radius: 20rpx 20rpx 0 0;
	box-shadow: 0 -10rpx 30rpx rgba(139, 69, 19, 0.3);
	padding: 30rpx 20rpx 20rpx;
	border: 2rpx solid #8B4513;
}

.picker-header {
	text-align: center;
	margin-bottom: 25rpx;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #8B4513;
}

.column-labels {
	display: flex;
	justify-content: space-around;
	margin-bottom: 15rpx;
	padding: 0 100rpx;
}

.column-label {
	font-size: 26rpx;
	color: #8B4513;
	font-weight: 600;
}

.picker-container {
	position: relative;
	height: 320rpx;
	margin-bottom: 30rpx;
}

.picker-indicator {
	position: absolute;
	top: 50%;
	left: 20rpx;
	right: 20rpx;
	height: 80rpx;
	transform: translateY(-50%);
	background: rgba(139, 0, 0, 0.1);
	border: 2rpx solid #8B0000;
	border-radius: 12rpx;
	pointer-events: none;
	z-index: 1;
}

.picker-view {
	width: 100%;
	height: 100%;
}

.picker-item {
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #8B4513;
	font-weight: 500;
}

.picker-footer {
	display: flex;
	gap: 15rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 70rpx;
	border-radius: 35rpx;
	border: none;
	font-size: 30rpx;
	font-weight: 600;
}

.btn-cancel {
	background: #F5F5DC;
	color: #8B4513;
	border: 2rpx solid #8B4513;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}
</style>
