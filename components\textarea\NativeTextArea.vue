<template>
  <view class="native-textarea-container" :class="{ 'focused': isFocused }">
    <!-- 纯原生textarea，无任何自定义事件处理 -->
    <textarea
      ref="textareaRef"
      class="native-textarea"
      :class="{
        'is-h5': isH5,
        'is-app': isApp,
        'is-mp': isMiniProgram
      }"
      :value="modelValue"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
      :auto-focus="autoFocus"
      :focus="focus"
      :auto-height="autoHeight"
      :fixed="fixed"
      :cursor-spacing="cursorSpacing"
      :cursor="cursor"
      :show-confirm-bar="showConfirmBar"
      :selection-start="selectionStart"
      :selection-end="selectionEnd"
      :adjust-position="adjustPosition"
      :hold-keyboard="holdKeyboard"
      :disable-default-padding="disableDefaultPadding"
      :confirm-type="confirmType"
      :confirm-hold="confirmHold"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @linechange="handleLineChange"
      @keyboardheightchange="handleKeyboardHeightChange"
    />
    
    <!-- 状态显示 -->
    <view class="status-info" v-if="showStatus">
      <text>光标: {{ cursor }} | 选择: {{ selectionStart }}-{{ selectionEnd }} | 平台: {{ platform }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'NativeTextArea',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    maxlength: {
      type: Number,
      default: -1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    focus: {
      type: Boolean,
      default: false
    },
    autoHeight: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    cursor: {
      type: Number,
      default: -1
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    selectionStart: {
      type: Number,
      default: -1
    },
    selectionEnd: {
      type: Number,
      default: -1
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    holdKeyboard: {
      type: Boolean,
      default: false
    },
    disableDefaultPadding: {
      type: Boolean,
      default: false
    },
    confirmType: {
      type: String,
      default: 'done'
    },
    confirmHold: {
      type: Boolean,
      default: false
    },
    showStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFocused: false,
      platform: '',
      isH5: false,
      isApp: false,
      isMiniProgram: false
    };
  },
  created() {
    this.detectPlatform();
  },
  mounted() {
    console.log(`🎯 NativeTextArea mounted on ${this.platform}`);
    this.updateCursorInfo();
  },
  methods: {
    // 检测平台
    detectPlatform() {
      // #ifdef H5
      this.platform = 'H5';
      this.isH5 = true;
      // #endif
      
      // #ifdef APP-PLUS
      this.platform = 'APP';
      this.isApp = true;
      // #endif
      
      // #ifdef MP
      this.platform = 'MP';
      this.isMiniProgram = true;
      // #endif
      
      console.log(`📱 检测到平台: ${this.platform}`);
    },

    // 输入处理
    handleInput(e) {
      const value = e.detail.value;
      this.$emit('update:modelValue', value);
      this.$emit('input', e);
      
      this.$nextTick(() => {
        this.updateCursorInfo();
      });
    },

    // 焦点处理
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
      
      console.log('📝 获得焦点');
      this.updateCursorInfo();
    },

    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
      
      console.log('📝 失去焦点');
    },

    // 确认处理
    handleConfirm(e) {
      this.$emit('confirm', e);
    },

    // 行变化处理
    handleLineChange(e) {
      this.$emit('linechange', e);
    },

    // 键盘高度变化处理
    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e);
    },

    // 更新光标信息
    updateCursorInfo() {
      this.$nextTick(() => {
        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // 在H5环境下可以获取选择信息
            if (this.isH5 && textarea.selectionStart !== undefined) {
              const start = textarea.selectionStart;
              const end = textarea.selectionEnd;
              
              this.$emit('cursor-change', {
                cursor: start,
                selectionStart: start,
                selectionEnd: end
              });
              
              console.log(`🎯 光标位置: ${start}, 选择: ${start}-${end}`);
            }
          } catch (error) {
            console.warn('获取光标信息失败:', error);
          }
        }
      });
    },

    // 设置光标位置
    setCursor(position) {
      const textarea = this.$refs.textareaRef;
      if (textarea && this.isH5) {
        try {
          textarea.setSelectionRange(position, position);
          textarea.focus();
          console.log(`🎯 设置光标到位置: ${position}`);
        } catch (error) {
          console.error('设置光标位置失败:', error);
        }
      }
    },

    // 设置选择范围
    setSelection(start, end) {
      const textarea = this.$refs.textareaRef;
      if (textarea && this.isH5) {
        try {
          textarea.setSelectionRange(start, end);
          textarea.focus();
          console.log(`🎯 设置选择范围: ${start}-${end}`);
        } catch (error) {
          console.error('设置选择范围失败:', error);
        }
      }
    },

    // 获取textarea元素
    getTextareaElement() {
      return this.$refs.textareaRef;
    },

    // 聚焦
    focusTextarea() {
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.focus();
      }
    },

    // 失焦
    blurTextarea() {
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.blur();
      }
    }
  }
};
</script>

<style scoped>
.native-textarea-container {
  position: relative;
  width: 100%;
}

.native-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.3s ease;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #fff;
}

.native-textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
  outline: none;
}

.native-textarea.is-h5 {
  /* H5特定样式 */
}

.native-textarea.is-app {
  /* APP特定样式 */
}

.native-textarea.is-mp {
  /* 小程序特定样式 */
}

.focused {
  /* 聚焦状态样式 */
}

.status-info {
  margin-top: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}
</style>
