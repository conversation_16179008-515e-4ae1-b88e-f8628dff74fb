<template>
  <view class="result-item" @tap="handleTap">
    <image :src="cover || defaultCover" mode="aspectFill" class="result-cover"></image>
    <view class="result-info">
      <view class="result-title">{{ title || '无标题' }}</view>
      <view class="result-date">{{ formatDate(createTime) }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ResultItem',
  props: {
    id: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    cover: {
      type: String,
      default: ''
    },
    createTime: {
      type: [Number, String, Date],
      default: Date.now
    },
    type: {
      type: String,
      default: 'text'
    },
    defaultCover: {
      type: String,
      default: '/static/images/default-text.png'
    }
  },
  methods: {
    formatDate(timestamp) {
      const date = new Date(timestamp);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${month}-${day}`;
    },
    handleTap() {
      this.$emit('tap', {
        id: this.id,
        type: this.type
      });
    }
  }
}
</script>

<style lang="scss">
.result-item {
  display: inline-block;
  width: 200rpx;
  margin: 0 10rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  .result-cover {
    width: 100%;
    height: 120rpx;
  }
  
  .result-info {
    padding: 10rpx;
  }
  
  .result-title {
    font-size: 24rpx;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333333;
  }
  
  .result-date {
    font-size: 20rpx;
    color: #999999;
    margin-top: 4rpx;
  }
}
</style> 