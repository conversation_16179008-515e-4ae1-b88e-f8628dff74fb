/**
 * 文生图功能工作流执行接口
 * 基于通用工作流基础类的文生图功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 文生图工作流配置, 文生图参数验证规则, 文生图错误码, 文生图状态 } from './工作流配置.js';

/**
 * 文生图工作流执行类
 */
class Workflow extends WorkflowBase {
    constructor() {
        super('文生图', 文生图工作流配置);
        this.validationRules = 文生图参数验证规则;
        this.errorCodes = 文生图错误码;
        this.statusCodes = 文生图状态;
    }

    /**
     * 执行文生图工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async execute(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`文生图进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '文生图',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('文生图工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证文生图参数
     * @param {Object} formData - 表单数据
     */
    validateParams(formData) {
        this.validateStructuredParams(formData, this.validationRules.required);
        return true;
    }

    /**
     * 构建文生图结构化参数
     * @param {Object} formData - 表单数据
     */
    buildParams(formData) {
        const builder = new StructuredParamsBuilder();

        
        if (formData.prompt) {
            builder.addTextParam('prompt', formData.prompt);
        }
        if (formData.style) {
            builder.addTextParam('style', formData.style);
        }
        if (formData.size) {
            builder.addTextParam('size', formData.size);
        }
        if (formData.quality) {
            builder.addTextParam('quality', formData.quality);
        }

        return builder.build();
    }
}

// 创建文生图工作流实例
const Workflow = new Workflow();

// 导出接口方法
export async function 执行文生图工作流(formData, options = {}) {
    return await Workflow.execute(formData, options);
}

export async function 查询文生图状态(requestId) {
    return await Workflow.queryWorkflowStatus(requestId);
}

export async function 取消文生图工作流(requestId) {
    return await Workflow.cancelWorkflow(requestId);
}

export default {
    执行文生图工作流,
    查询文生图状态,
    取消文生图工作流
};