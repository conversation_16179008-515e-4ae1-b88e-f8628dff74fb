<template>
	<view class="fullscreen-player" :style="playerStyles">
		<!-- 顶部栏 -->
		<view class="top-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<!-- 主题切换 -->
			<view class="theme-btn" @click="showThemeSelector = true">
				<text class="theme-text">主题颜色</text>
			</view>

			<!-- 关闭按钮 -->
			<view class="close-btn" @click="handleClose">
				<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
					<line x1="18" y1="6" x2="6" y2="18"></line>
					<line x1="6" y1="6" x2="18" y2="18"></line>
				</svg>
			</view>
		</view>
		
		<!-- 主内容区域 - 垂直滑动切歌 -->
		<swiper
			class="music-swiper"
			:vertical="true"
			:current="currentMusicIndex"
			@change="handleMusicChange"
			:duration="300"
		>
			<swiper-item v-for="(music, index) in playlist" :key="music.id">
				<view class="music-content">
					<!-- 上半部分: 封面区域(左右滑动) -->
					<view class="upper-content">
						<!-- 歌词显示模式 -->
						<view v-if="showLyricsMode" class="lyrics-mode-container">
							<scroll-view
								class="lyrics-scroll"
								scroll-y
								:scroll-into-view="'lyric-' + currentLyricsIndex"
								scroll-with-animation
							>
								<view
									v-for="(line, index) in parsedLyrics"
									:key="index"
									:id="'lyric-' + index"
									class="lyric-line"
									:class="{ active: index === currentLyricsIndex }"
									@click="handleLyricClick(line, index)"
								>
									<text class="lyric-text">{{ line.text }}</text>
								</view>
							</scroll-view>
						</view>

						<!-- 封面/推荐/评论模式 -->
						<swiper
							v-else
							class="cover-swiper"
							:indicator-dots="false"
							:duration="300"
							@change="handleSwiperChange"
							:current="currentSwiperIndex"
						>
							<!-- 第1页: 封面 -->
							<swiper-item>
								<view class="cover-page">
									<view class="cover-card">
										<image class="cover-image" :src="music.cover" mode="aspectFill"></image>
										<!-- 指示点 -->
										<view class="swiper-dots">
											<view
												v-for="(item, index) in swiperPages"
												:key="index"
												class="dot"
												:class="{ active: currentSwiperIndex === index }"
											></view>
										</view>
									</view>
								</view>
							</swiper-item>
							
							<!-- 第2页: 推荐歌曲 -->
							<swiper-item>
								<view class="recommend-page">
									<text class="page-title">推荐歌曲</text>
									<scroll-view class="recommend-list" scroll-y>
										<view
											v-for="item in recommendList"
											:key="item.id"
											class="recommend-item"
										>
											<image class="recommend-cover" :src="item.cover" mode="aspectFill"></image>
											<view class="recommend-info">
												<view class="recommend-title-wrapper">
													<text class="recommend-title">{{ item.title }}</text>
												</view>
												<text class="recommend-artist">{{ item.artist }}</text>
											</view>
											<view class="recommend-play-btn" @click.stop="handleRecommendClick(item)">
												<text class="play-icon">▶</text>
											</view>
										</view>
									</scroll-view>
								</view>
							</swiper-item>
							
							<!-- 第3页: 精选评论（仅在有评论时显示） -->
							<swiper-item v-if="hotComments.length > 0">
								<view class="comments-page">
									<text class="page-title">精选评论</text>
									<scroll-view class="comments-list" scroll-y>
										<view
											v-for="comment in hotComments"
											:key="comment.id"
											class="comment-item"
										>
											<view class="comment-header">
												<image class="comment-avatar" :src="comment.avatar" mode="aspectFill"></image>
												<view class="comment-user-info">
													<text class="comment-username">{{ comment.username }}</text>
													<text class="comment-time">{{ comment.time }}</text>
												</view>
											</view>
											<text class="comment-text">{{ comment.content }}</text>
											<view class="comment-footer">
												<view class="comment-likes" @click="handleCommentLike(comment)">
													<text class="like-icon">{{ comment.isLiked ? '❤️' : '🤍' }}</text>
													<text class="like-count">{{ formatLikeCount(comment.likes) }}</text>
												</view>
											</view>
										</view>
									</scroll-view>
								</view>
							</swiper-item>
						</swiper>

						<!-- 当前歌词显示（3行滚动） -->
						<view v-if="!showLyricsMode" class="current-lyrics-display">
							<view class="lyrics-lines">
								<text
									v-for="(line, index) in currentLyricsLines"
									:key="index"
									class="lyrics-line"
									:class="{ active: index === 1 }"
								>{{ line }}</text>
							</view>
						</view>
					</view>
					
					<!-- 下半部分: 信息和控制 -->
					<view class="lower-content">
						<!-- 歌曲标题和切换按钮 -->
						<view class="title-row">
							<view class="song-title-wrapper">
								<text class="song-title">{{ music.title }}</text>
							</view>
							<view class="lyrics-btn" @click="toggleLyricsMode">
								<text class="lyrics-text">{{ showLyricsMode ? '歌曲' : '歌词' }}</text>
							</view>
						</view>
						
						<!-- 作者和关注 -->
						<view class="author-row">
							<text class="author-name" @click="handleAuthorClick(music)">{{ music.artist }}</text>
							<view class="follow-btn" @click="handleFollow(music)">
								<text class="follow-text">+ 关注</text>
							</view>
						</view>
						
						<!-- 互动按钮 - 新顺序: 下载/分享/评论/喜欢/列表 -->
						<view class="interaction-row">
							<!-- 下载 -->
							<view class="action-btn" @click="handleDownload(music)">
								<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
									<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
									<polyline points="7 10 12 15 17 10"></polyline>
									<line x1="12" y1="15" x2="12" y2="3"></line>
								</svg>
								<text class="action-text">下载</text>
							</view>
							
							<!-- 分享 -->
							<view class="action-btn" @click="handleShare(music)">
								<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
									<circle cx="18" cy="5" r="3"></circle>
									<circle cx="6" cy="12" r="3"></circle>
									<circle cx="18" cy="19" r="3"></circle>
									<line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
									<line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
								</svg>
								<text class="action-text">分享</text>
							</view>
							
							<!-- 评论 -->
							<view class="action-btn" @click="handleComment(music)">
								<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
									<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
								</svg>
								<text class="action-text">{{ formatNumber(music.comments) }}</text>
							</view>
							
							<!-- 喜欢 -->
							<view class="action-btn like-btn" @click="handleLike(music)">
								<view class="heart-with-count">
									<svg class="action-icon heart-icon" :class="{ liked: music.isLiked }" viewBox="0 0 24 24" :fill="music.isLiked ? '#ff4757' : 'none'" stroke="currentColor" stroke-width="2">
										<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
									</svg>
									<text class="like-count-overlay">{{ formatLikeCount(music.likes) }}</text>
								</view>
								<text class="like-label-text">{{ music.isLiked ? '已喜欢' : '喜欢' }}</text>
							</view>
							
							<!-- 列表 -->
							<view class="action-btn" @click="showPlaylistDrawer = true">
								<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
									<line x1="8" y1="6" x2="21" y2="6"></line>
									<line x1="8" y1="12" x2="21" y2="12"></line>
									<line x1="8" y1="18" x2="21" y2="18"></line>
									<line x1="3" y1="6" x2="3.01" y2="6"></line>
									<line x1="3" y1="12" x2="3.01" y2="12"></line>
									<line x1="3" y1="18" x2="3.01" y2="18"></line>
								</svg>
							</view>
						</view>
						
						<!-- 进度条和循环按钮 -->
						<view class="progress-row">
							<!-- 播放按钮 -->
							<view class="play-btn-container" @click="handleTogglePlay">
								<svg v-if="!isPlaying" class="play-icon" viewBox="0 0 24 24" fill="currentColor">
									<polygon points="5 3 19 12 5 21 5 3"></polygon>
								</svg>
								<svg v-else class="play-icon" viewBox="0 0 24 24" fill="currentColor">
									<rect x="6" y="4" width="4" height="16"></rect>
									<rect x="14" y="4" width="4" height="16"></rect>
								</svg>
							</view>
							
							<!-- 进度条 -->
							<view class="progress-container">
								<text class="time-text">{{ formatTime(currentTime) }}</text>
								<view class="progress-bar" @click="handleProgressClick">
									<view class="progress-bg"></view>
									<view class="progress-fill" :style="{ width: progressPercent + '%', backgroundColor: currentTheme.colors.primary }"></view>
									<view class="progress-thumb" :style="{ left: progressPercent + '%', backgroundColor: currentTheme.colors.primary }"></view>
								</view>
								<text class="time-text">{{ formatTime(duration) }}</text>
							</view>
							
							<!-- 循环按钮 -->
							<view class="repeat-btn" @click="toggleRepeat">
								<svg class="repeat-icon" :class="{ active: isRepeat }" viewBox="0 0 24 24" fill="none" :stroke="isRepeat ? currentTheme.colors.primary : 'currentColor'" stroke-width="2">
									<polyline points="17 1 21 5 17 9"></polyline>
									<path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
									<polyline points="7 23 3 19 7 15"></polyline>
									<path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
								</svg>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
		
		<!-- 播放列表抽屉 -->
		<PlaylistDrawer
			:visible="showPlaylistDrawer"
			:playlist="playlist"
			:currentIndex="currentMusicIndex"
			:currentTheme="currentThemeKey"
			@close="showPlaylistDrawer = false"
			@select="handlePlaylistSelect"
			@delete="handlePlaylistDelete"
			@clear-all="handleClearPlaylist"
			@refresh="handlePlaylistRefresh"
		/>

		<!-- 全屏歌词页面已移除，改用内嵌歌词模式 -->
		
		<!-- 主题选择器 -->
		<view v-if="showThemeSelector" class="theme-selector-modal" @click.self="showThemeSelector = false">
			<view class="theme-selector-content">
				<view class="theme-selector-title">
					<text>选择主题</text>
				</view>
				<view class="theme-list">
					<view
						v-for="theme in themeList"
						:key="theme.key"
						class="theme-item"
						:class="{ active: currentThemeKey === theme.key }"
						@click="selectTheme(theme.key)"
					>
						<view
							class="theme-color-preview"
							:style="{ background: getTheme(theme.key).colors.background }"
						></view>
						<text class="theme-item-name">{{ theme.name }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import PlaylistDrawer from './PlaylistDrawer.vue';
import { getTheme, getAllThemes, applyTheme, getCurrentTheme } from '@/utils/playerThemes.js';

export default {
	name: 'FullScreenPlayer',
	components: {
		PlaylistDrawer
	},
	props: {
		// 播放列表
		playlist: {
			type: Array,
			required: true,
			default: () => []
		},
		// 初始播放索引
		initialIndex: {
			type: Number,
			default: 0
		},
		// 推荐列表
		recommendList: {
			type: Array,
			default: () => []
		},
		// 热门评论列表
		hotComments: {
			type: Array,
			default: () => []
		},
		// 播放状态(从父组件传入)
		isPlayingProp: {
			type: Boolean,
			default: false
		},
		// 当前播放时间(从父组件传入)
		currentTimeProp: {
			type: Number,
			default: 0
		},
		// 总时长(从父组件传入)
		durationProp: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			// 状态栏高度
			statusBarHeight: 0,

			// 当前播放索引
			currentMusicIndex: 0,

			// 播放状态(内部状态,会被props覆盖)
			isPlaying: false,
			currentTime: 0,
			duration: 0,
			currentLyricsIndex: 0,
			isRepeat: false,

			// UI状态
			showPlaylistDrawer: false,
			showLyricsPage: false,
			showThemeSelector: false,
			showLyricsMode: false, // 歌词显示模式
			currentSwiperIndex: 0, // 当前swiper索引

			// 主题
			currentThemeKey: 'tech_blue',
			themeList: []
		};
	},
	watch: {
		// 监听父组件传入的播放状态
		isPlayingProp(newVal) {
			this.isPlaying = newVal;
		},
		currentTimeProp(newVal) {
			this.currentTime = newVal;
		},
		durationProp(newVal) {
			this.duration = newVal;
		}
	},
	computed: {
		// 当前音乐
		currentMusic() {
			return this.playlist[this.currentMusicIndex] || {};
		},
		
		// 当前主题
		currentTheme() {
			return getTheme(this.currentThemeKey);
		},
		
		// 播放器样式
		playerStyles() {
			return {
				background: this.currentTheme.colors.background,
				color: this.currentTheme.colors.textPrimary
			};
		},
		
		// 进度百分比
		progressPercent() {
			if (!this.duration) return 0;
			return (this.currentTime / this.duration) * 100;
		},

		// Swiper页面数量（封面和推荐固定，评论动态）
		swiperPages() {
			// 封面和推荐页固定保留
			const pages = ['cover', 'recommend'];
			// 只有有评论时才添加评论页
			if (this.hotComments && this.hotComments.length > 0) {
				pages.push('comments');
			}
			return pages;
		},

		// 解析歌词
		parsedLyrics() {
			const music = this.currentMusic;
			if (!music.lyrics) return [{ text: '暂无歌词', time: 0 }];

			const lines = music.lyrics.split('\n').filter(line => line.trim());
			return lines.map((line, index) => ({
				text: line,
				time: index * 3 // 简单模拟时间，实际应该解析LRC格式
			}));
		},

		// 当前显示的歌词行（显示5行，当前行在第3行）
		currentLyricsLines() {
			const lyrics = this.parsedLyrics;
			if (lyrics.length === 0) return ['暂无歌词'];

			const currentIndex = this.currentLyricsIndex;
			const lines = [];

			// 显示前2行、当前行（第3行）、后2行（共5行）
			// 当前行在第3行位置（index=2）
			for (let i = currentIndex - 2; i <= currentIndex + 2; i++) {
				if (i >= 0 && i < lyrics.length) {
					lines.push(lyrics[i].text);
				} else {
					lines.push(''); // 填充空行
				}
			}

			return lines;
		}
	},
	mounted() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
		
		// 加载主题
		this.currentThemeKey = getCurrentTheme();
		this.themeList = getAllThemes();
		
		// 设置初始索引
		this.currentMusicIndex = this.initialIndex;
	},
	methods: {
		// 格式化时间
		formatTime(seconds) {
			const mins = Math.floor(seconds / 60);
			const secs = Math.floor(seconds % 60);
			return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
		},
		
		// 格式化数字
		formatNumber(num) {
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w';
			}
			if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k';
			}
			return num || 0;
		},

		// 格式化喜欢数量（更精确的显示）
		formatLikeCount(num) {
			if (!num) return '0';

			if (num >= 10000) {
				// 大于1万显示为 w
				return (num / 10000).toFixed(1).replace('.0', '') + 'w';
			}

			if (num >= 1000) {
				// 1000-9999 显示为 k
				const k = num / 1000;
				// 如果是整数k，不显示小数点
				if (k % 1 === 0) {
					return k + 'k';
				}
				// 如果小数部分是0.5，显示一位小数
				if (k % 1 === 0.5) {
					return k.toFixed(1) + 'k';
				}
				// 其他情况显示原数字
				return num.toString();
			}

			// 小于1000直接显示数字
			return num.toString();
		},
		
		// 获取当前歌词行
		getCurrentLyricLine(music) {
			if (!music.lyrics) return '暂无歌词';
			const lines = music.lyrics.split('\n').filter(line => line.trim());
			if (lines.length === 0) return '暂无歌词';
			// 返回第一句歌词
			return lines[0] || '暂无歌词';
		},
		
		// 关闭播放器
		handleClose() {
			this.$emit('close');
		},
		
		// 切换播放/暂停
		handleTogglePlay() {
			if (this.isPlaying) {
				this.$emit('pause');
			} else {
				this.$emit('play');
			}
		},
		
		// 切换循环模式
		toggleRepeat() {
			this.isRepeat = !this.isRepeat;
			this.$emit('repeat', this.isRepeat);
		},
		
		// 进度条点击
		handleProgressClick(e) {
			const { clientX } = e.detail || e;
			const { left, width } = e.currentTarget.getBoundingClientRect();
			const percent = (clientX - left) / width;
			const newTime = percent * this.duration;
			this.$emit('seek', newTime);
		},
		
		// 切换歌曲
		handleMusicChange(e) {
			const { current } = e.detail;
			this.currentMusicIndex = current;

			// 检查是否接近列表末尾（剩余3首时自动加载更多）
			const remainingSongs = this.playlist.length - current;
			if (remainingSongs <= 3) {
				console.log('接近列表末尾，自动加载更多推荐...');
				this.$emit('auto-load-more');
			}

			// 触发切换事件
			this.$emit('change', current);
		},
		
		// 切换歌词显示模式
		toggleLyricsMode() {
			this.showLyricsMode = !this.showLyricsMode;
		},

		// Swiper切换
		handleSwiperChange(e) {
			this.currentSwiperIndex = e.detail.current;
		},

		// 歌词点击（跳转播放）
		handleLyricClick(line, index) {
			this.currentLyricsIndex = index;
			// 根据歌词时间跳转播放
			const seekTime = line.time;
			this.$emit('seek', seekTime);
		},

		// 打开歌词页面（已废弃，改用toggleLyricsMode）
		openLyricsPage(music) {
			this.toggleLyricsMode();
		},

		// 作者点击
		handleAuthorClick(music) {
			this.$emit('author-click', music.authorId);
		},
		
		// 关注
		handleFollow(music) {
			this.$emit('follow', music.authorId);
		},
		
		// 下载
		handleDownload(music) {
			this.$emit('download', music);
		},
		
		// 分享
		handleShare(music) {
			this.$emit('share', music);
		},
		
		// 喜欢
		handleLike(music) {
			this.$emit('like', music);
		},
		
		// 评论
		handleComment(music) {
			this.$emit('comment', music);
		},
		
		// 推荐点击
		handleRecommendClick(item) {
			this.$emit('recommend-click', item);
		},

		// 评论点赞
		handleCommentLike(comment) {
			this.$emit('comment-like', comment);
		},

		// 格式化点赞数
		formatLikeCount(count) {
			if (count >= 10000) {
				return (count / 10000).toFixed(1) + 'w';
			}
			if (count >= 1000) {
				return (count / 1000).toFixed(1) + 'k';
			}
			return count || 0;
		},

		// 播放列表选择
		handlePlaylistSelect(index) {
			this.currentMusicIndex = index;
			this.showPlaylistDrawer = false;
			this.$emit('change', index);
		},
		
		// 播放列表删除
		handlePlaylistDelete(index) {
			this.$emit('playlist-delete', index);
		},
		
		// 清空播放列表
		handleClearPlaylist() {
			this.$emit('playlist-clear');
		},

		// 刷新播放列表推荐
		handlePlaylistRefresh() {
			this.$emit('playlist-refresh');
		},

		// 跳转
		handleSeek(time) {
			this.$emit('seek', time);
		},
		
		// 选择主题
		selectTheme(themeKey) {
			this.currentThemeKey = themeKey;
			applyTheme(themeKey);
			this.showThemeSelector = false;
		},

		// 获取主题配置（供模板使用）
		getTheme(themeName) {
			return getTheme(themeName);
		}
	}
};
</script>

<style lang="scss" scoped>
.fullscreen-player {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	flex-direction: column;
	/* 背景由 playerStyles 动态设置 */
}

/* 顶部栏 */
.top-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 20px;
	padding-top: 44px;
}

.theme-btn {
	padding: 8px 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 20px;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	cursor: pointer;
	transition: all 0.3s ease;
}

.theme-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.3);
}

.theme-text {
	font-size: 14px;
	color: #fff;
	font-weight: 500;
}

.close-btn {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.1);
	cursor: pointer;
}

.close-btn svg {
	width: 24px;
	height: 24px;
	color: #fff;
}

/* 音乐swiper */
.music-swiper {
	flex: 1;
	width: 100%;
}

.music-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}

/* 上半部分 */
.upper-content {
	flex: 1;
	padding: 20px;
	display: flex;
	flex-direction: column;
	position: relative;
}

.cover-swiper {
	width: 100%;
	flex: 1;
}

/* 封面页 */
.cover-page {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
}

/* 封面卡片 - 4:3横向比例 - 强制固定尺寸 */
.cover-card {
	position: relative;
	width: 400px !important; /* 4:3横向比例 - 强制固定 */
	height: 300px !important; /* 宽:高 = 4:3 - 强制固定 */
	min-width: 400px !important;
	max-width: 400px !important;
	min-height: 300px !important;
	max-height: 300px !important;
	background: rgba(255, 255, 255, 0.08);
	backdrop-filter: blur(20px);
	border-radius: 24px;
	overflow: hidden;
	/* 移除阴影 */
}

.cover-image {
	width: 100% !important;
	height: 100% !important;
	border-radius: 24px;
	display: block;
	object-fit: cover;
	object-position: center;
}

/* Swiper指示点 */
.swiper-dots {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 8px;
	margin-top: 15px;
}

.dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
}

.dot.active {
	width: 20px;
	border-radius: 3px;
	background: rgba(255, 255, 255, 0.9);
}

/* 当前歌词显示 */
.current-lyrics-display {
	margin-top: 20px;
	text-align: center;
	padding: 30px 20px;
	min-height: 180px;
	/* 移除背景 */
	transition: all 0.3s ease;
}

.lyrics-lines {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.lyrics-lines .lyrics-line {
	font-size: 20px; /* 未播放文字大 */
	color: rgba(255, 255, 255, 0.7);
	line-height: 2;
	transition: all 0.3s ease;
}

.lyrics-lines .lyrics-line.active {
	font-size: 18px; /* 播放中文字小 */
	color: rgba(255, 255, 255, 0.95);
	font-weight: 600;
	transform: scale(1.05); /* 轻微放大突出 */
}

/* 歌词显示模式 */
.lyrics-mode-container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.lyrics-scroll {
	flex: 1;
	padding: 20px 0;
}

.lyric-line {
	padding: 15px 20px;
	text-align: center;
	transition: all 0.3s ease;
	cursor: pointer;
}

.lyric-line .lyric-text {
	font-size: 16px;
	color: rgba(255, 255, 255, 0.5);
	line-height: 1.8;
	transition: all 0.3s ease;
}

.lyric-line.active .lyric-text {
	font-size: 20px;
	color: rgba(255, 255, 255, 1);
	font-weight: 500;
}

.lyric-line:active {
	background: rgba(255, 255, 255, 0.05);
}

/* 推荐页 */
.recommend-page, .comments-page {
	padding: 20px;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.page-title {
	font-size: 20px;
	font-weight: bold;
	color: #fff;
	margin-bottom: 20px;
}

.recommend-list, .comments-list {
	flex: 1;
}

.recommend-item {
	display: flex;
	align-items: center;
	padding: 12px;
	margin-bottom: 12px;
	background: rgba(255, 255, 255, 0.08);
	border-radius: 12px;
	transition: all 0.3s ease;
}

.recommend-item:active {
	background: rgba(255, 255, 255, 0.12);
}

.recommend-cover {
	width: 55px;
	height: 55px;
	border-radius: 10px;
	margin-right: 12px;
	flex-shrink: 0;
}

.recommend-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-width: 0;
	margin-right: 10px;
}

.recommend-title-wrapper {
	width: 100%;
	overflow: hidden;
}

.recommend-title {
	font-size: 15px;
	color: #fff;
	margin-bottom: 5px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* 移除固定动画，改为条件触发 */
}

/* 只有超长标题才滚动 */
.recommend-title.scrolling {
	animation: scroll-text 10s linear infinite;
}

@keyframes scroll-text {
	0% { transform: translateX(0); }
	50% { transform: translateX(calc(-100% + 80px)); }
	100% { transform: translateX(0); }
}

.recommend-artist {
	font-size: 13px;
	color: rgba(255, 255, 255, 0.6);
}

.recommend-play-btn {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	transition: all 0.3s ease;
}

.recommend-play-btn:active {
	transform: scale(0.9);
	background: rgba(255, 255, 255, 0.3);
}

.play-icon {
	font-size: 16px;
	color: #fff;
	margin-left: 2px;
}

/* 下半部分 */
.lower-content {
	padding: 20px;
	padding-bottom: 40px;
}

/* 标题行 */
.title-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	gap: 10px;
}

.song-title-wrapper {
	flex: 1;
	overflow: hidden;
	position: relative;
	max-width: calc(100% - 80px); /* 为歌词按钮留空间 */
}

.song-title {
	font-size: 20px;
	font-weight: bold;
	color: #fff;
	white-space: nowrap;
	display: inline-block;
}

/* 只有超长标题才滚动 - 通过JS动态添加类名 */
.song-title.scrolling {
	animation: scroll-title 15s linear infinite;
}

@keyframes scroll-title {
	0% { transform: translateX(0); }
	50% { transform: translateX(calc(-100% + 100px)); }
	100% { transform: translateX(0); }
}

.lyrics-btn {
	padding: 6px 16px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 18px;
	flex-shrink: 0;
	transition: all 0.3s ease;
}

.lyrics-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.3);
}

.lyrics-text {
	font-size: 14px;
	color: #fff;
	font-weight: 500;
}

/* 作者行 */
.author-row {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.author-name {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.8);
	margin-right: 10px;
}

.follow-btn {
	padding: 3px 10px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 12px;
}

.follow-text {
	font-size: 12px;
	color: #fff;
}

/* 互动行 */
.interaction-row {
	display: flex;
	justify-content: space-around;
	align-items: center;
	margin-bottom: 20px;
}

.action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5px;
}

.action-icon {
	width: 24px;
	height: 24px;
	color: rgba(255, 255, 255, 0.8);
}

.action-text {
	font-size: 12px;
	color: rgba(255, 255, 255, 0.6);
}

/* 喜欢按钮特殊样式 */
.like-btn {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 2px;
}

/* 心形+数字容器 */
.heart-with-count {
	position: relative;
	display: inline-block;
	width: 28px;
	height: 28px;
}

.heart-icon {
	width: 100%;
	height: 100%;
	transition: all 0.3s ease;
}

.heart-icon.liked {
	fill: #ff4757 !important;
	stroke: #ff4757;
	animation: heart-beat 0.3s ease;
}

/* 数字叠加在心形右上角 - 往上移，避免重叠 */
.like-count-overlay {
	position: absolute;
	top: -8px;
	right: -18px;
	font-size: 12px;
	color: #fff;
	font-weight: 700;
	z-index: 2;
	pointer-events: none;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* "喜欢"/"已喜欢"文字在下方 */
.like-label-text {
	font-size: 11px;
	color: rgba(255, 255, 255, 0.7);
}

@keyframes heart-beat {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.2); }
}

/* 进度行 */
.progress-row {
	display: flex;
	align-items: center;
	gap: 15px;
}

.play-btn-container {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	border: 1px solid rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		background: rgba(255, 255, 255, 1);
		transform: scale(1.1);
		box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
	}

	&:active {
		transform: scale(0.95);
	}
}

.play-icon {
	width: 20px;
	height: 20px;
	color: #333;
}

.progress-container {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 10px;
}

.time-text {
	font-size: 12px;
	color: rgba(255, 255, 255, 0.6);
}

.progress-bar {
	flex: 1;
	height: 4px;
	position: relative;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 2px;
}

.progress-fill {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	border-radius: 2px;
}

.progress-thumb {
	position: absolute;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 12px;
	height: 12px;
	border-radius: 50%;
}

.repeat-btn {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.repeat-icon {
	width: 24px;
	height: 24px;
	color: rgba(255, 255, 255, 0.6);
}

.repeat-icon.active {
	color: #fff;
}

/* 主题选择器 */
.theme-selector-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10000;
}

.theme-selector-content {
	width: 85%;
	max-width: 400px;
	background: #fff;
	border-radius: 20px;
	padding: 25px 20px;
	box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.theme-selector-title {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 20px;
	text-align: center;
}

.theme-list {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
}

.theme-item {
	flex: 0 0 calc(33.333% - 10px);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 15px;
	background: #f5f5f5;
	border-radius: 10px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.theme-item:active {
	transform: scale(0.95);
}

.theme-item.active {
	background: #e3f2fd;
	border: 2px solid #2196F3;
}

.theme-color-preview {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	margin-bottom: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.theme-item-name {
	font-size: 14px;
	color: #333;
	text-align: center;
}

/* 评论样式 */
.comment-item {
	padding: 15px;
	margin-bottom: 15px;
	background: rgba(255, 255, 255, 0.08);
	border-radius: 12px;
}

.comment-header {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.comment-avatar {
	width: 36px;
	height: 36px;
	border-radius: 50%;
	margin-right: 10px;
}

.comment-user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.comment-username {
	font-size: 14px;
	color: #fff;
	font-weight: 500;
	margin-bottom: 2px;
}

.comment-time {
	font-size: 12px;
	color: rgba(255, 255, 255, 0.5);
}

.comment-text {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.85);
	line-height: 1.6;
	margin-bottom: 10px;
}

.comment-footer {
	display: flex;
	justify-content: flex-end;
}

.comment-likes {
	display: flex;
	align-items: center;
	gap: 5px;
	padding: 5px 10px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 15px;
	cursor: pointer;
}

.like-icon {
	font-size: 14px;
}

.like-count {
	font-size: 12px;
	color: rgba(255, 255, 255, 0.7);
}

.empty-comments {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 200px;
}

.empty-text {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.5);
}
</style>

