/**
 * 统一的API请求工具
 * 提供基础的HTTP请求功能和错误处理
 */

// 基础配置
const API_BASE_URL = 'https://api.yunchuangai.com';
const API_VERSION = 'v1';

// 开发环境检测
const isDevelopment = () => {
	// 检测是否为开发环境
	try {
		return location.hostname === 'localhost' ||
		       location.hostname === '127.0.0.1' ||
		       location.port === '3000' ||
		       location.port === '5173' ||
		       location.href.includes('localhost') ||
		       (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development');
	} catch (error) {
		// 如果无法检测，默认为开发环境（安全起见）
		return true;
	}
};

/**
 * 统一的API请求方法
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 */
export async function apiRequest(endpoint, options = {}) {
	// 开发环境下直接返回失败，触发降级机制
	if (isDevelopment()) {
		console.log(`🔧 开发环境跳过API调用 [${endpoint}]，使用本地数据`);
		return Promise.reject(new Error('开发环境API跳过'));
	}

	const url = `${API_BASE_URL}/api/${endpoint}`;

	const defaultOptions = {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${getAuthToken()}`,
			'X-API-Version': API_VERSION
		}
	};

	const finalOptions = { ...defaultOptions, ...options };

	// 处理请求体
	if (finalOptions.body && typeof finalOptions.body === 'object') {
		finalOptions.body = JSON.stringify(finalOptions.body);
	}

	try {
		const response = await fetch(url, finalOptions);
		const data = await response.json();

		if (!response.ok) {
			throw new Error(data.message || `HTTP ${response.status}`);
		}

		return data;
	} catch (error) {
		console.error(`API请求失败 [${endpoint}]:`, error);
		throw error;
	}
}

/**
 * 获取认证Token
 */
function getAuthToken() {
	return uni.getStorageSync('authToken') || '';
}

/**
 * 设置认证Token
 * @param {string} token - 认证Token
 */
export function setAuthToken(token) {
	uni.setStorageSync('authToken', token);
}

/**
 * 清除认证Token
 */
export function clearAuthToken() {
	uni.removeStorageSync('authToken');
}

/**
 * 检查网络连接
 */
export function checkNetworkConnection() {
	return new Promise((resolve) => {
		uni.getNetworkType({
			success: (res) => {
				resolve(res.networkType !== 'none');
			},
			fail: () => {
				resolve(false);
			}
		});
	});
}

/**
 * 带重试的API请求
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @param {number} maxRetries - 最大重试次数
 */
export async function apiRequestWithRetry(endpoint, options = {}, maxRetries = 3) {
	let lastError;
	
	for (let i = 0; i <= maxRetries; i++) {
		try {
			// 检查网络连接
			const isConnected = await checkNetworkConnection();
			if (!isConnected) {
				throw new Error('网络连接不可用');
			}
			
			return await apiRequest(endpoint, options);
		} catch (error) {
			lastError = error;
			
			// 如果是最后一次重试，直接抛出错误
			if (i === maxRetries) {
				break;
			}
			
			// 等待一段时间后重试
			await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
		}
	}
	
	throw lastError;
}

export default {
	apiRequest,
	apiRequestWithRetry,
	setAuthToken,
	clearAuthToken,
	checkNetworkConnection
};
