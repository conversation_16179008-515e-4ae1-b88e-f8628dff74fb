/**
 * H5环境工具函数
 * 用于处理H5特定的功能和兼容性问题
 */

/**
 * 从URL中获取参数
 * @param {string} name 参数名
 * @param {string} [url] 可选的URL，默认使用当前页面URL
 * @returns {string|null} 参数值，如果不存在则返回null
 */
export const getUrlParam = (name, url) => {
  if (!url) url = window.location.href;
  name = name.replace(/[\[\]]/g, '\\$&');
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
  const results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
};

/**
 * 获取H5参数查询对象
 * 解析URL中的查询参数，返回对象
 * @param {string} [url] 可选的URL，默认使用当前页面URL
 * @returns {Object} 参数对象
 */
export const getH5QueryParams = (url) => {
  if (!url && typeof window !== 'undefined') {
    url = window.location.href;
  }
  if (!url) return {};
  
  // 解析URL中的查询参数
  const query = {};
  
  try {
    // 尝试使用URLSearchParams
    if (typeof URLSearchParams !== 'undefined') {
      // 获取查询部分
      let search = '';
      if (url.includes('?')) {
        search = url.split('?')[1];
        // 如果有哈希部分，移除
        if (search.includes('#')) {
          search = search.split('#')[0];
        }
      }
      
      const searchParams = new URLSearchParams(search);
      searchParams.forEach((value, key) => {
        query[key] = value;
      });
    } else {
      // 降级方案：手动解析
      const search = url.split('?')[1] || '';
      if (search) {
        const parts = search.split('&');
        parts.forEach(part => {
          if (part.includes('=')) {
            const [key, value] = part.split('=');
            query[decodeURIComponent(key)] = decodeURIComponent(value || '');
          }
        });
      }
    }
    
    console.log('解析H5参数完成:', query);
  } catch (e) {
    console.error('解析H5参数失败:', e);
  }
  
  return query;
};

/**
 * 检查是否为H5环境
 * @returns {boolean} 是否为H5环境
 */
export function isH5() {
  return typeof window !== 'undefined';
}

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器信息
 */
export const getBrowserInfo = () => {
  if (!isH5()) return { type: 'unknown', version: '0', isWechat: false };
  
  try {
    const ua = navigator.userAgent.toLowerCase();
    const isWechat = ua.indexOf('micromessenger') !== -1;
    let browserType = 'unknown';
    let browserVersion = '0';
    
    if (ua.match(/msie|trident/)) {
      browserType = 'ie';
      browserVersion = ua.match(/msie (\d+)/) ? RegExp.$1 : '11';
    } else if (ua.match(/firefox/)) {
      browserType = 'firefox';
      browserVersion = ua.match(/firefox\/(\d+)/) ? RegExp.$1 : '0';
    } else if (ua.match(/edge/)) {
      browserType = 'edge';
      browserVersion = ua.match(/edge\/(\d+)/) ? RegExp.$1 : '0';
    } else if (ua.match(/chrome/)) {
      browserType = 'chrome';
      browserVersion = ua.match(/chrome\/(\d+)/) ? RegExp.$1 : '0';
    } else if (ua.match(/safari/)) {
      browserType = 'safari';
      browserVersion = ua.match(/version\/(\d+)/) ? RegExp.$1 : '0';
    }
    
    return {
      type: browserType,
      version: browserVersion,
      isWechat: isWechat,
      userAgent: ua
    };
  } catch (e) {
    console.error('获取浏览器信息失败:', e);
    return { type: 'unknown', version: '0', isWechat: false };
  }
};

/**
 * 处理H5环境下的历史记录
 * 防止页面堆栈过多导致的问题
 */
export const cleanHistoryStack = () => {
  if (!isH5()) return;
  
  try {
    // 如果历史记录超过50个，进行清理
    if (window.history.length > 50) {
      // 使用replaceState替换当前页面，而不是添加新页面
      const currentUrl = window.location.href;
      window.history.replaceState(null, document.title, currentUrl);
      console.log('历史堆栈已清理');
    }
  } catch (e) {
    console.error('清理历史堆栈失败:', e);
  }
};

/**
 * 获取H5环境中的URL参数
 * @param {string} name - 参数名
 * @returns {string|null} 参数值
 * @deprecated 请使用getH5QueryParams代替
 */
export function getH5ParamQuery(name) {
  console.warn('getH5ParamQuery已废弃，请使用getH5QueryParams代替');
  
  if (!isH5()) return null;
  
  try {
    // 获取当前URL中的查询参数
    const hash = window.location.hash || '';
    const queryString = hash.split('?')[1] || '';
    
    if (!queryString) return null;
    
    // 解析查询参数
    const urlParams = new URLSearchParams(queryString);
    return urlParams.get(name);
  } catch (err) {
    console.error('获取H5参数出错:', err);
    return null;
  }
} 