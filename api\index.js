/**
 * API统一入口文件
 * 整合所有业务模块的接口
 * 更新时间：2025-01-11 - 添加新的功能板块
 */

// 导入各个业务模块
import 系统主页接口 from './系统主页/首页接口.js';
import 音乐主页接口 from './音乐主页/音乐主页接口.js';
import 音乐发现接口 from './音乐发现/音乐发现接口.js';
import 音乐交易接口 from './音乐交易/音乐交易接口.js';
import 创作者中心接口 from './创作者中心/创作者中心接口.js';
import 音乐创作接口 from './音乐创作/index.js';
import 用户系统接口 from './用户系统/用户系统接口.js';
import 支付系统接口 from './支付系统/支付系统接口.js';
import 个人中心接口 from './个人中心/个人中心接口.js';
import 其他功能接口 from './其他功能/其他功能接口.js';

// 导入新的功能板块
import { 起名功能API } from './起名功能/index.js';
import { 运程测试功能API } from './运程测试/index.js';
import { 姓名配对功能API } from './姓名配对/index.js';
import { 文生图功能API } from './文生图/index.js';
import { 文生视频功能API } from './文生视频/index.js';
import { 图生视频功能API } from './图生视频/index.js';
import { 文本处理功能API } from './文本处理/index.js';
import { 数字人功能API } from './数字人/index.js';

// ================================
// 🎯 统一API导出
// ================================

export const API = {
	// 系统主页
	系统主页: 系统主页接口,

	// 音乐相关
	音乐主页: 音乐主页接口,
	音乐发现: 音乐发现接口,
	音乐交易: 音乐交易接口,
	音乐创作: 音乐创作接口,

	// 用户相关
	创作者中心: 创作者中心接口,
	用户系统: 用户系统接口,
	个人中心: 个人中心接口,

	// 支付相关
	支付系统: 支付系统接口,

	// 其他功能
	其他功能: 其他功能接口,

	// 新功能板块 - 基于工作流的标准化接口
	起名功能: 起名功能API,
	运程测试: 运程测试功能API,
	姓名配对: 姓名配对功能API,
	文生图: 文生图功能API,
	文生视频: 文生视频功能API,
	图生视频: 图生视频功能API,
	文本处理: 文本处理功能API,
	数字人: 数字人功能API
};

// ================================
// 🚀 快捷方法导出
// ================================

// 用户认证相关
export const {
	用户登录,
	用户注册,
	用户登出,
	获取用户信息,
	完整登录流程
} = 用户系统接口;

// 音乐创作相关
export const {
	执行工作流,
	获取工作流完整配置,
	音乐创作完整业务流程,
	轮询工作流状态直到完成
} = 音乐创作接口;

// 支付相关
export const {
	获取用户金币余额,
	检查创作费用并扣费,
	完整充值流程,
	获取用户会员信息
} = 支付系统接口;

// 音乐发现相关
export const {
	获取热门音乐,
	获取最新音乐,
	获取推荐音乐,
	搜索音乐,
	获取音乐详情,
	点赞音乐,
	收藏音乐
} = 音乐发现接口;

// 交易相关
export const {
	获取交易统计,
	获取在售音乐列表,
	购买音乐,
	发布音乐销售,
	完整购买流程,
	完整销售流程
} = 音乐交易接口;

// 创作者相关
export const {
	获取创作者信息,
	获取创作者作品列表,
	发布作品,
	获取收益统计,
	完整发布作品流程,
	获取创作者完整信息
} = 创作者中心接口;

// 个人中心相关
export const {
	获取个人中心数据,
	获取用户订单列表,
	获取消费记录,
	获取创作历史,
	获取邀请信息,
	提交反馈,
	获取通知列表,
	获取个人中心完整数据
} = 个人中心接口;

// 其他功能相关
export const {
	完整算命流程,
	婚姻测试,
	运势测试,
	完整姓名生成流程,
	生成姓名,
	获取客服信息,
	创建客服会话,
	获取帮助文章列表,
	获取常见问题
} = 其他功能接口;

// ================================
// 🎯 业务场景封装
// ================================

/**
 * 用户完整登录并获取所有必要信息
 * @param {Object} loginData - 登录数据
 */
export async function 用户完整登录初始化(loginData) {
	try {
		// 1. 执行登录
		const loginResult = await 完整登录流程(loginData);
		
		// 2. 获取音乐相关数据
		const [
			musicHomeData,
			hotMusic,
			userCoins,
			membershipInfo
		] = await Promise.all([
			音乐主页接口.获取音乐主页数据(),
			音乐发现接口.获取热门音乐({ page: 1, pageSize: 10 }),
			支付系统接口.获取用户金币余额(),
			支付系统接口.获取用户会员信息()
		]);
		
		return {
			success: true,
			data: {
				...loginResult.data,
				musicHomeData: musicHomeData.data,
				hotMusic: hotMusic.data,
				userCoins: userCoins.data,
				membershipInfo: membershipInfo.data
			}
		};
		
	} catch (error) {
		console.error('用户完整登录初始化失败:', error);
		throw error;
	}
}

/**
 * 音乐创作完整业务流程（包含支付检查）
 * @param {string} mode - 创作模式
 * @param {Object} formData - 表单数据
 * @param {Object} options - 选项
 */
export async function 音乐创作完整业务流程增强版(mode, formData, options = {}) {
	try {
		// 1. 检查用户登录状态
		const userInfo = await 获取用户信息();
		if (!userInfo.success) {
			throw new Error('用户未登录');
		}
		
		// 2. 检查费用并扣费
		const paymentResult = await 检查创作费用并扣费({ mode, ...formData });
		if (!paymentResult.success) {
			return paymentResult; // 返回余额不足等错误
		}
		
		// 3. 执行音乐创作
		const creationResult = await 音乐创作完整业务流程(mode, formData, {
			...options,
			transactionId: paymentResult.data.transactionId
		});
		
		return {
			success: true,
			data: {
				...creationResult.data,
				paymentInfo: paymentResult.data
			}
		};
		
	} catch (error) {
		console.error('音乐创作完整业务流程增强版失败:', error);
		throw error;
	}
}

/**
 * 获取首页完整数据
 */
export async function 获取首页完整数据() {
	try {
		const [
			homeData,
			musicHomeData,
			hotMusic,
			userInfo,
			userCoins
		] = await Promise.all([
			系统主页接口.获取首页数据(),
			音乐主页接口.获取音乐主页数据(),
			音乐发现接口.获取热门音乐({ page: 1, pageSize: 6 }),
			用户系统接口.获取用户信息().catch(() => null), // 可能未登录
			支付系统接口.获取用户金币余额().catch(() => null) // 可能未登录
		]);
		
		return {
			success: true,
			data: {
				homeData: homeData.data,
				musicHomeData: musicHomeData.data,
				hotMusic: hotMusic.data,
				userInfo: userInfo?.data || null,
				userCoins: userCoins?.data || null
			}
		};
		
	} catch (error) {
		console.error('获取首页完整数据失败:', error);
		throw error;
	}
}

/**
 * 获取发现页完整数据
 */
export async function 获取发现页完整数据() {
	try {
		const [
			hotMusic,
			latestMusic,
			recommendedMusic,
			categories
		] = await Promise.all([
			音乐发现接口.获取热门音乐({ page: 1, pageSize: 10 }),
			音乐发现接口.获取最新音乐({ page: 1, pageSize: 10 }),
			音乐发现接口.获取推荐音乐({ page: 1, pageSize: 10 }),
			音乐发现接口.获取音乐分类()
		]);
		
		return {
			success: true,
			data: {
				hotMusic: hotMusic.data,
				latestMusic: latestMusic.data,
				recommendedMusic: recommendedMusic.data,
				categories: categories.data
			}
		};
		
	} catch (error) {
		console.error('获取发现页完整数据失败:', error);
		throw error;
	}
}

/**
 * 获取交易页完整数据
 */
export async function 获取交易页完整数据() {
	try {
		const [
			tradeStats,
			sellingMusic,
			tradeHistory
		] = await Promise.all([
			音乐交易接口.获取交易统计(),
			音乐交易接口.获取在售音乐列表({ page: 1, pageSize: 20 }),
			音乐交易接口.获取交易历史({ page: 1, pageSize: 10 })
		]);
		
		return {
			success: true,
			data: {
				tradeStats: tradeStats.data,
				sellingMusic: sellingMusic.data,
				tradeHistory: tradeHistory.data
			}
		};
		
	} catch (error) {
		console.error('获取交易页完整数据失败:', error);
		throw error;
	}
}

/**
 * 获取创作者中心完整数据
 */
export async function 获取创作者中心完整数据() {
	try {
		const [
			creatorInfo,
			creatorWorks,
			revenueStats
		] = await Promise.all([
			创作者中心接口.获取创作者完整信息(),
			创作者中心接口.获取创作者作品列表({ page: 1, pageSize: 20 }),
			创作者中心接口.获取收益统计()
		]);

		return {
			success: true,
			data: {
				creatorInfo: creatorInfo.data,
				creatorWorks: creatorWorks.data,
				revenueStats: revenueStats.data
			}
		};

	} catch (error) {
		console.error('获取创作者中心完整数据失败:', error);
		throw error;
	}
}

/**
 * 获取个人中心完整数据
 */
export async function 获取个人中心完整数据() {
	try {
		const [
			personalData,
			recentOrders,
			consumptionStats,
			membershipInfo,
			notifications
		] = await Promise.all([
			个人中心接口.获取个人中心完整数据(),
			个人中心接口.获取用户订单列表({ page: 1, pageSize: 5 }),
			个人中心接口.获取消费统计({ period: 'month' }),
			支付系统接口.获取用户会员信息(),
			个人中心接口.获取通知列表({ page: 1, pageSize: 10, unreadOnly: true })
		]);

		return {
			success: true,
			data: {
				personalData: personalData.data,
				recentOrders: recentOrders.data,
				consumptionStats: consumptionStats.data,
				membershipInfo: membershipInfo.data,
				notifications: notifications.data
			}
		};

	} catch (error) {
		console.error('获取个人中心完整数据失败:', error);
		throw error;
	}
}

// ================================
// 🔧 工具函数
// ================================

/**
 * 检查用户登录状态
 */
export async function 检查用户登录状态() {
	try {
		const userInfo = await 获取用户信息();
		return {
			isLoggedIn: userInfo.success,
			userInfo: userInfo.success ? userInfo.data : null
		};
	} catch (error) {
		return {
			isLoggedIn: false,
			userInfo: null
		};
	}
}

/**
 * 统一错误处理
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 */
export function 统一错误处理(error, context = '') {
	console.error(`${context}错误:`, error);
	
	// 根据错误类型返回用户友好的错误信息
	if (error.message?.includes('网络')) {
		return '网络连接异常，请检查网络后重试';
	} else if (error.message?.includes('登录')) {
		return '登录状态已过期，请重新登录';
	} else if (error.message?.includes('余额')) {
		return '金币余额不足，请先充值';
	} else {
		return error.message || '操作失败，请稍后重试';
	}
}

// 默认导出
export default API;
