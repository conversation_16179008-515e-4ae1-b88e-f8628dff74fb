<template>
  <component 
    :is="currentComponent" 
    :content="content" 
    @action="handleAction"
  />
</template>

<script>
// 导入场景展示组件
import DefaultTextDisplay from './display/DefaultTextDisplay.vue';
import StoryDisplay from './display/StoryDisplay.vue';
import HomeworkDisplay from './display/HomeworkDisplay.vue';
import ArticleDisplay from './display/ArticleDisplay.vue';
import ResumeDisplay from './display/ResumeDisplay.vue';
import TranslateDisplay from './display/TranslateDisplay.vue';
import { getDynamicAdapter } from '@/api/scenario';

export default {
  name: 'ScenarioDisplayManager',
  components: {
    DefaultTextDisplay,
    StoryDisplay,
    HomeworkDisplay,
    ArticleDisplay,
    ResumeDisplay,
    TranslateDisplay
  },
  props: {
    content: {
      type: Object,
      required: true
    },
    scenario: {
      type: String,
      default: 'general'
    },
    displayComponent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dynamicComponents: {},
      loadingComponent: false
    };
  },
  computed: {
    currentComponent() {
      // 首先检查是否有指定的显示组件
      if (this.displayComponent && this.$options.components[this.displayComponent]) {
        return this.displayComponent;
      }
      
      // 检查是否有动态注册的组件
      if (this.dynamicComponents[this.scenario]) {
        return this.dynamicComponents[this.scenario];
      }
      
      // 否则按照场景类型选择组件
      switch (this.scenario) {
        case 'story':
          return 'StoryDisplay';
        case 'homework':
          return 'HomeworkDisplay';
        case 'article':
          return 'ArticleDisplay';
        case 'resume':
          return 'ResumeDisplay';
        case 'translate':
          return 'TranslateDisplay';
        default:
          return 'DefaultTextDisplay';
      }
    }
  },
  created() {
    // 尝试加载动态场景适配器
    this.loadDynamicComponent();
  },
  watch: {
    scenario(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.loadDynamicComponent();
      }
    }
  },
  methods: {
    handleAction(action, data) {
      // 将子组件的事件向上传递
      this.$emit('action', action, data);
    },
    
    // 动态加载场景组件
    async loadDynamicComponent() {
      // 如果已经有这个场景的组件，不需要重新加载
      if (this.dynamicComponents[this.scenario]) {
        return;
      }
      
      // 如果是内置场景，不需要动态加载
      const builtInScenarios = ['general', 'story', 'homework', 'article', 'resume', 'translate'];
      if (builtInScenarios.includes(this.scenario)) {
        return;
      }
      
      this.loadingComponent = true;
      
      try {
        // 获取动态适配器
        const adapter = getDynamicAdapter(this.scenario);
        
        if (adapter && adapter.displayComponent) {
          // 检查是否是已注册的组件
          if (this.$options.components[adapter.displayComponent]) {
            this.dynamicComponents[this.scenario] = adapter.displayComponent;
          } else {
            // 尝试动态导入组件
            try {
              const dynamicComponent = await import(`@/components/display/${adapter.displayComponent}.vue`);
              // 注册组件
              this.$options.components[adapter.displayComponent] = dynamicComponent.default;
              this.dynamicComponents[this.scenario] = adapter.displayComponent;
            } catch (error) {
              console.error(`无法加载动态组件: ${adapter.displayComponent}`, error);
              // 使用默认组件
              this.dynamicComponents[this.scenario] = 'DefaultTextDisplay';
            }
          }
        } else {
          // 没有找到适配器或适配器没有指定显示组件，使用默认组件
          this.dynamicComponents[this.scenario] = 'DefaultTextDisplay';
        }
      } catch (error) {
        console.error(`加载动态组件失败: ${this.scenario}`, error);
        this.dynamicComponents[this.scenario] = 'DefaultTextDisplay';
      } finally {
        this.loadingComponent = false;
      }
    }
  }
}
</script>

<style scoped>
/* 无需特定样式，由子组件提供样式 */
</style> 