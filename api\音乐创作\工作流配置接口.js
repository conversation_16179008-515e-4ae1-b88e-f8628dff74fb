/**
 * 工作流配置接口
 * 动态获取每个工作流模式的独立配置信息
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🔧 工作流配置接口
// ================================

/**
 * 获取工作流基础配置
 * @param {string} workflowMode - 工作流模式 ("simple", "advanced", "instrumental")
 */
export async function 获取工作流基础配置(workflowMode) {
	return await apiRequest(`music-creation/workflow-config/${workflowMode}`);
}

/**
 * 获取工作流价格配置
 * @param {string} workflowMode - 工作流模式
 */
export async function 获取工作流价格配置(workflowMode) {
	return await apiRequest(`music-creation/workflow-pricing/${workflowMode}`);
}

/**
 * 获取工作流提示词库
 * @param {string} workflowMode - 工作流模式
 * @param {Object} params - 查询参数
 */
export async function 获取工作流提示词库(workflowMode, params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music-creation/workflow-prompts/${workflowMode}?${queryParams}`);
}

/**
 * 获取工作流资源库
 * @param {string} workflowMode - 工作流模式
 * @param {Object} params - 查询参数
 */
export async function 获取工作流资源库(workflowMode, params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music-creation/workflow-resources/${workflowMode}?${queryParams}`);
}

/**
 * 获取工作流参数设置
 * @param {string} workflowMode - 工作流模式
 */
export async function 获取工作流参数设置(workflowMode) {
	return await apiRequest(`music-creation/workflow-parameters/${workflowMode}`);
}

/**
 * 获取工作流完整配置（一次性获取所有配置）
 * @param {string} workflowMode - 工作流模式
 */
export async function 获取工作流完整配置(workflowMode) {
	return await apiRequest(`music-creation/workflow-full-config/${workflowMode}`);
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 初始化工作流模式配置
 * @param {string} workflowMode - 工作流模式
 */
export async function 初始化工作流模式配置(workflowMode) {
	try {
		// 并行获取所有配置
		const [
			基础配置,
			价格配置,
			提示词库,
			资源库,
			参数设置
		] = await Promise.all([
			获取工作流基础配置(workflowMode),
			获取工作流价格配置(workflowMode),
			获取工作流提示词库(workflowMode, { page: 1, pageSize: 20 }),
			获取工作流资源库(workflowMode, { page: 1, pageSize: 10 }),
			获取工作流参数设置(workflowMode)
		]);

		return {
			success: true,
			data: {
				workflowMode,
				基础配置: 基础配置.data,
				价格配置: 价格配置.data,
				提示词库: 提示词库.data,
				资源库: 资源库.data,
				参数设置: 参数设置.data
			}
		};

	} catch (error) {
		console.error(`初始化${workflowMode}模式配置失败:`, error);
		throw error;
	}
}

/**
 * 获取模式显示配置
 * @param {string} workflowMode - 工作流模式
 */
export async function 获取模式显示配置(workflowMode) {
	try {
		const result = await 获取工作流基础配置(workflowMode);
		return {
			success: true,
			data: {
				modeName: result.data.displayName,
				modeIcon: result.data.icon,
				modeDescription: result.data.description,
				isEnabled: result.data.enabled,
				sortOrder: result.data.sortOrder
			}
		};
	} catch (error) {
		console.error(`获取${workflowMode}显示配置失败:`, error);
		throw error;
	}
}

/**
 * 缓存工作流配置
 */
const configCache = new Map();

/**
 * 获取缓存的工作流配置
 * @param {string} workflowMode - 工作流模式
 * @param {boolean} forceRefresh - 是否强制刷新
 */
export async function 获取缓存的工作流配置(workflowMode, forceRefresh = false) {
	const cacheKey = `workflow_config_${workflowMode}`;
	
	if (!forceRefresh && configCache.has(cacheKey)) {
		const cached = configCache.get(cacheKey);
		// 检查缓存是否过期（5分钟）
		if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
			return cached.data;
		}
	}
	
	// 获取新配置
	const config = await 初始化工作流模式配置(workflowMode);
	
	// 缓存配置
	configCache.set(cacheKey, {
		data: config,
		timestamp: Date.now()
	});
	
	return config;
}

export default {
	获取工作流基础配置,
	获取工作流价格配置,
	获取工作流提示词库,
	获取工作流资源库,
	获取工作流参数设置,
	获取工作流完整配置,
	初始化工作流模式配置,
	获取模式显示配置,
	获取缓存的工作流配置
};
