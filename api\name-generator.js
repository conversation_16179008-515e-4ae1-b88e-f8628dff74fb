/**
 * 智能起名系统 API 接口
 * 提供基于生辰八字和五行理论的智能起名服务
 */

const BASE_URL = 'http://aaa.fanshengyun.com';

/**
 * 起名系统 API 类
 */
class NameGeneratorAPI {
    /**
     * 生成名字推荐
     * @param {Object} params - 起名参数
     * @param {string} params.surname - 姓氏
     * @param {string} params.gender - 性别 ('male' | 'female')
     * @param {string} params.birthDate - 出生日期 (YYYY-MM-DD)
     * @param {string} params.birthTime - 出生时间 (HH:MM)
     * @param {number} params.nameLength - 名字长度 (2 | 3)
     * @param {string} params.nameStyle - 起名风格
     * @param {string} params.expectedMeaning - 期望寓意
     * @returns {Promise<Object>} API响应
     */
    async generateNames(params) {
        try {
            console.log('调用起名API:', params);

            const response = await uni.request({
                url: `${BASE_URL}/api/name-generator`,
                method: 'POST',
                header: {
                    'Content-Type': 'application/json'
                },
                data: params,
                timeout: 30000
            });

            console.log('起名API响应:', response);

            if (response.statusCode === 200) {
                return response.data;
            } else {
                throw new Error(`API请求失败: ${response.statusCode}`);
            }

        } catch (error) {
            console.error('起名API调用失败:', error);
            
            // 返回模拟数据作为降级方案
            return this.getMockNameData(params);
        }
    }

    /**
     * 获取模拟起名数据
     * @param {Object} params - 起名参数
     * @returns {Object} 模拟数据
     */
    getMockNameData(params) {
        const { surname, gender, nameLength } = params;
        
        // 根据性别和长度生成不同的模拟数据
        const maleNames2 = [
            { givenName: '子轩', score: 95, meaning: '品德高尚，气宇轩昂', wuxing: '水土', strokes: '3+10', recommended: true },
            { givenName: '浩然', score: 92, meaning: '胸怀宽广，正气凛然', wuxing: '水金', strokes: '11+12' },
            { givenName: '文博', score: 90, meaning: '文采斐然，博学多才', wuxing: '水水', strokes: '4+12' },
            { givenName: '志远', score: 88, meaning: '志向远大，前程似锦', wuxing: '火土', strokes: '7+17' },
            { givenName: '晨阳', score: 87, meaning: '朝气蓬勃，阳光开朗', wuxing: '金土', strokes: '11+17' },
            { givenName: '思源', score: 85, meaning: '饮水思源，知恩图报', wuxing: '金水', strokes: '9+14' },
            { givenName: '宇航', score: 83, meaning: '胸怀宇宙，勇于探索', wuxing: '土水', strokes: '6+10' },
            { givenName: '明轩', score: 82, meaning: '聪明睿智，气度不凡', wuxing: '火土', strokes: '8+10' }
        ];

        const femaleNames2 = [
            { givenName: '雨萱', score: 96, meaning: '如雨后甘露，清香怡人', wuxing: '水木', strokes: '8+15', recommended: true },
            { givenName: '诗涵', score: 93, meaning: '诗情画意，内涵丰富', wuxing: '金水', strokes: '13+12' },
            { givenName: '梦琪', score: 91, meaning: '美梦成真，珍贵如玉', wuxing: '木木', strokes: '14+13' },
            { givenName: '欣怡', score: 89, meaning: '欣欣向荣，怡然自得', wuxing: '木土', strokes: '8+9' },
            { givenName: '婉清', score: 87, meaning: '温婉如玉，清雅脱俗', wuxing: '土水', strokes: '11+12' },
            { givenName: '芷若', score: 86, meaning: '芷兰之室，若水清澈', wuxing: '木木', strokes: '10+11' },
            { givenName: '语嫣', score: 84, meaning: '语笑嫣然，美丽动人', wuxing: '木土', strokes: '14+14' },
            { givenName: '慧心', score: 83, meaning: '慧心巧思，心灵手巧', wuxing: '水金', strokes: '15+4' }
        ];

        const maleNames3 = [
            { givenName: '子轩宇', score: 94, meaning: '品德高尚，气宇轩昂，胸怀宇宙', wuxing: '水土土', strokes: '3+10+6', recommended: true },
            { givenName: '浩然博', score: 91, meaning: '胸怀宽广，正气凛然，博学多才', wuxing: '水金水', strokes: '11+12+12' },
            { givenName: '文博轩', score: 89, meaning: '文采斐然，博学多才，气宇轩昂', wuxing: '水水土', strokes: '4+12+10' },
            { givenName: '志远航', score: 87, meaning: '志向远大，前程似锦，勇于探索', wuxing: '火土水', strokes: '7+17+10' },
            { givenName: '晨阳辉', score: 85, meaning: '朝气蓬勃，阳光开朗，光辉灿烂', wuxing: '金土火', strokes: '11+17+15' },
            { givenName: '思源泽', score: 84, meaning: '饮水思源，知恩图报，恩泽万物', wuxing: '金水水', strokes: '9+14+17' },
            { givenName: '宇航天', score: 82, meaning: '胸怀宇宙，勇于探索，天赋异禀', wuxing: '土水火', strokes: '6+10+4' },
            { givenName: '明轩昊', score: 81, meaning: '聪明睿智，气度不凡，昊天罔极', wuxing: '火土火', strokes: '8+10+8' }
        ];

        const femaleNames3 = [
            { givenName: '雨萱儿', score: 95, meaning: '如雨后甘露，清香怡人，天真可爱', wuxing: '水木金', strokes: '8+15+8', recommended: true },
            { givenName: '诗涵雅', score: 92, meaning: '诗情画意，内涵丰富，雅致脱俗', wuxing: '金水木', strokes: '13+12+12' },
            { givenName: '梦琪瑶', score: 90, meaning: '美梦成真，珍贵如玉，瑶池仙子', wuxing: '木木火', strokes: '14+13+15' },
            { givenName: '欣怡然', score: 88, meaning: '欣欣向荣，怡然自得，自然天成', wuxing: '木土金', strokes: '8+9+12' },
            { givenName: '婉清雪', score: 86, meaning: '温婉如玉，清雅脱俗，纯洁如雪', wuxing: '土水水', strokes: '11+12+11' },
            { givenName: '芷若兰', score: 85, meaning: '芷兰之室，若水清澈，兰心蕙质', wuxing: '木木木', strokes: '10+11+23' },
            { givenName: '语嫣然', score: 83, meaning: '语笑嫣然，美丽动人，自然天成', wuxing: '木土金', strokes: '14+14+12' },
            { givenName: '慧心妍', score: 82, meaning: '慧心巧思，心灵手巧，美丽动人', wuxing: '水金水', strokes: '15+4+7' }
        ];

        // 根据参数选择对应的名字数据
        let names;
        if (gender === 'male') {
            names = nameLength === 3 ? maleNames3 : maleNames2;
        } else {
            names = nameLength === 3 ? femaleNames3 : femaleNames2;
        }

        // 模拟五行分析数据
        const wuxingAnalysis = {
            elements: {
                金: Math.floor(Math.random() * 40) + 30,
                木: Math.floor(Math.random() * 40) + 40,
                水: Math.floor(Math.random() * 40) + 50,
                火: Math.floor(Math.random() * 40) + 20,
                土: Math.floor(Math.random() * 40) + 35
            },
            weakest: '火',
            strongest: '水',
            needsBalance: true
        };

        return {
            success: true,
            data: {
                recommendations: names,
                wuxingAnalysis,
                totalCandidates: names.length
            }
        };
    }

    /**
     * 获取名字详细分析
     * @param {string} fullName - 完整姓名
     * @returns {Promise<Object>} 详细分析结果
     */
    async getNameAnalysis(fullName) {
        try {
            const response = await uni.request({
                url: `${BASE_URL}/api/name-analysis`,
                method: 'POST',
                header: {
                    'Content-Type': 'application/json'
                },
                data: { fullName },
                timeout: 15000
            });

            if (response.statusCode === 200) {
                return response.data;
            } else {
                throw new Error(`API请求失败: ${response.statusCode}`);
            }

        } catch (error) {
            console.error('名字分析API调用失败:', error);
            
            // 返回模拟分析数据
            return this.getMockAnalysisData(fullName);
        }
    }

    /**
     * 获取模拟分析数据
     * @param {string} fullName - 完整姓名
     * @returns {Object} 模拟分析数据
     */
    getMockAnalysisData(fullName) {
        return {
            success: true,
            data: {
                fullName,
                overallScore: Math.floor(Math.random() * 20) + 80,
                wuxingAnalysis: {
                    balance: '五行较为均衡，有利于个人发展',
                    suggestion: '建议在日常生活中多接触木属性的事物，如绿色植物等'
                },
                strokeAnalysis: {
                    total: Math.floor(Math.random() * 30) + 20,
                    luck: '笔画数吉利，有利于事业发展和人际关系'
                },
                phoneticAnalysis: {
                    rhythm: '音韵和谐，读音响亮，朗朗上口',
                    tone: '声调搭配合理，富有节奏感'
                },
                culturalMeaning: {
                    origin: '名字蕴含深厚的文化底蕴',
                    symbolism: '寓意美好，象征着光明的前程和美好的未来'
                },
                suggestions: [
                    '这个名字整体寓意非常好，有利于个人成长',
                    '建议在重要场合多使用全名，增强个人气场',
                    '可以考虑在名片或签名中突出名字的书法美感'
                ]
            }
        };
    }

    /**
     * 批量验证名字
     * @param {Array} names - 名字列表
     * @returns {Promise<Object>} 验证结果
     */
    async validateNames(names) {
        try {
            const response = await uni.request({
                url: `${BASE_URL}/api/name-validate`,
                method: 'POST',
                header: {
                    'Content-Type': 'application/json'
                },
                data: { names },
                timeout: 15000
            });

            if (response.statusCode === 200) {
                return response.data;
            } else {
                throw new Error(`API请求失败: ${response.statusCode}`);
            }

        } catch (error) {
            console.error('名字验证API调用失败:', error);
            
            // 返回模拟验证结果
            return {
                success: true,
                data: names.map(name => ({
                    name,
                    isValid: true,
                    score: Math.floor(Math.random() * 20) + 80,
                    issues: []
                }))
            };
        }
    }

    /**
     * 获取起名建议
     * @param {Object} params - 用户信息
     * @returns {Promise<Object>} 起名建议
     */
    async getNameSuggestions(params) {
        try {
            const response = await uni.request({
                url: `${BASE_URL}/api/name-suggestions`,
                method: 'POST',
                header: {
                    'Content-Type': 'application/json'
                },
                data: params,
                timeout: 15000
            });

            if (response.statusCode === 200) {
                return response.data;
            } else {
                throw new Error(`API请求失败: ${response.statusCode}`);
            }

        } catch (error) {
            console.error('起名建议API调用失败:', error);
            
            // 返回模拟建议数据
            return {
                success: true,
                data: {
                    suggestions: [
                        '根据生辰八字分析，建议选择五行属火的字来平衡命理',
                        '考虑到现代社会的特点，建议选择笔画适中、易写易记的名字',
                        '可以从古典诗词中寻找灵感，增加名字的文化内涵',
                        '注意名字的音韵搭配，避免谐音带来的不良影响'
                    ],
                    recommendedChars: {
                        fire: ['炎', '焱', '烁', '煜', '炜', '烨', '灿', '辉'],
                        meaning: ['智', '慧', '德', '仁', '美', '好', '雅', '清']
                    },
                    avoidChars: ['某些生僻字', '谐音不好的字', '笔画过多的字']
                }
            };
        }
    }
}

// 创建API实例
const nameGeneratorAPI = new NameGeneratorAPI();

// 导出API方法
export {
    nameGeneratorAPI
};
