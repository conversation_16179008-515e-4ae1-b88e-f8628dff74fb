# 姓名配对功能 - 后端实现指导

## 🎯 **实现目标**

为姓名配对功能提供完整的后端实现，包括参数构建、大模型调用、结果解析和数据清洗。

## 🔧 **核心实现模块**

### **1. 参数验证器**
```python
class NameMatchingValidator:
    @staticmethod
    def validate_request(form_data):
        """验证请求参数"""
        errors = []
        
        # 验证姓名1
        name1 = form_data.get('name1', '').strip()
        if not name1:
            errors.append("name1不能为空")
        elif not re.match(r'^[\u4e00-\u9fa5]{2,4}$', name1):
            errors.append("name1必须为2-4个中文字符")
        
        # 验证姓名2
        name2 = form_data.get('name2', '').strip()
        if not name2:
            errors.append("name2不能为空")
        elif not re.match(r'^[\u4e00-\u9fa5]{2,4}$', name2):
            errors.append("name2必须为2-4个中文字符")
        
        # 验证不能相同
        if name1 == name2:
            errors.append("不能与自己进行配对")
        
        # 验证性别
        valid_genders = ['male', 'female', '']
        if form_data.get('gender1', '') not in valid_genders:
            errors.append("gender1参数无效")
        if form_data.get('gender2', '') not in valid_genders:
            errors.append("gender2参数无效")
        
        # 验证分析类型
        valid_types = ['basic', 'detailed', 'professional']
        analysis_type = form_data.get('analysisType', 'detailed')
        if analysis_type not in valid_types:
            errors.append("analysisType参数无效")
        
        return len(errors) == 0, errors

    @staticmethod
    def get_cost(analysis_type, is_member=False):
        """获取费用"""
        costs = {
            'basic': 12,
            'detailed': 17,
            'professional': 22
        }
        base_cost = costs.get(analysis_type, 17)
        return int(base_cost * 0.8) if is_member else base_cost
```

### **2. 结构化参数构建器**
```python
class NameMatchingParamsBuilder:
    @staticmethod
    def build(form_data, user_id):
        """构建发送给大模型的结构化参数"""
        request_id = f"姓名配对_{int(time.time() * 1000)}_{generate_random_id()}"
        
        return {
            "requestId": request_id,
            "userId": user_id,
            "timestamp": int(time.time() * 1000),
            "moduleName": "姓名配对",
            "workflowId": "name_matching_workflow_001",
            "workflowType": "name_compatibility",
            "structuredParams": {
                "name1": {
                    "type": "text",
                    "value": form_data.get('name1', '').strip(),
                    "placeholder": "{{name1}}",
                    "description": "第一个人的姓名"
                },
                "gender1": {
                    "type": "text",
                    "value": form_data.get('gender1', ''),
                    "placeholder": "{{gender1}}",
                    "description": "第一个人的性别"
                },
                "name2": {
                    "type": "text",
                    "value": form_data.get('name2', '').strip(),
                    "placeholder": "{{name2}}",
                    "description": "第二个人的姓名"
                },
                "gender2": {
                    "type": "text",
                    "value": form_data.get('gender2', ''),
                    "placeholder": "{{gender2}}",
                    "description": "第二个人的性别"
                },
                "analysisType": {
                    "type": "config",
                    "value": form_data.get('analysisType', 'detailed'),
                    "placeholder": "{{analysisType}}",
                    "description": "分析类型：basic|detailed|professional"
                },
                "returnFormat": {
                    "type": "config",
                    "value": {
                        "format": "json",
                        "structure": {
                            "matchScore": "number (0-100)",
                            "compatibility": "string (优秀|良好|一般|较差)",
                            "analysis": "string (详细分析文本)",
                            "strengths": "array<string> (优势列表)",
                            "challenges": "array<string> (挑战列表)",
                            "advice": "array<string> (建议列表)"
                        },
                        "required_fields": ["matchScore", "compatibility", "analysis", "strengths", "advice"]
                    },
                    "placeholder": "{{returnFormat}}",
                    "description": "指定返回数据的格式和结构"
                }
            }
        }
```

### **3. AI结果解析器**
```python
class NameMatchingResultParser:
    @staticmethod
    def parse(ai_response):
        """解析AI返回的结果"""
        try:
            # 策略1：提取JSON
            json_result = NameMatchingResultParser.extract_json(ai_response)
            if json_result and NameMatchingResultParser.validate_json_result(json_result):
                return NameMatchingResultParser.normalize_json_result(json_result)
            
            # 策略2：解析结构化文本
            text_result = NameMatchingResultParser.parse_structured_text(ai_response)
            if text_result:
                return text_result
            
            # 策略3：自然语言解析
            return NameMatchingResultParser.parse_natural_language(ai_response)
            
        except Exception as e:
            logger.error(f"解析AI结果失败: {str(e)}")
            return NameMatchingResultParser.create_fallback_result(ai_response)

    @staticmethod
    def extract_json(text):
        """从文本中提取JSON"""
        import re
        import json
        
        # 查找JSON格式
        json_patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 标准JSON
            r'```json\s*(.*?)\s*```',            # 代码块
            r'```\s*(.*?)\s*```'                 # 通用代码块
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match.strip())
                except:
                    continue
        return None

    @staticmethod
    def parse_structured_text(text):
        """解析结构化文本"""
        result = {}
        
        # 提取配对分数
        score_patterns = [
            r'配对分数[：:]\s*(\d+)',
            r'分数[：:]\s*(\d+)',
            r'(\d+)分',
            r'配对指数[：:]\s*(\d+)'
        ]
        
        for pattern in score_patterns:
            match = re.search(pattern, text)
            if match:
                result['matchScore'] = int(match.group(1))
                break
        
        # 提取配对等级
        level_patterns = [
            r'配对等级[：:]\s*([^\n\r]+)',
            r'等级[：:]\s*([^\n\r]+)',
            r'(优秀|良好|一般|较差)'
        ]
        
        for pattern in level_patterns:
            match = re.search(pattern, text)
            if match:
                result['compatibility'] = match.group(1).strip()
                break
        
        # 提取分析内容
        analysis_patterns = [
            r'详细分析[：:]([^\\n\\r]*(?:\\n[^\\n\\r]*)*?)(?=\\n\\n|\\n[^\\s]|$)',
            r'分析[：:]([^\\n\\r]*(?:\\n[^\\n\\r]*)*?)(?=\\n\\n|\\n[^\\s]|$)'
        ]
        
        for pattern in analysis_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                result['analysis'] = match.group(1).strip()
                break
        
        # 提取列表项
        result['strengths'] = NameMatchingResultParser.extract_list_items(text, ['优势', '长处', '优点'])
        result['challenges'] = NameMatchingResultParser.extract_list_items(text, ['挑战', '问题', '注意'])
        result['advice'] = NameMatchingResultParser.extract_list_items(text, ['建议', '推荐', '意见'])
        
        return result if len(result) >= 3 else None

    @staticmethod
    def extract_list_items(text, keywords):
        """提取列表项"""
        items = []
        
        for keyword in keywords:
            # 查找关键词后的内容
            pattern = f'{keyword}[：:]([^\\n\\r]*(?:\\n[^\\n\\r]*)*?)(?=\\n\\n|\\n[^\\s]|$)'
            match = re.search(pattern, text, re.DOTALL)
            
            if match:
                content = match.group(1)
                # 提取列表项
                list_patterns = [
                    r'[-•]\s*([^\n\r]+)',      # - 项目
                    r'\d+\.\s*([^\n\r]+)',     # 1. 项目
                    r'([^，。；,;]+)[，。；,;]'  # 逗号分隔
                ]
                
                for list_pattern in list_patterns:
                    matches = re.findall(list_pattern, content)
                    if matches:
                        items.extend([item.strip() for item in matches if item.strip()])
                        break
        
        return list(set(items))  # 去重

    @staticmethod
    def create_fallback_result(ai_response):
        """创建降级结果"""
        return {
            "matchScore": 0,
            "compatibility": "分析中",
            "analysis": "正在处理分析结果，请稍候...",
            "strengths": ["AI正在分析中"],
            "challenges": ["暂无数据"],
            "advice": ["请稍后重试"],
            "raw_response": ai_response,
            "parsed": False
        }
```

### **4. 显示数据生成器**
```python
class NameMatchingDisplayGenerator:
    @staticmethod
    def generate(core_data, form_data):
        """生成前端显示数据"""
        score = core_data.get('matchScore', 0)
        compatibility = core_data.get('compatibility', '未知')
        name1 = form_data.get('name1', '某人')
        name2 = form_data.get('name2', '某人')
        
        # 根据分数确定颜色和图标
        if score >= 90:
            color = "#52c41a"  # 绿色
            icon = "heart-fill"
            level_text = "完美配对"
        elif score >= 75:
            color = "#1890ff"  # 蓝色
            icon = "heart"
            level_text = "良好配对"
        elif score >= 60:
            color = "#faad14"  # 黄色
            icon = "heart-outline"
            level_text = "一般配对"
        else:
            color = "#f5222d"  # 红色
            icon = "heart-broken"
            level_text = "需要努力"
        
        return {
            "overview": {
                "title": f"{name1} ❤️ {name2}",
                "subtitle": "姓名配对分析",
                "score": score,
                "level": compatibility,
                "summary": core_data.get('analysis', '')[:100] + "..." if core_data.get('analysis') else '',
                "icon": icon,
                "color": color
            },
            
            "details": {
                "sections": [
                    {
                        "title": "配对分析",
                        "type": "text",
                        "content": core_data.get('analysis', ''),
                        "style": "paragraph"
                    },
                    {
                        "title": "配对优势",
                        "type": "list",
                        "content": [
                            {"text": item, "icon": "check", "color": "green"}
                            for item in core_data.get('strengths', [])
                        ],
                        "style": "positive-list"
                    },
                    {
                        "title": "需要注意",
                        "type": "list",
                        "content": [
                            {"text": item, "icon": "warning", "color": "orange"}
                            for item in core_data.get('challenges', [])
                        ],
                        "style": "warning-list"
                    },
                    {
                        "title": "关系建议",
                        "type": "list",
                        "content": [
                            {"text": item, "icon": "bulb", "color": "blue"}
                            for item in core_data.get('advice', [])
                        ],
                        "style": "advice-list"
                    }
                ]
            }
        }
```

### **5. 主接口实现**
```python
@app.route('/api/v1/name-matching/execute', methods=['POST'])
def name_matching_execute():
    try:
        # 1. 解析请求
        request_data = request.json
        user_id = request_data.get('userId')
        form_data = request_data.get('formData', {})
        
        # 2. 验证用户
        if not verify_user_token(user_id):
            return error_response("MATCH_006", "用户未登录")
        
        # 3. 验证参数
        is_valid, errors = NameMatchingValidator.validate_request(form_data)
        if not is_valid:
            return error_response("MATCH_001", "参数验证失败", errors)
        
        # 4. 检查余额
        user_info = get_user_info(user_id)
        is_member = user_info.get('is_member', False)
        cost = NameMatchingValidator.get_cost(form_data.get('analysisType', 'detailed'), is_member)
        
        if user_info.get('balance', 0) < cost:
            return error_response("MATCH_007", "金币余额不足")
        
        # 5. 构建结构化参数
        structured_params = NameMatchingParamsBuilder.build(form_data, user_id)
        
        # 6. 调用大模型
        ai_response = call_ai_workflow(structured_params)
        
        # 7. 解析结果
        core_data = NameMatchingResultParser.parse(ai_response)
        
        # 8. 生成显示数据
        display_data = NameMatchingDisplayGenerator.generate(core_data, form_data)
        
        # 9. 扣费
        deduct_user_balance(user_id, cost)
        
        # 10. 返回结果
        return success_response({
            "core": core_data,
            "display": display_data,
            "meta": {
                "processingTime": get_processing_time(),
                "confidence": calculate_confidence(core_data)
            }
        }, cost, user_info.get('balance', 0) - cost)
        
    except Exception as e:
        logger.error(f"姓名配对执行失败: {str(e)}")
        return error_response("MATCH_999", "系统内部错误")
```

## 📋 **实现检查清单**

### **必须实现的功能**
- [ ] 参数验证器
- [ ] 结构化参数构建器
- [ ] AI结果解析器（多策略）
- [ ] 显示数据生成器
- [ ] 主接口实现
- [ ] 错误处理机制
- [ ] 日志记录

### **可选优化功能**
- [ ] 结果缓存
- [ ] 异步处理
- [ ] 性能监控
- [ ] A/B测试支持

---

**文档版本**: v1.0  
**创建时间**: 2025-01-11  
**维护团队**: 姓名配对功能组
