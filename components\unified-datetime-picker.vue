<template>
	<view class="unified-datetime-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<!-- 头部 -->
			<view class="picker-header">
				<text class="header-title">选择出生日期时间</text>
			</view>

			<!-- 统一日期时间输入区域 -->
			<view class="datetime-input-container">
				<!-- 主要输入框 -->
				<view class="main-input-section">
					<view class="input-group">
						<text class="input-label">📅 完整日期时间</text>
						<input 
							type="datetime-local" 
							class="unified-datetime-input"
							:value="nativeDatetime"
							@input="onNativeDatetimeChange"
							:min="minDateTime"
							:max="maxDateTime"
						/>
					</view>
				</view>

				<!-- 或者分别输入 -->
				<view class="separator">
					<text class="separator-text">或者分别输入</text>
				</view>

				<!-- 分离输入区域 -->
				<view class="separate-input-section">
					<view class="input-row">
						<view class="input-group small">
							<text class="input-label">年</text>
							<input 
								type="number" 
								class="separate-input"
								v-model="separateYear"
								placeholder="1990"
								:min="1900"
								:max="2030"
								@input="onSeparateInputChange"
							/>
						</view>
						<view class="input-group small">
							<text class="input-label">月</text>
							<input 
								type="number" 
								class="separate-input"
								v-model="separateMonth"
								placeholder="8"
								:min="1"
								:max="12"
								@input="onSeparateInputChange"
							/>
						</view>
						<view class="input-group small">
							<text class="input-label">日</text>
							<input 
								type="number" 
								class="separate-input"
								v-model="separateDay"
								placeholder="19"
								:min="1"
								:max="31"
								@input="onSeparateInputChange"
							/>
						</view>
					</view>
					<view class="input-row">
						<view class="input-group small">
							<text class="input-label">时</text>
							<input 
								type="number" 
								class="separate-input"
								v-model="separateHour"
								placeholder="14"
								:min="0"
								:max="23"
								@input="onSeparateInputChange"
							/>
						</view>
						<view class="input-group small">
							<text class="input-label">分</text>
							<input 
								type="number" 
								class="separate-input"
								v-model="separateMinute"
								placeholder="30"
								:min="0"
								:max="59"
								@input="onSeparateInputChange"
							/>
						</view>
					</view>
				</view>

				<!-- 预览区域 -->
				<view class="preview-section" v-if="previewText">
					<text class="preview-label">预览：</text>
					<text class="preview-text">{{ previewText }}</text>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'UnifiedDatetimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			nativeDatetime: '',
			separateYear: '',
			separateMonth: '',
			separateDay: '',
			separateHour: '',
			separateMinute: '',
			minDateTime: '1900-01-01T00:00',
			maxDateTime: '2030-12-31T23:59'
		};
	},
	computed: {
		previewText() {
			if (this.nativeDatetime) {
				return this.formatPreview(this.nativeDatetime);
			} else if (this.separateYear && this.separateMonth && this.separateDay) {
				const datetime = this.buildDatetimeFromSeparate();
				return datetime ? this.formatPreview(datetime) : '';
			}
			return '';
		}
	},
	methods: {
		close() {
			this.$emit('close');
		},
		
		confirm() {
			let finalDatetime = '';
			
			if (this.nativeDatetime) {
				finalDatetime = this.nativeDatetime;
			} else if (this.separateYear && this.separateMonth && this.separateDay) {
				finalDatetime = this.buildDatetimeFromSeparate();
			}
			
			if (finalDatetime) {
				const result = this.parseDateTime(finalDatetime);
				this.$emit('confirm', result);
			} else {
				uni.showToast({
					title: '请选择日期时间',
					icon: 'none'
				});
			}
		},
		
		onNativeDatetimeChange(e) {
			this.nativeDatetime = e.target.value;
			// 清空分离输入
			this.clearSeparateInputs();
		},
		
		onSeparateInputChange() {
			// 清空原生输入
			this.nativeDatetime = '';
		},
		
		clearSeparateInputs() {
			this.separateYear = '';
			this.separateMonth = '';
			this.separateDay = '';
			this.separateHour = '';
			this.separateMinute = '';
		},
		
		buildDatetimeFromSeparate() {
			if (!this.separateYear || !this.separateMonth || !this.separateDay) {
				return '';
			}
			
			const year = this.separateYear.toString().padStart(4, '0');
			const month = this.separateMonth.toString().padStart(2, '0');
			const day = this.separateDay.toString().padStart(2, '0');
			const hour = (this.separateHour || '12').toString().padStart(2, '0');
			const minute = (this.separateMinute || '00').toString().padStart(2, '0');
			
			return `${year}-${month}-${day}T${hour}:${minute}`;
		},
		
		formatPreview(datetime) {
			try {
				const [datePart, timePart] = datetime.split('T');
				const [year, month, day] = datePart.split('-');
				const [hour, minute] = timePart.split(':');
				
				const date = `${year}年${parseInt(month)}月${parseInt(day)}日`;
				const time = `${hour}:${minute}`;
				
				// 计算星期几
				const dateObj = new Date(year, month - 1, day);
				const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
				const weekday = weekdays[dateObj.getDay()];
				
				return `${date} ${time} ${weekday}`;
			} catch (error) {
				return '';
			}
		},
		
		parseDateTime(datetime) {
			try {
				const [datePart, timePart] = datetime.split('T');
				const [year, month, day] = datePart.split('-');
				const [hour, minute] = timePart.split(':');
				
				const date = `${year}年${parseInt(month)}月${parseInt(day)}日`;
				const time = `${hour}:${minute}`;
				
				// 计算星期几
				const dateObj = new Date(year, month - 1, day);
				const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
				const weekdayText = weekdays[dateObj.getDay()];
				
				return { date, time, weekdayText };
			} catch (error) {
				console.error('日期时间解析失败:', error);
				return { date: '', time: '', weekdayText: '' };
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.unified-datetime-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	z-index: 2;
	background: #ffffff;
	border-radius: 20rpx;
	margin: 0 40rpx;
	max-width: 700rpx;
	width: 100%;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	max-height: 80vh;
	overflow-y: auto;
}

.picker-header {
	padding: 30rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.datetime-input-container {
	padding: 30rpx;
}

.main-input-section {
	margin-bottom: 30rpx;
}

.input-group {
	margin-bottom: 20rpx;
}

.input-group.small {
	flex: 1;
	margin-bottom: 0;
}

.input-label {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.unified-datetime-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #ddd;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
}

.unified-datetime-input:focus {
	border-color: #dc143c;
	outline: none;
	box-shadow: 0 0 0 4rpx rgba(220, 20, 60, 0.1);
}

.separator {
	text-align: center;
	margin: 30rpx 0;
	position: relative;
}

.separator::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 0;
	right: 0;
	height: 1rpx;
	background: #e0e0e0;
	z-index: 1;
}

.separator-text {
	background: #fff;
	padding: 0 20rpx;
	font-size: 22rpx;
	color: #999;
	position: relative;
	z-index: 2;
}

.separate-input-section {
	margin-bottom: 30rpx;
}

.input-row {
	display: flex;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.separate-input {
	width: 100%;
	height: 60rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	padding: 0 12rpx;
	font-size: 24rpx;
	color: #333;
	text-align: center;
}

.separate-input:focus {
	border-color: #dc143c;
	outline: none;
}

.preview-section {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.preview-label {
	font-size: 22rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.preview-text {
	font-size: 26rpx;
	color: #dc143c;
	font-weight: 600;
}

.picker-footer {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 100rpx;
	border: none;
	background: none;
	font-size: 32rpx;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	color: #666;
	border-right: 1rpx solid #f0f0f0;
}

.btn-cancel:hover {
	background: #f5f5f5;
}

.btn-confirm {
	color: #dc143c;
}

.btn-confirm:hover {
	background: rgba(220, 20, 60, 0.05);
}

.btn-cancel:active,
.btn-confirm:active {
	transform: scale(0.98);
}
</style>
