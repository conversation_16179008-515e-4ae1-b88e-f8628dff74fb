/**
 * 后端工作流编辑器配置
 * 对应后端工作流编辑界面的工具类型选择
 * 创建时间：2025-01-16
 */

/**
 * 工具类型配置 - 对应下拉菜单选项
 */
export const TOOL_TYPES = {
  TEXT_GENERATION: {
    value: 'text_generation',
    label: '文本生成',
    description: '智能文本创作工具，支持剧本、歌词、小说等多种场景',
    icon: '📝',
    color: '#007AFF',
    // 对应我们的文本生成工具
    handler: 'TextGenerationHandler',
    apiEndpoint: '/api/text-generation/execute'
  },
  
  IMAGE_GENERATION: {
    value: 'image_generation', 
    label: '图像生成',
    description: '基于文本描述生成图片',
    icon: '🎨',
    color: '#FF6B35',
    handler: 'ImageGenerationHandler',
    apiEndpoint: '/api/image-generation/execute'
  },
  
  VIDEO_GENERATION: {
    value: 'video_generation',
    label: '视频生成', 
    description: '文本转视频或图片转视频',
    icon: '🎬',
    color: '#8E44AD',
    handler: 'VideoGenerationHandler',
    apiEndpoint: '/api/video-generation/execute'
  },
  
  AUDIO_GENERATION: {
    value: 'audio_generation',
    label: '音频生成',
    description: '音乐创作和语音合成',
    icon: '🎵', 
    color: '#27AE60',
    handler: 'AudioGenerationHandler',
    apiEndpoint: '/api/audio-generation/execute'
  },
  
  MULTI_MODAL: {
    value: 'multi_modal',
    label: '多模态',
    description: '综合多种AI能力的复合工具',
    icon: '🔗',
    color: '#F39C12',
    handler: 'MultiModalHandler',
    apiEndpoint: '/api/multi-modal/execute'
  }
};

/**
 * 文本生成场景配置
 */
export const TEXT_SCENARIOS = {
  SCRIPT: {
    value: 'script',
    label: '剧本创作',
    description: '专业剧本创作，支持电影、电视剧、话剧等',
    icon: '🎭',
    promptTemplate: '你是专业编剧，请创作剧本...',
    outputFormat: 'screenplay'
  },
  
  LYRICS: {
    value: 'lyrics',
    label: '歌词创作',
    description: '音乐歌词创作，支持多种音乐风格',
    icon: '🎵',
    promptTemplate: '你是专业词作家，请创作歌词...',
    outputFormat: 'lyrics'
  },
  
  NOVEL: {
    value: 'novel',
    label: '小说创作',
    description: '长篇小说、短篇故事创作',
    icon: '📚',
    promptTemplate: '你是专业小说家，请创作小说...',
    outputFormat: 'narrative'
  },
  
  ARTICLE: {
    value: 'article',
    label: '文章写作',
    description: '新闻稿、博客文章、专业文章',
    icon: '📄',
    promptTemplate: '你是专业作家，请撰写文章...',
    outputFormat: 'article'
  },
  
  GENERAL: {
    value: 'general',
    label: '通用文本',
    description: '通用文本生成和处理',
    icon: '✏️',
    promptTemplate: '请根据要求生成文本内容...',
    outputFormat: 'text'
  }
};

/**
 * 工作流配置模板
 */
export const WORKFLOW_TEMPLATES = {
  // 文本生成工作流模板
  text_generation: {
    name: '文本生成工作流',
    description: '智能文本创作工作流',
    toolType: 'text_generation',
    defaultConfig: {
      scenario: 'general',
      style: '',
      length: 'medium',
      creativity: 0.7,
      temperature: 0.8,
      maxTokens: 2000
    },
    requiredParams: ['prompt'],
    optionalParams: ['style', 'length', 'creativity']
  },
  
  // 图像生成工作流模板
  image_generation: {
    name: '图像生成工作流',
    description: 'AI图像创作工作流',
    toolType: 'image_generation',
    defaultConfig: {
      style: 'realistic',
      size: '1024x1024',
      quality: 'high',
      steps: 50,
      guidance: 7.5
    },
    requiredParams: ['prompt'],
    optionalParams: ['style', 'size', 'quality', 'negativePrompt']
  },
  
  // 视频生成工作流模板
  video_generation: {
    name: '视频生成工作流',
    description: 'AI视频创作工作流',
    toolType: 'video_generation',
    defaultConfig: {
      duration: '10s',
      resolution: '1280x720',
      fps: 24,
      motionType: 'smooth'
    },
    requiredParams: ['prompt'],
    optionalParams: ['duration', 'resolution', 'motionType']
  }
};

/**
 * 工作流编辑器UI配置
 */
export const EDITOR_CONFIG = {
  // 工具选择下拉菜单配置
  toolSelector: {
    placeholder: '请选择工具类型',
    searchable: true,
    clearable: false,
    options: Object.values(TOOL_TYPES)
  },
  
  // 场景选择配置（针对文本生成）
  scenarioSelector: {
    placeholder: '请选择生成场景',
    searchable: true,
    clearable: false,
    options: Object.values(TEXT_SCENARIOS)
  },
  
  // 参数配置表单
  parameterForm: {
    style: {
      type: 'input',
      label: '风格',
      placeholder: '请输入创作风格',
      required: false
    },
    length: {
      type: 'select',
      label: '长度',
      options: [
        { value: 'short', label: '短篇' },
        { value: 'medium', label: '中篇' },
        { value: 'long', label: '长篇' }
      ],
      default: 'medium'
    },
    creativity: {
      type: 'slider',
      label: '创意度',
      min: 0,
      max: 1,
      step: 0.1,
      default: 0.7
    }
  }
};

/**
 * API接口配置
 */
export const API_CONFIG = {
  baseURL: '/api/workflow-editor',
  endpoints: {
    getTools: '/tools',
    getToolConfig: '/tool-config',
    createWorkflow: '/create-workflow',
    executeWorkflow: '/execute-workflow',
    getWorkflows: '/workflows',
    updateWorkflow: '/update-workflow',
    deleteWorkflow: '/delete-workflow'
  },
  timeout: 30000
};

/**
 * 工作流状态定义
 */
export const WORKFLOW_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active', 
  INACTIVE: 'inactive',
  TESTING: 'testing',
  ERROR: 'error'
};

/**
 * 获取工具类型选项（用于下拉菜单）
 */
export function getToolTypeOptions() {
  return Object.values(TOOL_TYPES).map(tool => ({
    value: tool.value,
    label: tool.label,
    description: tool.description,
    icon: tool.icon
  }));
}

/**
 * 获取文本场景选项
 */
export function getTextScenarioOptions() {
  return Object.values(TEXT_SCENARIOS).map(scenario => ({
    value: scenario.value,
    label: scenario.label,
    description: scenario.description,
    icon: scenario.icon
  }));
}

/**
 * 根据工具类型获取配置模板
 */
export function getWorkflowTemplate(toolType) {
  return WORKFLOW_TEMPLATES[toolType] || null;
}

/**
 * 验证工作流配置
 */
export function validateWorkflowConfig(config) {
  const errors = [];
  
  if (!config.name || config.name.trim() === '') {
    errors.push('工作流名称不能为空');
  }
  
  if (!config.toolType) {
    errors.push('必须选择工具类型');
  }
  
  if (config.toolType === 'text_generation' && !config.scenario) {
    errors.push('文本生成必须选择场景');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 生成工作流执行参数
 */
export function buildExecutionParams(workflowConfig, userInput) {
  const template = getWorkflowTemplate(workflowConfig.toolType);
  if (!template) {
    throw new Error(`未找到工具类型模板: ${workflowConfig.toolType}`);
  }
  
  return {
    toolType: workflowConfig.toolType,
    scenario: workflowConfig.scenario,
    ...template.defaultConfig,
    ...workflowConfig.customConfig,
    ...userInput
  };
}

export default {
  TOOL_TYPES,
  TEXT_SCENARIOS,
  WORKFLOW_TEMPLATES,
  EDITOR_CONFIG,
  API_CONFIG,
  WORKFLOW_STATUS,
  getToolTypeOptions,
  getTextScenarioOptions,
  getWorkflowTemplate,
  validateWorkflowConfig,
  buildExecutionParams
};
