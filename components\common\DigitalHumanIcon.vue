<template>
  <view class="digital-human-icon" :class="iconClass" :style="iconStyle">
    <!-- 上传图标 -->
    <svg v-if="name === 'upload'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 15V3M12 3L8 7M12 3L16 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M2 17L2 19C2 20.1046 2.89543 21 4 21L20 21C21.1046 21 22 20.1046 22 19L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
    </svg>

    <!-- 视频图标 -->
    <svg v-else-if="name === 'video'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 10L11 7V13L15 10Z" fill="currentColor"/>
      <path d="M3 6C3 4.34315 4.34315 3 6 3H18C19.6569 3 21 4.34315 21 6V18C21 19.6569 19.6569 21 18 21H6C4.34315 21 3 19.6569 3 18V6Z" stroke="currentColor" stroke-width="2"/>
    </svg>

    <!-- 麦克风图标 -->
    <svg v-else-if="name === 'microphone'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C13.1046 2 14 2.89543 14 4V12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12V4C10 2.89543 10.8954 2 12 2Z" fill="currentColor"/>
      <path d="M19 10V12C19 16.4183 15.4183 20 11 20H10V22H14C14.5523 22 15 22.4477 15 23C15 23.5523 14.5523 24 14 24H10C9.44772 24 9 23.5523 9 23C9 22.4477 9.44772 22 10 22V20H9C4.58172 20 1 16.4183 1 12V10C1 9.44772 1.44772 9 2 9C2.55228 9 3 9.44772 3 10V12C3 15.3137 5.68629 18 9 18H11C14.3137 18 17 15.3137 17 12V10C17 9.44772 17.4477 9 18 9C18.5523 9 19 9.44772 19 10Z" fill="currentColor"/>
    </svg>

    <!-- 音频波形图标 -->
    <svg v-else-if="name === 'audio-wave'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2 12H4L6 8L10 16L14 4L18 12L20 10H22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <!-- 魔法棒图标 -->
    <svg v-else-if="name === 'magic'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 4V2M15 16V14M8 9H10M20 9H22M17.8 11.8L19.2 13.2M17.8 6.2L19.2 4.8M12.2 13.2L10.8 11.8M12.2 4.8L10.8 6.2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="15" cy="9" r="3" stroke="currentColor" stroke-width="2"/>
      <path d="M2 21L16 7L18 9L4 23L2 21Z" fill="currentColor"/>
    </svg>

    <!-- 机器人图标 -->
    <svg v-else-if="name === 'robot'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="3" y="11" width="18" height="10" rx="2" stroke="currentColor" stroke-width="2"/>
      <circle cx="12" cy="5" r="2" stroke="currentColor" stroke-width="2"/>
      <path d="M12 7V11" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      <circle cx="9" cy="16" r="1" fill="currentColor"/>
      <circle cx="15" cy="16" r="1" fill="currentColor"/>
    </svg>

    <!-- 播放图标 -->
    <svg v-else-if="name === 'play'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
    </svg>

    <!-- 暂停图标 -->
    <svg v-else-if="name === 'pause'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="6" y="4" width="4" height="16" fill="currentColor"/>
      <rect x="14" y="4" width="4" height="16" fill="currentColor"/>
    </svg>

    <!-- 历史记录图标 -->
    <svg v-else-if="name === 'history'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
      <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
    </svg>

    <!-- 返回图标 -->
    <svg v-else-if="name === 'back'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <!-- 设置图标 -->
    <svg v-else-if="name === 'settings'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
      <path d="M19.4 15A1.65 1.65 0 0 0 21 13.09A1.65 1.65 0 0 0 19.4 9M20.75 9L19.4 9M19.4 15L20.75 15M4.6 9A1.65 1.65 0 0 0 3 10.91A1.65 1.65 0 0 0 4.6 15M3.25 15L4.6 15M4.6 9L3.25 9M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
    </svg>

    <!-- 检查图标 -->
    <svg v-else-if="name === 'check'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <!-- 关闭图标 -->
    <svg v-else-if="name === 'close'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

    <!-- 星星图标 -->
    <svg v-else-if="name === 'star'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
    </svg>

    <!-- 火花图标 -->
    <svg v-else-if="name === 'sparkles'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063L7.5 12L8.5 9.937A2 2 0 0 0 9.937 8.5L12 7.5L14.063 8.5A2 2 0 0 0 15.5 9.937L16.5 12L15.5 14.063A2 2 0 0 0 14.063 15.5L12 16.5L9.937 15.5Z" stroke="currentColor" stroke-width="2"/>
      <path d="M20 3V7M22 5H18M6 21V17M8 19H4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
    </svg>

    <!-- 默认图标 -->
    <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
      <path d="M8 12H16M12 8V16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
    </svg>
  </view>
</template>

<script>
export default {
  name: 'DigitalHumanIcon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: [String, Number],
      default: 24
    },
    color: {
      type: String,
      default: 'currentColor'
    },
    gradient: {
      type: Boolean,
      default: false
    },
    animated: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    iconClass() {
      return {
        'icon-gradient': this.gradient,
        'icon-animated': this.animated
      }
    },
    iconStyle() {
      return {
        width: typeof this.size === 'number' ? `${this.size}rpx` : this.size,
        height: typeof this.size === 'number' ? `${this.size}rpx` : this.size,
        color: this.gradient ? undefined : this.color
      }
    }
  }
}
</script>

<style scoped>
.digital-human-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.digital-human-icon svg {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

.icon-gradient svg {
  color: transparent;
}

.icon-gradient svg path,
.icon-gradient svg circle,
.icon-gradient svg rect {
  fill: url(#iconGradient);
  stroke: url(#iconGradient);
}

.icon-animated svg {
  animation: iconFloat 2s ease-in-out infinite alternate;
}

@keyframes iconFloat {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-4px); }
}

.digital-human-icon:hover svg {
  transform: scale(1.1);
}

/* 渐变定义 */
.icon-gradient::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
}

.icon-gradient svg defs {
  display: none;
}
</style>
