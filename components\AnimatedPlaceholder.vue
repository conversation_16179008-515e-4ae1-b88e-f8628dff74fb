<template>
  <view 
    class="animated-placeholder"
    :class="{'hidden': isHidden, 'typing': isTyping, 'paused': isPaused}"
  >
    <text class="placeholder-text">{{ displayText }}</text>
  </view>
</template>

<script>
export default {
  name: 'AnimatedPlaceholder',
  props: {
    // 文本数组
    text: {
      type: Array,
      default: () => ['请输入内容']
    },
    // 是否隐藏占位符
    isHidden: {
      type: Boolean,
      default: false
    },
    // 打字速度 (毫秒/字符)
    typingSpeed: {
      type: Number,
      default: 100
    },
    // 停顿时间 (毫秒)
    pauseTime: {
      type: Number,
      default: 2000
    },
    // 淡出时间 (毫秒)
    fadeTime: {
      type: Number,
      default: 800
    }
  },
  data() {
    return {
      currentIndex: 0,
      displayText: '',
      currentTextIndex: 0,
      isTyping: false,
      isPaused: false,
      animationTimer: null,
      fullText: ''
    };
  },
  watch: {
    isHidden(newVal) {
      if (newVal) {
        this.stopAnimation();
      } else {
        this.startAnimation();
      }
    },
    text: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.fullText = newVal[0];
          this.currentTextIndex = 0;
          
          // 如果不是隐藏状态，重新开始动画
          if (!this.isHidden) {
            this.restartAnimation();
          }
        }
      }
    }
  },
  created() {
    // 初始化第一个文本
    if (this.text && this.text.length > 0) {
      this.fullText = this.text[0];
    }
  },
  mounted() {
    if (!this.isHidden) {
      this.startAnimation();
    }
  },
  beforeUnmount() {
    this.stopAnimation();
  },
  methods: {
    /**
     * 启动动画
     */
    startAnimation() {
      this.stopAnimation(); // 先停止可能存在的动画
      this.isTyping = true;
      this.isPaused = false;
      this.currentIndex = 0;
      this.displayText = '';
      this.typeNextChar();
    },
    
    /**
     * 重新启动动画
     */
    restartAnimation() {
      this.stopAnimation();
      this.startAnimation();
    },
    
    /**
     * 停止动画
     */
    stopAnimation() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer);
        this.animationTimer = null;
      }
      this.isTyping = false;
      this.isPaused = false;
    },
    
    /**
     * 输入下一个字符
     */
    typeNextChar() {
      if (this.isHidden) return;
      
      if (this.currentIndex < this.fullText.length) {
        // 还有字符需要输入
        this.displayText = this.fullText.substring(0, this.currentIndex + 1);
        this.currentIndex++;
        
        // 设置下一个字符的输入
        this.animationTimer = setTimeout(() => {
          this.typeNextChar();
        }, this.typingSpeed);
      } else {
        // 所有字符都输入完毕，进入暂停状态
        this.isTyping = false;
        this.isPaused = true;
        
        // 在暂停后开始渐隐
        this.animationTimer = setTimeout(() => {
          this.startFadeOut();
        }, this.pauseTime);
      }
    },
    
    /**
     * 开始渐隐动画
     */
    startFadeOut() {
      if (this.isHidden) return;
      
      this.isPaused = false;
      
      // 渐隐后切换到下一段文字
      this.animationTimer = setTimeout(() => {
        this.switchToNextText();
      }, this.fadeTime);
    },
    
    /**
     * 切换到下一段文字
     */
    switchToNextText() {
      if (this.isHidden) return;
      
      if (this.text.length > 1) {
        this.currentTextIndex = (this.currentTextIndex + 1) % this.text.length;
        this.fullText = this.text[this.currentTextIndex];
      }
      
      // 重新开始打字动画
      this.currentIndex = 0;
      this.displayText = '';
      this.isTyping = true;
      this.typeNextChar();
    }
  }
};
</script>

<style>
.animated-placeholder {
  pointer-events: none;
  transition-property: opacity;
  transition-duration: 0.3s;
  opacity: 0.7;
}

.animated-placeholder.hidden {
  opacity: 0;
}

.animated-placeholder.typing .placeholder-text {
  border-right: 2px solid #409eff;
  animation: cursor-blink 0.8s step-end infinite;
}

.animated-placeholder.paused .placeholder-text {
  border-right: none;
}

.placeholder-text {
  display: inline-block;
  color: #909399;
  font-size: 16px;
  line-height: 1.5;
  transition: all 0.3s;
  white-space: pre-wrap;
  word-break: break-word;
}

@keyframes cursor-blink {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #409eff;
  }
}
</style> 