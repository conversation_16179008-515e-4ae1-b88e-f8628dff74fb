/**
 * 工作流执行接口
 * 统一的工作流执行入口，使用统一变量名 musicCreationData
 * 备份时间：2025-01-11
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🎵 工作流执行核心接口
// ================================

/**
 * 统一的工作流执行接口
 * @param {string} workflowMode - 工作流模式 ("simple", "advanced", "instrumental")
 * @param {Object} musicCreationData - 音乐创作数据（统一变量）
 */
export async function 执行工作流(workflowMode, musicCreationData) {
	// 工作流类型映射
	const workflowTypeMap = {
		'simple': 'simple_mode_workflow',
		'advanced': 'advanced_mode_workflow',
		'instrumental': 'instrumental_workflow'
	};

	const workflowType = workflowTypeMap[workflowMode];
	if (!workflowType) {
		throw new Error(`未知的工作流模式: ${workflowMode}`);
	}

	// 构建统一的请求格式
	const requestData = {
		requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
		userId: uni.getStorageSync('userId') || 'anonymous',
		timestamp: Date.now(),
		workflowType: workflowType,
		musicCreationData: {
			...musicCreationData,
			mode: workflowMode,
			timestamp: Date.now()
		}
	};

	return await apiRequest('music-creation/execute-workflow', {
		method: 'POST',
		body: requestData
	});
}

/**
 * 查询工作流状态
 * @param {string} requestId - 请求ID
 */
export async function 查询工作流状态(requestId) {
	return await apiRequest(`music-creation/workflow-status?requestId=${requestId}`);
}

/**
 * 获取工作流结果
 * @param {string} requestId - 请求ID
 */
export async function 获取工作流结果(requestId) {
	return await apiRequest(`music-creation/workflow-result?requestId=${requestId}`);
}

/**
 * 取消工作流执行
 * @param {string} requestId - 请求ID
 */
export async function 取消工作流执行(requestId) {
	return await apiRequest('music-creation/cancel-workflow', {
		method: 'POST',
		body: { requestId }
	});
}

// ================================
// 🔄 工作流状态轮询
// ================================

/**
 * 轮询工作流状态直到完成
 * @param {string} requestId - 请求ID
 * @param {Function} onProgress - 进度回调函数
 * @param {Object} options - 轮询选项
 */
export async function 轮询工作流状态直到完成(requestId, onProgress, options = {}) {
	const {
		maxPolls = 200,        // 最大轮询次数
		pollInterval = 3000,   // 轮询间隔（毫秒）
		timeout = 600000       // 总超时时间（毫秒）
	} = options;

	let pollCount = 0;
	const startTime = Date.now();

	return new Promise((resolve, reject) => {
		const poll = async () => {
			try {
				pollCount++;
				const currentTime = Date.now();

				// 检查超时
				if (currentTime - startTime > timeout) {
					reject(new Error('工作流执行超时'));
					return;
				}

				// 检查轮询次数
				if (pollCount > maxPolls) {
					reject(new Error('轮询次数超限'));
					return;
				}

				// 查询状态
				const statusResult = await 查询工作流状态(requestId);
				const status = statusResult.data.status;

				// 调用进度回调
				if (onProgress && typeof onProgress === 'function') {
					onProgress({
						...statusResult.data,
						pollCount,
						elapsedTime: currentTime - startTime
					});
				}

				// 处理不同状态
				if (status === 'completed') {
					// 获取最终结果
					const result = await 获取工作流结果(requestId);
					resolve(result);
				} else if (status === 'failed') {
					reject(new Error(statusResult.data.message || '工作流执行失败'));
				} else if (status === 'cancelled') {
					reject(new Error('工作流已被取消'));
				} else {
					// 继续轮询
					setTimeout(poll, pollInterval);
				}

			} catch (error) {
				reject(error);
			}
		};

		// 开始轮询
		poll();
	});
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整的音乐创作流程
 * @param {string} workflowMode - 工作流模式
 * @param {Object} formData - 表单数据
 * @param {Object} options - 选项
 */
export async function 完整音乐创作流程(workflowMode, formData, options = {}) {
	try {
		// 1. 数据预处理
		const musicCreationData = 预处理创作数据(workflowMode, formData);

		// 2. 执行工作流
		const workflowResult = await 执行工作流(workflowMode, musicCreationData);

		if (!workflowResult.success) {
			throw new Error(workflowResult.message || '工作流启动失败');
		}

		const requestId = workflowResult.data.requestId;

		// 3. 轮询状态
		const finalResult = await 轮询工作流状态直到完成(
			requestId,
			options.onProgress,
			{
				maxPolls: options.maxPolls || 200,
				pollInterval: options.pollInterval || 3000,
				timeout: options.timeout || 600000
			}
		);

		return {
			success: true,
			data: {
				requestId: requestId,
				workflowMode: workflowMode,
				result: finalResult.data,
				completedAt: new Date().toISOString()
			}
		};

	} catch (error) {
		console.error(`${workflowMode}模式创作流程失败:`, error);
		throw error;
	}
}

/**
 * 预处理创作数据
 * @param {string} workflowMode - 工作流模式
 * @param {Object} formData - 表单数据
 */
function 预处理创作数据(workflowMode, formData) {
	const baseData = {
		mode: workflowMode,
		timestamp: Date.now(),
		duration: formData.duration || 180,
		quality: formData.quality || 'standard'
	};

	if (workflowMode === 'simple') {
		return {
			...baseData,
			prompt: formData.simplePrompt || '',
			styles: formData.selectedStyles || [],
			vocalGender: formData.simpleSelectedVocalGender || 'auto',
			complexity: 'basic'
		};
	} else if (workflowMode === 'advanced') {
		return {
			...baseData,
			lyrics: formData.customLyrics || '',
			description: formData.songDescription || '',
			tags: formData.selectedTags || {},
			vocalStyle: formData.selectedTags?.vocal || [],
			genreTags: formData.selectedTags?.genre || [],
			moodTags: formData.selectedTags?.mood || [],
			complexity: 'advanced'
		};
	} else if (workflowMode === 'instrumental') {
		return {
			...baseData,
			style: formData.instrumentalStyle || 'classical',
			mood: formData.instrumentalMood || 'peaceful',
			instruments: formData.selectedInstruments || [],
			structure: formData.structure || 'standard',
			complexity: 'instrumental'
		};
	}

	return baseData;
}

/**
 * 批量执行多个工作流（用于对比测试）
 * @param {Array} workflowConfigs - 工作流配置数组
 */
export async function 批量执行工作流(workflowConfigs) {
	const results = [];

	for (const config of workflowConfigs) {
		try {
			const result = await 完整音乐创作流程(
				config.workflowMode,
				config.formData,
				config.options
			);
			results.push({
				...result,
				configId: config.id
			});
		} catch (error) {
			results.push({
				success: false,
				error: error.message,
				configId: config.id
			});
		}
	}

	return results;
}

export default {
	执行工作流,
	查询工作流状态,
	获取工作流结果,
	取消工作流执行,
	轮询工作流状态直到完成,
	完整音乐创作流程,
	批量执行工作流
};
