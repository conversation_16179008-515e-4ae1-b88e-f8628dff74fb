/**
 * 结果管理接口
 * 管理音乐创作结果的保存、查询、删除等操作
 */

import { apiRequest } from '../common/request.js';

// ================================
// 📁 结果管理接口
// ================================

/**
 * 保存创作结果
 * @param {Object} params - 保存参数
 */
export async function 保存创作结果(params) {
	return await apiRequest('music-creation/save-result', {
		method: 'POST',
		body: params
	});
}

/**
 * 获取我的创作列表
 * @param {Object} params - 查询参数
 */
export async function 获取我的创作列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music-creation/my-creations?${queryParams}`);
}

/**
 * 获取创作详情
 * @param {string} musicId - 音乐ID
 */
export async function 获取创作详情(musicId) {
	return await apiRequest(`music-creation/creation-detail?musicId=${musicId}`);
}

/**
 * 删除创作记录
 * @param {string} musicId - 音乐ID
 */
export async function 删除创作记录(musicId) {
	return await apiRequest('music-creation/delete-creation', {
		method: 'DELETE',
		body: { musicId }
	});
}

/**
 * 更新创作信息
 * @param {string} musicId - 音乐ID
 * @param {Object} updateData - 更新数据
 */
export async function 更新创作信息(musicId, updateData) {
	return await apiRequest('music-creation/update-creation', {
		method: 'PUT',
		body: {
			musicId,
			...updateData
		}
	});
}

/**
 * 分享创作
 * @param {string} musicId - 音乐ID
 * @param {Object} shareOptions - 分享选项
 */
export async function 分享创作(musicId, shareOptions) {
	return await apiRequest('music-creation/share-creation', {
		method: 'POST',
		body: {
			musicId,
			...shareOptions
		}
	});
}

/**
 * 下载创作文件
 * @param {string} musicId - 音乐ID
 * @param {string} format - 文件格式
 */
export async function 下载创作文件(musicId, format = 'mp3') {
	return await apiRequest(`music-creation/download-creation?musicId=${musicId}&format=${format}`);
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整保存创作流程
 * @param {string} requestId - 请求ID
 * @param {Object} creationResult - 创作结果
 * @param {Object} saveOptions - 保存选项
 */
export async function 完整保存创作流程(requestId, creationResult, saveOptions = {}) {
	try {
		// 准备保存数据
		const saveData = {
			requestId: requestId,
			title: saveOptions.title || creationResult.title || '未命名作品',
			tags: saveOptions.tags || [],
			isPublic: saveOptions.isPublic || false,
			description: saveOptions.description || '',
			workflowMode: creationResult.mode || 'simple',
			duration: creationResult.duration || 0,
			audioUrl: creationResult.audioUrl || '',
			coverUrl: creationResult.coverUrl || '',
			lyrics: creationResult.lyrics || '',
			metadata: {
				genre: creationResult.genre || '',
				mood: creationResult.mood || '',
				quality: creationResult.quality || 'standard',
				processingTime: creationResult.processingTime || 0,
				completedAt: creationResult.completedAt || new Date().toISOString()
			}
		};

		// 保存创作结果
		const saveResult = await 保存创作结果(saveData);

		if (!saveResult.success) {
			throw new Error(saveResult.message || '保存失败');
		}

		return {
			success: true,
			data: {
				musicId: saveResult.data.musicId,
				saveTime: saveResult.data.saveTime,
				message: '创作保存成功'
			}
		};

	} catch (error) {
		console.error('完整保存创作流程失败:', error);
		throw error;
	}
}

/**
 * 批量删除创作
 * @param {Array} musicIds - 音乐ID数组
 */
export async function 批量删除创作(musicIds) {
	const results = [];

	for (const musicId of musicIds) {
		try {
			const result = await 删除创作记录(musicId);
			results.push({
				musicId,
				success: true,
				data: result.data
			});
		} catch (error) {
			results.push({
				musicId,
				success: false,
				error: error.message
			});
		}
	}

	return {
		success: true,
		data: {
			total: musicIds.length,
			successful: results.filter(r => r.success).length,
			failed: results.filter(r => !r.success).length,
			results: results
		}
	};
}

/**
 * 获取创作统计信息
 * @param {Object} params - 统计参数
 */
export async function 获取创作统计信息(params = {}) {
	try {
		// 获取创作列表
		const listResult = await 获取我的创作列表({
			page: 1,
			pageSize: 1000, // 获取所有数据用于统计
			...params
		});

		const creations = listResult.data.items || [];

		// 统计分析
		const stats = {
			总数量: creations.length,
			按模式统计: {},
			按状态统计: {},
			总时长: 0,
			最近创作: null,
			最受欢迎: null
		};

		creations.forEach(creation => {
			// 按模式统计
			const mode = creation.mode || 'unknown';
			stats.按模式统计[mode] = (stats.按模式统计[mode] || 0) + 1;

			// 按状态统计
			const status = creation.status || 'unknown';
			stats.按状态统计[status] = (stats.按状态统计[status] || 0) + 1;

			// 总时长
			stats.总时长 += creation.duration || 0;

			// 最近创作
			if (!stats.最近创作 || new Date(creation.createTime) > new Date(stats.最近创作.createTime)) {
				stats.最近创作 = creation;
			}

			// 最受欢迎（按播放次数）
			if (!stats.最受欢迎 || (creation.playCount || 0) > (stats.最受欢迎.playCount || 0)) {
				stats.最受欢迎 = creation;
			}
		});

		return {
			success: true,
			data: stats
		};

	} catch (error) {
		console.error('获取创作统计信息失败:', error);
		throw error;
	}
}

/**
 * 搜索我的创作
 * @param {string} keyword - 搜索关键词
 * @param {Object} filters - 过滤条件
 */
export async function 搜索我的创作(keyword, filters = {}) {
	try {
		const searchParams = {
			keyword: keyword,
			page: 1,
			pageSize: 50,
			...filters
		};

		const result = await 获取我的创作列表(searchParams);

		// 客户端额外过滤（如果后端不支持复杂搜索）
		let filteredItems = result.data.items || [];

		if (keyword) {
			const lowerKeyword = keyword.toLowerCase();
			filteredItems = filteredItems.filter(item => 
				(item.title && item.title.toLowerCase().includes(lowerKeyword)) ||
				(item.description && item.description.toLowerCase().includes(lowerKeyword)) ||
				(item.tags && item.tags.some(tag => tag.toLowerCase().includes(lowerKeyword)))
			);
		}

		return {
			success: true,
			data: {
				...result.data,
				items: filteredItems,
				total: filteredItems.length
			}
		};

	} catch (error) {
		console.error('搜索我的创作失败:', error);
		throw error;
	}
}

export default {
	保存创作结果,
	获取我的创作列表,
	获取创作详情,
	删除创作记录,
	更新创作信息,
	分享创作,
	下载创作文件,
	完整保存创作流程,
	批量删除创作,
	获取创作统计信息,
	搜索我的创作
};
