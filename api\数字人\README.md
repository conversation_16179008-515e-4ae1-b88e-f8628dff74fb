# 数字人功能 API 文档

## 📋 概述

数字人生成和交互工作流

## 🚀 快速开始

```javascript
import { 数字人 } from '@/api/数字人/index.js';

const result = await 数字人({
    avatarType: '示例值',
    voiceType: '示例值',
    textInput: '示例值',
    emotion: '示例值'
});
```

## 📝 API 接口

### 主要接口

#### `数字人(formData, options)`
执行数字人功能的主接口。

**参数：**
- `avatarType` (string): avatarType *必需*
- `voiceType` (string): voiceType *必需*
- `textInput` (string): textInput *必需*
- `emotion` (string): emotion *必需*

**返回：**
```javascript
{
    success: true,
    data: {
        // 处理结果
    }
}
```

## ⚙️ 配置说明

### 工作流配置
- 工作流ID: `digital_human_workflow_001`
- 工作流类型: `digital_human`
- 基础费用: 30金币

## 🔧 工作流对接

### 结构化参数格式
```javascript
{
    requestId: "req_id",
    workflowId: "digital_human_workflow_001",
    workflowType: "digital_human",
    structuredParams: {
        avatarType: { type: "text", value: "值", placeholder: "{{avatarType}}" },
        voiceType: { type: "text", value: "值", placeholder: "{{voiceType}}" },
        textInput: { type: "text", value: "值", placeholder: "{{textInput}}" },
        emotion: { type: "text", value: "值", placeholder: "{{emotion}}" }
    }
}
```

## 🚨 错误处理

常见错误码：
- `DIGITAL_HUMAN_001`: 参数格式不正确
- `DIGITAL_HUMAN_007`: 金币余额不足
- `DIGITAL_HUMAN_008`: 工作流执行超时

## 📞 技术支持

如有问题，请联系开发团队。