<template>
  <view class="enhanced-textarea-container" :class="platformClass">
    <textarea
      ref="textareaRef"
      class="enhanced-textarea"
      :value="modelValue"
      :placeholder="showAnimatedPlaceholder ? '' : placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :auto-height="autoHeight"
      :cursor-spacing="cursorSpacing"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @paste="handlePaste"
      @keyboardheightchange="handleKeyboardHeightChange"
    ></textarea>
    
    <!-- 动态提示文字组件 -->
    <view 
      v-if="showAnimatedPlaceholder && !modelValue" 
      class="animated-placeholder"
      :class="{ 'is-focused': isFocused }"
    >
      <text class="placeholder-text">{{ currentPlaceholder }}</text>
    </view>
  </view>
</template>

<script>
import { platformAdapter } from '../utils/platform.js';

export default {
  name: 'EnhancedTextArea',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: false
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'confirm', 'paste', 'keyboardheightchange'],
  data() {
    return {
      isFocused: false,
      platformClass: '',
      currentPlaceholder: '',
      placeholderIndex: 0,
      placeholderTimer: null,
      isComposing: false // 用于处理输入法组合文字的状态
    };
  },
  watch: {
    placeholders: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.currentPlaceholder = newVal[0];
          this.startPlaceholderAnimation();
        } else {
          this.currentPlaceholder = this.placeholder;
        }
      }
    }
  },
  created() {
    this.detectPlatform();
  },
  mounted() {
    // 初始化后立即尝试设置滚动属性
    this.$nextTick(() => {
      this.setupScrollBehavior();
    });
  },
  beforeUnmount() {
    this.clearPlaceholderTimer();
    
    // 清理触摸事件处理器
    if (this._cleanupTouchHandlers) {
      this._cleanupTouchHandlers();
      this._cleanupTouchHandlers = null;
    }
    
    // 确保取消任何进行中的动画
    if (this._scrollAnimation) {
      cancelAnimationFrame(this._scrollAnimation);
      this._scrollAnimation = null;
    }
  },
  methods: {
    /**
     * 平台检测与初始化
     */
    detectPlatform() {
      const platform = platformAdapter.getPlatformType();
      this.platformClass = platform;
    },

    /**
     * 设置滚动行为
     */
    setupScrollBehavior() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      // 针对不同平台设置不同的滚动属性
      if (platformAdapter.isH5()) {
        // H5环境优化
        textarea.style.overscrollBehavior = 'none';
        textarea.style.webkitOverflowScrolling = 'touch';
        textarea.style.scrollBehavior = 'smooth';
        
        // 添加额外的滚动优化
        textarea.style.touchAction = 'pan-y'; // 允许垂直滑动
        textarea.style.userSelect = 'text'; // 允许文本选择
        
        // 对于移动端H5添加触摸事件监听
        if (platformAdapter.isMobileH5()) {
          this.setupTouchHandlers(textarea);
        }
      } else if (platformAdapter.isWechatMiniProgram() || platformAdapter.isAlipayMiniProgram()) {
        // 小程序环境优化
        // 小程序滚动在原生层处理
        if (textarea.setAttribute) {
          textarea.setAttribute('show-scrollbar', 'true');
          textarea.setAttribute('enhanced', 'true');
          textarea.setAttribute('bounces', 'true');
        }
      } else if (platformAdapter.isApp()) {
        // APP环境优化
        textarea.style.webkitOverflowScrolling = 'touch';
        textarea.style.overscrollBehavior = 'none';
        
        // APP环境下可能需要额外的滚动优化
        this.setupTouchHandlers(textarea);
      }
    },
    
    /**
     * 为文本框添加触摸事件处理
     */
    setupTouchHandlers(element) {
      if (!element) return;
      
      // 存储状态
      let startY = 0;
      let startX = 0;
      let lastY = 0;
      let scrolling = false;
      let momentum = 0;
      
      // 触摸开始
      const touchStart = (e) => {
        startY = e.touches[0].clientY;
        startX = e.touches[0].clientX;
        lastY = startY;
        scrolling = false;
        momentum = 0;
        
        // 停止任何正在进行的惯性滚动
        if (this._scrollAnimation) {
          cancelAnimationFrame(this._scrollAnimation);
          this._scrollAnimation = null;
        }
      };
      
      // 触摸移动
      const touchMove = (e) => {
        const currentY = e.touches[0].clientY;
        const currentX = e.touches[0].clientX;
        
        // 判断主要滑动方向
        if (!scrolling) {
          const deltaY = Math.abs(currentY - startY);
          const deltaX = Math.abs(currentX - startX);
          if (deltaY > deltaX && deltaY > 10) {
            scrolling = 'vertical';
          } else if (deltaX > deltaY && deltaX > 10) {
            scrolling = 'horizontal';
            return; // 水平滑动不处理
          }
        }
        
        if (scrolling === 'vertical') {
          const deltaY = currentY - lastY;
          element.scrollTop -= deltaY * 1.2; // 略微加速滚动速度
          
          // 计算动量
          momentum = deltaY * 0.8;
          
          lastY = currentY;
          
          // 防止页面滚动
          e.preventDefault();
        }
      };
      
      // 触摸结束 - 添加惯性滚动
      const touchEnd = () => {
        if (scrolling === 'vertical' && Math.abs(momentum) > 0.5) {
          let currentMomentum = momentum;
          
          const animateScroll = () => {
            // 减速
            currentMomentum *= 0.95;
            
            // 应用滚动
            element.scrollTop -= currentMomentum;
            
            // 当动量很小时停止
            if (Math.abs(currentMomentum) > 0.5) {
              this._scrollAnimation = requestAnimationFrame(animateScroll);
            }
          };
          
          this._scrollAnimation = requestAnimationFrame(animateScroll);
        }
        
        scrolling = false;
      };
      
      // 添加事件监听
      element.addEventListener('touchstart', touchStart, { passive: true });
      element.addEventListener('touchmove', touchMove, { passive: false });
      element.addEventListener('touchend', touchEnd, { passive: true });
      
      // 存储清理函数，以便在组件销毁时移除
      this._cleanupTouchHandlers = () => {
        element.removeEventListener('touchstart', touchStart);
        element.removeEventListener('touchmove', touchMove);
        element.removeEventListener('touchend', touchEnd);
        
        if (this._scrollAnimation) {
          cancelAnimationFrame(this._scrollAnimation);
          this._scrollAnimation = null;
        }
      };
    },

    /**
     * 获取文本框DOM元素
     */
    getTextareaElement() {
      if (!this.$refs.textareaRef) return null;
      
      if (platformAdapter.isH5()) {
        return this.$refs.textareaRef.$el || this.$refs.textareaRef;
      } else {
        return this.$refs.textareaRef;
      }
    },

    /**
     * 启动占位符动画
     */
    startPlaceholderAnimation() {
      // 清除之前的定时器
      this.clearPlaceholderTimer();
      
      // 如果只有一个占位符或没有占位符，不启动动画
      if (!this.placeholders || this.placeholders.length <= 1) return;
      
      this.placeholderTimer = setInterval(() => {
        this.placeholderIndex = (this.placeholderIndex + 1) % this.placeholders.length;
        this.currentPlaceholder = this.placeholders[this.placeholderIndex];
      }, 3000); // 每3秒切换一次
    },

    /**
     * 清除占位符定时器
     */
    clearPlaceholderTimer() {
      if (this.placeholderTimer) {
        clearInterval(this.placeholderTimer);
        this.placeholderTimer = null;
      }
    },

    /**
     * 处理输入事件
     */
    handleInput(e) {
      const value = e.detail?.value || e.target?.value || '';
      this.$emit('update:modelValue', value);
    },

    /**
     * 处理获得焦点事件
     */
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },

    /**
     * 处理失去焦点事件
     */
    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    /**
     * 处理确认事件
     */
    handleConfirm(e) {
      this.$emit('confirm', e);
    },

    /**
     * 处理粘贴事件
     */
    handlePaste(e) {
      this.$emit('paste', e);
      // 粘贴后将光标定位到末尾
      this.$nextTick(() => {
        this.setCursorToEnd();
      });
    },

    /**
     * 处理键盘高度变化事件
     */
    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e);
    },

    /**
     * 设置光标到文本末尾
     */
    setCursorToEnd() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      try {
        if (platformAdapter.isH5()) {
          // H5环境
          textarea.focus();
          
          // 如果有selection API支持
          if (typeof textarea.selectionStart !== 'undefined') {
            const len = this.modelValue.length;
            textarea.selectionStart = len;
            textarea.selectionEnd = len;
          }
        } else {
          // 小程序和APP环境
          // 使用context方法设置光标位置
          const len = this.modelValue.length;
          
          if (uni && uni.createSelectorQuery) {
            const query = uni.createSelectorQuery().in(this);
            query.select('.enhanced-textarea').context((res) => {
              if (res && res.context) {
                res.context.setSelectionRange(len, len);
              }
            }).exec();
          }
        }
      } catch (error) {
        console.error('设置光标位置失败:', error);
      }
    },

    /**
     * 外部调用：手动设置文本并将光标移动到末尾
     */
    setText(text) {
      this.$emit('update:modelValue', text);
      this.$nextTick(() => {
        this.setCursorToEnd();
      });
    },

    /**
     * 外部调用：清空文本
     */
    clearText() {
      this.$emit('update:modelValue', '');
    },

    /**
     * 外部调用：获取焦点
     */
    focus() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.focus();
      }
    },

    /**
     * 外部调用：失去焦点
     */
    blur() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.blur();
      }
    }
  }
};
</script>

<style>
.enhanced-textarea-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.enhanced-textarea {
  width: 100%;
  min-height: 80px;
  box-sizing: border-box;
  padding: 12px;
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  background-color: transparent;
  color: #333;
  outline: none;
  resize: none;
  transition: border-color 0.3s, box-shadow 0.3s;
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  /* 增强滚动行为 */
  overscroll-behavior: contain;
  overscroll-behavior-y: contain;
  scrollbar-width: thin;
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent;
}

.enhanced-textarea:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 自定义滚动条样式 */
.enhanced-textarea::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
}

.enhanced-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
  transition: background-color 0.3s;
}

.enhanced-textarea::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

/* 动态提示文字样式 */
.animated-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 12px;
  pointer-events: none;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  opacity: 0.6;
  transition: opacity 0.3s;
}

.animated-placeholder.is-focused {
  opacity: 0.4;
}

.placeholder-text {
  color: #909399;
  font-size: 16px;
  line-height: 1.5;
  transition: all 0.3s;
}

/* 平台特定样式 */
.mp-weixin .enhanced-textarea,
.mp-alipay .enhanced-textarea {
  /* 小程序特定样式 */
  padding: 10px;
}

.app .enhanced-textarea {
  /* App特定样式 */
  -webkit-overflow-scrolling: touch;
}

.mobile-h5 .enhanced-textarea {
  /* 移动H5特定样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(192, 196, 204, 0.5) transparent;
  touch-action: pan-y;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-h5 .enhanced-textarea::-webkit-scrollbar {
  width: 4px;
}

.mobile-h5 .enhanced-textarea::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.mobile-h5 .enhanced-textarea::-webkit-scrollbar-thumb {
  background: rgba(192, 196, 204, 0.5);
  border-radius: 2px;
}

.pc .enhanced-textarea {
  /* PC特定样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(192, 196, 204, 0.5) transparent;
}

.pc .enhanced-textarea::-webkit-scrollbar {
  width: 6px;
}

.pc .enhanced-textarea::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.pc .enhanced-textarea::-webkit-scrollbar-thumb {
  background: rgba(192, 196, 204, 0.5);
  border-radius: 3px;
}

/* 添加滚动中反馈样式 */
.enhanced-textarea.scrolling {
  backdrop-filter: blur(0);  /* 滚动时减少模糊以提高性能 */
}

/* 反馈状态 */
@keyframes scrollPulse {
  0% { box-shadow: 0 0 0 rgba(64, 158, 255, 0); }
  50% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.3); }
  100% { box-shadow: 0 0 0 rgba(64, 158, 255, 0); }
}

.enhanced-textarea.momentum-scrolling {
  animation: scrollPulse 1s ease-out;
}
</style> 