<template>
  <view class="adaptive-scroll-wrapper">
    <!-- 使用scroll-view组件，自动适配所有平台 -->
    <scroll-view
      v-bind="scrollConfig"
      class="adaptive-scroll-view"
      @scroll="handleScroll"
      @scrolltoupper="handleScrollToUpper"
      @scrolltolower="handleScrollToLower"
    >
      <view class="scroll-content-wrapper">
        <slot></slot>
      </view>
    </scroll-view>
    
    <!-- 滚动指示器 -->
    <view v-if="showIndicator && hasMoreContent" class="scroll-indicator">
      <text class="indicator-text">{{ indicatorText }}</text>
    </view>
  </view>
</template>

<script>
import { ScrollConfig, EventAdapter, DebugAdapter } from '@/utils/platform-adapter'

export default {
  name: 'AdaptiveScrollView',
  
  props: {
    // 滚动区域高度
    height: {
      type: String,
      default: '400px'
    },
    
    // 是否显示滚动指示器
    showIndicator: {
      type: Boolean,
      default: false
    },
    
    // 指示器文本
    indicatorText: {
      type: String,
      default: '↓ 滑动查看更多 ↓'
    },
    
    // 是否启用下拉刷新
    enableRefresh: {
      type: Boolean,
      default: false
    },
    
    // 是否启用上拉加载
    enableLoadMore: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      hasMoreContent: true,
      scrollTop: 0
    }
  },
  
  computed: {
    // 滚动配置 - 使用适配器自动适配
    scrollConfig() {
      const config = ScrollConfig.getScrollViewProps(this.height)
      
      // 根据props调整配置
      if (this.enableRefresh) {
        config.refresherEnabled = true
        config.refresherTriggered = false
      }
      
      return config
    }
  },
  
  methods: {
    // 统一的滚动处理
    handleScroll(e) {
      const handler = EventAdapter.getScrollHandler((data) => {
        const { scrollTop, scrollPercent, scrollHeight, clientHeight } = data
        
        this.scrollTop = scrollTop
        
        // 判断是否还有更多内容
        this.hasMoreContent = scrollTop < (scrollHeight - clientHeight - 50)
        
        // 向父组件发送滚动事件
        this.$emit('scroll', {
          scrollTop,
          scrollPercent,
          scrollHeight,
          clientHeight
        })
        
        // 调试信息
        DebugAdapter.log('滚动事件:', data)
      })
      
      handler(e)
    },
    
    // 滚动到顶部
    handleScrollToUpper() {
      this.$emit('scrolltoupper')
      
      if (this.enableRefresh) {
        this.$emit('refresh')
      }
      
      DebugAdapter.log('滚动到顶部')
    },
    
    // 滚动到底部
    handleScrollToLower() {
      this.$emit('scrolltolower')
      
      if (this.enableLoadMore) {
        this.$emit('loadmore')
      }
      
      DebugAdapter.log('滚动到底部')
    },
    
    // 滚动到指定位置
    scrollTo(scrollTop, animated = true) {
      // 这里可以通过uni的API实现
      // 但需要根据不同平台做适配
      DebugAdapter.log('滚动到:', scrollTop)
    }
  }
}
</script>

<style lang="scss" scoped>
.adaptive-scroll-wrapper {
  position: relative;
  width: 100%;
}

.adaptive-scroll-view {
  width: 100%;
  
  /* 基础滚动优化 - 让uni-app处理平台差异 */
  -webkit-overflow-scrolling: touch;
}

.scroll-content-wrapper {
  width: 100%;
  min-height: 100%;
}

.scroll-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0.8;
  animation: bounce 2s infinite;
  z-index: 10;
}

.indicator-text {
  color: #ffffff;
  font-size: 12px;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-3px);
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.adaptive-scroll-view {
  /* H5端可能需要额外的样式 */
}
/* #endif */

/* #ifdef APP-PLUS */
.adaptive-scroll-view {
  /* APP端可能需要额外的样式 */
}
/* #endif */

/* #ifndef H5 */
/* #ifndef APP-PLUS */
.adaptive-scroll-view {
  /* 小程序端可能需要额外的样式 */
}
/* #endif */
/* #endif */
</style>
