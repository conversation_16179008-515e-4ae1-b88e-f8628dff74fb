# 支付系统 - 支付接口文档

## 📋 **功能概述**

支付系统负责用户充值、扣费、余额管理、订单处理等金融相关功能。

## 💰 **金币体系**

### **金币说明**
- 1元人民币 = 10金币
- 最小充值金额：10元（100金币）
- 金币永久有效，不过期

### **功能费用**
| 功能 | 基础费用 | 会员折扣 |
|------|---------|---------|
| 姓名配对 | 12金币 | 8折 |
| 起名功能 | 25-55金币 | 8折 |
| 运程测试 | 15金币 | 8折 |
| 文生图 | 20金币 | 8折 |
| 文生视频 | 50金币 | 8折 |

## 🔧 **核心接口**

### **1. 获取用户余额**
```
GET /api/v1/payment/balance
Authorization: Bearer {token}

响应：
{
    "success": true,
    "data": {
        "userId": "user_123",
        "balance": 150,
        "frozenBalance": 0,
        "totalRecharge": 500,
        "totalSpent": 350,
        "isMember": false,
        "memberExpiry": null
    }
}
```

### **2. 创建充值订单**
```
POST /api/v1/payment/recharge/create
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "amount": 100,
    "paymentMethod": "wechat|alipay|bank",
    "returnUrl": "充值成功回调URL",
    "notifyUrl": "服务器通知URL"
}

响应：
{
    "success": true,
    "data": {
        "orderId": "order_123456",
        "amount": 100,
        "coins": 1000,
        "paymentMethod": "wechat",
        "paymentUrl": "支付链接",
        "qrCode": "二维码数据",
        "expiresIn": 1800,
        "status": "pending"
    }
}
```

### **3. 查询充值订单状态**
```
GET /api/v1/payment/recharge/status/{orderId}
Authorization: Bearer {token}

响应：
{
    "success": true,
    "data": {
        "orderId": "order_123456",
        "userId": "user_123",
        "amount": 100,
        "coins": 1000,
        "paymentMethod": "wechat",
        "status": "paid|pending|failed|cancelled",
        "createTime": "2025-01-11T10:00:00.000Z",
        "payTime": "2025-01-11T10:05:00.000Z",
        "transactionId": "第三方交易号"
    }
}
```

### **4. 扣费接口**
```
POST /api/v1/payment/deduct
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "userId": "user_123",
    "amount": 12,
    "reason": "姓名配对服务",
    "orderId": "service_order_123",
    "metadata": {
        "service": "name-matching",
        "requestId": "req_123"
    }
}

响应：
{
    "success": true,
    "data": {
        "transactionId": "txn_123456",
        "userId": "user_123",
        "amount": 12,
        "balanceBefore": 150,
        "balanceAfter": 138,
        "reason": "姓名配对服务",
        "timestamp": "2025-01-11T10:30:00.000Z"
    }
}
```

### **5. 退款接口**
```
POST /api/v1/payment/refund
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "transactionId": "txn_123456",
    "amount": 12,
    "reason": "服务异常退款",
    "refundType": "full|partial"
}

响应：
{
    "success": true,
    "data": {
        "refundId": "refund_123456",
        "originalTransactionId": "txn_123456",
        "refundAmount": 12,
        "balanceBefore": 138,
        "balanceAfter": 150,
        "reason": "服务异常退款",
        "status": "success",
        "timestamp": "2025-01-11T10:35:00.000Z"
    }
}
```

### **6. 交易记录查询**
```
GET /api/v1/payment/transactions
Authorization: Bearer {token}
Query参数：
- page: 页码（默认1）
- limit: 每页数量（默认20）
- type: 交易类型（recharge|deduct|refund）
- startDate: 开始日期
- endDate: 结束日期

响应：
{
    "success": true,
    "data": {
        "transactions": [
            {
                "id": "txn_123456",
                "type": "deduct",
                "amount": 12,
                "balanceBefore": 150,
                "balanceAfter": 138,
                "reason": "姓名配对服务",
                "status": "success",
                "timestamp": "2025-01-11T10:30:00.000Z",
                "metadata": {
                    "service": "name-matching",
                    "requestId": "req_123"
                }
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 50,
            "totalPages": 3
        }
    }
}
```

### **7. 会员购买**
```
POST /api/v1/payment/membership/purchase
Authorization: Bearer {token}
Content-Type: application/json

请求体：
{
    "membershipType": "monthly|quarterly|yearly",
    "paymentMethod": "wechat|alipay|balance"
}

响应：
{
    "success": true,
    "data": {
        "orderId": "member_order_123",
        "membershipType": "monthly",
        "price": 29,
        "duration": 30,
        "benefits": [
            "所有功能8折优惠",
            "优先处理服务",
            "专属客服支持"
        ],
        "paymentUrl": "支付链接",
        "expiresIn": 1800
    }
}
```

### **8. 支付回调处理**
```
POST /api/v1/payment/callback/{provider}
Content-Type: application/json

请求体：
{
    // 第三方支付平台回调数据
}

响应：
{
    "success": true,
    "message": "处理成功"
}
```

## 📊 **数据模型**

### **充值订单表**
```sql
CREATE TABLE recharge_orders (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    coins INT NOT NULL,
    payment_method ENUM('wechat', 'alipay', 'bank') NOT NULL,
    status ENUM('pending', 'paid', 'failed', 'cancelled') DEFAULT 'pending',
    payment_url TEXT,
    qr_code TEXT,
    transaction_id VARCHAR(100),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    pay_time DATETIME,
    expire_time DATETIME,
    notify_url VARCHAR(255),
    return_url VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_create_time (create_time)
);
```

### **交易记录表**
```sql
CREATE TABLE transactions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    type ENUM('recharge', 'deduct', 'refund') NOT NULL,
    amount INT NOT NULL,
    balance_before INT NOT NULL,
    balance_after INT NOT NULL,
    reason VARCHAR(255) NOT NULL,
    status ENUM('success', 'failed', 'pending') DEFAULT 'success',
    related_order_id VARCHAR(50),
    metadata JSON,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_type_time (user_id, type, create_time),
    INDEX idx_related_order (related_order_id)
);
```

### **会员订单表**
```sql
CREATE TABLE membership_orders (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    membership_type ENUM('monthly', 'quarterly', 'yearly') NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration_days INT NOT NULL,
    payment_method ENUM('wechat', 'alipay', 'balance') NOT NULL,
    status ENUM('pending', 'paid', 'failed', 'cancelled') DEFAULT 'pending',
    start_date DATE,
    end_date DATE,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    pay_time DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 💳 **支付方式配置**

### **微信支付**
```javascript
const wechatConfig = {
    appId: "微信应用ID",
    mchId: "商户号",
    apiKey: "API密钥",
    notifyUrl: "https://api.example.com/payment/callback/wechat",
    returnUrl: "https://app.example.com/payment/success"
};
```

### **支付宝**
```javascript
const alipayConfig = {
    appId: "支付宝应用ID",
    privateKey: "应用私钥",
    publicKey: "支付宝公钥",
    notifyUrl: "https://api.example.com/payment/callback/alipay",
    returnUrl: "https://app.example.com/payment/success"
};
```

## 🔒 **安全措施**

### **支付安全**
- 所有支付接口使用HTTPS
- 支付密码二次验证
- 异常交易监控和风控

### **数据安全**
- 敏感信息加密存储
- 交易日志完整记录
- 定期对账和审计

### **防刷措施**
- 单用户单日充值限额
- 异常交易实时监控
- IP频率限制

## 🚨 **错误码说明**

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| PAY_001 | 余额不足 | 400 |
| PAY_002 | 充值金额无效 | 400 |
| PAY_003 | 支付方式不支持 | 400 |
| PAY_004 | 订单不存在 | 404 |
| PAY_005 | 订单已过期 | 400 |
| PAY_006 | 订单状态异常 | 400 |
| PAY_007 | 支付失败 | 400 |
| PAY_008 | 退款失败 | 400 |
| PAY_009 | 重复支付 | 400 |
| PAY_010 | 系统维护中 | 503 |

---

**文档版本**: v1.0  
**创建时间**: 2025-01-11  
**维护团队**: 支付系统组
