<template>
  <view
    class="touch-scroll-enhancer"
    :class="{ 'h5-optimized': platform === 'h5', 'mp-optimized': platform === 'mp', 'app-optimized': platform === 'app' }"
    :style="customStyle"
    @touchstart="touchStart"
    @touchmove="touchMove"
    @touchend="touchEnd"
    ref="container"
  >
    <slot></slot>
  </view>
</template>

<script>
import { platformAdapter } from '../utils/platform.js';

export default {
  name: 'TouchScrollEnhancer',
  props: {
    // 滚动方向
    direction: {
      type: String,
      default: 'vertical', // 'vertical' 或 'horizontal'
    },
    // 自定义样式
    style: {
      type: Object,
      default: () => ({})
    },
    // 是否启用H5优化
    enableH5Optimizations: {
      type: Boolean,
      default: true
    },
    // 是否启用小程序优化
    enableMpOptimizations: {
      type: Boolean,
      default: true
    },
    // 是否启用APP优化
    enableAppOptimizations: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      platform: '',
      touchStartY: 0,
      touchStartX: 0,
      lastTouchY: 0,
      lastTouchX: 0,
      isTouching: false,
      scrollSpeed: 0,
      scrollAnimation: null,
      target: null,
      isScrolling: false,
      // H5特定参数
      momentum: 0,
      isUsingH5Optimizations: false,
      // 小程序特定参数
      isMpOptimized: false,
      // APP特定参数
      isAppOptimized: false,
      // 通用参数
      scrollTopCache: 0,
      lastScrollTime: 0,
      scrollMultiplier: 1.0 // 默认滚动倍率
    };
  },
  computed: {
    customStyle() {
      return {
        ...this.style,
        height: this.style.height || 'auto',
        maxHeight: this.style.maxHeight || 'none'
      };
    }
  },
  created() {
    // 获取当前平台
    if (platformAdapter.isWechatMiniProgram() || platformAdapter.isAlipayMiniProgram()) {
      this.platform = 'mp';
    } else if (platformAdapter.isApp()) {
      this.platform = 'app';
    } else if (platformAdapter.isMobileH5()) {
      this.platform = 'h5';
    } else if (platformAdapter.isPC()) {
      this.platform = 'pc';
    }
    
    // 根据平台设置滚动倍率
    this.initScrollMultiplier();
  },
  mounted() {
    console.log('🚫 TouchScrollEnhancer已被禁用');
    // 完全禁用组件初始化
    return;
  },
  beforeDestroy() {
    // 清理惯性滚动
    if (this.scrollAnimation) {
      cancelAnimationFrame(this.scrollAnimation);
      this.scrollAnimation = null;
    }
  },
  methods: {
    // 根据平台初始化滚动倍率
    initScrollMultiplier() {
      if (this.platform === 'h5') {
        this.scrollMultiplier = 1.8; // H5需要更高的倍率
      } else if (this.platform === 'mp') {
        this.scrollMultiplier = 2.0; // 小程序需要最高倍率
      } else if (this.platform === 'app') {
        this.scrollMultiplier = 1.5; // APP倍率适中
      } else {
        this.scrollMultiplier = 1.0; // 默认值
      }
    },
    
    // H5平台专用优化
    setupH5Optimizations() {
      // 查找目标元素
      const textarea = this.$el.querySelector('textarea');
      const input = this.$el.querySelector('input');
      
      this.target = textarea || input || this.$el;
      
      if (!this.target) return;
      
      // 应用H5特定样式
      this.target.style.webkitOverflowScrolling = 'touch';
      this.target.style.overscrollBehavior = 'none';
      this.target.style.touchAction = 'manipulation';
      this.target.style.willChange = 'scroll-position';
      
      // 如果目标是textarea或input，增加滚动边距
      if (textarea || input) {
        this.target.style.paddingTop = '12px';
        this.target.style.paddingBottom = '12px';
      }
      
      console.log('已应用H5滚动优化到', this.target.tagName);
    },
    
    // 小程序平台专用优化
    setupMpOptimizations() {
      // 查找目标元素 - 小程序环境下
      const textarea = this.$el.querySelector('textarea');
      const input = this.$el.querySelector('input');
      
      this.target = textarea || input || this.$el;
      
      if (!this.target) return;
      
      // 小程序环境下应用滚动属性
      if (this.direction === 'vertical') {
        this.target.style.overflow = 'auto';
        this.target.style.overflowY = 'auto';
      } else {
        this.target.style.overflow = 'auto';
        this.target.style.overflowX = 'auto';
      }
      
      console.log('已应用小程序滚动优化');
    },
    
    // APP平台专用优化
    setupAppOptimizations() {
      // 查找目标元素 - APP环境下
      const textarea = this.$el.querySelector('textarea');
      const input = this.$el.querySelector('input');
      
      this.target = textarea || input || this.$el;
      
      if (!this.target) return;
      
      // APP环境下应用样式
      if (this.direction === 'vertical') {
        this.target.style.overflowY = 'auto';
        this.target.style.webkitOverflowScrolling = 'touch';
      } else {
        this.target.style.overflowX = 'auto';
        this.target.style.webkitOverflowScrolling = 'touch';
      }
      
      console.log('已应用APP滚动优化');
    },
    
    // 寻找真正需要滚动的元素
    findScrollTarget() {
      // 首先尝试找textarea或input
      const textarea = this.$el.querySelector('textarea');
      const input = this.$el.querySelector('input');
      
      if (textarea) {
        this.target = textarea;
      } else if (input) {
        this.target = input;
      } else {
        // 如果没有找到，就使用容器本身
        this.target = this.$el;
      }
      
      // 确保目标元素有必要的样式
      if (this.target) {
        this.target.style.webkitOverflowScrolling = 'touch';
        this.target.style.overscrollBehavior = 'contain';
        
        if (this.direction === 'vertical') {
          this.target.style.overflowY = 'auto';
          this.target.style.touchAction = 'pan-y';
        } else {
          this.target.style.overflowX = 'auto';
          this.target.style.touchAction = 'pan-x';
        }
      }
    },
    
    // 触摸开始
    touchStart(e) {
      this.isTouching = true;
      this.touchStartY = e.touches[0].clientY;
      this.touchStartX = e.touches[0].clientX;
      this.lastTouchY = this.touchStartY;
      this.lastTouchX = this.touchStartX;
      this.isScrolling = false;
      this.momentum = 0; // 重置惯性
      this.lastScrollTime = Date.now();
      
      // 缓存当前滚动位置
      if (this.target) {
        this.scrollTopCache = this.platform === 'mp' ? 
          (this.target.scrollTop || 0) : 
          (this.target.scrollTop || 0);
      }
      
      // 停止惯性滚动
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
        this.scrollAnimation = null;
      }
    },
    
    // 触摸移动
    touchMove(e) {
      if (!this.isTouching || !this.target) return;
      
      const currentY = e.touches[0].clientY;
      const currentX = e.touches[0].clientX;
      
      // 确定滚动方向 (如果还没确定)
      if (!this.isScrolling) {
        const deltaY = Math.abs(currentY - this.touchStartY);
        const deltaX = Math.abs(currentX - this.touchStartX);
        if (deltaY > deltaX) {
          this.isScrolling = 'vertical';
        } else if (deltaX > deltaY) {
          this.isScrolling = 'horizontal';
        } else {
          return; // 无法确定方向，暂不处理
        }
      }
      
      // 根据滚动方向处理
      if (this.isScrolling === 'vertical' && this.direction === 'vertical') {
        const deltaY = currentY - this.lastTouchY;
        
        // 计算滚动速度
        this.scrollSpeed = deltaY * this.scrollMultiplier;
        
        // 计算动量 (所有平台通用)
        const now = Date.now();
        const elapsed = now - this.lastScrollTime;
        if (elapsed > 0) {
          this.momentum = (deltaY / elapsed) * 20 * this.scrollMultiplier;
          this.lastScrollTime = now;
        }
        
        // 应用滚动 - 注意这里使用target而非$el
        if (this.platform === 'mp') {
          // 小程序环境特殊处理
          this.scrollTopCache = Math.max(0, this.scrollTopCache - deltaY * this.scrollMultiplier);
          this.target.scrollTop = this.scrollTopCache;
        } else {
          // 其他环境
          this.target.scrollTop = Math.max(0, this.target.scrollTop - deltaY * this.scrollMultiplier);
        }
        
        // 更新位置
        this.lastTouchY = currentY;
      } else if (this.isScrolling === 'horizontal' && this.direction === 'horizontal') {
        const deltaX = currentX - this.lastTouchX;
        
        // 计算滚动速度
        this.scrollSpeed = deltaX * this.scrollMultiplier;
        
        // 应用滚动
        this.target.scrollLeft = Math.max(0, this.target.scrollLeft - deltaX * this.scrollMultiplier);
        
        // 更新位置
        this.lastTouchX = currentX;
      }
      
      // 只在必要时阻止默认行为
      if (e.cancelable && this.isScrolling) {
        e.preventDefault();
      }
    },
    
    // 触摸结束 - 恢复温和的惯性滚动
    touchEnd(e) {
      if (!this.isTouching || !this.target) return;

      this.isTouching = false;

      // 启用温和的惯性滚动
      if (Math.abs(this.momentum) > 0.5) {
        console.log('🔧 启用温和的惯性滚动');
        this.startControlledInertialScroll();
      }
    },

    // 启动受控的惯性滚动
    startControlledInertialScroll() {
      if (!this.target) return;

      let momentum = Math.max(-3, Math.min(3, this.momentum)); // 限制最大动量
      const startTime = performance.now();

      const scroll = () => {
        const elapsed = performance.now() - startTime;

        // 温和的衰减
        const dampingFactor = 0.94;
        momentum *= dampingFactor;

        // 如果动量太小或时间太长，停止滚动
        if (Math.abs(momentum) < 0.2 || elapsed > 800) {
          cancelAnimationFrame(this.scrollAnimation);
          return;
        }

        // 应用滚动
        if (this.direction === 'vertical') {
          this.target.scrollTop = Math.max(0, this.target.scrollTop - momentum);
        } else {
          this.target.scrollLeft = Math.max(0, this.target.scrollLeft - momentum);
        }

        this.scrollAnimation = requestAnimationFrame(scroll);
      };

      this.scrollAnimation = requestAnimationFrame(scroll);
    },

    // H5专用惯性滚动 - 使用更平滑的减速效果
    startH5InertialScroll() {
      if (!this.target) return;
      
      let momentum = this.momentum;
      let acceleration = 0.02; // 额外的加速度，增强滚动感
      let startTime = performance.now();
      
      const scroll = () => {
        const elapsed = performance.now() - startTime;
        
        // 在500ms内的滚动，使用较慢的衰减
        const dampingFactor = elapsed < 500 ? 0.96 : 0.92;
        
        // 如果动量太小，停止滚动
        if (Math.abs(momentum) < 0.3) {
          cancelAnimationFrame(this.scrollAnimation);
          this.scrollAnimation = null;
          return;
        }
        
        // 在开始阶段轻微增加动量，让滚动感更明显
        if (elapsed < 100) {
          momentum += Math.sign(momentum) * acceleration;
        }
        
        // 应用滚动
        this.target.scrollTop = Math.max(0, this.target.scrollTop - momentum);
        
        // 减速 (模拟摩擦)
        momentum *= dampingFactor;
        
        // 继续滚动
        this.scrollAnimation = requestAnimationFrame(scroll);
      };
      
      this.scrollAnimation = requestAnimationFrame(scroll);
    },
    
    // 小程序专用惯性滚动
    startMpInertialScroll() {
      if (!this.target) return;
      
      let momentum = this.momentum;
      let damping = 0.90; // 小程序需要更快的减速
      
      const scroll = () => {
        // 如果动量太小，停止滚动
        if (Math.abs(momentum) < 0.5) {
          cancelAnimationFrame(this.scrollAnimation);
          this.scrollAnimation = null;
          return;
        }
        
        // 应用滚动
        this.scrollTopCache = Math.max(0, this.scrollTopCache - momentum);
        this.target.scrollTop = this.scrollTopCache;
        
        // 减速 (模拟摩擦)
        momentum *= damping;
        
        // 继续滚动
        this.scrollAnimation = requestAnimationFrame(scroll);
      };
      
      this.scrollAnimation = requestAnimationFrame(scroll);
    },
    
    // APP专用惯性滚动
    startAppInertialScroll() {
      if (!this.target) return;
      
      let momentum = this.momentum;
      let damping = 0.94; // APP减速较慢，视觉效果更流畅
      
      const scroll = () => {
        // 如果动量太小，停止滚动
        if (Math.abs(momentum) < 0.3) {
          cancelAnimationFrame(this.scrollAnimation);
          this.scrollAnimation = null;
          return;
        }
        
        // 应用滚动
        this.target.scrollTop = Math.max(0, this.target.scrollTop - momentum);
        
        // 减速 (模拟摩擦)
        momentum *= damping;
        
        // 继续滚动
        this.scrollAnimation = requestAnimationFrame(scroll);
      };
      
      this.scrollAnimation = requestAnimationFrame(scroll);
    },
    
    // 标准惯性滚动
    startInertialScroll() {
      if (!this.target) return;
      
      let speed = this.scrollSpeed;
      const isVertical = this.isScrolling === 'vertical';
      
      const scroll = () => {
        if (Math.abs(speed) < 0.5) {
          cancelAnimationFrame(this.scrollAnimation);
          this.scrollAnimation = null;
          return;
        }
        
        // 应用滚动
        if (isVertical) {
          this.target.scrollTop = Math.max(0, this.target.scrollTop - speed);
        } else {
          this.target.scrollLeft = Math.max(0, this.target.scrollLeft - speed);
        }
        
        // 减速 (模拟摩擦)
        speed *= 0.92;
        
        // 继续滚动
        this.scrollAnimation = requestAnimationFrame(scroll);
      };
      
      this.scrollAnimation = requestAnimationFrame(scroll);
    }
  }
};
</script>

<style>
.touch-scroll-enhancer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
}

/* 基本滚动样式 */
.touch-scroll-enhancer > textarea,
.touch-scroll-enhancer > input {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  touch-action: pan-y;
  box-sizing: border-box;
  padding: 8px;
}

/* H5环境特殊优化 */
.touch-scroll-enhancer.h5-optimized {
  overflow: hidden;
  transform: translateZ(0); /* 启用硬件加速 */
}

.touch-scroll-enhancer.h5-optimized > textarea,
.touch-scroll-enhancer.h5-optimized > input {
  overscroll-behavior: none;
  will-change: scroll-position; /* 提示浏览器优化滚动性能 */
  touch-action: manipulation; /* 增强触摸响应 */
  padding: 12px; /* 增加内边距 */
  
  /* 更平滑的滚动效果 */
  scroll-behavior: smooth;
  
  /* 消除滚动边界的视觉反馈 */
  -ms-overflow-style: none; /* IE/Edge 隐藏滚动条 */
  scrollbar-width: thin; /* Firefox 细滚动条 */
}

/* 小程序环境特殊优化 */
.touch-scroll-enhancer.mp-optimized {
  overflow: hidden;
}

.touch-scroll-enhancer.mp-optimized > textarea,
.touch-scroll-enhancer.mp-optimized > input {
  padding: 12px; /* 增加内边距，提高触摸区域 */
}

/* APP环境特殊优化 */
.touch-scroll-enhancer.app-optimized {
  overflow: hidden;
}

.touch-scroll-enhancer.app-optimized > textarea,
.touch-scroll-enhancer.app-optimized > input {
  -webkit-overflow-scrolling: touch;
  padding: 10px; /* 适中内边距 */
  scroll-behavior: smooth;
}

/* 定制H5滚动条样式 */
.touch-scroll-enhancer.h5-optimized > textarea::-webkit-scrollbar,
.touch-scroll-enhancer.h5-optimized > input::-webkit-scrollbar {
  width: 3px;
}

.touch-scroll-enhancer.h5-optimized > textarea::-webkit-scrollbar-thumb,
.touch-scroll-enhancer.h5-optimized > input::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}
</style> 