/**
 * 图生视频功能统一入口文件
 * 整合图生视频功能的所有接口和业务逻辑
 * 创建时间：2025-01-11
 */

import { 执行图生视频工作流, 查询图生视频状态, 取消图生视频工作流 } from './工作流执行接口.js';
import { 图生视频工作流配置, 图生视频参数验证规则, 图生视频错误码, 图生视频状态 } from './工作流配置.js';

// 图生视频功能统一API
export const 图生视频功能API = {
    执行图生视频工作流,
    查询图生视频状态,
    取消图生视频工作流,
    
    配置: 图生视频工作流配置,
    验证规则: 图生视频参数验证规则,
    错误码: 图生视频错误码,
    状态码: 图生视频状态
};

// 快捷方法
export async function 图生视频(formData, options = {}) {
    return await 执行图生视频工作流(formData, options);
}

export default 图生视频功能API;