/**
 * 运程测试功能工作流配置
 * 定义运程测试功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 运程测试工作流配置
 */
export const 运程测试工作流配置 = {
    // 工作流基础信息
    workflowId: 'fortune_test_workflow_001',
    workflowType: 'fortune_analysis',
    moduleName: '运程测试',
    
    // 工作流描述
    description: '基于传统易经学文化的2025年个人运程分析工作流',
    
    // 支持的测试类型
    supportedTypes: [
        'yearly',      // 年度运程
        'monthly',     // 月度运程
        'daily',       // 日运程
        'career',      // 事业运程
        'love',        // 感情运程
        'wealth',      // 财运分析
        'health',      // 健康运程
        'comprehensive' // 综合运程
    ],

    // 结构化参数定义
    structuredParams: {
        // 基础信息参数
        name: {
            type: 'text',
            required: true,
            placeholder: '{{name}}',
            description: '姓名',
            validation: {
                minLength: 2,
                maxLength: 10,
                pattern: '^[\u4e00-\u9fa5a-zA-Z]+$'
            }
        },
        
        gender: {
            type: 'text',
            required: true,
            placeholder: '{{gender}}',
            description: '性别',
            allowedValues: ['male', 'female']
        },

        // 生辰信息参数
        birthDate: {
            type: 'text',
            required: true,
            placeholder: '{{birthDate}}',
            description: '出生日期',
            format: 'YYYY-MM-DD'
        },

        birthTime: {
            type: 'text',
            required: true,
            placeholder: '{{birthTime}}',
            description: '出生时间',
            format: 'HH:MM'
        },

        birthPlace: {
            type: 'text',
            required: false,
            placeholder: '{{birthPlace}}',
            description: '出生地点',
            maxLength: 50
        },

        // 测试配置参数
        testType: {
            type: 'text',
            required: true,
            placeholder: '{{testType}}',
            description: '测试类型',
            defaultValue: 'yearly',
            allowedValues: ['yearly', 'monthly', 'daily', 'career', 'love', 'wealth', 'health', 'comprehensive']
        },

        testYear: {
            type: 'config',
            required: false,
            placeholder: '{{testYear}}',
            description: '测试年份',
            defaultValue: 2025,
            range: { min: 2024, max: 2030 }
        },

        testMonth: {
            type: 'config',
            required: false,
            placeholder: '{{testMonth}}',
            description: '测试月份',
            range: { min: 1, max: 12 }
        },

        // 分析深度参数
        analysisDepth: {
            type: 'config',
            required: false,
            placeholder: '{{analysisDepth}}',
            description: '分析深度',
            defaultValue: 'standard',
            allowedValues: ['basic', 'standard', 'detailed', 'comprehensive']
        },

        // 关注领域参数
        focusAreas: {
            type: 'config',
            required: false,
            placeholder: '{{focusAreas}}',
            description: '关注领域',
            defaultValue: ['career', 'love', 'wealth', 'health'],
            allowedValues: ['career', 'love', 'wealth', 'health', 'family', 'study', 'travel']
        },

        // 输出格式参数
        includeAdvice: {
            type: 'config',
            required: false,
            placeholder: '{{includeAdvice}}',
            description: '包含建议',
            defaultValue: true
        },

        includeLuckyItems: {
            type: 'config',
            required: false,
            placeholder: '{{includeLuckyItems}}',
            description: '包含幸运物品',
            defaultValue: true
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下信息进行2025年运程分析：

个人信息：
- 姓名：{{name}}
- 性别：{{gender}}
- 出生日期：{{birthDate}}
- 出生时间：{{birthTime}}
- 出生地点：{{birthPlace}}

分析要求：
- 测试类型：{{testType}}
- 测试年份：{{testYear}}
- 测试月份：{{testMonth}}
- 分析深度：{{analysisDepth}}
- 关注领域：{{focusAreas}}

输出要求：
- 包含建议：{{includeAdvice}}
- 包含幸运物品：{{includeLuckyItems}}

请基于传统易经学文化，结合现代生活实际，提供详细的运程分析，包含：
1. 整体运势概况
2. 各领域详细分析
3. 月度运势变化
4. 开运建议和注意事项
5. 幸运颜色、数字、方位等
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: {
                overallFortune: 'object',
                detailedAnalysis: 'object',
                monthlyFortune: 'array',
                luckyItems: 'object',
                advice: 'array',
                warnings: 'array',
                generatedAt: 'string'
            }
        }
    },

    // 费用配置
    pricing: {
        basic: 8,           // 基础运程测试
        standard: 15,       // 标准运程测试
        detailed: 25,       // 详细运程测试
        comprehensive: 40,  // 综合运程测试
        memberDiscount: 0.8 // 会员折扣
    },

    // 执行配置
    execution: {
        timeout: 180000,      // 3分钟超时
        maxRetries: 3,        // 最大重试次数
        pollInterval: 2000,   // 轮询间隔
        enableCache: true     // 启用缓存
    }
};

/**
 * 运程测试参数验证规则
 */
export const 运程测试参数验证规则 = {
    // 必需参数验证
    required: ['name', 'gender', 'birthDate', 'birthTime', 'testType'],
    
    // 参数格式验证
    formats: {
        birthDate: /^\d{4}-\d{2}-\d{2}$/,
        birthTime: /^\d{2}:\d{2}$/,
        name: /^[\u4e00-\u9fa5a-zA-Z]{2,10}$/
    },

    // 参数范围验证
    ranges: {
        testYear: { min: 2024, max: 2030 },
        testMonth: { min: 1, max: 12 },
        birthPlace: { maxLength: 50 }
    },

    // 枚举值验证
    enums: {
        gender: ['male', 'female'],
        testType: ['yearly', 'monthly', 'daily', 'career', 'love', 'wealth', 'health', 'comprehensive'],
        analysisDepth: ['basic', 'standard', 'detailed', 'comprehensive']
    }
};

/**
 * 运程测试错误码定义
 */
export const 运程测试错误码 = {
    INVALID_NAME: { code: 'FORTUNE_001', message: '姓名格式不正确' },
    INVALID_GENDER: { code: 'FORTUNE_002', message: '性别参数无效' },
    INVALID_BIRTH_DATE: { code: 'FORTUNE_003', message: '出生日期格式不正确' },
    INVALID_BIRTH_TIME: { code: 'FORTUNE_004', message: '出生时间格式不正确' },
    INVALID_TEST_TYPE: { code: 'FORTUNE_005', message: '测试类型参数无效' },
    INVALID_TEST_YEAR: { code: 'FORTUNE_006', message: '测试年份参数无效' },
    INSUFFICIENT_COINS: { code: 'FORTUNE_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'FORTUNE_008', message: '运程测试工作流执行超时' },
    WORKFLOW_FAILED: { code: 'FORTUNE_009', message: '运程测试工作流执行失败' },
    UNKNOWN_ERROR: { code: 'FORTUNE_999', message: '未知错误' }
};

/**
 * 运程测试状态定义
 */
export const 运程测试状态 = {
    PENDING: 'pending',           // 等待中
    PROCESSING: 'processing',     // 处理中
    COMPLETED: 'completed',       // 已完成
    FAILED: 'failed',            // 失败
    CANCELLED: 'cancelled',       // 已取消
    TIMEOUT: 'timeout'           // 超时
};

/**
 * 运程等级定义
 */
export const 运程等级 = {
    EXCELLENT: { level: 5, name: '极佳', color: '#ff6b6b', description: '运势极佳，诸事顺利' },
    GOOD: { level: 4, name: '良好', color: '#4ecdc4', description: '运势良好，多有收获' },
    AVERAGE: { level: 3, name: '一般', color: '#45b7d1', description: '运势平稳，需要努力' },
    POOR: { level: 2, name: '较差', color: '#f9ca24', description: '运势较差，需要谨慎' },
    BAD: { level: 1, name: '不佳', color: '#6c5ce7', description: '运势不佳，宜静不宜动' }
};

export default {
    运程测试工作流配置,
    运程测试参数验证规则,
    运程测试错误码,
    运程测试状态,
    运程等级
};
