<template>
  <view class="touchable-textarea-container" :class="platformClass">
    <textarea
      ref="textareaRef"
      class="touchable-textarea"
      :value="value"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
      :auto-height="autoHeight"
      :fixed="fixed"
      :focus="focus"
      :cursor-spacing="cursorSpacing"
      :selection-start="selectionStart"
      :selection-end="selectionEnd"
      :show-confirm-bar="showConfirmBar"
      :confirm-type="confirmType"
      :confirm-hold="confirmHold"
      :hold-keyboard="holdKeyboard"
      :cursor="cursor"
      :adjust-position="adjustPosition"
      :show-count="showCount"
      :auto-blur="autoBlur"
      :disable-default-padding="disableDefaultPadding"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @keyboardheightchange="handleKeyboardHeightChange"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    ></textarea>
  </view>
</template>

<script>
import { platformAdapter } from '../utils/platform.js';

export default {
  name: 'TouchableTextarea',
  props: {
    value: { type: String, default: '' },
    placeholder: { type: String, default: '请输入内容' },
    maxlength: { type: Number, default: -1 },
    disabled: { type: Boolean, default: false },
    autoHeight: { type: Boolean, default: true },
    fixed: { type: Boolean, default: false },
    focus: { type: Boolean, default: false },
    cursorSpacing: { type: Number, default: 0 },
    selectionStart: { type: Number, default: -1 },
    selectionEnd: { type: Number, default: -1 },
    showConfirmBar: { type: Boolean, default: true },
    confirmType: { type: String, default: 'done' },
    confirmHold: { type: Boolean, default: false },
    holdKeyboard: { type: Boolean, default: false },
    cursor: { type: Number, default: -1 },
    adjustPosition: { type: Boolean, default: true },
    showCount: { type: Boolean, default: true },
    autoBlur: { type: Boolean, default: false },
    disableDefaultPadding: { type: Boolean, default: false }
  },
  data() {
    return {
      platformClass: '',
      isTouching: false,
      lastTouchY: 0,
      scrollSpeed: 0,
      scrollAnimation: null,
      touchStartTime: 0,
      initialScrollTop: 0,
      isScrolling: false,
      internalValue: this.value
    };
  },
  created() {
    // 检测平台
    const platform = platformAdapter.getPlatformType();
    this.platformClass = platform;
    
    // 初始化平台特定配置
    this.initPlatformConfig();
  },
  methods: {
    initPlatformConfig() {
      // 平台特定配置初始化
      if (platformAdapter.isWechatMiniProgram()) {
        // 微信小程序特定配置
        this.enableTouchScroll = true;
      } else if (platformAdapter.isAlipayMiniProgram()) {
        // 支付宝小程序特定配置
        this.enableTouchScroll = true;
      } else if (platformAdapter.isApp()) {
        // APP特定配置
        this.enableTouchScroll = true;
      } else if (platformAdapter.isMobileH5()) {
        // 移动端H5特定配置
        this.enableTouchScroll = true;
      } else if (platformAdapter.isPC()) {
        // PC特定配置 - PC通常有鼠标滚轮，不需要触摸滑动
        this.enableTouchScroll = false;
      }
    },
    
    // 处理输入事件
    handleInput(e) {
      const value = e.detail.value;
      this.internalValue = value;
      this.$emit('input', value);
    },
    
    // 处理焦点事件
    handleFocus(e) {
      this.$emit('focus', e);
    },
    
    // 处理失焦事件
    handleBlur(e) {
      this.$emit('blur', e);
    },
    
    // 处理确认事件
    handleConfirm(e) {
      this.$emit('confirm', e);
    },
    
    // 处理键盘高度变化事件
    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e);
    },
    
    // 触摸开始事件
    handleTouchStart(e) {
      if (!this.enableTouchScroll) return;
      
      this.isTouching = true;
      this.lastTouchY = e.touches[0].clientY;
      this.touchStartTime = Date.now();
      this.scrollSpeed = 0;
      
      // 获取当前滚动位置
      this.initialScrollTop = this.getScrollTop();
      
      // 停止动画
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
        this.scrollAnimation = null;
      }
      
      this.$emit('touchstart', e);
    },
    
    // 触摸移动事件
    handleTouchMove(e) {
      if (!this.enableTouchScroll || !this.isTouching) return;
      
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - this.lastTouchY;
      
      // 如果是垂直滚动
      if (Math.abs(deltaY) > 5) {
        // 阻止默认行为，防止页面滚动
        e.preventDefault && e.preventDefault();
        
        // 计算滚动速度
        const now = Date.now();
        const elapsed = now - this.touchStartTime;
        if (elapsed > 0) {
          this.scrollSpeed = deltaY / elapsed * 15; // 调整系数，控制惯性滚动速度
        }
        
        // 滚动文本区域
        this.scrollTextarea(deltaY);
        
        // 记录本次触摸位置和时间
        this.lastTouchY = currentY;
        this.touchStartTime = now;
        this.isScrolling = true;
      }
      
      this.$emit('touchmove', e);
    },
    
    // 触摸结束事件
    handleTouchEnd(e) {
      if (!this.enableTouchScroll) return;
      
      this.isTouching = false;
      
      // 启动惯性滚动
      if (this.isScrolling && Math.abs(this.scrollSpeed) > 0.5) {
        this.startInertialScroll();
      }
      
      this.isScrolling = false;
      this.$emit('touchend', e);
    },
    
    // 滚动文本区域
    scrollTextarea(deltaY) {
      // 获取当前滚动位置
      const scrollTop = this.getScrollTop();
      
      // 设置新的滚动位置
      this.setScrollTop(scrollTop - deltaY);
    },
    
    // 获取滚动位置
    getScrollTop() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return 0;
      
      // 不同平台可能有不同的API
      if (platformAdapter.isH5()) {
        return textarea.$el ? textarea.$el.scrollTop : 0;
      } else {
        // 小程序和APP
        return textarea.scrollTop || 0;
      }
    },
    
    // 设置滚动位置
    setScrollTop(value) {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;
      
      // 不同平台可能有不同的API
      if (platformAdapter.isH5()) {
        if (textarea.$el) {
          textarea.$el.scrollTop = value;
        }
      } else {
        // 小程序和APP
        if (typeof textarea.scrollTo === 'function') {
          textarea.scrollTo({ top: value });
        } else {
          textarea.scrollTop = value;
        }
      }
    },
    
    // 启动惯性滚动
    startInertialScroll() {
      let speed = this.scrollSpeed;
      
      const inertialScroll = () => {
        if (Math.abs(speed) < 0.1) {
          cancelAnimationFrame(this.scrollAnimation);
          this.scrollAnimation = null;
          return;
        }
        
        // 获取当前滚动位置
        const scrollTop = this.getScrollTop();
        
        // 设置新的滚动位置
        this.setScrollTop(scrollTop - speed);
        
        // 减少速度（模拟摩擦）
        speed *= 0.95;
        
        // 继续动画
        this.scrollAnimation = requestAnimationFrame(inertialScroll);
      };
      
      // 启动动画
      this.scrollAnimation = requestAnimationFrame(inertialScroll);
    }
  }
};
</script>

<style>
.touchable-textarea-container {
  width: 100%;
  position: relative;
}

.touchable-textarea {
  width: 100%;
  box-sizing: border-box;
  line-height: 1.5;
  font-size: 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 在iOS上流畅滚动 */
  border-radius: 8px; /* 方形带倒角 */
  border: 1px solid #dcdfe6; /* 边框 */
  padding: 10px; /* 内边距 */
}

/* 在不同平台上的特定样式 */
.mp-weixin .touchable-textarea,
.mp-alipay .touchable-textarea {
  /* 小程序样式 */
  border-radius: 8px;
}

.app .touchable-textarea {
  /* APP样式 */
  border-radius: 8px;
}

.mobile-h5 .touchable-textarea {
  /* 移动端H5样式 */
  border-radius: 8px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c0c4cc #f4f4f5; /* Firefox */
}

.mobile-h5 .touchable-textarea::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.mobile-h5 .touchable-textarea::-webkit-scrollbar-track {
  background: #f4f4f5; /* 滚动条轨道背景色 */
  border-radius: 4px; /* 轨道倒角 */
}

.mobile-h5 .touchable-textarea::-webkit-scrollbar-thumb {
  background: #c0c4cc; /* 滚动条滑块颜色 */
  border-radius: 4px; /* 滑块倒角 */
}

.pc .touchable-textarea {
  /* PC端样式 */
  border-radius: 8px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c0c4cc #f4f4f5; /* Firefox */
}

.pc .touchable-textarea::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

.pc .touchable-textarea::-webkit-scrollbar-track {
  background: #f4f4f5; /* 滚动条轨道背景色 */
  border-radius: 4px; /* 轨道倒角 */
}

.pc .touchable-textarea::-webkit-scrollbar-thumb {
  background: #c0c4cc; /* 滚动条滑块颜色 */
  border-radius: 4px; /* 滑块倒角 */
}
</style> 