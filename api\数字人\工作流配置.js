/**
 * 数字人功能工作流配置
 * 定义数字人功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 数字人工作流配置
 */
export const 数字人工作流配置 = {
    // 工作流基础信息
    workflowId: 'digital_human_workflow_001',
    workflowType: 'digital_human',
    moduleName: '数字人',
    
    // 工作流描述
    description: '数字人生成和交互工作流',

    // 结构化参数定义
    structuredParams: {
        
        avatarType: {
            type: 'text',
            required: true,
            placeholder: '{{avatarType}}',
            description: 'avatarType'
        },
        
        voiceType: {
            type: 'text',
            required: true,
            placeholder: '{{voiceType}}',
            description: 'voiceType'
        },
        
        textInput: {
            type: 'text',
            required: true,
            placeholder: '{{textInput}}',
            description: 'textInput'
        },
        
        emotion: {
            type: 'text',
            required: true,
            placeholder: '{{emotion}}',
            description: 'emotion'
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下参数执行数字人任务：

- avatarType：{{avatarType}}
- voiceType：{{voiceType}}
- textInput：{{textInput}}
- emotion：{{emotion}}

请提供详细的处理结果。
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: 'object'
        }
    },

    // 费用配置
    pricing: {
        basePrice: 30,
        memberDiscount: 0.8
    },

    // 执行配置
    execution: {
        timeout: 300000,
        maxRetries: 3,
        pollInterval: 2000,
        enableCache: true
    }
};

/**
 * 数字人参数验证规则
 */
export const 数字人参数验证规则 = {
    required: ['avatarType', 'voiceType', 'textInput', 'emotion'],
    formats: {},
    ranges: {},
    enums: {}
};

/**
 * 数字人错误码定义
 */
export const 数字人错误码 = {
    INVALID_PARAMS: { code: 'DIGITAL_HUMAN_001', message: '参数格式不正确' },
    INSUFFICIENT_COINS: { code: 'DIGITAL_HUMAN_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'DIGITAL_HUMAN_008', message: '工作流执行超时' },
    WORKFLOW_FAILED: { code: 'DIGITAL_HUMAN_009', message: '工作流执行失败' },
    UNKNOWN_ERROR: { code: 'DIGITAL_HUMAN_999', message: '未知错误' }
};

/**
 * 数字人状态定义
 */
export const 数字人状态 = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

export default {
    数字人工作流配置,
    数字人参数验证规则,
    数字人错误码,
    数字人状态
};