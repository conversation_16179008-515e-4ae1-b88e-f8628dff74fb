# 音乐发行模块

## 模块概述

音乐发行模块是云创AI平台的核心功能之一，为用户提供完整的音乐发行服务，包括多平台发行、独家发行、定制服务等功能。

## 功能特性

### 🎵 多平台发行
- 支持酷狗、酷我、QQ音乐、网易云、汽水音乐等主流平台
- 一键提交，多平台同步发行
- 实时价格更新和配置管理

### 🎯 独家发行
- 支持单平台独家发行
- 独家标识和推荐位
- 更优惠的分成比例

### 🛠️ 深度原创定制服务
- 提高作者原创度
- 获取更多收益
- 专业客服指导
- 微信一对一服务

### 📊 数据统计
- 发行状态实时跟踪
- 收益统计分析
- 平台分布数据

## 文件结构

```
api/音乐发行/
├── README.md                 # 模块说明文档
├── 音乐发行接口.js           # 主要API接口定义
└── docs/                     # 详细文档目录
    └── API文档.md           # API接口详细文档
```

## 核心接口

### 平台管理
- `获取发行平台配置()` - 获取可用平台列表
- `获取平台价格()` - 获取实时价格信息
- `更新平台价格(config)` - 更新价格配置

### 发行管理
- `提交发行申请(data)` - 提交发行申请
- `获取发行状态(id)` - 查询发行状态
- `获取发行历史(params)` - 获取历史记录

### 定制服务
- `获取定制服务配置()` - 获取定制服务信息
- `获取定制微信()` - 获取客服微信
- `提交定制服务申请(data)` - 申请定制服务

## 使用方式

### 1. 导入模块

```javascript
import 音乐发行API from '@/api/音乐发行/音乐发行接口.js';
```

### 2. 调用接口

```javascript
// 获取平台价格
const prices = await 音乐发行API.获取平台价格();

// 提交发行申请
const result = await 音乐发行API.提交发行申请({
  workId: 'work123',
  publishType: 'multi',
  contactInfo: {
    name: '张三',
    phone: '13800138000'
  }
});
```

## 页面集成

### 音乐发行表单页面
- **路径**: `pages/music/publish-form/index.vue`
- **功能**: 音乐发行申请表单
- **集成接口**:
  - 平台价格加载
  - 定制服务配置
  - 发行申请提交

### 主要修改内容

1. **文字优化**
   - 将"提高作品的原创度,获取更多的收益"修改为"提高作者原创度，获取更多收益"
   - 使用中文逗号替代英文逗号

2. **API集成**
   - 集成真实的平台价格API
   - 集成定制微信获取API
   - 集成发行申请提交API

3. **功能完善**
   - 添加错误处理机制
   - 添加默认配置fallback
   - 优化用户体验

## 后端接口要求

### 必需实现的接口

1. **GET /api/music/publish/platform-prices**
   - 返回各平台的实时价格
   - 格式: `{ "kugou": 59.9, "kuwo": 49.9, ... }`

2. **GET /api/music/publish/custom-wechat**
   - 返回定制服务微信号
   - 格式: `{ "wechatId": "YunChuangAI-Service" }`

3. **POST /api/music/publish/submit**
   - 接收发行申请数据
   - 返回发行ID和状态

4. **GET /api/music/publish/custom-service-config**
   - 返回定制服务配置
   - 控制是否显示定制服务模块

## 数据流程

```
用户填写表单 → 调用价格API → 显示实时价格 → 用户提交 → 调用发行API → 跳转支付页面
```

## 错误处理

- API调用失败时使用默认配置
- 网络错误时显示友好提示
- 表单验证确保数据完整性

## 扩展性

模块设计支持以下扩展：
- 新增发行平台
- 自定义价格策略
- 多语言支持
- 批量发行功能

## 维护说明

1. **价格更新**: 通过管理后台更新平台价格
2. **微信更新**: 通过API更新定制服务微信号
3. **平台管理**: 可动态启用/禁用发行平台
4. **监控统计**: 实时监控发行状态和数据

## 注意事项

- 所有价格以人民币为单位
- 微信号需要定期验证有效性
- 发行状态需要与平台方同步
- 用户隐私信息需要加密存储
