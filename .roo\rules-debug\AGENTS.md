# Project Debug Rules (Non-Obvious Only)

## Hidden Debug Tools
- Emergency recovery: Long-press Escape key (2+ seconds) in H5 triggers `forceRecoverPage()`
- Route hijacking detection runs every 3 seconds - check console for "检测到XXX方法被劫持"
- Global error handlers in `App.vue` catch scrollLeft errors and auto-apply fixes
- Scroll diagnostics: Call `diagnoseScrolling()` from `utils/simple-scroll-fix.js` in console

## Critical Debug Flags
- `window.APP_INITIALIZED` - prevents duplicate app initialization
- `window.SCROLL_FIX_INITIALIZED` - prevents duplicate scroll fix application  
- `window.PLATFORM_ADAPTER_INITIALIZED` - prevents duplicate platform adapter setup
- `window.__processingHashChange` - prevents hash change event loops

## Non-Standard Log Locations
- Platform adapter logs only show once (prevented by `window.PLATFORM_ADAPTER_LOG_SHOWN`)
- Router enhancement logs prefixed with "拦截到" (intercepted) for navigation tracking
- Scroll fix logs prefixed with "🔧" for scroll-related operations
- Touch directive logs prefixed with "🚫" (disabled functionality)

## Testing Debug Gotchas
- Playwright tests expect dev server on port 8080 but `npm run dev:h5` starts on 5173
- E2E tests require exactly 20 test items for scroll functionality validation
- Test selectors: `.models-scroll`, `.chat-page`, `.text-create-container` are critical DOM markers
- Browser console errors about "scrollLeft" are expected and auto-fixed

## H5-Specific Debug Issues
- Hash navigation may not trigger page updates - check `checkAndFixRouteState()` calls
- Page containers must exist for route validation (see `isDOMPageLoaded()` function)
- Manual hash changes need `window.__manualHashChange = true` to prevent auto-correction
- Route state checking looks for specific containers per page type (see `pageIdentifiers` object)

## Emergency Recovery Commands
```javascript
// Force page recovery
window.forceRecoverPage()

// Check route state
window.checkAndFixRouteState()

// Diagnose scroll issues  
window.diagnoseScrolling()

// Reset all scroll states
window.resetAllScrolling()