<template>
  <view class="text-to-image-form">
    <view class="form-section">
      <text class="section-title">提示词</text>
      <view class="prompt-wrapper">
        <textarea
          class="prompt-input"
          v-model="prompt"
          placeholder="请描述您想要生成的图像内容，例如：一只金色的猫咪在海边奔跑，阳光照耀着海面"
          :maxlength="1000"
          @input="handlePromptChange"
        ></textarea>
        <view class="text-counter">{{ prompt.length }}/1000</view>
      </view>
      

    </view>

    <view class="form-section">
      <view class="section-header">
        <text class="section-title">反向提示词</text>
        <text class="section-subtitle">描述您不希望在图像中出现的内容</text>
      </view>
      <textarea
        class="negative-prompt-input"
        v-model="negativePrompt"
        placeholder="例如：模糊、低质量、扭曲的脸、不自然的姿势"
        :maxlength="200"
      ></textarea>
      <view class="text-counter">{{ negativePrompt.length }}/200</view>
    </view>



    <view class="form-section">
      <text class="section-title">图像参数</text>
      <view class="parameters-container">
        <!-- 图像比例 -->
        <view class="parameter-item full-width">
          <text class="parameter-label">比例</text>
          <view class="ratio-options">
            <view 
              v-for="(ratio, index) in imageRatios" 
              :key="index"
              class="ratio-btn" 
              :class="{ active: selectedRatio === ratio.value }"
              @tap="selectRatio(ratio.value)"
            >
              <view class="ratio-preview" :style="ratio.style"></view>
              <text>{{ ratio.label }}</text>
            </view>
          </view>
        </view>
        

      </view>
    </view>


  </view>
</template>

<script>
export default {
  name: 'TextToImageForm',
  data() {
    return {
      prompt: '',
      negativePrompt: '',
      selectedRatio: '16:9',
      estimatedCost: 50,

      imageRatios: [
        {
          label: '横屏16:9',
          value: '16:9',
          style: { width: '60rpx', height: '33.75rpx', margin: '13.125rpx auto' }
        },
        {
          label: '正方形1:1',
          value: '1:1',
          style: { width: '60rpx', height: '60rpx', margin: '0 auto' }
        },
        {
          label: '竖屏9:16',
          value: '9:16',
          style: { width: '33.75rpx', height: '60rpx', margin: '0 auto' }
        },
        {
          label: '传统4:3',
          value: '4:3',
          style: { width: '60rpx', height: '45rpx', margin: '7.5rpx auto' }
        }
      ]
    }
  },
  mounted() {
    this.calculateEstimatedCost();
  },
  methods: {
    handlePromptChange() {
      this.calculateEstimatedCost();
    },
    

    
    selectRatio(ratio) {
      this.selectedRatio = ratio;
      this.calculateEstimatedCost();
    },
    
    calculateEstimatedCost() {
      // 基础成本
      let baseCost = 50;

      // 根据比例调整成本（只保留4种常见比例）
      const ratioMultiplier = {
        '16:9': 1.0,     // 1152x896 - 默认比例，基础价格
        '1:1': 1.0,      // 1024x1024 - 常用比例，基础价格
        '9:16': 1.1,     // 896x1152 - 竖屏比例，略高
        '4:3': 1.1       // 1152x896 - 传统比例，略高
      };

      // 应用比例倍数
      if (ratioMultiplier[this.selectedRatio]) {
        baseCost *= ratioMultiplier[this.selectedRatio];
      }

      // 设置估算成本
      this.estimatedCost = Math.round(baseCost);
    },
    
    generateImage() {
      if (!this.prompt.trim()) {
        uni.showToast({
          title: '请先输入提示词',
          icon: 'none'
        });
        return;
      }
      
      // 准备生成参数
      const params = {
        prompt: this.prompt,
        negativePrompt: this.negativePrompt,
        ratio: this.selectedRatio,
        cost: this.estimatedCost
      };
      
      // 触发生成事件
      this.$emit('generate', params);
    },

  }
}
</script>

<style lang="scss" scoped>
.text-to-image-form {
  width: 100%;
  padding: 0 20rpx;
  position: relative;
  
  .dropdown-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
  }
  
  .form-section {
    margin-bottom: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #e6e6e6;
      margin-bottom: 16rpx;
    }
    
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;
      
      .section-subtitle {
        font-size: 24rpx;
        color: #a0a0a0;
      }
    }
  }
  
  .prompt-wrapper {
    position: relative;
    margin-bottom: 16rpx;
    
    .prompt-input {
      width: 100%;
      height: 180rpx;
      background-color: rgba(30, 55, 153, 0.2);
      border: 2rpx solid rgba(74, 105, 189, 0.5);
      border-radius: 12rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #ffffff;
      box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }
    
    .text-counter {
      position: absolute;
      bottom: 10rpx;
      right: 20rpx;
      font-size: 24rpx;
      color: #a0a0a0;
    }
  }
  
  .negative-prompt-input {
    width: 100%;
    height: 120rpx;
    background-color: rgba(30, 55, 153, 0.2);
    border: 2rpx solid rgba(74, 105, 189, 0.5);
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 28rpx;
    color: #ffffff;
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    margin-bottom: 10rpx;
  }
  
  .text-counter {
    font-size: 24rpx;
    color: #a0a0a0;
    text-align: right;
  }
  

  

  
  .parameters-container {
    width: 100%;
    margin-top: 20rpx;
    
    .parameter-item {
      margin-bottom: 20rpx;
      
      &.full-width {
        width: 100%;
      }
      
      &.half-width {
        width: 48%;
      }
      
      .parameter-label {
        font-size: 28rpx;
        color: #e6e6e6;
        margin-bottom: 16rpx;
      }
    }
    
    .horizontal-params {
      display: flex;
      justify-content: space-between;
    }
    
    .ratio-options {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 5rpx 0;
      width: 100%;
      
      .ratio-btn {
        width: 18%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10rpx 0;
        margin-bottom: 10rpx;
        border-radius: 8rpx;
        background-color: rgba(30, 55, 153, 0.3);
        border: 1rpx solid rgba(74, 105, 189, 0.5);
        transition: all 0.3s;
        
        .ratio-preview {
          width: 60rpx;
          height: 60rpx;
          background-color: rgba(74, 105, 189, 0.6);
          border-radius: 4rpx;
          position: relative;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
        }
        
        text {
          font-size: 22rpx;
          color: #cccccc;
          margin-top: 8rpx;
          text-align: center;
          width: 100%;
        }
        
        &.active {
          background: linear-gradient(180deg, rgba(10, 189, 227, 0.6) 0%, rgba(30, 55, 153, 0.6) 100%);
          border-color: rgba(10, 189, 227, 0.8);
          box-shadow: 0 0 10rpx rgba(10, 189, 227, 0.4);
          
          .ratio-preview {
            background-color: rgba(10, 189, 227, 0.6);
          }
          
          text {
            color: #ffffff;
            font-weight: 600;
          }
        }
      }
    }

  }
}
</style> 