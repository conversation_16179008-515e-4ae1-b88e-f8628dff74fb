<template>
  <view class="scroll-controller">
    <!-- 智能跟随滚动 - 回到底部按钮 -->
    <view
      class="scroll-to-bottom-btn"
      v-if="showScrollToBottom"
      @tap="scrollToBottom"
      :class="{'slide-in': showScrollToBottom}"
    >
      <view class="scroll-btn-icon">↓</view>
      <view class="scroll-btn-text">回到底部</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ScrollController',
  props: {
    showScrollToBottom: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    scrollToBottom() {
      this.$emit('scroll-to-bottom');
    }
  }
}
</script>

<style scoped>
/* 智能跟随滚动 - 回到底部按钮样式 */
.scroll-to-bottom-btn {
  position: fixed;
  right: 20px;
  bottom: 180px;
  width: 80px;
  height: 36px;
  background: linear-gradient(135deg, rgba(77, 157, 255, 0.9), rgba(110, 86, 207, 0.9));
  border-radius: 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(20px) scale(0.8);
  pointer-events: none;
}

.scroll-to-bottom-btn.slide-in {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-to-bottom-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, rgba(110, 86, 207, 0.95), rgba(77, 157, 255, 0.95));
}

.scroll-btn-icon {
  font-size: 16px;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 2px;
  animation: bounce 2s infinite;
}

.scroll-btn-text {
  font-size: 10px;
  color: #ffffff;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
}

/* 滑入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  80% {
    transform: translateY(-2px);
  }
}
</style>
