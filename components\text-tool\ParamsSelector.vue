<template>
  <view class="params-selector">
    <view class="params-container">
      <!-- 数量滑动条 -->
      <view class="param-row quantity-row">
        <view class="param-label">数量</view>
        <view class="param-control">
          <view class="quantity-slider-container">
            <slider
              class="quantity-slider"
              :value="quantity"
              :min="1"
              :max="maxQuantity"
              :show-value="true"
              @change="onQuantityChange"
            />
            <text class="quantity-value">{{ quantity }}</text>
          </view>
        </view>
      </view>
      
      <!-- 动态参数配置 -->
      <view 
        class="param-row" 
        v-for="(param, index) in params" 
        :key="index"
      >
        <view class="param-label" :title="param.label">
          {{ param.label }}
        </view>
        <view class="param-control">
          <!-- 文本输入和预设选项 -->
          <view v-if="param.type === 'style' || param.presets" class="param-style">
            <!-- 输入框 -->
            <view class="style-input-wrapper">
              <input 
                type="text" 
                :placeholder="param.placeholder || `请输入或选择${param.label}`"
                v-model="paramValues[param.key]"
                @input="(e) => onParamInput(param.key, e.detail.value)"
                class="style-input"
                confirm-type="done"
              />
              <view class="input-clear-btn" v-if="paramValues[param.key]" @tap="clearParamValue(param.key)">
                <text class="clear-icon">×</text>
              </view>
            </view>
            
            <!-- 水平滚动的预设选项 -->
            <scroll-view 
              scroll-x 
              class="style-presets" 
              show-scrollbar="true"
              enhanced="true"
              :style="{
                overflowX: 'auto',
                whiteSpace: 'nowrap',
                width: '100%',
                scrollBehavior: 'smooth',
                WebkitOverflowScrolling: 'touch'
              }"
            >
              <view class="presets-wrapper">
                <view 
                  v-for="(preset, presetIndex) in param.presets" 
                  :key="presetIndex"
                  :id="`${param.key}_preset_${presetIndex}`"
                  class="preset-item"
                  :class="{ 'active': paramValues[param.key] === preset.value || paramValues[param.key] === preset.label }"
                  @tap="selectPreset(param.key, preset.value || preset.label)"
                >
                  {{ preset.label }}
                </view>
              </view>
            </scroll-view>
          </view>
          
          <!-- 标签选择 -->
          <view v-else-if="param.type === 'tags'" class="param-tags">
            <scroll-view 
              scroll-x 
              class="tags-scroll"
              enhanced="true"
              :style="{
                overflowX: 'auto',
                whiteSpace: 'nowrap',
                width: '100%',
                WebkitOverflowScrolling: 'touch',
                scrollBehavior: 'smooth'
              }"
            >
              <view 
                v-for="(tag, tagIndex) in param.options" 
                :key="tagIndex"
                :id="`${param.key}_tag_${tagIndex}`"
                class="tag-item" 
                :class="{ 'active': isTagSelected(param.key, tag.value || tag.label) }"
                @tap="toggleTag(param.key, tag.value || tag.label)"
              >
                {{ tag.label }}
              </view>
            </scroll-view>
          </view>
          
          <!-- 其他类型输入 -->
          <view v-else class="param-other">
            <input 
              type="text" 
              :placeholder="param.placeholder || `请输入${param.label}`"
              v-model="paramValues[param.key]"
              @input="(e) => onParamInput(param.key, e.detail.value)"
              class="other-input"
              confirm-type="done"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ParamsSelector',
  props: {
    params: {
      type: Array,
      default: () => []
    },
    initialValues: {
      type: Object,
      default: () => ({})
    },
    maxQuantity: {
      type: Number,
      default: 1
    },
    initialQuantity: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      paramValues: {},
      quantity: this.initialQuantity,
      selectedTags: {}
    };
  },
  created() {
    // 初始化参数值
    this.initParams();
  },
  watch: {
    initialValues: {
      handler(newVal) {
        this.initParams();
      },
      deep: true
    }
  },
  methods: {
    initParams() {
      // 初始化参数值
      const values = { ...this.initialValues };
      
      // 初始化空值
      this.params.forEach(param => {
        if (!values[param.key] && param.defaultValue) {
          values[param.key] = param.defaultValue;
        }
      });
      
      this.paramValues = values;
      
      // 初始化标签选择
      const tags = {};
      this.params.forEach(param => {
        if (param.type === 'tags' && values[param.key]) {
          tags[param.key] = Array.isArray(values[param.key]) 
            ? values[param.key] 
            : [values[param.key]];
        }
      });
      
      this.selectedTags = tags;
    },
    
    // 参数输入变化
    onParamInput(key, value) {
      this.paramValues[key] = value;
      this.$emit('param-change', { key, value });
    },
    
    // 清除参数值
    clearParamValue(key) {
      this.paramValues[key] = '';
      this.$emit('param-change', { key, value: '' });
    },
    
    // 选择预设
    selectPreset(key, value) {
      this.paramValues[key] = value;
      this.$emit('param-change', { key, value });
    },
    
    // 切换标签
    toggleTag(key, value) {
      if (!this.selectedTags[key]) {
        this.selectedTags[key] = [];
      }
      
      const index = this.selectedTags[key].indexOf(value);
      
      if (index > -1) {
        // 移除标签
        this.selectedTags[key].splice(index, 1);
      } else {
        // 添加标签
        this.selectedTags[key].push(value);
      }
      
      // 更新参数值
      this.paramValues[key] = this.selectedTags[key].join(',');
      this.$emit('tag-change', { key, tags: this.selectedTags[key] });
    },
    
    // 检查标签是否被选中
    isTagSelected(key, value) {
      return this.selectedTags[key] && this.selectedTags[key].includes(value);
    },
    
    // 数量变化
    onQuantityChange(e) {
      this.quantity = e.detail.value;
      this.$emit('quantity-change', this.quantity);
    },
    
    // 获取所有参数值
    getAllParams() {
      return {
        ...this.paramValues,
        quantity: this.quantity
      };
    },
    
    // 重置所有参数
    resetParams() {
      this.initParams();
      this.quantity = this.initialQuantity;
    }
  }
};
</script>

<style>
.params-selector {
  width: 100%;
  padding: 10rpx;
}

.params-container {
  width: 100%;
}

.param-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  width: 100%;
}

.param-label {
  font-size: 28rpx;
  min-width: 100rpx;
  padding-right: 20rpx;
  color: #666;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.param-control {
  flex: 1;
  width: 100%;
}

/* 数量滑动条 */
.quantity-slider-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.quantity-slider {
  flex: 1;
  margin: 0 10rpx;
}

.quantity-value {
  font-size: 26rpx;
  color: #666;
  min-width: 40rpx;
  text-align: center;
}

/* 样式输入 */
.style-input-wrapper {
  position: relative;
  width: 100%;
  margin-bottom: 10rpx;
}

.style-input {
  width: 100%;
  height: 60rpx;
  border-radius: 6rpx;
  background-color: #f5f5f7;
  padding: 0 60rpx 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.input-clear-btn {
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}

.clear-icon {
  font-size: 28rpx;
}

/* 预设选项 */
.style-presets {
  width: 100%;
  white-space: nowrap;
}

.presets-wrapper {
  display: inline-flex;
  flex-wrap: nowrap;
  padding: 5rpx 0;
}

.preset-item {
  display: inline-block;
  padding: 8rpx 20rpx;
  margin-right: 15rpx;
  background: rgba(70, 70, 90, 0.3);
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  min-width: 80rpx;
}

.preset-item.active {
  background: rgba(110, 86, 207, 0.8);
  color: #fff;
}

/* 标签选择 */
.tags-scroll {
  width: 100%;
  white-space: nowrap;
}

.tag-item {
  display: inline-block;
  padding: 8rpx 20rpx;
  margin-right: 15rpx;
  background: rgba(70, 70, 90, 0.3);
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #fff;
  min-width: 80rpx;
  text-align: center;
}

.tag-item.active {
  background: rgba(110, 86, 207, 0.8);
  color: #fff;
}

/* 其他输入 */
.other-input {
  width: 100%;
  height: 60rpx;
  border-radius: 6rpx;
  background-color: #f5f5f7;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* iOS 优化 */
@supports (-webkit-overflow-scrolling: touch) {
  .style-presets, .tags-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
  }
  
  .preset-item, .tag-item {
    scroll-snap-align: start;
  }
}
</style> 