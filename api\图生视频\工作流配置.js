/**
 * 图生视频功能工作流配置
 * 定义图生视频功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 图生视频工作流配置
 */
export const 图生视频工作流配置 = {
    // 工作流基础信息
    workflowId: 'image_to_video_workflow_001',
    workflowType: 'image_to_video',
    moduleName: '图生视频',
    
    // 工作流描述
    description: '基于图片生成视频的AI工作流',

    // 结构化参数定义
    structuredParams: {
        
        imageInput: {
            type: 'text',
            required: true,
            placeholder: '{{imageInput}}',
            description: 'imageInput'
        },
        
        duration: {
            type: 'text',
            required: true,
            placeholder: '{{duration}}',
            description: 'duration'
        },
        
        motionType: {
            type: 'text',
            required: true,
            placeholder: '{{motionType}}',
            description: 'motionType'
        },
        
        resolution: {
            type: 'text',
            required: true,
            placeholder: '{{resolution}}',
            description: 'resolution'
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下参数执行图生视频任务：

- imageInput：{{imageInput}}
- duration：{{duration}}
- motionType：{{motionType}}
- resolution：{{resolution}}

请提供详细的处理结果。
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: 'object'
        }
    },

    // 费用配置
    pricing: {
        basePrice: 40,
        memberDiscount: 0.8
    },

    // 执行配置
    execution: {
        timeout: 300000,
        maxRetries: 3,
        pollInterval: 2000,
        enableCache: true
    }
};

/**
 * 图生视频参数验证规则
 */
export const 图生视频参数验证规则 = {
    required: ['imageInput', 'duration', 'motionType', 'resolution'],
    formats: {},
    ranges: {},
    enums: {}
};

/**
 * 图生视频错误码定义
 */
export const 图生视频错误码 = {
    INVALID_PARAMS: { code: 'IMAGE_TO_VIDEO_001', message: '参数格式不正确' },
    INSUFFICIENT_COINS: { code: 'IMAGE_TO_VIDEO_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'IMAGE_TO_VIDEO_008', message: '工作流执行超时' },
    WORKFLOW_FAILED: { code: 'IMAGE_TO_VIDEO_009', message: '工作流执行失败' },
    UNKNOWN_ERROR: { code: 'IMAGE_TO_VIDEO_999', message: '未知错误' }
};

/**
 * 图生视频状态定义
 */
export const 图生视频状态 = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

export default {
    图生视频工作流配置,
    图生视频参数验证规则,
    图生视频错误码,
    图生视频状态
};