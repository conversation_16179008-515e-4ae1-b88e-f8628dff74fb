/**
 * 音乐发行接口
 * 处理音乐发行相关的所有API调用
 */

import { apiRequest } from '../common/request.js';

// ================================
// 📋 平台配置接口
// ================================

/**
 * 获取发行平台列表及价格
 */
export async function 获取发行平台配置() {
	return await apiRequest('music/publish/platforms');
}

/**
 * 获取平台实时价格
 */
export async function 获取平台价格() {
	return await apiRequest('music/publish/platform-prices');
}

/**
 * 更新平台价格配置
 * @param {Object} priceConfig 价格配置
 */
export async function 更新平台价格(priceConfig) {
	return await apiRequest('music/publish/update-prices', {
		method: 'POST',
		data: priceConfig
	});
}

// ================================
// 🎵 发行管理接口
// ================================

/**
 * 提交音乐发行申请
 * @param {Object} publishData 发行数据
 */
export async function 提交发行申请(publishData) {
	return await apiRequest('music/publish/submit', {
		method: 'POST',
		data: publishData
	});
}

/**
 * 获取发行状态
 * @param {string} publishId 发行ID
 */
export async function 获取发行状态(publishId) {
	return await apiRequest(`music/publish/status/${publishId}`);
}

/**
 * 获取用户发行历史
 * @param {Object} params 查询参数
 */
export async function 获取发行历史(params = {}) {
	return await apiRequest('music/publish/history', {
		data: params
	});
}

/**
 * 取消发行申请
 * @param {string} publishId 发行ID
 */
export async function 取消发行申请(publishId) {
	return await apiRequest(`music/publish/cancel/${publishId}`, {
		method: 'POST'
	});
}

// ================================
// 🎯 独家发行接口
// ================================

/**
 * 获取独家发行平台选项
 */
export async function 获取独家发行选项() {
	return await apiRequest('music/publish/exclusive-options');
}

/**
 * 提交独家发行申请
 * @param {Object} exclusiveData 独家发行数据
 */
export async function 提交独家发行申请(exclusiveData) {
	return await apiRequest('music/publish/exclusive-submit', {
		method: 'POST',
		data: exclusiveData
	});
}

// ================================
// 🛠️ 定制服务接口
// ================================

/**
 * 获取定制服务配置
 */
export async function 获取定制服务配置() {
	return await apiRequest('music/publish/custom-service-config');
}

/**
 * 获取定制服务微信号
 */
export async function 获取定制微信() {
	return await apiRequest('music/publish/custom-wechat');
}

/**
 * 更新定制服务微信号
 * @param {string} wechatId 微信号
 */
export async function 更新定制微信(wechatId) {
	return await apiRequest('music/publish/update-custom-wechat', {
		method: 'POST',
		data: { wechatId }
	});
}

/**
 * 提交定制服务申请
 * @param {Object} customData 定制服务数据
 */
export async function 提交定制服务申请(customData) {
	return await apiRequest('music/publish/custom-service-apply', {
		method: 'POST',
		data: customData
	});
}

// ================================
// 📊 统计分析接口
// ================================

/**
 * 获取发行统计数据
 */
export async function 获取发行统计() {
	return await apiRequest('music/publish/statistics');
}

/**
 * 获取平台发行数据统计
 * @param {Object} params 查询参数
 */
export async function 获取平台发行统计(params = {}) {
	return await apiRequest('music/publish/platform-statistics', {
		data: params
	});
}

/**
 * 获取收益统计
 * @param {Object} params 查询参数
 */
export async function 获取发行收益统计(params = {}) {
	return await apiRequest('music/publish/revenue-statistics', {
		data: params
	});
}

// ================================
// 🔧 系统配置接口
// ================================

/**
 * 获取发行系统配置
 */
export async function 获取发行系统配置() {
	return await apiRequest('music/publish/system-config');
}

/**
 * 更新发行系统配置
 * @param {Object} config 系统配置
 */
export async function 更新发行系统配置(config) {
	return await apiRequest('music/publish/update-system-config', {
		method: 'POST',
		data: config
	});
}

// ================================
// 📄 协议管理接口
// ================================

/**
 * 获取发行服务协议
 */
export async function 获取发行协议() {
	return await apiRequest('music/publish/agreement');
}

/**
 * 更新发行服务协议
 * @param {Object} agreement 协议内容
 */
export async function 更新发行协议(agreement) {
	return await apiRequest('music/publish/update-agreement', {
		method: 'POST',
		data: agreement
	});
}

// ================================
// 导出所有接口
// ================================

export default {
	// 平台配置
	获取发行平台配置,
	获取平台价格,
	更新平台价格,
	
	// 发行管理
	提交发行申请,
	获取发行状态,
	获取发行历史,
	取消发行申请,
	
	// 独家发行
	获取独家发行选项,
	提交独家发行申请,
	
	// 定制服务
	获取定制服务配置,
	获取定制微信,
	更新定制微信,
	提交定制服务申请,
	
	// 统计分析
	获取发行统计,
	获取平台发行统计,
	获取发行收益统计,
	
	// 系统配置
	获取发行系统配置,
	更新发行系统配置,
	
	// 协议管理
	获取发行协议,
	更新发行协议
};
