/**
 * 用户状态接口
 * 管理用户登录、会员、金币等状态信息
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🔐 用户状态检测接口
// ================================

/**
 * 检查用户登录状态
 */
export async function 检查用户登录状态() {
	return await apiRequest('user/login-status');
}

/**
 * 获取用户会员信息
 */
export async function 获取用户会员信息() {
	return await apiRequest('user/membership-info');
}

/**
 * 获取用户金币余额
 */
export async function 获取用户金币余额() {
	return await apiRequest('user/coin-balance');
}

/**
 * 获取用户完整状态信息
 */
export async function 获取用户完整状态() {
	try {
		const [登录状态, 会员信息, 金币余额] = await Promise.all([
			检查用户登录状态(),
			获取用户会员信息(),
			获取用户金币余额()
		]);

		return {
			success: true,
			data: {
				登录状态: 登录状态.data,
				会员信息: 会员信息.data,
				金币余额: 金币余额.data
			}
		};
	} catch (error) {
		console.error('获取用户完整状态失败:', error);
		throw error;
	}
}

/**
 * 验证用户创作权限
 * @param {string} workflowMode - 工作流模式
 * @param {Object} creationParams - 创作参数
 */
export async function 验证用户创作权限(workflowMode, creationParams) {
	try {
		// 检查登录状态
		const loginResult = await 检查用户登录状态();
		if (!loginResult.data.isLoggedIn) {
			return {
				success: false,
				errorCode: 'USER_NOT_LOGGED_IN',
				message: '请先登录'
			};
		}

		// 检查会员权限
		const memberResult = await 获取用户会员信息();
		const memberType = memberResult.data.membershipType;

		// 检查金币余额
		const coinResult = await 获取用户金币余额();
		const availableCoins = coinResult.data.availableCoins;

		// 估算创作费用（这里需要调用价格配置接口）
		const estimatedCost = await 估算创作费用(workflowMode, creationParams);

		return {
			success: true,
			data: {
				canCreate: availableCoins >= estimatedCost,
				memberType: memberType,
				availableCoins: availableCoins,
				estimatedCost: estimatedCost,
				needRecharge: availableCoins < estimatedCost
			}
		};

	} catch (error) {
		console.error('验证用户创作权限失败:', error);
		throw error;
	}
}

/**
 * 估算创作费用（临时实现，实际应该调用价格配置接口）
 * @param {string} workflowMode - 工作流模式
 * @param {Object} creationParams - 创作参数
 */
async function 估算创作费用(workflowMode, creationParams) {
	// 基础费用
	const baseCosts = {
		'simple': 20,
		'advanced': 50,
		'instrumental': 40
	};

	let cost = baseCosts[workflowMode] || 20;

	// 时长费用
	const duration = creationParams.duration || 180;
	if (duration > 180) {
		cost += Math.ceil((duration - 180) / 30) * 5;
	}

	// 质量费用
	const quality = creationParams.quality || 'standard';
	if (quality === 'high') {
		cost += 10;
	} else if (quality === 'premium') {
		cost += 20;
	}

	return cost;
}

export default {
	检查用户登录状态,
	获取用户会员信息,
	获取用户金币余额,
	获取用户完整状态,
	验证用户创作权限
};
