<template>
  <view class="text-prompt-tool">
    <!-- 参数配置区域 - 可展开收起，使用绝对定位固定在顶部 -->
    <view
      class="params-area"
      :class="{ 'params-area-expanded': isPanelExpanded }"
    >
      <view class="panel-toggle" @tap="togglePanel">
        <text class="toggle-text">{{ isPanelExpanded ? '收起参数' : '展开参数' }}</text>
        <text class="toggle-icon">{{ isPanelExpanded ? '▲' : '▼' }}</text>
      </view>
      
      <view class="params-content" v-show="isPanelExpanded">
        <ParamsSelector
          :params="config.params || []"
          :initial-values="paramValues"
          :max-quantity="config.maxQuantity || 1"
          :initial-quantity="quantity"
          @param-change="onParamChange"
          @tag-change="onTagChange"
          @quantity-change="onQuantityChange"
          ref="paramsSelector"
        />
      </view>
    </view>
    
    <!-- 消息输入区域 - 通过margin-top为参数区域留出空间 -->
    <view 
      class="message-area"
      :style="messageAreaStyle"
    >
      <MessageInput
        :value="promptText"
        :param-values="paramValues"
        :params="config.params || []"
        :max-length="config.maxLength || 500"
        :is-generating="isGenerating"
        :suggestions="config.suggestions"
        :show-suggestions="config.showSuggestions"
        :suggestion-title="config.suggestionTitle"
        :linkedWithParams="true"
        @input="onPromptInput"
        @submit="submitPrompt"
        @stop-generation="stopGeneration"
        @use-suggestion="useSuggestion"
        @height-change="onMessageHeightChange"
        ref="messageInput"
      />
    </view>
    
    <!-- 可能的状态提示区域 -->
    <view class="status-area" v-if="showStatus">
      <view class="status-item">
        <text v-if="isGenerating" class="status-text generating">AI 正在生成内容...</text>
        <text v-else-if="remainingCount !== undefined" class="status-text remaining">剩余使用次数: {{ remainingCount }}</text>
      </view>
      
      <view class="coin-cost" v-if="coinCost">
        <text class="coin-text">需要消耗 {{ coinCost }} 个AI币</text>
      </view>
    </view>
  </view>
</template>

<script>
import ParamsSelector from './ParamsSelector.vue';
import MessageInput from './MessageInput.vue';

export default {
  name: 'NewTextPromptTool',
  components: {
    ParamsSelector,
    MessageInput
  },
  props: {
    // 配置信息
    config: {
      type: Object,
      required: true
    },
    // 是否展开参数面板
    isPanelExpanded: {
      type: Boolean,
      default: true
    },
    // 是否正在生成内容
    isGenerating: {
      type: Boolean,
      default: false
    },
    // 剩余使用次数
    remainingCount: {
      type: Number,
      default: undefined
    },
    // 使用成本
    coinCost: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      promptText: '',
      paramValues: {},
      quantity: 1,
      selectedTags: {},
      showStatus: true,
      // 输入框相关
      messageInputHeight: 50, // 初始高度与MessageInput组件保持一致
      messageAreaPadding: 10
    };
  },
  computed: {
    // 是否可以提交
    canSubmit() {
      return this.promptText.trim().length > 0 && !this.isGenerating;
    },
    
    // 是否有选择的参数值
    hasSelectedValues() {
      return Object.keys(this.paramValues).filter(key => !!this.paramValues[key]).length > 0;
    },
    
    // 消息区域样式，基于参数面板展开状态调整上边距
    messageAreaStyle() {
      // 根据参数面板展开状态动态计算上边距
      const topMargin = this.isPanelExpanded ? 'auto' : '45rpx';
      
      return {
        transition: 'all 0.3s',
        minHeight: `${this.messageInputHeight + this.messageAreaPadding}rpx`,
        marginTop: topMargin
      };
    }
  },
  created() {
    // 初始化参数值
    this.initParams();
  },
  watch: {
    // 监听面板状态变化
    isPanelExpanded(newValue) {
      // 通知输入框组件参数面板状态变化
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.adjustLayoutForParams(newValue);
        }
      });
    }
  },
  methods: {
    // 处理输入框高度变化
    onMessageHeightChange(height) {
      this.messageInputHeight = height;
    },
    
    // 初始化参数值
    initParams() {
      const values = {};
      
      // 如果有参数配置，设置默认值
      if (this.config.params) {
        this.config.params.forEach(param => {
          if (param.defaultValue) {
            values[param.key] = param.defaultValue;
          } else {
            values[param.key] = '';
          }
        });
      }
      
      this.paramValues = values;
      this.quantity = 1;
    },
    
    // 参数变更
    onParamChange(event) {
      const { key, value } = event;
      this.paramValues[key] = value;
      this.$emit('param-change', { key, value });
    },
    
    // 标签变更
    onTagChange(event) {
      const { key, tags } = event;
      this.selectedTags[key] = tags;
      this.$emit('tag-change', { key, tags });
    },
    
    // 数量变更
    onQuantityChange(value) {
      this.quantity = value;
      this.$emit('quantity-change', value);
    },
    
    // 输入框内容变更
    onPromptInput(text) {
      this.promptText = text;
    },
    
    // 提交生成请求
    submitPrompt() {
      if (!this.canSubmit) return;
      
      // 整合参数
      const params = { ...this.paramValues };
      params.quantity = this.quantity;
      
      // 发送事件
      this.$emit('submit', {
        prompt: this.promptText,
        params: params
      });
    },
    
    // 停止生成
    stopGeneration() {
      this.$emit('stop-generation');
    },
    
    // 使用推荐提示词
    useSuggestion(suggestion) {
      this.promptText = suggestion.content || suggestion.title;
      this.$emit('use-suggestion', suggestion);
    },
    
    // 切换参数面板展开/收起
    togglePanel() {
      this.$emit('toggle-panel', !this.isPanelExpanded);
    },
    
    // 清空输入框
    clearPrompt() {
      this.promptText = '';
      if (this.$refs.messageInput) {
        this.$refs.messageInput.clearInput();
      }
    },
    
    // 重置所有参数
    resetAll() {
      this.clearPrompt();
      this.initParams();
      
      if (this.$refs.paramsSelector) {
        this.$refs.paramsSelector.resetParams();
      }
    },
    
    // 兼容原有组件的reset方法
    reset() {
      this.resetAll();
    }
  }
};
</script>

<style>
.text-prompt-tool {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 12rpx;
  overflow: visible; /* 允许内容溢出，避免裁剪参数区域 */
  position: relative; /* 添加相对定位 */
}

/* 参数区域样式 */
.params-area {
  width: 100%;
  background-color: rgba(250, 250, 252, 0.9);
  border-radius: 12rpx 12rpx 0 0;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 5rpx;
  z-index: 30; /* 进一步增加层级，确保参数区域在最上方 */
  position: sticky; /* 使用sticky定位 */
  top: 0; /* 固定在顶部 */
}

.params-area-expanded {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.panel-toggle {
  width: 100%;
  padding: 15rpx 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
}

.params-content {
  padding: 0 15rpx 15rpx;
}

/* 消息输入区域样式 */
.message-area {
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: visible; /* 修改为visible，允许输入框扩展 */
  position: relative; /* 添加相对定位 */
  z-index: 10; /* 设置合适的层级 */
  transition: all 0.3s; /* 添加过渡效果 */
  padding-bottom: 10rpx; /* 底部留出间距 */
  margin-top: 5rpx; /* 与参数区域保持一定间距 */
}

/* 状态区域样式 */
.status-area {
  margin-top: 20rpx; /* 增加与输入框的间距 */
  padding: 0 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 5; /* 设置较低的层级 */
  position: relative; /* 添加相对定位 */
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

.status-text.generating {
  color: rgba(110, 86, 207, 0.9);
}

.status-text.remaining {
  color: #666;
}

.coin-cost {
  font-size: 24rpx;
  color: #ff9500;
}

/* iOS 特定优化 */
@supports (-webkit-overflow-scrolling: touch) {
  .text-prompt-tool {
    /* 添加iOS特定优化样式 */
    -webkit-overflow-scrolling: touch;
  }
  
  /* iOS下输入框特殊优化 */
  .message-area {
    padding-bottom: 15rpx; /* iOS下底部多留一些空间 */
  }
}

/* 媒体查询适配小屏幕设备 */
@media screen and (max-height: 600px) {
  .params-area:not(.params-area-expanded) {
    margin-bottom: 2rpx;
  }
  
  .status-area {
    margin-top: 10rpx;
  }
}
</style> 