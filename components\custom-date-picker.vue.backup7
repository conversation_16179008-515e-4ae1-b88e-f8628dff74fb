<template>
	<view class="custom-date-picker" v-if="visible" @touchmove.stop.prevent @wheel.stop.prevent>
		<view class="picker-mask" @click="close" @touchmove.stop.prevent @wheel.stop.prevent></view>
		<view class="picker-content" @touchmove.stop.prevent @wheel.stop.prevent>
			<view class="date-selector" @touchmove.stop.prevent @wheel.stop.prevent>
				<!-- 年份选择 -->
				<view class="selector-column">
					<text class="column-title">年</text>
					<view
						class="scroll-list"
						@touchstart.stop.prevent="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop.prevent="onTouchEnd"
						@touchcancel.stop.prevent="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						@mousedown.stop.prevent="onMouseDown"
						@mousemove.stop.prevent="onMouseMove"
						@mouseup.stop.prevent="onMouseUp"
						@mouseleave.stop.prevent="onMouseUp"
						ref="yearScroll"
					>
						<view
							v-for="year in years"
							:key="year"
							:id="'year-' + year"
							class="scroll-item"
							:class="{ active: year === selectedYear }"
							@click="selectYear(year)"
						>
							{{ year }}
						</view>
					</view>
				</view>

				<!-- 月份选择 -->
				<view class="selector-column">
					<text class="column-title">月</text>
					<view
						class="scroll-list"
						@touchstart.stop.prevent="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop.prevent="onTouchEnd"
						@touchcancel.stop.prevent="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						@mousedown.stop.prevent="onMouseDown"
						@mousemove.stop.prevent="onMouseMove"
						@mouseup.stop.prevent="onMouseUp"
						@mouseleave.stop.prevent="onMouseUp"
						ref="monthScroll"
					>
						<view
							v-for="month in months"
							:key="month"
							:id="'month-' + month"
							class="scroll-item"
							:class="{ active: month === selectedMonth }"
							@click="selectMonth(month)"
						>
							{{ month }}月
						</view>
					</view>
				</view>

				<!-- 日期选择 -->
				<view class="selector-column">
					<text class="column-title">日</text>
					<view
						class="scroll-list"
						@touchstart.stop.prevent="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop.prevent="onTouchEnd"
						@touchcancel.stop.prevent="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						@mousedown.stop.prevent="onMouseDown"
						@mousemove.stop.prevent="onMouseMove"
						@mouseup.stop.prevent="onMouseUp"
						@mouseleave.stop.prevent="onMouseUp"
						ref="dayScroll"
					>
						<view
							v-for="day in days"
							:key="day"
							:id="'day-' + day"
							class="scroll-item"
							:class="{ active: day === selectedDay }"
							@click="selectDay(day)"
						>
							{{ day }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedYear: new Date().getFullYear(),
			selectedMonth: new Date().getMonth() + 1,
			selectedDay: new Date().getDate(),
			years: [],
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
			touchStartY: 0,
			mouseStartY: 0,
			scrollTop: 0,
			isScrolling: false,
			isMouseScrolling: false,
			platform: ''
		}
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			return Array.from({ length: daysInMonth }, (_, i) => i + 1);
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [year, month, day] = newVal.split('-');
					this.selectedYear = parseInt(year);
					this.selectedMonth = parseInt(month);
					this.selectedDay = parseInt(day);
				}
			},
			immediate: true
		},
		selectedMonth() {
			// 当月份改变时，检查日期是否有效
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
			}
		}
	},
	created() {
		this.initYears();
		this.detectPlatform();
	},
	methods: {
		detectPlatform() {
			// #ifdef H5
			this.platform = 'h5';
			// #endif
			// #ifdef MP-WEIXIN
			this.platform = 'weixin';
			// #endif
			// #ifdef APP-PLUS
			this.platform = 'app';
			// #endif

			// 通过userAgent检测
			if (typeof navigator !== 'undefined') {
				const ua = navigator.userAgent;
				if (/iPhone|iPad|iPod/i.test(ua)) {
					this.platform = 'ios';
				} else if (/Android/i.test(ua)) {
					this.platform = 'android';
				}
			}
		},
		initYears() {
			const currentYear = new Date().getFullYear();
			for (let i = 1900; i <= currentYear; i++) {
				this.years.push(i);
			}
		},
		selectYear(year) {
			this.selectedYear = year;
			this.autoConfirm();
		},
		selectMonth(month) {
			this.selectedMonth = month;
			this.autoConfirm();
		},
		selectDay(day) {
			this.selectedDay = day;
			this.autoConfirm();
		},
		autoConfirm() {
			// 延迟一点时间让用户看到选择效果
			setTimeout(() => {
				this.confirm();
			}, 300);
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const year = this.selectedYear;
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateStr = `${year}-${month}-${day}`;
			this.$emit('confirm', dateStr);
		},
		onTouchStart(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			// 获取触摸点位置
			const touch = e.touches && e.touches[0] || e.changedTouches && e.changedTouches[0] || e;
			this.touchStartY = touch.clientY || touch.pageY;
			this.isScrolling = true;
		},
		onTouchMove(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			if (!this.isScrolling) return;

			// 获取触摸点位置
			const touch = e.touches && e.touches[0] || e.changedTouches && e.changedTouches[0] || e;
			const currentY = touch.clientY || touch.pageY;
			const deltaY = currentY - this.touchStartY;

			const target = e.currentTarget;
			// 根据平台调整滑动灵敏度
			let sensitivity = 1.0;
			if (this.platform === 'ios' || this.platform === 'h5') {
				sensitivity = 1.2; // iOS和H5更灵敏
			} else if (this.platform === 'android') {
				sensitivity = 0.8; // Android稍微不那么灵敏
			}

			target.scrollTop -= deltaY * sensitivity;
			this.touchStartY = currentY;
		},
		onTouchEnd(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.isScrolling = false;
		},
		onMouseDown(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			this.mouseStartY = e.clientY;
			this.isMouseScrolling = true;
		},
		onMouseMove(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			if (!this.isMouseScrolling) return;

			const deltaY = e.clientY - this.mouseStartY;
			const target = e.currentTarget;
			target.scrollTop -= deltaY * 0.8;
			this.mouseStartY = e.clientY;
		},
		onMouseUp(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.isMouseScrolling = false;
		},
		onWheel(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			const target = e.currentTarget;
			const scrollAmount = e.deltaY > 0 ? 60 : -60; // 固定滚动距离
			target.scrollTop += scrollAmount;
		}
	}
}
</script>

<style scoped>
.custom-date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 600rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
	border-radius: 20rpx;
	overflow: hidden;
	animation: scaleIn 0.3s ease;
	box-shadow: 0 10rpx 40rpx rgba(220, 20, 60, 0.3);
	border: 2rpx solid #DC143C;
	z-index: 2;
}

@keyframes scaleIn {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}



.date-selector {
	display: flex;
	height: 400rpx;
	padding: 30rpx 15rpx;
	overflow: hidden;
}

.selector-column {
	flex: 1;
	display: flex;
	flex-direction: column;
	margin: 0 5rpx;
}

.column-title {
	text-align: center;
	font-size: 26rpx;
	color: #DC143C;
	font-weight: bold;
	margin-bottom: 15rpx;
	background: linear-gradient(135deg, rgba(220, 20, 60, 0.1), rgba(255, 182, 193, 0.2));
	padding: 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(220, 20, 60, 0.2);
}

.scroll-list {
	flex: 1;
	height: 320rpx;
	overflow-y: auto;
	overflow-x: hidden;
	scroll-behavior: smooth;
}

.scroll-list::-webkit-scrollbar {
	width: 8rpx;
}

.scroll-list::-webkit-scrollbar-track {
	background: rgba(220, 20, 60, 0.1);
	border-radius: 4rpx;
}

.scroll-list::-webkit-scrollbar-thumb {
	background: linear-gradient(135deg, #FF6B6B, #DC143C);
	border-radius: 4rpx;
	box-shadow: 0 2rpx 4rpx rgba(220, 20, 60, 0.3);
}

.scroll-list::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(135deg, #DC143C, #B22222);
}

.scroll-item {
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #DC143C;
	margin: 4rpx 8rpx;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1rpx solid transparent;
}

.scroll-item:hover {
	background: linear-gradient(135deg, rgba(220, 20, 60, 0.1), rgba(255, 182, 193, 0.2));
	transform: scale(1.02);
	border-color: rgba(220, 20, 60, 0.3);
}

.scroll-item.active {
	background: linear-gradient(135deg, #DC143C, #B22222);
	color: white;
	font-weight: bold;
	transform: scale(1.05);
	box-shadow: 0 4rpx 12rpx rgba(220, 20, 60, 0.4);
	border-color: #DC143C;
}
</style>
