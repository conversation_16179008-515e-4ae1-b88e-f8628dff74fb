<template>
	<view class="date-picker-modal" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
			</view>

			<view class="date-input-container">
				<!-- 年份输入框 -->
				<view class="input-group">
					<label class="input-label">年份</label>
					<input 
						type="number" 
						class="date-input"
						v-model="selectedYear"
						placeholder="1990"
						:min="1900"
						:max="2030"
						@input="validateYear"
					/>
					<text class="input-unit">年</text>
				</view>

				<!-- 月份输入框 -->
				<view class="input-group">
					<label class="input-label">月份</label>
					<input 
						type="number" 
						class="date-input"
						v-model="selectedMonth"
						placeholder="8"
						:min="1"
						:max="12"
						@input="validateMonth"
					/>
					<text class="input-unit">月</text>
				</view>

				<!-- 日期输入框 -->
				<view class="input-group">
					<label class="input-label">日期</label>
					<input 
						type="number" 
						class="date-input"
						v-model="selectedDay"
						placeholder="19"
						:min="1"
						:max="maxDaysInMonth"
						@input="validateDay"
					/>
					<text class="input-unit">日</text>
				</view>
			</view>

			<!-- 当前选择显示 -->
			<view class="current-selection">
				<text class="selection-text">当前选择：{{ formatSelectedDate }}</text>
			</view>

			<view class="modal-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirmSelection" :disabled="!isValidDate">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PerfectDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		const now = new Date();
		return {
			selectedYear: now.getFullYear(),
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate()
		}
	},
	computed: {
		// 计算当月最大天数
		maxDaysInMonth() {
			if (!this.selectedYear || !this.selectedMonth) return 31;
			return new Date(this.selectedYear, this.selectedMonth, 0).getDate();
		},
		
		// 格式化显示的日期
		formatSelectedDate() {
			if (!this.selectedYear || !this.selectedMonth || !this.selectedDay) {
				return '请输入完整日期';
			}
			return `${this.selectedYear}年${this.selectedMonth}月${this.selectedDay}日`;
		},
		
		// 验证日期是否有效
		isValidDate() {
			if (!this.selectedYear || !this.selectedMonth || !this.selectedDay) return false;
			
			const year = parseInt(this.selectedYear);
			const month = parseInt(this.selectedMonth);
			const day = parseInt(this.selectedDay);
			
			// 基本范围检查
			if (year < 1900 || year > 2030) return false;
			if (month < 1 || month > 12) return false;
			if (day < 1 || day > this.maxDaysInMonth) return false;
			
			// 检查日期是否真实存在
			const date = new Date(year, month - 1, day);
			return date.getFullYear() === year && 
				   date.getMonth() === month - 1 && 
				   date.getDate() === day;
		}
	},
	watch: {
		value: {
			handler(newValue) {
				if (newValue) {
					this.parseValue(newValue);
				}
			},
			immediate: true
		},
		
		// 当月份或年份改变时，调整日期
		selectedMonth() {
			this.adjustDay();
		},
		selectedYear() {
			this.adjustDay();
		}
	},
	methods: {
		// 解析传入的日期值
		parseValue(value) {
			if (!value) return;
			
			const parts = value.split('-');
			if (parts.length === 3) {
				this.selectedYear = parseInt(parts[0]);
				this.selectedMonth = parseInt(parts[1]);
				this.selectedDay = parseInt(parts[2]);
			}
		},
		
		// 验证年份
		validateYear() {
			const year = parseInt(this.selectedYear);
			if (year < 1900) this.selectedYear = 1900;
			if (year > 2030) this.selectedYear = 2030;
		},
		
		// 验证月份
		validateMonth() {
			const month = parseInt(this.selectedMonth);
			if (month < 1) this.selectedMonth = 1;
			if (month > 12) this.selectedMonth = 12;
		},
		
		// 验证日期
		validateDay() {
			const day = parseInt(this.selectedDay);
			if (day < 1) this.selectedDay = 1;
			if (day > this.maxDaysInMonth) this.selectedDay = this.maxDaysInMonth;
		},
		
		// 调整日期（当月份改变时）
		adjustDay() {
			if (this.selectedDay > this.maxDaysInMonth) {
				this.selectedDay = this.maxDaysInMonth;
			}
		},
		
		// 关闭选择器
		close() {
			this.$emit('close');
		},
		
		// 确认选择
		confirmSelection() {
			if (!this.isValidDate) {
				uni.showToast({
					title: '请输入有效的日期',
					icon: 'none'
				});
				return;
			}
			
			const year = String(this.selectedYear).padStart(4, '0');
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateString = `${year}-${month}-${day}`;
			
			console.log('选择的日期:', dateString);
			this.$emit('confirm', dateString);
			this.close();
		}
	}
}
</script>

<style scoped>
/* 模态框主体 - 最高层级 */
.date-picker-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 模态框遮罩 - 在模态框内部 */
.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 400px;
	padding: 0;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	animation: slideUp 0.3s ease-out;
	position: relative;
	z-index: 10;
	opacity: 1;
	pointer-events: auto;
}

@keyframes slideUp {
	from {
		transform: translateY(100px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 头部 */
.picker-header {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	padding: 20px;
	border-radius: 20px 20px 0 0;
	text-align: center;
}

.header-title {
	font-size: 18px;
	font-weight: 600;
}

/* 输入容器 */
.date-input-container {
	padding: 30px 20px;
}

.input-group {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
	padding: 15px;
	background: #ffffff;
	border-radius: 12px;
	border: 2px solid #e9ecef;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-group:focus-within {
	border-color: #dc143c;
	background: #ffffff;
	box-shadow: 0 0 0 3px rgba(220, 20, 60, 0.15), 0 4px 12px rgba(0, 0, 0, 0.15);
}

.input-label {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	width: 60px;
	flex-shrink: 0;
}

.date-input {
	flex: 1;
	border: none;
	background: transparent;
	font-size: 18px;
	font-weight: 600;
	color: #333;
	text-align: center;
	outline: none;
	padding: 5px 10px;
}

.date-input::placeholder {
	color: #999;
	font-weight: normal;
}

.input-unit {
	font-size: 16px;
	color: #666;
	width: 30px;
	text-align: center;
	flex-shrink: 0;
}

/* 当前选择显示 */
.current-selection {
	padding: 0 20px 20px;
	text-align: center;
}

.selection-text {
	font-size: 16px;
	color: #dc143c;
	font-weight: 500;
	padding: 10px 20px;
	background: rgba(220, 20, 60, 0.1);
	border-radius: 8px;
	display: inline-block;
}

/* 底部按钮 */
.modal-footer {
	display: flex;
	border-top: 1px solid #e9ecef;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	padding: 18px;
	border: none;
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: #f8f9fa;
	color: #666;
	border-radius: 0 0 0 20px;
}

.btn-cancel:hover {
	background: #e9ecef;
}

.btn-confirm {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	border-radius: 0 0 20px 0;
}

.btn-confirm:hover {
	background: linear-gradient(135deg, #b91c3c 0%, #a0182f 100%);
}

.btn-confirm:disabled {
	background: #ccc;
	cursor: not-allowed;
}

.btn-confirm:disabled:hover {
	background: #ccc;
}
</style>
