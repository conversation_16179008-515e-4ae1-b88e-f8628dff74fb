# 文生视频功能 API 文档

## 📋 概述

基于文本描述生成视频的AI工作流

## 🚀 快速开始

```javascript
import { 文生视频 } from '@/api/文生视频/index.js';

const result = await 文生视频({
    prompt: '示例值',
    duration: '示例值',
    style: '示例值',
    resolution: '示例值'
});
```

## 📝 API 接口

### 主要接口

#### `文生视频(formData, options)`
执行文生视频功能的主接口。

**参数：**
- `prompt` (string): prompt *必需*
- `duration` (string): duration *必需*
- `style` (string): style *必需*
- `resolution` (string): resolution *必需*

**返回：**
```javascript
{
    success: true,
    data: {
        // 处理结果
    }
}
```

## ⚙️ 配置说明

### 工作流配置
- 工作流ID: `text_to_video_workflow_001`
- 工作流类型: `text_to_video`
- 基础费用: 50金币

## 🔧 工作流对接

### 结构化参数格式
```javascript
{
    requestId: "req_id",
    workflowId: "text_to_video_workflow_001",
    workflowType: "text_to_video",
    structuredParams: {
        prompt: { type: "text", value: "值", placeholder: "{{prompt}}" },
        duration: { type: "text", value: "值", placeholder: "{{duration}}" },
        style: { type: "text", value: "值", placeholder: "{{style}}" },
        resolution: { type: "text", value: "值", placeholder: "{{resolution}}" }
    }
}
```

## 🚨 错误处理

常见错误码：
- `TEXT_TO_VIDEO_001`: 参数格式不正确
- `TEXT_TO_VIDEO_007`: 金币余额不足
- `TEXT_TO_VIDEO_008`: 工作流执行超时

## 📞 技术支持

如有问题，请联系开发团队。