<template>
  <view class="advanced-text-selector" :class="{ 'active': isSelectionMode }">
    <!-- 选择手柄容器 -->
    <view 
      class="selection-handles-container" 
      v-if="isSelectionMode && hasSelection"
      :style="containerStyle"
    >
      <!-- 起始选择手柄 -->
      <view 
        class="selection-handle start-handle"
        :style="startHandleStyle"
        @touchstart="handleStartDrag"
        @touchmove="handleStartMove"
        @touchend="handleDragEnd"
      >
        <view class="handle-circle"></view>
        <view class="handle-line"></view>
      </view>

      <!-- 结束选择手柄 -->
      <view 
        class="selection-handle end-handle"
        :style="endHandleStyle"
        @touchstart="handleEndDrag"
        @touchmove="handleEndMove"
        @touchend="handleDragEnd"
      >
        <view class="handle-circle"></view>
        <view class="handle-line"></view>
      </view>

      <!-- 选择区域高亮 -->
      <view 
        class="selection-highlight"
        :style="highlightStyle"
      ></view>
    </view>

    <!-- 光标定位指示器 -->
    <view 
      class="cursor-indicator"
      v-if="showCursorIndicator"
      :style="cursorStyle"
    ></view>
  </view>
</template>

<script>
export default {
  name: 'AdvancedTextSelector',
  props: {
    textareaElement: {
      type: Object,
      default: null
    },
    value: {
      type: String,
      default: ''
    },
    selectionStart: {
      type: Number,
      default: 0
    },
    selectionEnd: {
      type: Number,
      default: 0
    },
    isSelectionMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDragging: false,
      dragType: '', // 'start' or 'end'
      showCursorIndicator: false,
      cursorPosition: 0,
      
      // 文本框的位置和尺寸信息
      textareaRect: null,
      lineHeight: 20,
      charWidth: 8,
      
      // 选择手柄位置
      startHandlePos: { x: 0, y: 0 },
      endHandlePos: { x: 0, y: 0 },
      
      // 触摸状态
      touchStartPos: { x: 0, y: 0 },
      lastTouchPos: { x: 0, y: 0 }
    };
  },
  computed: {
    hasSelection() {
      return this.selectionStart !== this.selectionEnd;
    },
    
    containerStyle() {
      if (!this.textareaRect) return {};
      return {
        left: this.textareaRect.left + 'px',
        top: this.textareaRect.top + 'px',
        width: this.textareaRect.width + 'px',
        height: this.textareaRect.height + 'px'
      };
    },
    
    startHandleStyle() {
      return {
        left: this.startHandlePos.x + 'px',
        top: this.startHandlePos.y + 'px'
      };
    },
    
    endHandleStyle() {
      return {
        left: this.endHandlePos.x + 'px',
        top: this.endHandlePos.y + 'px'
      };
    },
    
    highlightStyle() {
      const startX = Math.min(this.startHandlePos.x, this.endHandlePos.x);
      const endX = Math.max(this.startHandlePos.x, this.endHandlePos.x);
      const startY = Math.min(this.startHandlePos.y, this.endHandlePos.y);
      const endY = Math.max(this.startHandlePos.y, this.endHandlePos.y);
      
      return {
        left: startX + 'px',
        top: startY + 'px',
        width: (endX - startX) + 'px',
        height: Math.max(this.lineHeight, endY - startY) + 'px'
      };
    },
    
    cursorStyle() {
      const pos = this.getPositionFromIndex(this.cursorPosition);
      return {
        left: pos.x + 'px',
        top: pos.y + 'px'
      };
    }
  },
  watch: {
    selectionStart: {
      handler() {
        this.updateHandlePositions();
      },
      immediate: true
    },
    selectionEnd: {
      handler() {
        this.updateHandlePositions();
      },
      immediate: true
    },
    textareaElement: {
      handler() {
        this.updateTextareaInfo();
      },
      immediate: true
    }
  },
  mounted() {
    this.initSelector();
    this.addGlobalClickListener();
  },
  methods: {
    // 初始化选择器
    initSelector() {
      this.updateTextareaInfo();
      this.updateHandlePositions();
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.updateTextareaInfo);
    },
    
    // 更新文本框信息
    updateTextareaInfo() {
      if (!this.textareaElement) return;
      
      try {
        this.textareaRect = this.textareaElement.getBoundingClientRect();
        
        // 计算字符尺寸
        const computedStyle = window.getComputedStyle(this.textareaElement);
        this.lineHeight = parseInt(computedStyle.lineHeight) || 20;
        
        // 估算字符宽度
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.font = computedStyle.font;
        this.charWidth = ctx.measureText('M').width || 8;
        
      } catch (error) {
        console.error('更新文本框信息失败:', error);
      }
    },
    
    // 更新选择手柄位置
    updateHandlePositions() {
      if (!this.hasSelection) return;
      
      this.startHandlePos = this.getPositionFromIndex(this.selectionStart);
      this.endHandlePos = this.getPositionFromIndex(this.selectionEnd);
    },
    
    // 根据文本索引获取位置
    getPositionFromIndex(index) {
      if (!this.value || !this.textareaRect) {
        return { x: 0, y: 0 };
      }
      
      const text = this.value.substring(0, index);
      const lines = text.split('\n');
      const lineIndex = lines.length - 1;
      const charIndex = lines[lineIndex].length;
      
      return {
        x: charIndex * this.charWidth,
        y: lineIndex * this.lineHeight
      };
    },
    
    // 根据位置获取文本索引
    getIndexFromPosition(x, y) {
      if (!this.value) return 0;
      
      const lineIndex = Math.floor(y / this.lineHeight);
      const charIndex = Math.floor(x / this.charWidth);
      
      const lines = this.value.split('\n');
      let index = 0;
      
      for (let i = 0; i < lineIndex && i < lines.length; i++) {
        index += lines[i].length + 1; // +1 for newline
      }
      
      if (lineIndex < lines.length) {
        index += Math.min(charIndex, lines[lineIndex].length);
      }
      
      return Math.max(0, Math.min(index, this.value.length));
    },
    
    // 处理起始手柄拖拽
    handleStartDrag(e) {
      this.isDragging = true;
      this.dragType = 'start';
      this.touchStartPos = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      };
      
      // 阻止默认行为
      e.preventDefault();
      e.stopPropagation();
    },
    
    // 处理结束手柄拖拽
    handleEndDrag(e) {
      this.isDragging = true;
      this.dragType = 'end';
      this.touchStartPos = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      };
      
      // 阻止默认行为
      e.preventDefault();
      e.stopPropagation();
    },
    
    // 处理起始手柄移动
    handleStartMove(e) {
      if (!this.isDragging || this.dragType !== 'start') return;
      
      const touch = e.touches[0];
      const relativeX = touch.clientX - this.textareaRect.left;
      const relativeY = touch.clientY - this.textareaRect.top;
      
      const newIndex = this.getIndexFromPosition(relativeX, relativeY);
      
      // 确保起始位置不超过结束位置
      const newStart = Math.min(newIndex, this.selectionEnd - 1);
      
      this.$emit('selection-change', {
        start: newStart,
        end: this.selectionEnd
      });
      
      e.preventDefault();
      e.stopPropagation();
    },
    
    // 处理结束手柄移动
    handleEndMove(e) {
      if (!this.isDragging || this.dragType !== 'end') return;
      
      const touch = e.touches[0];
      const relativeX = touch.clientX - this.textareaRect.left;
      const relativeY = touch.clientY - this.textareaRect.top;
      
      const newIndex = this.getIndexFromPosition(relativeX, relativeY);
      
      // 确保结束位置不小于起始位置
      const newEnd = Math.max(newIndex, this.selectionStart + 1);
      
      this.$emit('selection-change', {
        start: this.selectionStart,
        end: newEnd
      });
      
      e.preventDefault();
      e.stopPropagation();
    },
    
    // 处理拖拽结束
    handleDragEnd(e) {
      this.isDragging = false;
      this.dragType = '';
      
      // 触觉反馈
      if (uni.vibrateShort) {
        uni.vibrateShort();
      }
      
      e.preventDefault();
      e.stopPropagation();
    },
    
    // 处理点击定位光标
    handleTextareaClick(e) {
      if (this.isSelectionMode) return;

      const relativeX = e.clientX - this.textareaRect.left;
      const relativeY = e.clientY - this.textareaRect.top;

      const index = this.getIndexFromPosition(relativeX, relativeY);

      this.$emit('cursor-position-change', index);

      // 显示光标指示器
      this.cursorPosition = index;
      this.showCursorIndicator = true;

      setTimeout(() => {
        this.showCursorIndicator = false;
      }, 1000);
    },

    // 添加全局点击监听器（修复内存泄露）
    addGlobalClickListener() {
      // 先移除可能存在的监听器，避免重复添加
      this.removeGlobalClickListener();

      document.addEventListener('click', this.handleGlobalClick, true);
      document.addEventListener('touchend', this.handleGlobalTouch, true);

      console.log('✅ AdvancedTextSelector: 已添加全局事件监听器');
    },

    // 移除全局点击监听器（修复内存泄露）
    removeGlobalClickListener() {
      document.removeEventListener('click', this.handleGlobalClick, true);
      document.removeEventListener('touchend', this.handleGlobalTouch, true);

      console.log('🧹 AdvancedTextSelector: 已移除全局事件监听器');
    },

    // 处理全局点击事件
    handleGlobalClick(e) {
      this.handleGlobalInteraction(e);
    },

    // 处理全局触摸事件
    handleGlobalTouch(e) {
      this.handleGlobalInteraction(e);
    },

    // 处理全局交互事件
    handleGlobalInteraction(e) {
      if (!this.isSelectionMode || !this.hasSelection) return;

      const target = e.target;

      // 检查是否点击在选择相关元素上
      if (this.isSelectionRelatedElement(target)) {
        return; // 不取消选择
      }

      // 检查是否点击在文本框内
      if (this.textareaElement && (target === this.textareaElement || this.textareaElement.contains(target))) {
        return; // 不取消选择
      }

      // 点击在外部区域，取消选择
      this.cancelSelection();
    },

    // 检查是否为选择相关元素
    isSelectionRelatedElement(element) {
      const selectionClasses = [
        'selection-handle',
        'selection-handles-container',
        'selection-highlight',
        'advanced-text-selector',
        'text-selection-menu',
        'selection-menu'
      ];

      return selectionClasses.some(className => {
        return element.classList?.contains(className) ||
               element.closest(`.${className}`);
      });
    },

    // 取消选择
    cancelSelection() {
      console.log('取消文本选择');

      // 触觉反馈
      if (uni.vibrateShort) {
        uni.vibrateShort();
      }

      // 发射取消选择事件
      this.$emit('selection-cancel');
    }
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.updateTextareaInfo);
    this.removeGlobalClickListener();
  }
}
</script>

<style scoped>
.advanced-text-selector {
  position: relative;
  width: 100%;
  height: 100%;
}

.selection-handles-container {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
}

.selection-handle {
  position: absolute;
  pointer-events: auto;
  z-index: 10000;
  width: 44px;
  height: 44px;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selection-handle:active {
  cursor: grabbing;
}

.handle-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #007AFF;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  position: relative;
  transition: transform 0.2s ease;
}

.selection-handle:active .handle-circle {
  transform: scale(1.2);
}

.handle-line {
  width: 3px;
  height: 24px;
  background: #007AFF;
  position: absolute;
  top: 24px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

.start-handle .handle-circle {
  background: #34C759;
}

.start-handle .handle-line {
  background: #34C759;
}

.selection-highlight {
  position: absolute;
  background: rgba(0, 122, 255, 0.2);
  border-radius: 2px;
  pointer-events: none;
}

.cursor-indicator {
  position: absolute;
  width: 2px;
  height: 20px;
  background: #007AFF;
  animation: blink 1s infinite;
  pointer-events: none;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 选择模式激活状态 */
.advanced-text-selector.active {
  user-select: text;
  -webkit-user-select: text;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .selection-handle {
    width: 48px;
    height: 48px;
  }

  .handle-circle {
    width: 28px;
    height: 28px;
    border: 4px solid white;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
  }

  .handle-line {
    width: 4px;
    height: 28px;
    top: 28px;
  }

  .cursor-indicator {
    width: 4px;
    height: 28px;
  }

  .selection-highlight {
    border-radius: 4px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .selection-handle {
    width: 52px;
    height: 52px;
  }

  .handle-circle {
    width: 32px;
    height: 32px;
    border: 4px solid white;
  }

  .handle-line {
    width: 4px;
    height: 32px;
    top: 32px;
  }
}
</style>
