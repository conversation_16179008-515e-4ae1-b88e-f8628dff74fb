<template>
	<view class="text-to-music">
		<!-- 文本描述输入 -->
		<view class="section">
			<view class="section-title">
				<text class="section-title-text">音乐描述</text>
				<text class="char-counter">{{ prompt.length }}/200</text>
			</view>
			<view class="prompt-input-area">
				<textarea
					class="prompt-input"
					v-model="prompt"
					placeholder="描述您想要生成的音乐，例如：一首充满活力的夏日热舞曲，适合沙滩派对"
					maxlength="200"
				></textarea>
			</view>
		</view>
		
		<!-- 预设提示词 -->
		<view class="section">
			<view class="section-title">
				<text class="section-title-text">预设提示词</text>
			</view>
			<scroll-view class="presets-list" scroll-x>
				<view 
					v-for="(preset, index) in presetPrompts" 
					:key="index" 
					class="preset-item" 
					@click="selectPreset(preset.prompt)"
				>
					<text class="preset-name">{{ preset.name }}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 情绪选择 -->
		<view class="section">
			<view class="section-title">
				<text class="section-title-text">情绪选择</text>
			</view>
			<scroll-view class="mood-list" scroll-x>
				<view 
					v-for="(mood, index) in moods" 
					:key="index" 
					class="mood-item" 
					:class="{ active: selectedMood === mood.value }"
					@click="selectMood(mood)"
				>
					<view class="mood-icon">{{ mood.icon }}</view>
					<text class="mood-name">{{ mood.name }}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 音乐风格选择 -->
		<view class="section">
			<view class="section-title">
				<text class="section-title-text">音乐风格</text>
			</view>
			<scroll-view class="style-list" scroll-x>
				<view 
					v-for="(style, index) in musicStyles" 
					:key="index" 
					class="style-item" 
					:class="{ active: selectedStyle === style.value }"
					@click="selectStyle(style)"
				>
					<view class="style-icon">{{ style.icon }}</view>
					<text class="style-name">{{ style.name }}</text>
				</view>
			</scroll-view>
			
			<!-- 风格描述 -->
			<view class="style-description" v-if="currentStyleDescription">
				<text>{{ currentStyleDescription }}</text>
			</view>
		</view>
		
		<!-- 音乐时长 -->
		<view class="section">
			<view class="section-title">
				<text class="section-title-text">音乐时长</text>
				<text class="duration-value">{{ duration }}秒</text>
			</view>
			<slider 
				:value="duration" 
				:min="30" 
				:max="180" 
				:step="30" 
				:block-size="24" 
				block-color="#6e56cf"
				background-color="rgba(255,255,255,0.1)"
				active-color="#6e56cf"
				@change="onDurationChange"
			/>
		</view>
		
		<!-- 生成按钮 -->
		<view class="btn-container">
			<button 
				class="generate-btn" 
				@click="generate"
				:disabled="generating || !canGenerate"
				:class="{ disabled: !canGenerate }"
			>
				{{ generatePrice }}金币生成音乐
			</button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TextToMusic',
	props: {
		generating: {
			type: Boolean,
			default: false
		},
		generatingProgress: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			prompt: '',
			selectedMood: 'happy',
			selectedStyle: 'pop',
			duration: 60,
			currentStyleDescription: '',
			
			// 预设提示词
			presetPrompts: [
				{ name: '轻松愉快', prompt: '一首轻快欢乐的歌曲，带有轻松的节奏和明亮的旋律，适合早晨听' },
				{ name: '史诗震撼', prompt: '一首史诗般的背景音乐，带有宏大的管弦乐和强烈的鼓点，适合冒险场景' },
				{ name: '浪漫抒情', prompt: '一首浪漫抒情的钢琴曲，柔和的旋律配以轻柔的弦乐，表达爱与思念' },
				{ name: '动感电子', prompt: '一首动感十足的电子舞曲，带有强烈的节奏和现代感的合成器音效' },
				{ name: '自然放松', prompt: '一首自然放松的环境音乐，融合了流水声和轻柔的竖琴声，有助于冥想' }
			],
			
			// 情绪选项
			moods: [
				{ name: '欢快', value: 'happy', icon: '😊', desc: '充满活力和积极情绪' },
				{ name: '悲伤', value: 'sad', icon: '😢', desc: '忧郁、伤感的情绪' },
				{ name: '平静', value: 'calm', icon: '😌', desc: '平和、舒缓的情绪' },
				{ name: '浪漫', value: 'romantic', icon: '💕', desc: '充满爱意的情感' },
				{ name: '激情', value: 'passionate', icon: '🔥', desc: '热情洋溢的情绪' },
				{ name: '紧张', value: 'tense', icon: '😰', desc: '紧张、悬疑的氛围' },
				{ name: '神秘', value: 'mysterious', icon: '🔮', desc: '神秘、未知的气息' },
				{ name: '欢乐', value: 'joyful', icon: '🎉', desc: '庆祝、喜悦的情绪' }
			],
			
			// 音乐风格
			musicStyles: [
				{ 
					name: '流行', 
					value: 'pop', 
					icon: '🎵',
					description: '现代流行风格，通常包含朗朗上口的旋律和现代制作技术'
				},
				{ 
					name: '摇滚', 
					value: 'rock', 
					icon: '🎸',
					description: '以电吉他和强劲的鼓点为特色，带有强烈的节奏感和力量感'
				},
				{ 
					name: '电子', 
					value: 'electronic', 
					icon: '🎧',
					description: '使用电子合成器和采样器创作，节奏感强，适合舞蹈'
				},
				{ 
					name: '民谣', 
					value: 'folk', 
					icon: '🪕',
					description: '以原声乐器为主，歌词叙事性强，通常风格朴实自然'
				},
				{ 
					name: '古典', 
					value: 'classical', 
					icon: '🎻',
					description: '以管弦乐和钢琴等传统乐器为主，结构严谨，情感丰富'
				},
				{ 
					name: '爵士', 
					value: 'jazz', 
					icon: '🎷',
					description: '即兴演奏，节奏复杂多变，和声丰富，氛围随性'
				},
				{ 
					name: '嘻哈', 
					value: 'hiphop', 
					icon: '🎤',
					description: '强烈的节奏和韵律，通常包含说唱元素和采样技术'
				},
				{ 
					name: '乡村', 
					value: 'country', 
					icon: '🤠',
					description: '以吉他、班卓琴等为特色，歌词讲述乡村生活和情感故事'
				}
			]
		}
	},
	created() {
		// 初始化风格描述
		this.updateStyleDescription();
	},
	computed: {
		canGenerate() {
			return this.prompt.trim().length > 5;
		},
		generatePrice() {
			// 根据时长计算价格
			return Math.floor(this.duration / 30) * 15;
		},
		moodName() {
			const mood = this.moods.find(m => m.value === this.selectedMood);
			return mood ? mood.name : '';
		},
		styleName() {
			const style = this.musicStyles.find(s => s.value === this.selectedStyle);
			return style ? style.name : '';
		}
	},
	methods: {
		onDurationChange(e) {
			this.duration = e.detail.value;
		},
		selectPreset(promptText) {
			this.prompt = promptText;
		},
		selectMood(mood) {
			this.selectedMood = mood.value;
			// 可以在这里添加额外逻辑，比如在提示词中添加情绪词
		},
		selectStyle(style) {
			this.selectedStyle = style.value;
			this.updateStyleDescription();
		},
		updateStyleDescription() {
			const style = this.musicStyles.find(s => s.value === this.selectedStyle);
			this.currentStyleDescription = style ? style.description : '';
		},
		generate() {
			if (!this.canGenerate || this.generating) return;
			
			const data = {
				type: 'text-to-music',
				data: {
					prompt: this.prompt,
					mood: this.selectedMood,
					moodName: this.moodName,
					style: this.selectedStyle,
					styleName: this.styleName,
					duration: this.duration
				}
			};
			
			this.$emit('generate', data);
		}
	}
}
</script>

<style lang="scss" scoped>
.text-to-music {
	background-color: rgba(255, 255, 255, 0.05);
	border-radius: 16rpx;
	padding: 30rpx;
}

.section {
	margin-bottom: 30rpx;
	
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.section-title-text {
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
			position: relative;
			padding-left: 20rpx;
			
			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 8rpx;
				height: 32rpx;
				background: linear-gradient(to bottom, #7879F1, #6e56cf);
				border-radius: 4rpx;
			}
		}
		
		.char-counter, .duration-value {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.5);
		}
	}
}

.prompt-input-area {
	width: 100%;
	border-radius: 16rpx;
	background-color: rgba(255, 255, 255, 0.05);
	padding: 20rpx;
	box-sizing: border-box;
	
	.prompt-input {
		width: 100%;
		height: 200rpx;
		color: rgba(255, 255, 255, 0.9);
		font-size: 28rpx;
	}
}

.presets-list {
	white-space: nowrap;
	padding: 10rpx 0;
	
	.preset-item {
		display: inline-block;
		padding: 10rpx 20rpx;
		margin-right: 15rpx;
		background-color: rgba(255, 255, 255, 0.05);
		border-radius: 30rpx;
		border: 1px solid rgba(110, 86, 207, 0.3);
		
		.preset-name {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.7);
		}
		
		&:active {
			background-color: rgba(110, 86, 207, 0.2);
		}
	}
}

.mood-list, .style-list {
	white-space: nowrap;
	padding: 10rpx 0;
	
	.mood-item, .style-item {
		display: inline-block;
		width: 140rpx;
		margin: 0 10rpx;
		text-align: center;
		transition: all 0.3s ease;
		
		&:first-child {
			margin-left: 0;
		}
		
		.mood-icon, .style-icon {
			height: 100rpx;
			width: 100rpx;
			line-height: 100rpx;
			font-size: 40rpx;
			margin: 0 auto;
			margin-bottom: 10rpx;
			background-color: rgba(255, 255, 255, 0.05);
			border-radius: 20rpx;
			transition: all 0.3s ease;
		}
		
		.mood-name, .style-name {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.7);
			transition: all 0.3s ease;
		}
		
		&.active {
			.mood-icon, .style-icon {
				background: linear-gradient(135deg, #7879F1, #6e56cf);
				box-shadow: 0 10rpx 20rpx rgba(110, 86, 207, 0.3);
				transform: scale(1.05);
			}
			
			.mood-name, .style-name {
				color: #fff;
				font-weight: bold;
			}
		}
	}
}

.style-description {
	margin-top: 15rpx;
	padding: 15rpx;
	background-color: rgba(255, 255, 255, 0.03);
	border-radius: 8rpx;
	border-left: 3rpx solid rgba(110, 86, 207, 0.5);
	
	text {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.6);
		line-height: 1.4;
	}
}

.btn-container {
	margin-top: 40rpx;
	
	.generate-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
		background: linear-gradient(135deg, #7879F1, #6e56cf);
		border-radius: 45rpx;
		box-shadow: 0 10rpx 20rpx rgba(110, 86, 207, 0.3);
		transition: all 0.3s ease;
		
		&:active {
			transform: scale(0.98);
			box-shadow: 0 5rpx 10rpx rgba(110, 86, 207, 0.3);
		}
		
		&.disabled {
			background: rgba(255, 255, 255, 0.1);
			box-shadow: none;
			color: rgba(255, 255, 255, 0.3);
		}
	}
}
</style> 