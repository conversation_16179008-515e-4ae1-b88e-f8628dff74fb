<template>
	<view class="custom-time-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生时间</text>
			</view>

			<picker-view
				class="picker-view"
				:value="pickerValue"
				@change="onPickerChange"
				@pickstart="onPickStart"
				@pickend="onPickEnd"
				@wheel.prevent="onWheel"
				:indicator-style="indicatorStyle"
				:mask-style="maskStyle"
				:immediate-change="false"
			>
				<!-- 小时列 -->
				<picker-view-column>
					<view
						v-for="(hour, index) in hours"
						:key="index"
						class="picker-item"
					>
						{{ String(hour).padStart(2, '0') }}时
					</view>
				</picker-view-column>

				<!-- 分钟列 -->
				<picker-view-column>
					<view
						v-for="(minute, index) in minutes"
						:key="index"
						class="picker-item"
					>
						{{ String(minute).padStart(2, '0') }}分
					</view>
				</picker-view-column>
			</picker-view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedHour: new Date().getHours(),
			selectedMinute: new Date().getMinutes(),
			hours: Array.from({ length: 24 }, (_, i) => i),
			minutes: Array.from({ length: 60 }, (_, i) => i),
			pickerValue: [0, 0], // picker-view的选中索引

			// 优化的指示器样式 - 行业标准设计
			indicatorStyle: `
				height: 80rpx;
				background: linear-gradient(135deg,
					rgba(220, 20, 60, 0.08) 0%,
					rgba(255, 182, 193, 0.15) 50%,
					rgba(220, 20, 60, 0.08) 100%
				);
				border-radius: 16rpx;
				border: 2rpx solid rgba(220, 20, 60, 0.3);
				box-shadow:
					0 4rpx 20rpx rgba(220, 20, 60, 0.15),
					inset 0 2rpx 8rpx rgba(255, 255, 255, 0.5);
			`,

			// 优化的遮罩样式
			maskStyle: `
				background: linear-gradient(180deg,
					rgba(255, 255, 255, 0.98) 0%,
					rgba(255, 255, 255, 0.7) 35%,
					rgba(255, 255, 255, 0.1) 45%,
					transparent 50%,
					rgba(255, 255, 255, 0.1) 55%,
					rgba(255, 255, 255, 0.7) 65%,
					rgba(255, 255, 255, 0.98) 100%
				);
			`,

			isPickerScrolling: false,
			wheelTimer: null, // 鼠标滚轮定时器
			momentumTimer: null, // 惯性动画定时器
			lastScrollTime: 0,
			scrollVelocity: 0
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [hour, minute] = newVal.split(':');
					this.selectedHour = parseInt(hour);
					this.selectedMinute = parseInt(minute);
					this.updatePickerValue();
				}
			},
			immediate: true
		},
		visible: {
			handler(newVal) {
				if (newVal) {
					// 当选择器显示时，确保正确居中
					this.$nextTick(() => {
						setTimeout(() => {
							this.updatePickerValue();
						}, 100);
					});
				}
			},
			immediate: true
		}
	},
	created() {
		this.updatePickerValue();
	},
	methods: {
		updatePickerValue() {
			// 更新picker-view的选中索引
			this.pickerValue = [this.selectedHour, this.selectedMinute];

			// 强制更新picker-view的显示
			this.$nextTick(() => {
				this.forcePickerUpdate();
			});
		},
		forcePickerUpdate() {
			// 通过微调pickerValue来强制picker-view重新渲染和居中
			const currentValue = [...this.pickerValue];
			this.pickerValue = [0, 0];
			this.$nextTick(() => {
				this.pickerValue = currentValue;
			});
		},
		onPickerChange(e) {
			console.log('picker change:', e.detail.value);
			let newValue = e.detail.value;

			// 确保数组长度正确（小时、分钟 = 2个元素）
			if (newValue.length !== 2) {
				console.warn('picker value length mismatch:', newValue.length, 'expected: 2');
				newValue = newValue.slice(0, 2); // 只取前2个元素
				if (newValue.length < 2) {
					// 如果不足2个元素，补充默认值
					while (newValue.length < 2) {
						newValue.push(0);
					}
				}
			}

			// 如果不在滚动中，进行精确位置计算
			if (!this.isPickerScrolling) {
				const correctedValue = this.calculateSnapPosition(newValue);
				this.pickerValue = correctedValue;
				this.updateTimeFromPickerValue(correctedValue);
			} else {
				this.pickerValue = newValue;
				this.updateTimeFromPickerValue(newValue);
			}
		},

		updateTimeFromPickerValue(value) {
			const [hourIndex, minuteIndex] = value;
			this.selectedHour = hourIndex;
			this.selectedMinute = minuteIndex;
		},
		onPickStart() {
			this.isPickerScrolling = true;
		},
		onPickEnd() {
			console.log('时间选择器滚动结束，开始磁性吸附');
			this.isPickerScrolling = false;

			// 立即进行磁性吸附，确保完美居中
			setTimeout(() => {
				this.performPreciseMagneticSnap();
			}, 30); // 减少延迟，更快响应
		},
		// 执行精确的磁性吸附
		performPreciseMagneticSnap() {
			const currentValue = [...this.pickerValue];
			console.log('时间选择器当前值:', currentValue);

			// 计算精确的吸附位置
			const correctedValue = this.calculatePreciseSnapPosition(currentValue);
			console.log('时间选择器目标值:', correctedValue);

			// 如果需要修正位置，则进行吸附
			if (this.needsSnap(currentValue, correctedValue)) {
				this.performPreciseSnap(correctedValue);
			} else {
				// 确保最终值是精确的整数
				this.pickerValue = [...correctedValue];
				this.updateTimeFromPickerValue(correctedValue);
			}
		},

		// 保持原有方法的兼容性
		magneticSnap() {
			this.performPreciseMagneticSnap();
		},

		// 计算精确的吸附位置
		calculatePreciseSnapPosition(currentValue) {
			// 确保输入数组长度正确
			const safeValue = [...currentValue];
			while (safeValue.length < 2) {
				safeValue.push(0);
			}
			if (safeValue.length > 2) {
				safeValue.splice(2); // 只保留前2个元素
			}

			// 计算每个列的精确吸附位置
			const correctedValue = [0, 0];

			// 小时精确吸附计算
			const hourIndex = safeValue[0] || 0;
			const hourCount = this.hours.length;
			correctedValue[0] = this.snapToNearestIndex(hourIndex, hourCount);

			// 分钟精确吸附计算
			const minuteIndex = safeValue[1] || 0;
			const minuteCount = this.minutes.length;
			correctedValue[1] = this.snapToNearestIndex(minuteIndex, minuteCount);

			console.log('时间吸附计算:', {
				原始值: safeValue,
				目标值: correctedValue,
				小时: `${hourIndex} -> ${correctedValue[0]}`,
				分钟: `${minuteIndex} -> ${correctedValue[1]}`
			});

			return correctedValue;
		},

		// 保持原有方法的兼容性
		calculateSnapPosition(currentValue) {
			return this.calculatePreciseSnapPosition(currentValue);
		},

		snapToNearestIndex(currentIndex, maxCount) {
			// 确保索引在有效范围内
			if (currentIndex < 0) return 0;
			if (currentIndex >= maxCount) return maxCount - 1;

			// 强化的磁性吸附算法 - 确保完美居中
			const decimal = currentIndex - Math.floor(currentIndex);
			const intPart = Math.floor(currentIndex);

			// 更严格的吸附阈值，确保选项完美居中
			if (decimal < 0.08) {
				// 非常接近下边界，强制向下吸附
				return intPart;
			} else if (decimal > 0.92) {
				// 非常接近上边界，强制向上吸附
				return Math.min(intPart + 1, maxCount - 1);
			} else if (decimal >= 0.25 && decimal <= 0.75) {
				// 扩大中心区域，强制居中对齐
				return Math.round(currentIndex);
			} else {
				// 其他区域，选择最近的整数位置，确保精确对齐
				const target = decimal < 0.5 ? intPart : Math.min(intPart + 1, maxCount - 1);
				console.log(`索引吸附: ${currentIndex.toFixed(3)} -> ${target} (小数: ${decimal.toFixed(3)})`);
				return target;
			}
		},

		needsSnap(currentValue, correctedValue) {
			// 确保两个数组都是正确长度
			if (currentValue.length !== 2 || correctedValue.length !== 2) {
				return true; // 长度不对就需要修正
			}

			// 检查是否需要进行位置修正
			return currentValue[0] !== correctedValue[0] ||
			       currentValue[1] !== correctedValue[1];
		},

		performPreciseSnap(targetValue) {
			// 确保目标值是正确的2元素数组
			const safeTargetValue = [...targetValue];
			while (safeTargetValue.length < 2) {
				safeTargetValue.push(0);
			}
			if (safeTargetValue.length > 2) {
				safeTargetValue.splice(2);
			}

			// 检查是否需要动画
			const currentValue = [...this.pickerValue];
			const needsAnimation = currentValue.some((val, index) =>
				Math.abs(val - safeTargetValue[index]) > 0.1
			);

			if (needsAnimation) {
				// 执行平滑的吸附动画
				this.animateToTarget(currentValue, safeTargetValue);
			} else {
				// 直接设置位置
				this.pickerValue = [...safeTargetValue];
			}
		},

		// 平滑动画到目标位置 - 行业标准的缓动效果
		animateToTarget(startValue, targetValue) {
			const startTime = Date.now();
			const duration = 280; // 优化动画时长，更接近原生体验

			// 清除之前的动画
			if (this.momentumTimer) {
				cancelAnimationFrame(this.momentumTimer);
			}

			const animate = () => {
				const elapsed = Date.now() - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// 使用iOS风格的缓动函数：cubic-bezier(0.25, 0.46, 0.45, 0.94)
				// 提供更自然的减速效果
				let easeProgress;
				if (progress < 0.5) {
					easeProgress = 4 * progress * progress * progress;
				} else {
					easeProgress = 1 - Math.pow(-2 * progress + 2, 3) / 2;
				}

				// 计算当前位置
				const currentPos = startValue.map((start, index) => {
					const target = targetValue[index];
					return start + (target - start) * easeProgress;
				});

				this.pickerValue = currentPos;

				if (progress < 1) {
					this.momentumTimer = requestAnimationFrame(animate);
				} else {
					// 动画结束，设置精确值并更新时间
					this.pickerValue = [...targetValue];
					this.updateTimeFromPickerValue(targetValue);
					this.momentumTimer = null;
				}
			};

			this.momentumTimer = requestAnimationFrame(animate);
		},

		ensureAlignment() {
			// 兼容性方法，调用磁性吸附
			this.magneticSnap();
		},

		// 处理鼠标滚轮事件
		onWheel(e) {
			try {
				e.preventDefault();
				e.stopPropagation();

				// 获取滚动方向
				const delta = e.deltaY > 0 ? 1 : -1;

				// 简化的列选择逻辑，避免使用getBoundingClientRect
				// 默认滚动小时列，使用Shift键切换到分钟列
				let columnIndex = e.shiftKey ? 1 : 0;

				let newValue = [...this.pickerValue];

				if (columnIndex === 0) {
					// 滚动小时列
					newValue[0] = Math.max(0, Math.min(23, newValue[0] + delta));
				} else {
					// 滚动分钟列
					newValue[1] = Math.max(0, Math.min(59, newValue[1] + delta));
				}

				// 更新picker值
				this.pickerValue = newValue;
				this.updateTimeFromPickerValue(newValue);

				// 延迟执行吸附
				clearTimeout(this.wheelTimer);
				this.wheelTimer = setTimeout(() => {
					this.magneticSnap();
				}, 150);
			} catch (error) {
				console.warn('滚轮事件处理出错:', error);
			}
		},

		close() {
			this.$emit('close');
		},
		confirm() {
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');
			const timeStr = `${hour}:${minute}`;
			this.$emit('confirm', timeStr);
		}
	}
}
</script>

<style scoped>
.custom-time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 750rpx; /* 增加最大宽度 */
	min-width: 600rpx; /* 设置最小宽度 */
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	animation: slideUp 0.3s ease;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid #e0e0e0;
	z-index: 2;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.picker-header {
	padding: 24rpx 30rpx;
	text-align: center;
	background: #f8f9fa;
	color: #333;
	border-bottom: 1rpx solid #e0e0e0;
}

.header-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.picker-view {
	height: 400rpx; /* 进一步增加高度，多端适配 */
	padding: 24rpx 30rpx; /* 增加内边距 */
	/* 确保picker-view有足够的空间进行吸附 */
	overflow: hidden;
	/* 增强磁性吸附效果 */
	scroll-behavior: smooth;
	position: relative;

	/* 优化选择区域指示器 */
	background: linear-gradient(180deg,
		rgba(248, 249, 250, 0.98) 0%,
		rgba(248, 249, 250, 0.8) 30%,
		rgba(248, 249, 250, 0.3) 40%,
		transparent 50%,
		rgba(248, 249, 250, 0.3) 60%,
		rgba(248, 249, 250, 0.8) 70%,
		rgba(248, 249, 250, 0.98) 100%
	);
}

/* 中心选择指示器 - 多端适配优化 */
.picker-view::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 30rpx; /* 增加左右边距 */
	right: 30rpx;
	height: 88rpx; /* 增加高度 */
	transform: translateY(-50%);

	/* 更明显的背景渐变 */
	background: linear-gradient(135deg,
		rgba(220, 20, 60, 0.06) 0%,
		rgba(255, 182, 193, 0.12) 25%,
		rgba(255, 255, 255, 0.20) 50%,
		rgba(255, 182, 193, 0.12) 75%,
		rgba(220, 20, 60, 0.06) 100%
	);

	/* 更明显的边框 */
	border: 3rpx solid rgba(220, 20, 60, 0.35);
	border-radius: 18rpx;

	/* 增强的阴影效果 */
	box-shadow:
		0 4rpx 12rpx rgba(220, 20, 60, 0.12),
		0 12rpx 28rpx rgba(220, 20, 60, 0.08),
		inset 0 3rpx 6rpx rgba(255, 255, 255, 0.6),
		inset 0 -2rpx 4rpx rgba(220, 20, 60, 0.1);

	/* 添加动画效果 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	pointer-events: none;
	z-index: 10;
}

/* 强制picker-view内部元素精确对齐 */
.picker-view ::v-deep .uni-picker-view-group {
	height: 88rpx !important;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-view ::v-deep .uni-picker-view-content {
	padding: 0 !important;
	margin: 0 !important;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-view ::v-deep .uni-picker-view-indicator {
	height: 88rpx !important;
	background: transparent !important;
	border: none !important;
}

.picker-view ::v-deep .uni-picker-view-mask {
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.98) 0%,
		rgba(255, 255, 255, 0.7) 35%,
		rgba(255, 255, 255, 0.2) 45%,
		transparent 50%,
		rgba(255, 255, 255, 0.2) 55%,
		rgba(255, 255, 255, 0.7) 65%,
		rgba(255, 255, 255, 0.98) 100%) !important;
}

.picker-item {
	height: 80rpx; /* 增加高度，与指示器保持一致 */
	line-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx; /* 增大字体，提升可读性 */
	color: #333;
	font-weight: 500;
	text-align: center;
	box-sizing: border-box;
	position: relative;

	/* 磁性吸附效果 */
	scroll-snap-align: center;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	/* 优化字体渲染 */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* 选中状态的时间项样式 */
.picker-item:nth-child(3) {
	color: #dc143c;
	font-weight: 600;
	font-size: 36rpx;
	transform: scale(1.08);
	text-shadow: 0 2rpx 4rpx rgba(220, 20, 60, 0.2);
}

/* 相邻项目的渐变效果 */
.picker-item:nth-child(2),
.picker-item:nth-child(4) {
	color: #666;
	font-size: 30rpx;
	opacity: 0.8;
}

.picker-item:nth-child(1),
.picker-item:nth-child(5) {
	color: #999;
	font-size: 28rpx;
	opacity: 0.6;
}

.picker-footer {
	display: flex;
	padding: 24rpx 30rpx 30rpx;
	gap: 24rpx;
	background: #f8f9fa;
	border-top: 1rpx solid #e0e0e0;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 72rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
	border: 1rpx solid #ddd;
}

.btn-cancel:hover {
	background: #e9e9e9;
}

.btn-confirm {
	background: #007aff;
	color: white;
}

.btn-confirm:hover {
	background: #0056d3;
}

/* 多端响应式优化 */

/* 小屏设备 (手机竖屏) */
@media (max-width: 750rpx) {
	.picker-content {
		max-width: 680rpx;
		min-width: 500rpx;
	}

	.picker-view {
		height: 360rpx;
		padding: 20rpx 25rpx;
	}

	.picker-view::before {
		left: 25rpx;
		right: 25rpx;
		height: 80rpx;
	}

	.picker-item {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 30rpx;
	}

	.picker-item:nth-child(3) {
		font-size: 34rpx;
	}

	.picker-footer {
		padding: 20rpx 25rpx 30rpx;
		gap: 20rpx;
	}

	.btn-cancel, .btn-confirm {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
	}
}

/* 中等屏幕 (平板) */
@media (min-width: 751rpx) and (max-width: 1200rpx) {
	.picker-content {
		max-width: 800rpx;
	}

	.picker-view {
		height: 420rpx;
	}

	.picker-view::before {
		height: 90rpx;
	}

	.picker-item {
		height: 90rpx;
		line-height: 90rpx;
		font-size: 34rpx;
	}

	.picker-item:nth-child(3) {
		font-size: 38rpx;
	}
}

/* 大屏设备 (桌面端) */
@media (min-width: 1201rpx) {
	.picker-content {
		max-width: 900rpx;
	}

	.picker-view {
		height: 440rpx;
	}

	.picker-view::before {
		height: 95rpx;
	}

	.picker-item {
		height: 95rpx;
		line-height: 95rpx;
		font-size: 36rpx;
	}

	.picker-item:nth-child(3) {
		font-size: 40rpx;
	}

	.btn-cancel, .btn-confirm {
		height: 88rpx;
		line-height: 88rpx;
		font-size: 30rpx;
	}
}
</style>
