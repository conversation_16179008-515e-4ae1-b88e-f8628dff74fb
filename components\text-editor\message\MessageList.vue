<template>
  <view class="messages-container">
    <view
      class="message-item"
      v-for="(message, index) in messages"
      :key="index"
      :class="message.type === 'ai' ? 'ai-message-item' : 'user-message-item'"
    >
      <view class="message" :class="message.type === 'ai' ? 'ai-message' : 'user-message'">
        <!-- 根据消息类型调整头像位置，移除时间显示 -->
        <view class="message-header" :class="{'user-header': message.type === 'user'}">
          <view class="avatar"
            :class="message.type === 'ai' ? 'ai-avatar' : 'user-avatar'"
            :style="{
              backgroundColor: message.type === 'ai' ? '#2563eb' : '#2196f3',
              color: '#f0d78c',
              fontSize: '12px',
              fontWeight: 'normal',
              boxShadow: message.type === 'ai' ? '0 0 10px rgba(37, 99, 235, 0.5)' : '0 0 10px rgba(33, 150, 243, 0.5)',
              width: '28px',
              height: '28px'
            }">
            <text style="color: #f0d78c; font-size: 12px; font-weight: normal;">{{ message.type === 'ai' ? 'AI' : '您' }}</text>
          </view>
        </view>

        <view class="message-content">
          <view class="message-bubble" :class="{'selectable-text': true, 'ai-message-bubble': message.type === 'ai', 'user-message-bubble': message.type === 'user'}">
            <text class="message-text" :class="{'selectable-text': true}" @longpress="enableTextSelection">{{ message.content }}</text>

            <!-- 媒体内容 - 仅用户消息显示 -->
            <view class="message-media-content" v-if="message.type === 'user' && message.media && message.media.length > 0">
              <view class="message-media-list">
                <view
                  class="message-media-item"
                  v-for="(media, mediaIndex) in message.media"
                  :key="mediaIndex"
                  :class="media.type"
                >
                  <!-- 图片预览 -->
                  <image
                    v-if="media.type === 'image'"
                    class="media-preview-image"
                    :src="media.src"
                    mode="aspectFit"
                    @tap="previewImage(media.src)"
                    @longtap="handleImageLongPress(media.src)"
                  />

                  <!-- 文档预览 -->
                  <view v-if="media.type === 'document'" class="message-media-doc" @tap="openDocument(media)">
                    <text class="message-doc-icon">📄</text>
                    <text class="message-doc-name">{{media.name}}</text>
                    <text v-if="media.size" class="message-doc-size">{{media.size}}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 消息操作按钮 - AI和用户消息都显示 -->
            <view class="message-actions" v-if="message.content">
              <view class="action-btn copy-btn" @tap="copyMessage(message.content)">
                <text class="action-text">复制</text>
              </view>
              <view class="action-btn speak-btn" @tap="speakMessage(message.content, index)" v-if="message.type === 'ai'" :class="{'speaking': isSpeaking && currentSpeakingIndex === index}">
                <text class="action-text">{{ isSpeaking && currentSpeakingIndex === index ? '停止' : '播放' }}</text>
              </view>
              <view class="action-btn regenerate-btn" @tap="regenerateMessage(index)" v-if="message.type === 'ai' && index === messages.length - 1 && !isGenerating">
                <text class="action-text">重生成</text>
                <text class="regenerate-cost">-{{ coinCost }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 将消息时间移至内容下方 -->
        <view class="message-time" :class="{'user-message-time': message.type === 'user'}">
          {{ formatTime(message.timestamp) }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MessageList',
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    isGenerating: {
      type: Boolean,
      default: false
    },
    coinCost: {
      type: Number,
      default: 10
    },
    isSpeaking: {
      type: Boolean,
      default: false
    },
    currentSpeakingIndex: {
      type: Number,
      default: -1
    }
  },
  methods: {
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;

      // 如果是今天
      if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
      }

      // 如果是昨天
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      if (date.getDate() === yesterday.getDate() &&
          date.getMonth() === yesterday.getMonth() &&
          date.getFullYear() === yesterday.getFullYear()) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
      }

      // 其他日期
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },

    // 启用文本选择
    enableTextSelection(e) {
      // 长按启用文本选择功能
      console.log('启用文本选择');
    },

    // 预览图片
    previewImage(src) {
      this.$emit('preview-image', src);
    },

    // 打开文档
    openDocument(media) {
      this.$emit('open-document', media);
    },

    // 复制消息
    copyMessage(content) {
      this.$emit('copy-message', content);
    },

    // 语音播放
    speakMessage(content, index) {
      this.$emit('speak-message', content, index);
    },

    // 重新生成消息
    regenerateMessage(index) {
      this.$emit('regenerate-message', index);
    },

    // 处理图片长按
    handleImageLongPress(imageSrc) {
      this.$emit('image-long-press', imageSrc);
    }
  }
}
</script>

<style scoped>
.messages-container {
  width: 100%;
  padding: 10px 15px;
  padding-bottom: 160px; /* 增加底部内边距，为输入区域和媒体预览区预留空间 */
}

/* 消息样式 */
.message-item {
  margin-bottom: 20px;
  width: 100%;
}

.message {
  display: flex;
  flex-direction: column; /* 修改为纵向排列 */
  max-width: 94%; /* 调整最大宽度，更充分利用空间 */
  margin: 0 auto; /* 居中显示 */
}

.ai-message-item {
  align-items: flex-start;
  width: 100%;
}

.user-message-item {
  align-items: flex-end;
}

.avatar {
  width: 28px; /* 减小头像尺寸 */
  height: 28px; /* 减小头像尺寸 */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  color: #f0d78c; /* 淡金黄色文字 */
  font-weight: normal; /* 移除文字加粗 */
  font-size: 12px; /* 减小文字大小 */
}

.ai-avatar {
  background-color: #2563eb; /* 保持背景颜色不变 */
  box-shadow: 0 0 10px rgba(37, 99, 235, 0.5); /* 保持光晕效果 */
}

.user-avatar {
  background-color: #2196f3; /* 保持背景颜色不变 */
  box-shadow: 0 0 10px rgba(33, 150, 243, 0.5); /* 保持光晕效果 */
}

.message-content {
  flex: 1;
  width: 100%; /* 调整宽度占满 */
}

/* 调整消息气泡样式，确保内部有足够空间放置操作按钮 */
.message-bubble {
  padding: 12px;
  padding-bottom: 40px; /* 为操作按钮预留更多空间 */
  border-radius: 12px;
  max-width: 100%; /* 调整为100%宽度 */
  position: relative;
  word-break: break-word;
  overflow-wrap: break-word;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%; /* 确保气泡占满内容区 */
}

.ai-message .message-bubble {
  background-color: #0f3460;
  border-radius: 12px; /* 调整为四角都是圆角 */
  color: #ffffff;
}

.user-message .message-bubble {
  background-color: #1e88e5;
  border-radius: 12px; /* 调整为四角都是圆角 */
  color: #ffffff;
}

.message-text {
  font-size: 15px;
  line-height: 1.5;
  white-space: pre-wrap;
  direction: ltr;
  color: #ffffff;
}

.message-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
  text-align: right;
}

/* 用户消息时间靠右对齐 */
.user-message-time {
  text-align: right;
  padding-right: 5px;
}

/* 消息操作按钮放在气泡内底部 */
.message-actions {
  position: absolute;
  bottom: 5px;
  right: 10px;
  display: flex;
  gap: 6px;
  align-items: center;
  z-index: 10;
}

/* 用户消息的操作按钮位置调整 */
.user-message .message-actions {
  right: 10px;
  bottom: 5px;
}

/* 操作按钮样式 */
.action-btn {
  margin-right: 10px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.25);
}

.action-btn:hover, .action-btn:active {
  color: rgba(255, 255, 255, 1);
}

.action-text {
  font-size: 12px;
  margin-left: 2px;
}

.regenerate-cost {
  font-size: 10px;
  color: #ffd700;
  margin-left: 4px;
}

/* 正在播放状态的样式 */
.speak-btn.speaking {
  animation: pulse-speak 1.5s infinite alternate;
  color: #4CAF50;
}

@keyframes pulse-speak {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* 媒体内容样式 */
.message-media-content {
  margin-top: 8px;
}

.message-media-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-media-item {
  max-width: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.media-preview-image {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.message-media-doc {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
}

.message-doc-icon {
  font-size: 20px;
  margin-right: 8px;
}

.message-doc-name {
  font-size: 14px;
  color: #ffffff;
  flex: 1;
}

.message-doc-size {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 8px;
}

/* 可选择文本样式 */
.selectable-text {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* 长按高亮效果 */
.message-text:active {
  background-color: rgba(77, 157, 255, 0.1);
}

/* AI消息气泡样式特殊处理 */
.ai-message-bubble {
  max-width: 100%; /* AI消息气泡宽度最大化 */
  width: calc(100% - 10px); /* 设置宽度为100%减去右侧边距 */
}

/* 新增：消息头部样式 */
.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  height: 32px;
}

/* AI消息头像在左 */
.ai-message .message-header {
  justify-content: flex-start;
}

/* 用户消息头像在右 */
.user-header {
  flex-direction: row-reverse;
  justify-content: flex-start;
}
</style>