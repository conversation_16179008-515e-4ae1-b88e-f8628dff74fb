<template>
  <view class="smart-textarea-container" :class="containerClasses">
    <!-- 原生textarea组件 -->
    <textarea
      ref="textareaRef"
      class="smart-textarea"
      :class="textareaClasses"
      v-model="inputValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :auto-height="autoHeight"
      :cursor-spacing="cursorSpacing"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @paste="handlePaste"
      @keyboardheightchange="handleKeyboardHeightChange"
      @selectionchange="handleSelectionChange"
    />
    
    <!-- 文本选择菜单 -->
    <view
      v-if="showSelectionMenu"
      class="selection-menu"
      :style="selectionMenuStyle"
    >
      <view class="menu-btn" @click="copySelectedText">
        <text class="btn-icon">📋</text>
        <text class="btn-text">复制</text>
      </view>
      <view class="menu-btn" @click="cutSelectedText">
        <text class="btn-icon">✂️</text>
        <text class="btn-text">剪切</text>
      </view>
      <view class="menu-btn" @click="selectAll">
        <text class="btn-icon">📝</text>
        <text class="btn-text">全选</text>
      </view>
      <view class="menu-btn" @click="deleteSelectedText">
        <text class="btn-icon">🗑️</text>
        <text class="btn-text">删除</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SmartTextArea',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean,
      default: true
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: false
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    minHeight: {
      type: Number,
      default: 100
    },
    maxHeight: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      inputValue: this.value,
      isFocused: false,
      isKeyboardVisible: false,
      showSelectionMenu: false,
      selectionMenuPosition: { x: 0, y: 0 },
      
      // 事件处理
      _dblclickHandler: null,
      
      // 平台检测
      isH5: false,
      isApp: false,
      isMobile: false
    };
  },
  computed: {
    containerClasses() {
      return {
        'focused': this.isFocused,
        'keyboard-visible': this.isKeyboardVisible,
        'h5-platform': this.isH5,
        'app-platform': this.isApp,
        'mobile-device': this.isMobile
      };
    },
    
    textareaClasses() {
      return {
        'editing': this.isFocused,
        'scrollable': !this.isFocused && this.inputValue.length > 100
      };
    },
    
    textareaStyle() {
      return {
        minHeight: this.minHeight + 'px',
        maxHeight: this.maxHeight + 'px'
      };
    },
    
    selectionMenuStyle() {
      return {
        left: this.selectionMenuPosition.x + 'px',
        top: this.selectionMenuPosition.y + 'px'
      };
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal !== this.inputValue) {
          this.inputValue = newVal;
        }
      },
      immediate: true
    },
    
    inputValue(newVal) {
      this.$emit('input', newVal);
      this.$emit('update:value', newVal);
    }
  },
  mounted() {
    this.detectPlatform();
    this.initTextarea();

    // 延迟设置事件监听器，确保DOM已渲染
    this.$nextTick(() => {
      // 先解决可能的事件冲突
      this.resolveEventConflicts();

      // 然后设置原生事件监听器
      this.setupNativeEventListeners();
    });
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    // 平台检测
    detectPlatform() {
      // #ifdef H5
      this.isH5 = true;
      // #endif
      
      // #ifdef APP-PLUS
      this.isApp = true;
      // #endif
      
      // 检测移动设备
      if (typeof navigator !== 'undefined') {
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      } else {
        this.isMobile = true; // 非H5环境默认为移动设备
      }
    },
    
    // 初始化文本框
    initTextarea() {
      console.log('SmartTextArea 初始化完成');
    },
    
    // 处理输入
    handleInput(e) {
      const value = e.detail.value;
      this.inputValue = value;
    },
    
    // 处理焦点获得
    handleFocus(e) {
      console.log('文本框获得焦点');
      this.isFocused = true;
      this.hideSelectionMenu();
      this.$emit('focus', e);
    },
    
    // 处理焦点失去
    handleBlur(e) {
      console.log('文本框失去焦点');
      this.isFocused = false;
      this.hideSelectionMenu();
      this.$emit('blur', e);
    },
    
    // 处理确认
    handleConfirm(e) {
      this.$emit('confirm', e);
    },
    
    // 处理粘贴
    handlePaste(e) {
      this.$emit('paste', e);
    },
    
    // 处理键盘高度变化
    handleKeyboardHeightChange(e) {
      const height = e.detail.height;
      this.isKeyboardVisible = height > 0;

      console.log('键盘高度变化:', height);
      this.$emit('keyboardheightchange', e);
    },

    // 处理选择变化
    handleSelectionChange(e) {
      const start = e.detail.selectionStart;
      const end = e.detail.selectionEnd;

      console.log('选择变化:', start, end);

      if (start !== end) {
        // 有文本被选中，显示选择菜单
        const selectedText = this.inputValue.substring(start, end);
        console.log('选中文本:', selectedText);

        // 延迟显示菜单，避免与点击事件冲突
        setTimeout(() => {
          if (this.getSelectedText()) {
            this.showSelectionMenuAtCenter();
          }
        }, 100);
      } else {
        // 没有选中文本，隐藏菜单
        this.hideSelectionMenu();
      }
    },
    
    // 简化的双击处理 - 通过原生事件监听
    setupNativeEventListeners() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;

      // 清理旧的监听器
      this.cleanupNativeEventListeners();

      // 添加双击监听器
      this._dblclickHandler = (e) => {
        console.log('原生双击事件');
        this.handleDoubleClick(e);
      };

      textarea.addEventListener('dblclick', this._dblclickHandler);

      console.log('已设置原生事件监听器');
    },

    // 清理原生事件监听器
    cleanupNativeEventListeners() {
      const textarea = this.$refs.textareaRef;
      if (textarea && this._dblclickHandler) {
        textarea.removeEventListener('dblclick', this._dblclickHandler);
        this._dblclickHandler = null;
      }
    },

    // 解决事件冲突
    resolveEventConflicts() {
      try {
        console.log('🔧 SmartTextArea: 开始解决事件冲突...');

        // 检测并清理可能的全局点击监听器冲突
        this.clearGlobalClickConflicts();

        // 清理可能的触摸事件冲突
        this.clearTouchEventConflicts();

        console.log('✅ SmartTextArea: 事件冲突解决完成');
      } catch (error) {
        console.error('❌ SmartTextArea: 解决事件冲突时发生错误:', error);
      }
    },

    // 清理全局点击冲突
    clearGlobalClickConflicts() {
      if (typeof document === 'undefined') return;

      // 临时存储当前的全局点击监听器
      const existingHandlers = [];

      // 创建一个测试事件来检测是否有捕获阶段的监听器
      const testEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        clientX: 0,
        clientY: 0
      });

      let captureDetected = false;
      const testHandler = (e) => {
        captureDetected = true;
        e.stopImmediatePropagation();
      };

      // 添加测试监听器
      document.addEventListener('click', testHandler, true);

      // 分发测试事件
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.dispatchEvent(testEvent);
      }

      // 移除测试监听器
      document.removeEventListener('click', testHandler, true);

      if (captureDetected) {
        console.log('🔧 检测到全局点击捕获监听器，已处理冲突');
      }
    },

    // 清理触摸事件冲突
    clearTouchEventConflicts() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;

      // 清理可能的自定义事件处理器
      if (textarea._eventHandlers) {
        const touchEvents = ['touchstart', 'touchmove', 'touchend'];
        touchEvents.forEach(eventType => {
          const handler = textarea._eventHandlers[eventType];
          if (handler) {
            textarea.removeEventListener(eventType, handler);
            console.log(`🔧 已清理冲突的 ${eventType} 处理器`);
          }
        });
        textarea._eventHandlers = null;
      }

      // 清理可能的状态属性
      if (textarea._selectionState) {
        textarea._selectionState = null;
      }
      if (textarea._touchState) {
        textarea._touchState = null;
      }
    },



    // 清理资源时清理canvas
    cleanupMeasureCanvas() {
      if (this._measureCanvas) {
        this._measureCanvas = null;
        this._measureContext = null;
      }
    },

    // 从DOM节点获取文本偏移
    getTextOffsetFromNode(node, offset) {
      // 这个方法用于处理caretPositionFromPoint的结果
      try {
        const textarea = this.$refs.textareaRef;
        if (!textarea || node !== textarea) return 0;

        return Math.min(offset, this.inputValue.length);
      } catch (error) {
        console.warn('从节点获取偏移失败:', error);
        return 0;
      }
    },

    // 从Range获取文本偏移
    getTextOffsetFromRange(range) {
      try {
        const textarea = this.$refs.textareaRef;
        if (!textarea) return 0;

        // 创建一个从textarea开始到range开始的范围
        const fullRange = document.createRange();
        fullRange.setStart(textarea, 0);
        fullRange.setEnd(range.startContainer, range.startOffset);

        const textContent = fullRange.toString();
        return Math.min(textContent.length, this.inputValue.length);
      } catch (error) {
        console.warn('从Range获取偏移失败:', error);
        return 0;
      }
    },

    // 优化的获取事件光标位置
    getCursorPositionFromEvent(e) {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return -1;

      let clientX, clientY;

      if (e.touches && e.touches[0]) {
        clientX = e.touches[0].clientX;
        clientY = e.touches[0].clientY;
      } else if (e.changedTouches && e.changedTouches[0]) {
        clientX = e.changedTouches[0].clientX;
        clientY = e.changedTouches[0].clientY;
      } else {
        clientX = e.clientX || 0;
        clientY = e.clientY || 0;
      }

      return this.getAccurateCursorPosition(textarea, clientX, clientY);
    },
    
    // 处理双击 - 简化版本
    handleDoubleClick(e) {
      console.log('双击检测到，显示选择菜单');

      // 延迟显示菜单，让原生双击选择先完成
      setTimeout(() => {
        const textarea = this.$refs.textareaRef;
        if (textarea) {
          const start = textarea.selectionStart;
          const end = textarea.selectionEnd;

          if (start !== end) {
            // 有文本被选中，显示菜单
            this.showSelectionMenuAtPosition(e);
            console.log(`已选中文本: "${this.inputValue.substring(start, end)}"`);
          }
        }
      }, 100);
    },



    // 查找单词边界
    findWordBounds(position) {
      const text = this.inputValue;
      let start = position;
      let end = position;

      // 单词边界字符
      const wordBoundary = /[\s\n\t,.;:'"!?()[\]{}\/\\|<>+=*&^%$#@~`-]/;

      // 向前查找单词开始
      while (start > 0 && !wordBoundary.test(text[start - 1])) {
        start--;
      }

      // 向后查找单词结束
      while (end < text.length && !wordBoundary.test(text[end])) {
        end++;
      }

      return { start, end };
    },
    

    
    // 聚焦文本框
    focusTextarea() {
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.focus();
      }
    },
    
    // 失焦文本框
    blurTextarea() {
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.blur();
      }
    },
    
    // 在指定位置显示选择菜单
    showSelectionMenuAtPosition(e) {
      // 计算菜单位置
      let x = 100, y = 50;
      
      if (e && e.touches && e.touches[0]) {
        x = e.touches[0].clientX - 60;
        y = e.touches[0].clientY - 60;
      } else if (e && e.clientX) {
        x = e.clientX - 60;
        y = e.clientY - 60;
      }
      
      // 确保菜单在屏幕内
      const screenWidth = uni.getSystemInfoSync().windowWidth;
      const screenHeight = uni.getSystemInfoSync().windowHeight;
      
      x = Math.max(10, Math.min(x, screenWidth - 200));
      y = Math.max(10, Math.min(y, screenHeight - 100));
      
      this.selectionMenuPosition = { x, y };
      this.showSelectionMenu = true;
      
      // 3秒后自动隐藏
      setTimeout(() => {
        this.hideSelectionMenu();
      }, 3000);
    },
    
    // 隐藏选择菜单
    hideSelectionMenu() {
      this.showSelectionMenu = false;
    },
    
    // 复制选中文本
    copySelectedText() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;

      const selectedText = this.getSelectedText();
      if (!selectedText) {
        uni.showToast({ title: '没有选中文本', icon: 'none' });
        return;
      }

      // 复制到剪贴板
      this.copyToClipboard(selectedText);
      this.hideSelectionMenu();
    },

    // 剪切选中文本
    cutSelectedText() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;

      const selectedText = this.getSelectedText();
      if (!selectedText) {
        uni.showToast({ title: '没有选中文本', icon: 'none' });
        return;
      }

      // 复制到剪贴板
      this.copyToClipboard(selectedText);

      // 删除选中文本
      this.deleteSelectedText();
    },

    // 删除选中文本
    deleteSelectedText() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      if (start === end) {
        uni.showToast({ title: '没有选中文本', icon: 'none' });
        return;
      }

      // 删除选中的文本
      const newValue = this.inputValue.substring(0, start) + this.inputValue.substring(end);
      this.inputValue = newValue;

      // 设置光标位置
      this.$nextTick(() => {
        textarea.setSelectionRange(start, start);
      });

      uni.showToast({ title: '已删除', icon: 'success' });
      this.hideSelectionMenu();
    },

    // 获取选中文本
    getSelectedText() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return '';

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      if (start === end) return '';

      return this.inputValue.substring(start, end);
    },

    // 复制到剪贴板
    copyToClipboard(text) {
      if (this.isH5) {
        try {
          if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
              uni.showToast({ title: '已复制', icon: 'success' });
            });
          } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            uni.showToast({ title: '已复制', icon: 'success' });
          }
        } catch (err) {
          console.warn('复制失败:', err);
          uni.showToast({ title: '复制失败', icon: 'none' });
        }
      } else {
        // 非H5环境
        uni.setClipboardData({
          data: text,
          success: () => {
            uni.showToast({ title: '已复制', icon: 'success' });
          },
          fail: () => {
            uni.showToast({ title: '复制失败', icon: 'none' });
          }
        });
      }
    },
    
    // 全选文本
    selectAll() {
      const textarea = this.$refs.textareaRef;
      if (textarea && this.inputValue) {
        textarea.setSelectionRange(0, this.inputValue.length);
        uni.showToast({ title: '已全选', icon: 'none' });

        // 重新显示选择菜单
        this.$nextTick(() => {
          this.showSelectionMenuAtCenter();
        });
      } else {
        uni.showToast({ title: '没有内容可选', icon: 'none' });
        this.hideSelectionMenu();
      }
    },

    // 在屏幕中央显示选择菜单
    showSelectionMenuAtCenter() {
      const screenWidth = uni.getSystemInfoSync().windowWidth;
      const screenHeight = uni.getSystemInfoSync().windowHeight;

      this.selectionMenuPosition = {
        x: screenWidth / 2 - 100,
        y: screenHeight / 2 - 50
      };

      this.showSelectionMenu = true;

      // 5秒后自动隐藏
      setTimeout(() => {
        this.hideSelectionMenu();
      }, 5000);
    },
    
    // 清理资源
    cleanup() {
      this.hideSelectionMenu();
      this.cleanupMeasureCanvas();
      this.cleanupNativeEventListeners();
      console.log('SmartTextArea 资源清理完成');
    }
  }
}
</script>

<style scoped>
.smart-textarea-container {
  position: relative;
  width: 100%;
}

.smart-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  background: #fff;
  resize: none;
  outline: none;
  transition: border-color 0.2s ease;
}

.smart-textarea.editing {
  border-color: #007AFF;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.smart-textarea.scrollable {
  overflow-y: auto;
}

/* 选择菜单 */
.selection-menu {
  position: fixed;
  display: flex;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px;
  padding: 8px;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.menu-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  margin: 0 4px;
  border-radius: 8px;
  min-width: 50px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.menu-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.btn-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.btn-text {
  font-size: 11px;
  color: white;
  font-weight: 500;
}

/* 平台适配 */
.h5-platform .smart-textarea {
  -webkit-user-select: text;
  user-select: text;
}

.mobile-device .menu-btn {
  min-width: 60px;
  padding: 10px 14px;
}

.mobile-device .btn-icon {
  font-size: 20px;
}

.mobile-device .btn-text {
  font-size: 12px;
}

/* 键盘显示时的样式 */
.keyboard-visible .smart-textarea {
  border-color: #007AFF;
}

/* 聚焦状态 */
.focused .smart-textarea {
  border-color: #007AFF;
}
</style>
