<template>
	<view v-if="visible" class="simple-picker-overlay" @click="close">
		<view class="simple-picker-content" @click.stop>
			<view class="picker-header">
				<text class="picker-title">选择出生日期</text>
			</view>
			
			<view class="date-inputs">
				<view class="input-item">
					<text class="input-label">年</text>
					<input 
						class="date-input" 
						type="number" 
						v-model="selectedYear" 
						@input="validateYear"
						:min="1900" 
						:max="2045"
					/>
				</view>
				
				<view class="input-item">
					<text class="input-label">月</text>
					<input 
						class="date-input" 
						type="number" 
						v-model="selectedMonth" 
						@input="validateMonth"
						:min="1" 
						:max="12"
					/>
				</view>
				
				<view class="input-item">
					<text class="input-label">日</text>
					<input 
						class="date-input" 
						type="number" 
						v-model="selectedDay" 
						@input="validateDay"
						:min="1" 
						:max="daysInMonth"
					/>
				</view>
			</view>
			
			<view class="picker-buttons">
				<button class="btn btn-cancel" @click="close">取消</button>
				<button class="btn btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'SimpleDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const now = new Date();
		return {
			selectedYear: now.getFullYear(),
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate()
		}
	},
	computed: {
		daysInMonth() {
			if (this.selectedYear && this.selectedMonth) {
				return new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			}
			return 31;
		}
	},
	watch: {
		selectedMonth() {
			if (this.selectedDay > this.daysInMonth) {
				this.selectedDay = this.daysInMonth;
			}
		},
		selectedYear() {
			if (this.selectedDay > this.daysInMonth) {
				this.selectedDay = this.daysInMonth;
			}
		}
	},
	methods: {
		validateYear() {
			if (this.selectedYear < 1900) this.selectedYear = 1900;
			if (this.selectedYear > 2045) this.selectedYear = 2045;
		},
		validateMonth() {
			if (this.selectedMonth < 1) this.selectedMonth = 1;
			if (this.selectedMonth > 12) this.selectedMonth = 12;
		},
		validateDay() {
			if (this.selectedDay < 1) this.selectedDay = 1;
			if (this.selectedDay > this.daysInMonth) this.selectedDay = this.daysInMonth;
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const dateStr = `${this.selectedYear}-${String(this.selectedMonth).padStart(2, '0')}-${String(this.selectedDay).padStart(2, '0')}`;
			this.$emit('confirm', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				dateString: dateStr
			});
		}
	}
}
</script>

<style scoped>
.simple-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.simple-picker-content {
	background: #F5E6D3;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 40rpx;
	max-width: 500rpx;
	width: 90%;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.picker-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.picker-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #8B4513;
}

.date-inputs {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.input-item {
	flex: 1;
	text-align: center;
}

.input-label {
	display: block;
	font-size: 28rpx;
	color: #8B4513;
	margin-bottom: 10rpx;
	font-weight: bold;
}

.date-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #8B4513;
	border-radius: 12rpx;
	text-align: center;
	font-size: 32rpx;
	color: #8B4513;
	background: white;
	padding: 0 10rpx;
}

.picker-buttons {
	display: flex;
	gap: 30rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
}

.btn-cancel {
	background: #F5E6D3;
	color: #8B4513;
	border: 2rpx solid #8B4513;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}
</style>
