/**
 * 姓名配对业务逻辑接口
 * 处理姓名配对的核心业务逻辑、费用计算、结果格式化
 * 创建时间：2025-01-11
 */

import { 获取用户信息, 检查用户登录状态 } from '../用户系统/用户系统接口.js';
import { 查询用户余额, 扣除用户金币, 退还用户金币 } from '../支付系统/支付系统接口.js';

/**
 * 姓名配对错误码定义
 */
export const 姓名配对错误码 = {
    INVALID_NAME_FORMAT: { code: 'MATCH_001', message: '姓名格式不正确' },
    SAME_NAME_ERROR: { code: 'MATCH_002', message: '不能与自己配对' },
    INVALID_GENDER: { code: 'MATCH_003', message: '性别参数无效' },
    INVALID_BIRTH_INFO: { code: 'MATCH_004', message: '出生信息格式不正确' },
    MISSING_REQUIRED_FIELD: { code: 'MATCH_005', message: '缺少必需参数' },
    USER_NOT_LOGGED_IN: { code: 'MATCH_006', message: '用户未登录' },
    INSUFFICIENT_COINS: { code: 'MATCH_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'MATCH_008', message: '工作流执行超时' },
    ANALYSIS_FAILED: { code: 'MATCH_009', message: '配对分析失败' }
};

/**
 * 姓名配对费用配置
 */
export const 姓名配对费用配置 = {
    基础费用: 12,
    会员折扣: 0.8,
    详细分析加费: 5,
    专业分析加费: 10
};

/**
 * 结构化参数构建器 - 姓名配对专用
 */
export class 姓名配对参数构建器 {
    constructor() {
        this.params = {};
    }

    /**
     * 添加基础信息参数
     */
    添加基础信息(name1, gender1, name2, gender2) {
        this.params.name1 = {
            type: "text",
            value: name1,
            placeholder: "{{name1}}",
            description: "第一个人的姓名"
        };
        
        this.params.gender1 = {
            type: "text", 
            value: gender1,
            placeholder: "{{gender1}}",
            description: "第一个人的性别"
        };
        
        this.params.name2 = {
            type: "text",
            value: name2,
            placeholder: "{{name2}}",
            description: "第二个人的姓名"
        };
        
        this.params.gender2 = {
            type: "text",
            value: gender2,
            placeholder: "{{gender2}}",
            description: "第二个人的性别"
        };
        
        return this;
    }

    /**
     * 添加出生信息参数（可选）
     */
    添加出生信息(birthDate1, birthTime1, birthDate2, birthTime2) {
        if (birthDate1 && birthTime1) {
            this.params.birthDate1 = {
                type: "text",
                value: birthDate1,
                placeholder: "{{birthDate1}}",
                description: "第一个人的出生日期"
            };
            
            this.params.birthTime1 = {
                type: "text",
                value: birthTime1,
                placeholder: "{{birthTime1}}",
                description: "第一个人的出生时间"
            };
        }
        
        if (birthDate2 && birthTime2) {
            this.params.birthDate2 = {
                type: "text",
                value: birthDate2,
                placeholder: "{{birthDate2}}",
                description: "第二个人的出生日期"
            };
            
            this.params.birthTime2 = {
                type: "text",
                value: birthTime2,
                placeholder: "{{birthTime2}}",
                description: "第二个人的出生时间"
            };
        }
        
        return this;
    }

    /**
     * 添加分析类型参数
     */
    添加分析类型(analysisType = 'basic') {
        this.params.analysisType = {
            type: "config",
            value: analysisType,
            placeholder: "{{analysisType}}",
            description: "分析类型：basic-基础分析, detailed-详细分析, professional-专业分析"
        };
        
        return this;
    }

    /**
     * 添加返回格式要求
     */
    添加返回格式要求() {
        this.params.returnFormat = {
            type: "config",
            value: {
                includeScore: true,
                includeAnalysis: true,
                includeAdvice: true,
                includeCompatibility: true,
                includeElementAnalysis: true,
                format: "structured_json"
            },
            placeholder: "{{returnFormat}}",
            description: "指定返回内容的格式和包含的信息"
        };
        
        return this;
    }

    /**
     * 构建最终参数
     */
    构建() {
        return this.params;
    }
}

/**
 * 姓名配对业务逻辑处理器
 */
export class 姓名配对业务处理器 {
    constructor() {
        this.费用配置 = 姓名配对费用配置;
    }

    /**
     * 验证输入参数
     */
    验证参数(formData) {
        const errors = [];

        // 验证必需字段
        if (!formData.name1 || !formData.name1.trim()) {
            errors.push(姓名配对错误码.MISSING_REQUIRED_FIELD);
        }

        if (!formData.name2 || !formData.name2.trim()) {
            errors.push(姓名配对错误码.MISSING_REQUIRED_FIELD);
        }

        // 验证姓名格式
        const nameRegex = /^[\u4e00-\u9fa5]{2,4}$/;
        if (formData.name1 && !nameRegex.test(formData.name1.trim())) {
            errors.push(姓名配对错误码.INVALID_NAME_FORMAT);
        }

        if (formData.name2 && !nameRegex.test(formData.name2.trim())) {
            errors.push(姓名配对错误码.INVALID_NAME_FORMAT);
        }

        // 验证不能与自己配对
        if (formData.name1 === formData.name2) {
            errors.push(姓名配对错误码.SAME_NAME_ERROR);
        }

        // 验证性别
        const validGenders = ['male', 'female'];
        if (formData.gender1 && !validGenders.includes(formData.gender1)) {
            errors.push(姓名配对错误码.INVALID_GENDER);
        }

        if (formData.gender2 && !validGenders.includes(formData.gender2)) {
            errors.push(姓名配对错误码.INVALID_GENDER);
        }

        // 验证出生信息格式（如果提供）
        if (formData.birthDate1) {
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(formData.birthDate1)) {
                errors.push(姓名配对错误码.INVALID_BIRTH_INFO);
            }
        }

        return errors;
    }

    /**
     * 计算费用
     */
    计算费用(formData, userInfo) {
        let baseCost = this.费用配置.基础费用;

        // 根据分析类型调整费用
        if (formData.analysisType === 'detailed') {
            baseCost += this.费用配置.详细分析加费;
        } else if (formData.analysisType === 'professional') {
            baseCost += this.费用配置.专业分析加费;
        }

        // 会员折扣
        const finalCost = userInfo.isVip ?
            Math.ceil(baseCost * this.费用配置.会员折扣) : baseCost;

        return {
            baseCost,
            finalCost,
            discount: userInfo.isVip ? (baseCost - finalCost) : 0,
            isVip: userInfo.isVip
        };
    }

    /**
     * 构建结构化参数
     */
    构建结构化参数(formData) {
        const builder = new 姓名配对参数构建器();

        // 添加基础信息
        builder.添加基础信息(
            formData.name1,
            formData.gender1 || 'unknown',
            formData.name2,
            formData.gender2 || 'unknown'
        );

        // 添加出生信息（如果有）
        if (formData.birthDate1 || formData.birthDate2) {
            builder.添加出生信息(
                formData.birthDate1,
                formData.birthTime1,
                formData.birthDate2,
                formData.birthTime2
            );
        }

        // 添加分析类型
        builder.添加分析类型(formData.analysisType || 'basic');

        // 添加返回格式要求
        builder.添加返回格式要求();

        return builder.构建();
    }

    /**
     * 格式化工作流返回结果
     */
    格式化返回结果(workflowResult, costInfo) {
        try {
            // 解析工作流返回的结果
            let analysisData;

            if (typeof workflowResult.result === 'string') {
                // 尝试解析JSON字符串
                try {
                    analysisData = JSON.parse(workflowResult.result);
                } catch (e) {
                    // 如果不是JSON，则作为文本处理
                    analysisData = {
                        textResult: workflowResult.result,
                        matchScore: this.提取配对分数(workflowResult.result),
                        analysis: workflowResult.result
                    };
                }
            } else {
                analysisData = workflowResult.result;
            }

            // 标准化返回格式
            return {
                success: true,
                requestId: workflowResult.requestId,
                data: {
                    // 配对基础信息
                    matchInfo: {
                        name1: analysisData.name1 || workflowResult.originalParams?.name1?.value,
                        name2: analysisData.name2 || workflowResult.originalParams?.name2?.value,
                        matchScore: analysisData.matchScore || 0,
                        compatibility: analysisData.compatibility || '未知'
                    },

                    // 详细分析结果
                    analysis: {
                        overall: analysisData.analysis || analysisData.textResult || '分析结果获取失败',
                        strengths: analysisData.strengths || [],
                        challenges: analysisData.challenges || [],
                        advice: analysisData.advice || []
                    },

                    // 五行分析（如果有）
                    elementAnalysis: analysisData.elementAnalysis || null,

                    // 数字分析（如果有）
                    numerologyAnalysis: analysisData.numerologyAnalysis || null
                },

                // 费用信息
                cost: costInfo,

                // 时间戳
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('格式化返回结果失败:', error);

            // 返回基础格式
            return {
                success: true,
                requestId: workflowResult.requestId,
                data: {
                    matchInfo: {
                        name1: workflowResult.originalParams?.name1?.value || '未知',
                        name2: workflowResult.originalParams?.name2?.value || '未知',
                        matchScore: 0,
                        compatibility: '分析失败'
                    },
                    analysis: {
                        overall: workflowResult.result || '分析结果获取失败',
                        strengths: [],
                        challenges: [],
                        advice: []
                    }
                },
                cost: costInfo,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 从文本中提取配对分数
     */
    提取配对分数(text) {
        if (!text) return 0;

        // 尝试匹配分数模式
        const scorePatterns = [
            /配对指数[：:]\s*(\d+)/,
            /匹配度[：:]\s*(\d+)/,
            /分数[：:]\s*(\d+)/,
            /(\d+)分/,
            /(\d+)%/
        ];

        for (const pattern of scorePatterns) {
            const match = text.match(pattern);
            if (match) {
                return parseInt(match[1]);
            }
        }

        return 0;
    }
}

/**
 * 主要业务函数：执行姓名配对分析
 */
export async function 执行姓名配对分析(formData, userInfo, costInfo) {
    const processor = new 姓名配对业务处理器();

    try {
        // 1. 验证参数
        const validationErrors = processor.验证参数(formData);
        if (validationErrors.length > 0) {
            throw new Error(validationErrors[0].message);
        }

        // 2. 构建结构化参数
        const structuredParams = processor.构建结构化参数(formData);

        console.log('📋 构建的结构化参数:', structuredParams);

        return {
            success: true,
            structuredParams,
            message: '参数构建成功，准备发送到工作流'
        };

    } catch (error) {
        console.error('❌ 姓名配对分析失败:', error);
        throw error;
    }
}
