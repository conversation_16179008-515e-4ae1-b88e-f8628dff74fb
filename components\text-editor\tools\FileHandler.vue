<template>
  <view class="file-handler">
    <!-- 文件处理器不需要可视化界面，只提供功能 -->
  </view>
</template>

<script>
export default {
  name: 'FileHandler',
  methods: {
    // 处理图片上传
    handleImageUpload() {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        // 在H5环境中使用文件选择对话框
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.multiple = true;

        input.onchange = (event) => {
          const files = event.target.files;
          if (files && files.length > 0) {
            this.processImageFiles(files).then(resolve).catch(reject);
          } else {
            reject(new Error('未选择文件'));
          }
        };

        input.click();
        // #endif

        // #ifndef H5
        // 在非H5环境中使用平台图片选择API
        uni.chooseImage({
          count: 9,
          sizeType: ['original', 'compressed'],
          sourceType: ['album', 'camera'],
          success: (res) => {
            this.processUniImageFiles(res.tempFilePaths).then(resolve).catch(reject);
          },
          fail: (error) => {
            reject(error);
          }
        });
        // #endif
      });
    },

    // 处理文档上传
    handleDocumentUpload() {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        // 在H5环境中使用文件选择对话框
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.pdf,.doc,.docx,.txt,.md';
        input.multiple = true;

        input.onchange = (event) => {
          const files = event.target.files;
          if (files && files.length > 0) {
            this.processDocumentFiles(files).then(resolve).catch(reject);
          } else {
            reject(new Error('未选择文件'));
          }
        };

        input.click();
        // #endif

        // #ifndef H5
        // 在非H5环境中使用平台文件选择API
        uni.chooseMessageFile({
          count: 5,
          type: 'file',
          success: (res) => {
            this.processUniDocumentFiles(res.tempFiles).then(resolve).catch(reject);
          },
          fail: (error) => {
            reject(error);
          }
        });
        // #endif
      });
    },

    // 处理H5环境的图片文件
    async processImageFiles(files) {
      const mediaItems = [];
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
          // 创建文件URL
          const src = URL.createObjectURL(file);
          
          // 获取图片信息
          const imageInfo = await this.getImageInfo(src);
          
          const mediaItem = {
            type: 'image',
            src: src,
            name: file.name,
            size: this.formatFileSize(file.size),
            width: imageInfo.width,
            height: imageInfo.height,
            index: i + 1
          };
          
          mediaItems.push(mediaItem);
        } catch (error) {
          console.error('处理图片文件失败:', error);
        }
      }
      
      return mediaItems;
    },

    // 处理uni-app环境的图片文件
    async processUniImageFiles(tempFilePaths) {
      const mediaItems = [];
      
      for (let i = 0; i < tempFilePaths.length; i++) {
        const filePath = tempFilePaths[i];
        
        try {
          // 获取图片信息
          const imageInfo = await this.getUniImageInfo(filePath);
          
          const mediaItem = {
            type: 'image',
            src: filePath,
            name: `图片${i + 1}.jpg`,
            size: this.formatFileSize(imageInfo.size || 0),
            width: imageInfo.width,
            height: imageInfo.height,
            index: i + 1
          };
          
          mediaItems.push(mediaItem);
        } catch (error) {
          console.error('处理图片文件失败:', error);
        }
      }
      
      return mediaItems;
    },

    // 处理H5环境的文档文件
    async processDocumentFiles(files) {
      const mediaItems = [];
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
          const mediaItem = {
            type: 'document',
            src: URL.createObjectURL(file),
            name: file.name,
            size: this.formatFileSize(file.size),
            mimeType: file.type,
            index: i + 1
          };
          
          mediaItems.push(mediaItem);
        } catch (error) {
          console.error('处理文档文件失败:', error);
        }
      }
      
      return mediaItems;
    },

    // 处理uni-app环境的文档文件
    async processUniDocumentFiles(tempFiles) {
      const mediaItems = [];
      
      for (let i = 0; i < tempFiles.length; i++) {
        const file = tempFiles[i];
        
        try {
          const mediaItem = {
            type: 'document',
            src: file.path,
            name: file.name,
            size: this.formatFileSize(file.size || 0),
            mimeType: file.type || 'application/octet-stream',
            index: i + 1
          };
          
          mediaItems.push(mediaItem);
        } catch (error) {
          console.error('处理文档文件失败:', error);
        }
      }
      
      return mediaItems;
    },

    // 获取图片信息 (H5)
    getImageInfo(src) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight
          });
        };
        img.onerror = reject;
        img.src = src;
      });
    },

    // 获取图片信息 (uni-app)
    getUniImageInfo(src) {
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: src,
          success: resolve,
          fail: reject
        });
      });
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 预览图片
    previewImage(src, urls = []) {
      // #ifdef H5
      // H5环境直接在新窗口打开
      window.open(src, '_blank');
      // #endif

      // #ifndef H5
      // 使用uni-app的图片预览
      uni.previewImage({
        current: src,
        urls: urls.length > 0 ? urls : [src]
      });
      // #endif
    },

    // 打开文档
    openDocument(media) {
      if (!media || !media.src) return;

      // #ifdef H5
      // H5平台直接打开链接
      if (media.src) {
        window.open(media.src, '_blank');
      }
      // #endif

      // #ifndef H5
      // 非H5平台使用内置打开方式
      uni.downloadFile({
        url: media.src,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('文档打开成功');
              },
              fail: (error) => {
                console.error('文档打开失败:', error);
                uni.showToast({
                  title: '文档打开失败',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: (error) => {
          console.error('文档下载失败:', error);
          uni.showToast({
            title: '文档下载失败',
            icon: 'none'
          });
        }
      });
      // #endif
    },

    // 保存图片到本地
    saveImageToLocal(imageSrc) {
      // #ifdef APP-PLUS
      // 应用内保存图片逻辑
      uni.saveImageToPhotosAlbum({
        filePath: imageSrc,
        success: () => {
          uni.showToast({
            title: '图片已保存到相册',
            icon: 'success'
          });
        },
        fail: (error) => {
          console.error('保存图片失败:', error);
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      });
      // #endif

      // #ifdef H5
      // H5中的保存图片逻辑
      const link = document.createElement('a');
      link.href = imageSrc;
      link.download = 'image.jpg';
      link.click();
      // #endif
    }
  }
}
</script>

<style scoped>
.file-handler {
  display: none; /* 隐藏容器，因为这是一个功能性组件 */
}
</style>
