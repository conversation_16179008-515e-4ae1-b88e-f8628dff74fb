/**
 * 数字人功能工作流执行接口
 * 基于通用工作流基础类的数字人功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 数字人工作流配置, 数字人参数验证规则, 数字人错误码, 数字人状态 } from './工作流配置.js';

/**
 * 数字人工作流执行类
 */
class Workflow extends WorkflowBase {
    constructor() {
        super('数字人', 数字人工作流配置);
        this.validationRules = 数字人参数验证规则;
        this.errorCodes = 数字人错误码;
        this.statusCodes = 数字人状态;
    }

    /**
     * 执行数字人工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async execute(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`数字人进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '数字人',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('数字人工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证数字人参数
     * @param {Object} formData - 表单数据
     */
    validateParams(formData) {
        this.validateStructuredParams(formData, this.validationRules.required);
        return true;
    }

    /**
     * 构建数字人结构化参数
     * @param {Object} formData - 表单数据
     */
    buildParams(formData) {
        const builder = new StructuredParamsBuilder();

        
        if (formData.avatarType) {
            builder.addTextParam('avatarType', formData.avatarType);
        }
        if (formData.voiceType) {
            builder.addTextParam('voiceType', formData.voiceType);
        }
        if (formData.textInput) {
            builder.addTextParam('textInput', formData.textInput);
        }
        if (formData.emotion) {
            builder.addTextParam('emotion', formData.emotion);
        }

        return builder.build();
    }
}

// 创建数字人工作流实例
const Workflow = new Workflow();

// 导出接口方法
export async function 执行数字人工作流(formData, options = {}) {
    return await Workflow.execute(formData, options);
}

export async function 查询数字人状态(requestId) {
    return await Workflow.queryWorkflowStatus(requestId);
}

export async function 取消数字人工作流(requestId) {
    return await Workflow.cancelWorkflow(requestId);
}

export default {
    执行数字人工作流,
    查询数字人状态,
    取消数字人工作流
};