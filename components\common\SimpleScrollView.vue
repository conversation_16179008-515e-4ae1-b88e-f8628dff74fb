<template>
  <view class="simple-scroll-container">
    <!-- 标题 -->
    <view class="header">
      <text class="title">{{ title }}</text>
      <text class="subtitle">{{ subtitle }}</text>
    </view>
    
    <!-- 滚动区域 - 使用uni-app推荐的scroll-view -->
    <scroll-view
      scroll-y="true"
      class="scroll-area"
      :style="{ height: scrollHeight }"
      :show-scrollbar="showScrollbar"
      :enable-back-to-top="enableBackToTop"
      :scroll-with-animation="scrollWithAnimation"
      @scroll="onScroll"
      @scrolltoupper="onScrollToUpper"
      @scrolltolower="onScrollToLower"
    >
      <view class="content">
        <slot></slot>
      </view>
    </scroll-view>
    
    <!-- 滚动指示器 -->
    <view v-if="showIndicator && hasMoreContent" class="scroll-indicator">
      <text class="indicator-text">{{ indicatorText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SimpleScrollView',
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 副标题
    subtitle: {
      type: String,
      default: ''
    },
    // 滚动区域高度
    scrollHeight: {
      type: String,
      default: '400px'
    },
    // 是否显示滚动条
    showScrollbar: {
      type: Boolean,
      default: false
    },
    // 是否启用回到顶部
    enableBackToTop: {
      type: Boolean,
      default: false
    },
    // 是否启用滚动动画
    scrollWithAnimation: {
      type: Boolean,
      default: false
    },
    // 是否显示滚动指示器
    showIndicator: {
      type: Boolean,
      default: true
    },
    // 指示器文本
    indicatorText: {
      type: String,
      default: '↓ 滑动查看更多 ↓'
    }
  },
  
  data() {
    return {
      hasMoreContent: true,
      scrollTop: 0
    }
  },
  
  methods: {
    // 滚动事件
    onScroll(e) {
      const { scrollTop, scrollHeight, height } = e.detail;
      this.scrollTop = scrollTop;
      
      // 判断是否还有更多内容
      this.hasMoreContent = scrollTop < (scrollHeight - height - 50);
      
      // 向父组件发送滚动事件
      this.$emit('scroll', e.detail);
    },
    
    // 滚动到顶部
    onScrollToUpper() {
      this.$emit('scrolltoupper');
    },
    
    // 滚动到底部
    onScrollToLower() {
      this.$emit('scrolltolower');
    },
    
    // 滚动到指定位置
    scrollTo(scrollTop, animated = true) {
      // 这里可以通过ref调用scroll-view的方法
      // 但uni-app的scroll-view不直接支持，需要其他方式实现
      console.log('滚动到:', scrollTop);
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-scroll-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
}

.header {
  padding: 16px 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  display: block;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 14px;
  color: #6c757d;
  display: block;
}

.scroll-area {
  flex: 1;
  width: 100%;
  /* 关键：让uni-app处理所有平台的滚动优化 */
  -webkit-overflow-scrolling: touch;
}

.content {
  padding: 16px;
  min-height: 100%;
}

.scroll-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0.8;
  animation: bounce 2s infinite;
}

.indicator-text {
  color: #ffffff;
  font-size: 12px;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-3px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .subtitle {
    font-size: 13px;
  }
  
  .content {
    padding: 12px;
  }
}
</style>
