/**
 * 后台管理 - 会员套餐管理接口
 * 管理VIP套餐配置、价格、默认选项等
 */

import { apiRequest } from '../common/request.js';

// ================================
// 👑 VIP套餐管理接口
// ================================

/**
 * 获取所有VIP套餐配置
 */
export async function 获取VIP套餐配置列表() {
	return await apiRequest('admin/vip-packages');
}

/**
 * 获取单个VIP套餐配置
 * @param {string} packageId - 套餐ID
 */
export async function 获取VIP套餐配置详情(packageId) {
	return await apiRequest(`admin/vip-packages/${packageId}`);
}

/**
 * 创建VIP套餐配置
 * @param {Object} packageData - 套餐数据
 */
export async function 创建VIP套餐配置(packageData) {
	return await apiRequest('admin/vip-packages', {
		method: 'POST',
		body: packageData
	});
}

/**
 * 更新VIP套餐配置
 * @param {string} packageId - 套餐ID
 * @param {Object} packageData - 套餐数据
 */
export async function 更新VIP套餐配置(packageId, packageData) {
	return await apiRequest(`admin/vip-packages/${packageId}`, {
		method: 'PUT',
		body: packageData
	});
}

/**
 * 删除VIP套餐配置
 * @param {string} packageId - 套餐ID
 */
export async function 删除VIP套餐配置(packageId) {
	return await apiRequest(`admin/vip-packages/${packageId}`, {
		method: 'DELETE'
	});
}

/**
 * 设置默认VIP套餐
 * @param {string} packageId - 套餐ID
 */
export async function 设置默认VIP套餐(packageId) {
	return await apiRequest(`admin/vip-packages/${packageId}/set-default`, {
		method: 'POST'
	});
}

/**
 * 批量更新VIP套餐排序
 * @param {Array} sortData - 排序数据 [{id, sortOrder}]
 */
export async function 批量更新VIP套餐排序(sortData) {
	return await apiRequest('admin/vip-packages/batch-sort', {
		method: 'POST',
		body: { packages: sortData }
	});
}

/**
 * 启用/禁用VIP套餐
 * @param {string} packageId - 套餐ID
 * @param {boolean} enabled - 是否启用
 */
export async function 切换VIP套餐状态(packageId, enabled) {
	return await apiRequest(`admin/vip-packages/${packageId}/toggle`, {
		method: 'POST',
		body: { enabled }
	});
}

// ================================
// 💰 金币套餐管理接口
// ================================

/**
 * 获取所有金币套餐配置
 */
export async function 获取金币套餐配置列表() {
	return await apiRequest('admin/coin-packages');
}

/**
 * 更新金币套餐配置
 * @param {string} packageId - 套餐ID
 * @param {Object} packageData - 套餐数据
 */
export async function 更新金币套餐配置(packageId, packageData) {
	return await apiRequest(`admin/coin-packages/${packageId}`, {
		method: 'PUT',
		body: packageData
	});
}

/**
 * 创建金币套餐配置
 * @param {Object} packageData - 套餐数据
 */
export async function 创建金币套餐配置(packageData) {
	return await apiRequest('admin/coin-packages', {
		method: 'POST',
		body: packageData
	});
}

// ================================
// 🎨 套餐模板管理接口
// ================================

/**
 * 获取套餐模板列表
 */
export async function 获取套餐模板列表() {
	return await apiRequest('admin/package-templates');
}

/**
 * 应用套餐模板
 * @param {string} templateId - 模板ID
 */
export async function 应用套餐模板(templateId) {
	return await apiRequest(`admin/package-templates/${templateId}/apply`, {
		method: 'POST'
	});
}

// ================================
// 📊 套餐统计接口
// ================================

/**
 * 获取套餐销售统计
 * @param {Object} params - 查询参数
 */
export async function 获取套餐销售统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`admin/package-stats?${queryParams}`);
}

/**
 * 获取套餐转化率统计
 * @param {Object} params - 查询参数
 */
export async function 获取套餐转化率统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`admin/package-conversion-stats?${queryParams}`);
}

// ================================
// 🔧 套餐配置工具函数
// ================================

/**
 * 验证套餐配置数据
 * @param {Object} packageData - 套餐数据
 */
export function 验证套餐配置数据(packageData) {
	const errors = [];
	
	if (!packageData.title || packageData.title.trim() === '') {
		errors.push('套餐标题不能为空');
	}
	
	if (!packageData.duration || packageData.duration.trim() === '') {
		errors.push('套餐时长不能为空');
	}
	
	if (!packageData.price || packageData.price <= 0) {
		errors.push('套餐价格必须大于0');
	}
	
	if (packageData.originalPrice && packageData.originalPrice <= packageData.price) {
		errors.push('原价必须大于现价');
	}
	
	if (!packageData.giftCoins || packageData.giftCoins < 0) {
		errors.push('赠送金币不能为负数');
	}
	
	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * 格式化套餐数据
 * @param {Object} rawData - 原始数据
 */
export function 格式化套餐数据(rawData) {
	return {
		id: rawData.id || `vip_${Date.now()}`,
		title: rawData.title?.trim() || '',
		duration: rawData.duration?.trim() || '',
		price: Number(rawData.price) || 0,
		originalPrice: Number(rawData.originalPrice) || null,
		tag: rawData.tag?.trim() || '',
		giftCoins: Number(rawData.giftCoins) || 0,
		perDay: rawData.perDay?.trim() || '',
		features: rawData.features?.trim() || '',
		extraBonus: rawData.extraBonus?.trim() || '',
		isDefault: Boolean(rawData.isDefault),
		sortOrder: Number(rawData.sortOrder) || 0,
		enabled: Boolean(rawData.enabled !== false), // 默认启用
		createdAt: rawData.createdAt || new Date().toISOString(),
		updatedAt: new Date().toISOString()
	};
}

export default {
	获取VIP套餐配置列表,
	获取VIP套餐配置详情,
	创建VIP套餐配置,
	更新VIP套餐配置,
	删除VIP套餐配置,
	设置默认VIP套餐,
	批量更新VIP套餐排序,
	切换VIP套餐状态,
	获取金币套餐配置列表,
	更新金币套餐配置,
	创建金币套餐配置,
	获取套餐模板列表,
	应用套餐模板,
	获取套餐销售统计,
	获取套餐转化率统计,
	验证套餐配置数据,
	格式化套餐数据
};
