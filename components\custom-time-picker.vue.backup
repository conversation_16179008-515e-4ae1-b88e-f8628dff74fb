<template>
	<view class="custom-time-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生时间</text>
			</view>

			<picker-view
				class="picker-view"
				:value="pickerValue"
				@change="onPickerChange"
				@pickstart="onPickStart"
				@pickend="onPickEnd"
				@wheel.prevent="onWheel"
				:indicator-style="indicatorStyle"
				:mask-style="maskStyle"
				:immediate-change="false"
			>
				<!-- 小时列 -->
				<picker-view-column>
					<view
						v-for="(hour, index) in hours"
						:key="index"
						class="picker-item"
					>
						{{ String(hour).padStart(2, '0') }}时
					</view>
				</picker-view-column>

				<!-- 分钟列 -->
				<picker-view-column>
					<view
						v-for="(minute, index) in minutes"
						:key="index"
						class="picker-item"
					>
						{{ String(minute).padStart(2, '0') }}分
					</view>
				</picker-view-column>
			</picker-view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedHour: new Date().getHours(),
			selectedMinute: new Date().getMinutes(),
			hours: Array.from({ length: 24 }, (_, i) => i),
			minutes: Array.from({ length: 60 }, (_, i) => i),
			pickerValue: [0, 0], // picker-view的选中索引
			indicatorStyle: 'height: 80rpx; background: linear-gradient(135deg, rgba(220, 20, 60, 0.15), rgba(255, 182, 193, 0.25)); border-radius: 12rpx; border: 2rpx solid rgba(220, 20, 60, 0.4); box-shadow: 0 4rpx 16rpx rgba(220, 20, 60, 0.2);',
			maskStyle: 'background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.6) 40%, transparent 50%, transparent 50%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.95) 100%);',
			isPickerScrolling: false,
			wheelTimer: null // 鼠标滚轮定时器
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [hour, minute] = newVal.split(':');
					this.selectedHour = parseInt(hour);
					this.selectedMinute = parseInt(minute);
					this.updatePickerValue();
				}
			},
			immediate: true
		},
		visible: {
			handler(newVal) {
				if (newVal) {
					// 当选择器显示时，确保正确居中
					this.$nextTick(() => {
						setTimeout(() => {
							this.updatePickerValue();
						}, 100);
					});
				}
			},
			immediate: true
		}
	},
	created() {
		this.updatePickerValue();
	},
	methods: {
		updatePickerValue() {
			// 更新picker-view的选中索引
			this.pickerValue = [this.selectedHour, this.selectedMinute];

			// 强制更新picker-view的显示
			this.$nextTick(() => {
				this.forcePickerUpdate();
			});
		},
		forcePickerUpdate() {
			// 通过微调pickerValue来强制picker-view重新渲染和居中
			const currentValue = [...this.pickerValue];
			this.pickerValue = [0, 0];
			this.$nextTick(() => {
				this.pickerValue = currentValue;
			});
		},
		onPickerChange(e) {
			console.log('picker change:', e.detail.value);
			let newValue = e.detail.value;

			// 确保数组长度正确（小时、分钟 = 2个元素）
			if (newValue.length !== 2) {
				console.warn('picker value length mismatch:', newValue.length, 'expected: 2');
				newValue = newValue.slice(0, 2); // 只取前2个元素
				if (newValue.length < 2) {
					// 如果不足2个元素，补充默认值
					while (newValue.length < 2) {
						newValue.push(0);
					}
				}
			}

			// 如果不在滚动中，进行精确位置计算
			if (!this.isPickerScrolling) {
				const correctedValue = this.calculateSnapPosition(newValue);
				this.pickerValue = correctedValue;
				this.updateTimeFromPickerValue(correctedValue);
			} else {
				this.pickerValue = newValue;
				this.updateTimeFromPickerValue(newValue);
			}
		},

		updateTimeFromPickerValue(value) {
			const [hourIndex, minuteIndex] = value;
			this.selectedHour = hourIndex;
			this.selectedMinute = minuteIndex;
		},
		onPickStart() {
			this.isPickerScrolling = true;
		},
		onPickEnd() {
			this.isPickerScrolling = false;
			// 滚动结束后进行磁性吸附
			this.$nextTick(() => {
				this.magneticSnap();
			});
		},
		magneticSnap() {
			// 基于位置计算的精确磁性吸附
			const currentValue = [...this.pickerValue];
			const correctedValue = this.calculateSnapPosition(currentValue);

			// 如果需要修正位置，则进行吸附
			if (this.needsSnap(currentValue, correctedValue)) {
				this.performPreciseSnap(correctedValue);
			}
		},

		calculateSnapPosition(currentValue) {
			// 确保输入数组长度正确
			const safeValue = [...currentValue];
			while (safeValue.length < 2) {
				safeValue.push(0);
			}
			if (safeValue.length > 2) {
				safeValue.splice(2); // 只保留前2个元素
			}

			// 计算每个列的精确吸附位置
			const correctedValue = [0, 0];

			// 小时吸附计算
			const hourIndex = safeValue[0] || 0;
			const hourCount = this.hours.length;
			correctedValue[0] = this.snapToNearestIndex(hourIndex, hourCount);

			// 分钟吸附计算
			const minuteIndex = safeValue[1] || 0;
			const minuteCount = this.minutes.length;
			correctedValue[1] = this.snapToNearestIndex(minuteIndex, minuteCount);

			return correctedValue;
		},

		snapToNearestIndex(currentIndex, maxCount) {
			// 确保索引在有效范围内
			if (currentIndex < 0) return 0;
			if (currentIndex >= maxCount) return maxCount - 1;

			// 优化的吸附规则计算，提供更流畅的体验
			const decimal = currentIndex - Math.floor(currentIndex);

			// 更敏感的吸附阈值，提供更好的居中效果
			if (decimal < 0.2) {
				// 偏移量很小，向下吸附
				return Math.floor(currentIndex);
			}
			else if (decimal > 0.8) {
				// 偏移量很大，向上吸附
				return Math.ceil(currentIndex);
			}
			else {
				// 中间区域，吸附到最近的整数，确保居中
				return Math.round(currentIndex);
			}
		},

		needsSnap(currentValue, correctedValue) {
			// 确保两个数组都是正确长度
			if (currentValue.length !== 2 || correctedValue.length !== 2) {
				return true; // 长度不对就需要修正
			}

			// 检查是否需要进行位置修正
			return currentValue[0] !== correctedValue[0] ||
			       currentValue[1] !== correctedValue[1];
		},

		performPreciseSnap(targetValue) {
			// 确保目标值是正确的2元素数组
			const safeTargetValue = [...targetValue];
			while (safeTargetValue.length < 2) {
				safeTargetValue.push(0);
			}
			if (safeTargetValue.length > 2) {
				safeTargetValue.splice(2);
			}

			// 检查是否需要动画
			const currentValue = [...this.pickerValue];
			const needsAnimation = currentValue.some((val, index) =>
				Math.abs(val - safeTargetValue[index]) > 0.1
			);

			if (needsAnimation) {
				// 执行平滑的吸附动画
				this.animateToTarget(currentValue, safeTargetValue);
			} else {
				// 直接设置位置
				this.pickerValue = [...safeTargetValue];
			}
		},

		// 平滑动画到目标位置
		animateToTarget(startValue, targetValue) {
			const startTime = Date.now();
			const duration = 200; // 动画时长200ms

			const animate = () => {
				const elapsed = Date.now() - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// 使用缓动函数：先快后慢
				const easeProgress = 1 - Math.pow(1 - progress, 3);

				// 计算当前位置
				const currentPos = startValue.map((start, index) => {
					const target = targetValue[index];
					return start + (target - start) * easeProgress;
				});

				this.pickerValue = currentPos;

				if (progress < 1) {
					requestAnimationFrame(animate);
				} else {
					// 动画结束，设置精确值并更新时间
					this.pickerValue = [...targetValue];
					this.updateTimeFromPickerValue(targetValue);
				}
			};

			requestAnimationFrame(animate);
		},

		ensureAlignment() {
			// 兼容性方法，调用磁性吸附
			this.magneticSnap();
		},

		// 处理鼠标滚轮事件
		onWheel(e) {
			try {
				e.preventDefault();
				e.stopPropagation();

				// 获取滚动方向
				const delta = e.deltaY > 0 ? 1 : -1;

				// 简化的列选择逻辑，避免使用getBoundingClientRect
				// 默认滚动小时列，使用Shift键切换到分钟列
				let columnIndex = e.shiftKey ? 1 : 0;

				let newValue = [...this.pickerValue];

				if (columnIndex === 0) {
					// 滚动小时列
					newValue[0] = Math.max(0, Math.min(23, newValue[0] + delta));
				} else {
					// 滚动分钟列
					newValue[1] = Math.max(0, Math.min(59, newValue[1] + delta));
				}

				// 更新picker值
				this.pickerValue = newValue;
				this.updateTimeFromPickerValue(newValue);

				// 延迟执行吸附
				clearTimeout(this.wheelTimer);
				this.wheelTimer = setTimeout(() => {
					this.magneticSnap();
				}, 150);
			} catch (error) {
				console.warn('滚轮事件处理出错:', error);
			}
		},

		close() {
			this.$emit('close');
		},
		confirm() {
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');
			const timeStr = `${hour}:${minute}`;
			this.$emit('confirm', timeStr);
		}
	}
}
</script>

<style scoped>
.custom-time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 400rpx;
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	animation: slideUp 0.3s ease;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid #e0e0e0;
	z-index: 2;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.picker-header {
	padding: 24rpx 30rpx;
	text-align: center;
	background: #f8f9fa;
	color: #333;
	border-bottom: 1rpx solid #e0e0e0;
}

.header-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.picker-view {
	height: 280rpx;
	padding: 16rpx 20rpx;
	/* 确保picker-view有足够的空间进行吸附 */
	overflow: hidden;
	/* 增强磁性吸附效果 */
	scroll-behavior: smooth;
}

/* 确保picker-view-column正确显示 */
.picker-view ::v-deep .uni-picker-view-group {
	height: 80rpx;
}

.picker-view ::v-deep .uni-picker-view-content {
	padding: 0;
}

.picker-view ::v-deep .uni-picker-view-indicator {
	height: 80rpx;
}

.picker-view ::v-deep .uni-picker-view-mask {
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(255, 255, 255, 0.6) 40%,
		transparent 50%,
		transparent 50%,
		rgba(255, 255, 255, 0.6) 60%,
		rgba(255, 255, 255, 0.95) 100%);
}

.picker-item {
	height: 70rpx;
	line-height: 70rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #333;
	font-weight: 400;
	text-align: center;
	box-sizing: border-box;
	/* 磁性吸附效果 */
	scroll-snap-align: center;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.picker-footer {
	display: flex;
	padding: 24rpx 30rpx 30rpx;
	gap: 24rpx;
	background: #f8f9fa;
	border-top: 1rpx solid #e0e0e0;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 72rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
	border: 1rpx solid #ddd;
}

.btn-cancel:hover {
	background: #e9e9e9;
}

.btn-confirm {
	background: #007aff;
	color: white;
}

.btn-confirm:hover {
	background: #0056d3;
}
</style>
