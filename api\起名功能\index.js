/**
 * 起名功能统一入口文件
 * 整合起名功能的所有接口和业务逻辑
 * 创建时间：2025-01-11
 */

// 导入工作流相关
import {
    执行起名工作流,
    批量起名,
    获取起名历史,
    收藏名字,
    分享名字结果,
    查询起名状态,
    取消起名工作流
} from './工作流执行接口.js';

// 导入业务逻辑相关
import {
    完整起名业务流程,
    获取用户起名历史,
    获取起名统计信息,
    计算起名费用,
    验证起名结果,
    重新生成名字
} from './业务逻辑接口.js';

// 导入配置
import { 起名工作流配置, 起名参数验证规则, 起名错误码, 起名状态 } from './工作流配置.js';

// ================================
// 🎯 起名功能统一API
// ================================

export const 起名功能API = {
    // 核心业务流程
    完整起名业务流程,
    
    // 工作流执行
    执行起名工作流,
    批量起名,
    查询起名状态,
    取消起名工作流,
    
    // 历史和统计
    获取起名历史,
    获取用户起名历史,
    获取起名统计信息,
    
    // 辅助功能
    计算起名费用,
    验证起名结果,
    重新生成名字,
    收藏名字,
    分享名字结果,
    
    // 配置信息
    配置: 起名工作流配置,
    验证规则: 起名参数验证规则,
    错误码: 起名错误码,
    状态码: 起名状态
};

// ================================
// 🚀 快捷方法导出
// ================================

/**
 * 起名功能主入口 - 推荐使用
 * @param {Object} formData - 表单数据
 * @param {Object} options - 选项
 */
export async function 智能起名(formData, options = {}) {
    return await 完整起名业务流程(formData, options);
}

/**
 * 快速起名 - 使用默认配置
 * @param {Object} basicInfo - 基础信息 {surname, gender, birthDate, birthTime}
 */
export async function 快速起名(basicInfo) {
    const defaultFormData = {
        ...basicInfo,
        nameLength: 2,
        nameStyle: 'traditional',
        generateCount: 8
    };
    
    return await 完整起名业务流程(defaultFormData);
}

/**
 * 高级起名 - 支持更多自定义选项
 * @param {Object} advancedFormData - 高级表单数据
 */
export async function 高级起名(advancedFormData) {
    return await 完整起名业务流程(advancedFormData, {
        enableCache: false,
        timeout: 600000
    });
}

/**
 * 预览起名费用 - 不执行实际起名
 * @param {Object} formData - 表单数据
 */
export async function 预览起名费用(formData) {
    return await 计算起名费用(formData);
}

/**
 * 获取我的起名记录
 * @param {Object} params - 查询参数
 */
export async function 我的起名记录(params = {}) {
    return await 获取用户起名历史(params);
}

// ================================
// 🎯 业务场景封装
// ================================

/**
 * 新用户起名引导流程
 * @param {Object} userInfo - 用户信息
 * @param {Object} basicInfo - 基础信息
 */
export async function 新用户起名引导(userInfo, basicInfo) {
    try {
        // 1. 检查是否首次使用
        const stats = await 获取起名统计信息();
        const isFirstTime = !stats.success || stats.data.totalCount === 0;
        
        // 2. 如果是首次使用，提供新手优惠
        let formData = {
            ...basicInfo,
            nameLength: 2,
            nameStyle: 'traditional',
            generateCount: isFirstTime ? 10 : 8 // 新用户多送2个名字
        };
        
        // 3. 执行起名
        const result = await 完整起名业务流程(formData, {
            isNewUser: isFirstTime
        });
        
        return {
            ...result,
            isFirstTime,
            welcomeMessage: isFirstTime ? '欢迎使用智能起名，为您特别生成10个精选名字！' : null
        };
        
    } catch (error) {
        console.error('新用户起名引导失败:', error);
        throw error;
    }
}

/**
 * 家庭起名套餐 - 为多个家庭成员起名
 * @param {Array} familyMembers - 家庭成员信息列表
 */
export async function 家庭起名套餐(familyMembers) {
    try {
        const nameRequests = familyMembers.map((member, index) => ({
            id: `family_${index}`,
            formData: {
                surname: member.surname,
                gender: member.gender,
                birthDate: member.birthDate,
                birthTime: member.birthTime,
                nameLength: member.nameLength || 2,
                nameStyle: member.nameStyle || 'traditional',
                generateCount: 8
            },
            options: {
                familyPackage: true
            }
        }));
        
        return await 批量起名(nameRequests);
        
    } catch (error) {
        console.error('家庭起名套餐失败:', error);
        throw error;
    }
}

/**
 * 起名结果对比分析
 * @param {Array} nameResults - 多个起名结果
 */
export async function 起名结果对比(nameResults) {
    try {
        // 分析不同起名结果的特点
        const comparison = {
            totalResults: nameResults.length,
            averageScore: 0,
            bestNames: [],
            wuxingDistribution: {},
            styleDistribution: {},
            recommendations: []
        };
        
        // 计算平均分数
        let totalScore = 0;
        let nameCount = 0;
        
        nameResults.forEach(result => {
            if (result.success && result.data.recommendations) {
                result.data.recommendations.forEach(name => {
                    totalScore += name.score || 0;
                    nameCount++;
                    
                    // 收集最佳名字
                    if (name.score >= 90) {
                        comparison.bestNames.push({
                            ...name,
                            source: result.data.formData?.nameStyle || 'unknown'
                        });
                    }
                });
            }
        });
        
        comparison.averageScore = nameCount > 0 ? Math.round(totalScore / nameCount) : 0;
        
        // 排序最佳名字
        comparison.bestNames.sort((a, b) => (b.score || 0) - (a.score || 0));
        comparison.bestNames = comparison.bestNames.slice(0, 10); // 取前10个
        
        return {
            success: true,
            data: comparison
        };
        
    } catch (error) {
        console.error('起名结果对比失败:', error);
        throw error;
    }
}

// ================================
// 🔧 工具函数
// ================================

/**
 * 验证起名表单数据
 * @param {Object} formData - 表单数据
 */
export function 验证起名表单(formData) {
    try {
        const requiredFields = ['surname', 'gender', 'birthDate', 'birthTime'];
        const missingFields = requiredFields.filter(field => !formData[field]);
        
        if (missingFields.length > 0) {
            return {
                valid: false,
                message: `请填写必需信息: ${missingFields.join(', ')}`
            };
        }
        
        // 验证格式
        if (!/^[\u4e00-\u9fa5]{1,2}$/.test(formData.surname)) {
            return { valid: false, message: '姓氏格式不正确' };
        }
        
        if (!['male', 'female'].includes(formData.gender)) {
            return { valid: false, message: '请选择正确的性别' };
        }
        
        if (!/^\d{4}-\d{2}-\d{2}$/.test(formData.birthDate)) {
            return { valid: false, message: '出生日期格式不正确' };
        }
        
        if (!/^\d{2}:\d{2}$/.test(formData.birthTime)) {
            return { valid: false, message: '出生时间格式不正确' };
        }
        
        return { valid: true, message: '验证通过' };
        
    } catch (error) {
        return { valid: false, message: '验证过程出错' };
    }
}

/**
 * 格式化起名结果
 * @param {Object} result - 原始结果
 */
export function 格式化起名结果(result) {
    if (!result.success || !result.data) {
        return result;
    }
    
    return {
        ...result,
        data: {
            ...result.data,
            recommendations: result.data.recommendations?.map(name => ({
                ...name,
                displayName: `${result.data.formData?.surname || ''}${name.givenName}`,
                scoreLevel: name.score >= 90 ? '优秀' : name.score >= 80 ? '良好' : name.score >= 70 ? '一般' : '待改进',
                formattedMeaning: name.meaning?.replace(/，/g, '\n') || ''
            })) || []
        }
    };
}

// 默认导出
export default 起名功能API;
