<template>
	<view class="lyrics-view-overlay" @click.self="handleClose">
		<view class="lyrics-view" :style="themeStyles">
			<!-- 关闭按钮 -->
			<view class="close-btn" @click="handleClose">
				<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
					<line x1="18" y1="6" x2="6" y2="18"></line>
					<line x1="6" y1="6" x2="18" y2="18"></line>
				</svg>
			</view>

			<!-- 歌词滚动区域 -->
			<scroll-view
				class="lyrics-scroll"
				scroll-y
				:scroll-top="lyricsScrollTop"
				:scroll-with-animation="!isScrolling"
				@scroll="handleScroll"
				@touchstart="handleTouchStart"
				@touchend="handleTouchEnd"
			>
				<view class="lyrics-content">
					<!-- 顶部空白 -->
					<view class="lyrics-spacer"></view>

					<!-- 歌词列表 -->
					<view
						v-for="(line, index) in lyricsLines"
						:key="index"
						class="lyrics-line"
						:class="{
							active: index === displayIndex,
							played: index < displayIndex,
							upcoming: index > displayIndex
						}"
						@click="handleLyricsClick(index)"
					>
						{{ line.text || line }}
					</view>

					<!-- 底部空白 -->
					<view class="lyrics-spacer"></view>
				</view>
			</scroll-view>

			<!-- 滑动预览提示 -->
			<view v-if="isScrolling" class="scroll-hint">
				<text class="hint-text">松开跳转到此处播放</text>
				<text class="hint-time">{{ formatTime(previewTime) }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getTheme } from '@/utils/playerThemes.js';

export default {
	name: 'LyricsView',
	props: {
		// 当前音乐
		music: {
			type: Object,
			required: true,
			default: () => ({
				lyrics: []
			})
		},
		// 当前播放时间(秒)
		currentTime: {
			type: Number,
			default: 0
		},
		// 总时长(秒)
		duration: {
			type: Number,
			default: 0
		},
		// 当前歌词索引
		currentLyricsIndex: {
			type: Number,
			default: 0
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	data() {
		return {
			lyricsScrollTop: 0,
			isScrolling: false,
			previewIndex: 0,
			scrollTimer: null
		};
	},
	computed: {
		// 歌词行数组
		lyricsLines() {
			if (!this.music.lyrics) return [];

			// 如果是字符串,按换行符分割
			if (typeof this.music.lyrics === 'string') {
				const lines = this.music.lyrics.split('\n').filter(line => line.trim());
				const totalLines = lines.length;

				return lines.map((text, index) => ({
					text,
					time: this.duration > 0 ? (this.duration / totalLines) * index : 0
				}));
			}

			// 如果是数组,直接返回
			return this.music.lyrics;
		},
		
		// 显示的索引(滑动时显示预览索引,否则显示当前索引)
		displayIndex() {
			return this.isScrolling ? this.previewIndex : this.currentLyricsIndex;
		},
		
		// 预览时间
		previewTime() {
			const line = this.lyricsLines[this.previewIndex];
			return line?.time || 0;
		},
		
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--text-primary': theme.colors.textPrimary,
				'--text-secondary': theme.colors.textSecondary,
				'--text-tertiary': theme.colors.textTertiary,
				'--icon-color-active': theme.colors.iconColorActive,
				'--card-bg': theme.colors.cardBg
			};
		}
	},
	watch: {
		// 监听当前歌词索引变化,自动滚动
		currentLyricsIndex(newIndex) {
			if (!this.isScrolling) {
				this.scrollToLyrics(newIndex);
			}
		}
	},
	methods: {
		// 滚动到指定歌词
		scrollToLyrics(index) {
			const lineHeight = 80; // 每行歌词高度
			const containerHeight = 600; // 容器高度
			const targetScrollTop = Math.max(0, (index * lineHeight) - (containerHeight / 2));
			
			this.lyricsScrollTop = targetScrollTop;
		},
		
		// 处理滚动事件
		handleScroll(e) {
			if (this.isScrolling) {
				const scrollTop = e.detail.scrollTop;
				const lineHeight = 80;
				const containerHeight = 600;
				const centerIndex = Math.round((scrollTop + containerHeight / 2) / lineHeight);
				
				this.previewIndex = Math.max(0, Math.min(this.lyricsLines.length - 1, centerIndex));
			}
		},
		
		// 触摸开始
		handleTouchStart() {
			this.isScrolling = true;
			
			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}
		},
		
		// 触摸结束
		handleTouchEnd() {
			// 延迟恢复,给用户时间看清楚
			this.scrollTimer = setTimeout(() => {
				if (this.isScrolling) {
					// 跳转到预览位置
					const line = this.lyricsLines[this.previewIndex];
					if (line?.time !== undefined) {
						this.$emit('seek', line.time);
					}
					
					this.isScrolling = false;
				}
			}, 500);
		},
		
		// 点击歌词
		handleLyricsClick(index) {
			const line = this.lyricsLines[index];
			if (line?.time !== undefined) {
				this.$emit('seek', line.time);
			}
		},

		// 格式化时间
		formatTime(seconds) {
			if (!seconds || isNaN(seconds)) return '00:00';
			const min = Math.floor(seconds / 60);
			const sec = Math.floor(seconds % 60);
			return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`;
		},

		// 关闭歌词页面
		handleClose() {
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
.lyrics-view-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.95);
	backdrop-filter: blur(20rpx);
	z-index: 10000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lyrics-view {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.close-btn {
	position: absolute;
	top: 40rpx;
	right: 40rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 50%;
	cursor: pointer;
	z-index: 10;
	transition: all 0.3s;

	svg {
		width: 30rpx;
		height: 30rpx;
		stroke: var(--text-primary, #FFFFFF);
	}

	&:hover {
		background: rgba(255, 255, 255, 0.2);
		transform: scale(1.1);
	}

	&:active {
		transform: scale(0.95);
	}
}

.lyrics-scroll {
	flex: 1;
	height: 100%;
}

.lyrics-content {
	padding: 0 40rpx;
	min-height: 100%;
}

.lyrics-spacer {
	height: 400rpx;
}

.lyrics-line {
	padding: 20rpx 0;
	color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
	font-size: 28rpx;
	line-height: 50rpx;
	text-align: center;
	transition: all 0.3s;
	cursor: pointer;
	min-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&.active {
		color: var(--icon-color-active, #50E3C2);
		font-size: 36rpx;
		font-weight: bold;
		transform: scale(1.1);
		text-shadow: 0 0 20rpx var(--icon-color-active, #50E3C2);
	}
	
	&.played {
		color: var(--text-secondary, rgba(255, 255, 255, 0.7));
		font-size: 26rpx;
	}
	
	&.upcoming {
		color: var(--text-tertiary, rgba(255, 255, 255, 0.5));
		font-size: 28rpx;
	}
	
	&:active {
		transform: scale(0.95);
		background: var(--card-bg, rgba(255, 255, 255, 0.1));
		border-radius: 10rpx;
	}
}

.scroll-hint {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	padding: 20rpx 40rpx;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(20rpx);
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	pointer-events: none;
	z-index: 10;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.5);
}

.hint-text {
	color: var(--text-primary, #FFFFFF);
	font-size: 24rpx;
}

.hint-time {
	color: var(--icon-color-active, #50E3C2);
	font-size: 32rpx;
	font-weight: bold;
}

/* H5端优化 */
/* #ifdef H5 */
.lyrics-line {
	cursor: pointer;
	
	&:hover {
		color: var(--text-primary, #FFFFFF);
		background: var(--card-bg, rgba(255, 255, 255, 0.1));
		border-radius: 10rpx;
	}
}
/* #endif */
</style>

