/**
 * 音乐交易接口
 * 管理音乐买卖、交易统计、合同管理等功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 💰 交易统计接口
// ================================

/**
 * 获取交易统计
 */
export async function 获取交易统计() {
	return await apiRequest('music/trade/stats');
}

/**
 * 获取收入统计
 * @param {Object} params - 统计参数
 */
export async function 获取收入统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/trade/income-stats?${queryParams}`);
}

/**
 * 获取销售排行
 * @param {Object} params - 查询参数
 */
export async function 获取销售排行(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/trade/sales-ranking?${queryParams}`);
}

// ================================
// 🎵 音乐销售管理接口
// ================================

/**
 * 获取在售音乐列表
 * @param {Object} params - 查询参数
 */
export async function 获取在售音乐列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/trade/selling?${queryParams}`);
}

/**
 * 发布音乐销售
 * @param {Object} params - 发布参数
 */
export async function 发布音乐销售(params) {
	return await apiRequest('music/trade/publish', {
		method: 'POST',
		body: params
	});
}

/**
 * 更新音乐销售信息
 * @param {string} musicId - 音乐ID
 * @param {Object} updateData - 更新数据
 */
export async function 更新音乐销售信息(musicId, updateData) {
	return await apiRequest('music/trade/update-sale', {
		method: 'PUT',
		body: { musicId, ...updateData }
	});
}

/**
 * 下架音乐
 * @param {string} musicId - 音乐ID
 */
export async function 下架音乐(musicId) {
	return await apiRequest('music/trade/remove-sale', {
		method: 'POST',
		body: { musicId }
	});
}

/**
 * 批量管理音乐销售
 * @param {Object} params - 批量操作参数
 */
export async function 批量管理音乐销售(params) {
	return await apiRequest('music/trade/batch-manage', {
		method: 'POST',
		body: params
	});
}

// ================================
// 🛒 音乐购买接口
// ================================

/**
 * 获取音乐购买信息
 * @param {string} musicId - 音乐ID
 */
export async function 获取音乐购买信息(musicId) {
	return await apiRequest(`music/trade/purchase-info?musicId=${musicId}`);
}

/**
 * 购买音乐
 * @param {Object} params - 购买参数
 */
export async function 购买音乐(params) {
	return await apiRequest('music/trade/purchase', {
		method: 'POST',
		body: params
	});
}

/**
 * 获取购买历史
 * @param {Object} params - 查询参数
 */
export async function 获取购买历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/trade/purchase-history?${queryParams}`);
}

/**
 * 申请退款
 * @param {Object} params - 退款参数
 */
export async function 申请退款(params) {
	return await apiRequest('music/trade/refund', {
		method: 'POST',
		body: params
	});
}

// ================================
// 📋 交易历史接口
// ================================

/**
 * 获取交易历史
 * @param {Object} params - 查询参数
 */
export async function 获取交易历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/trade/history?${queryParams}`);
}

/**
 * 获取交易详情
 * @param {string} tradeId - 交易ID
 */
export async function 获取交易详情(tradeId) {
	return await apiRequest(`music/trade/detail?tradeId=${tradeId}`);
}

/**
 * 确认收货
 * @param {string} tradeId - 交易ID
 */
export async function 确认收货(tradeId) {
	return await apiRequest('music/trade/confirm-receipt', {
		method: 'POST',
		body: { tradeId }
	});
}

// ================================
// 📄 合同管理接口
// ================================

/**
 * 生成购买合同
 * @param {Object} params - 合同参数
 */
export async function 生成购买合同(params) {
	return await apiRequest('music/trade/generate-contract', {
		method: 'POST',
		body: params
	});
}

/**
 * 获取合同详情
 * @param {string} contractId - 合同ID
 */
export async function 获取合同详情(contractId) {
	return await apiRequest(`music/trade/contract-detail?contractId=${contractId}`);
}

/**
 * 签署合同
 * @param {string} contractId - 合同ID
 * @param {Object} signatureData - 签名数据
 */
export async function 签署合同(contractId, signatureData) {
	return await apiRequest('music/trade/sign-contract', {
		method: 'POST',
		body: { contractId, ...signatureData }
	});
}

/**
 * 获取合同列表
 * @param {Object} params - 查询参数
 */
export async function 获取合同列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/trade/contracts?${queryParams}`);
}

// ================================
// 💳 支付接口
// ================================

/**
 * 创建支付订单
 * @param {Object} params - 支付参数
 */
export async function 创建支付订单(params) {
	return await apiRequest('music/trade/create-payment', {
		method: 'POST',
		body: params
	});
}

/**
 * 查询支付状态
 * @param {string} orderId - 订单ID
 */
export async function 查询支付状态(orderId) {
	return await apiRequest(`music/trade/payment-status?orderId=${orderId}`);
}

/**
 * 取消支付
 * @param {string} orderId - 订单ID
 */
export async function 取消支付(orderId) {
	return await apiRequest('music/trade/cancel-payment', {
		method: 'POST',
		body: { orderId }
	});
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整购买流程
 * @param {string} musicId - 音乐ID
 * @param {Object} purchaseOptions - 购买选项
 */
export async function 完整购买流程(musicId, purchaseOptions) {
	try {
		// 1. 获取购买信息
		const purchaseInfo = await 获取音乐购买信息(musicId);
		
		// 2. 生成合同
		const contract = await 生成购买合同({
			musicId,
			...purchaseOptions
		});
		
		// 3. 创建支付订单
		const payment = await 创建支付订单({
			contractId: contract.data.contractId,
			...purchaseOptions
		});
		
		return {
			success: true,
			data: {
				purchaseInfo: purchaseInfo.data,
				contract: contract.data,
				payment: payment.data
			}
		};
		
	} catch (error) {
		console.error('完整购买流程失败:', error);
		throw error;
	}
}

/**
 * 完整销售流程
 * @param {string} musicId - 音乐ID
 * @param {Object} saleOptions - 销售选项
 */
export async function 完整销售流程(musicId, saleOptions) {
	try {
		// 1. 发布销售
		const saleResult = await 发布音乐销售({
			musicId,
			...saleOptions
		});
		
		return {
			success: true,
			data: saleResult.data
		};
		
	} catch (error) {
		console.error('完整销售流程失败:', error);
		throw error;
	}
}

export default {
	获取交易统计,
	获取收入统计,
	获取销售排行,
	获取在售音乐列表,
	发布音乐销售,
	更新音乐销售信息,
	下架音乐,
	批量管理音乐销售,
	获取音乐购买信息,
	购买音乐,
	获取购买历史,
	申请退款,
	获取交易历史,
	获取交易详情,
	确认收货,
	生成购买合同,
	获取合同详情,
	签署合同,
	获取合同列表,
	创建支付订单,
	查询支付状态,
	取消支付,
	完整购买流程,
	完整销售流程
};
