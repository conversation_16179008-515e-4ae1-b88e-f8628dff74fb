# 运程测试功能模块

## 📋 功能概述

运程测试功能基于传统易经学和现代数据分析，为用户提供2025年的运程分析和预测服务。支持多种测试类型和分析深度，帮助用户了解未来一年的运势走向。

## 🎯 主要功能

### 1. 多种测试类型
- **年度运程** - 整年运势分析
- **月度运程** - 逐月运势详解
- **事业运程** - 职场发展预测
- **财运分析** - 财富运势走向
- **感情运程** - 情感关系分析
- **健康运程** - 身体健康预测

### 2. 分析深度选择
- **基础分析** (8金币) - 简要运势概述
- **详细分析** (15金币) - 深入运势解读
- **专业分析** (25金币) - 全面运势报告
- **大师级分析** (40金币) - 顶级运势指导

## 🏗️ 技术架构

### 文件结构
```
api/运程测试/
├── index.js                 # 统一入口文件
├── 工作流配置.js             # 工作流配置和参数定义
├── 工作流执行接口.js         # 工作流执行相关接口
└── README.md                # 本文档
```

### 核心组件

#### 1. 工作流配置 (`工作流配置.js`)
- 工作流ID: `fortune_test_workflow_001`
- 工作流类型: `fortune_analysis`
- 结构化参数定义
- 费用配置

#### 2. 工作流执行接口 (`工作流执行接口.js`)
- 基于 `WorkflowBase` 基础类
- 统一的参数构建和验证
- 标准化的错误处理

#### 3. 统一入口 (`index.js`)
- 主要API函数导出
- 参数验证和预处理
- 结果格式化

## 🚀 使用方法

### 基础用法

```javascript
import { 运程测试 } from '@/api/运程测试/index.js';

// 执行运程测试
const result = await 运程测试({
    name: '张三',
    gender: 'male',
    birthDate: '1990-01-01',
    birthTime: '08:30',
    testType: 'yearly',
    analysisDepth: 'detailed'
});
```

### 高级用法

```javascript
import { 
    运程测试,
    查询运程测试状态,
    取消运程测试,
    获取运程测试配置
} from '@/api/运程测试/index.js';

// 获取配置信息
const config = 获取运程测试配置();
console.log('支持的测试类型:', config.testTypes);

// 执行测试
const result = await 运程测试(formData);

// 查询状态
const status = await 查询运程测试状态(result.requestId);

// 取消测试
if (needCancel) {
    await 取消运程测试(result.requestId);
}
```

## 📊 参数说明

### 输入参数

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| name | string | ✅ | 用户姓名 | "张三" |
| gender | string | ✅ | 性别 | "male" / "female" |
| birthDate | string | ✅ | 出生日期 | "1990-01-01" |
| birthTime | string | ✅ | 出生时间 | "08:30" |
| testType | string | ✅ | 测试类型 | "yearly" / "monthly" / "career" |
| analysisDepth | string | ❌ | 分析深度 | "basic" / "detailed" / "professional" / "master" |

### 测试类型详解

| 类型 | 值 | 说明 | 基础费用 |
|------|-----|------|----------|
| 年度运程 | yearly | 2025年整年运势 | 8金币 |
| 月度运程 | monthly | 逐月运势分析 | 12金币 |
| 事业运程 | career | 职场发展预测 | 10金币 |
| 财运分析 | wealth | 财富运势走向 | 10金币 |
| 感情运程 | love | 情感关系分析 | 8金币 |
| 健康运程 | health | 身体健康预测 | 8金币 |

### 分析深度说明

| 深度 | 值 | 费用倍数 | 说明 |
|------|-----|----------|------|
| 基础分析 | basic | 1.0x | 简要运势概述 |
| 详细分析 | detailed | 1.5x | 深入运势解读 |
| 专业分析 | professional | 2.5x | 全面运势报告 |
| 大师级分析 | master | 4.0x | 顶级运势指导 |

## 📤 返回结果

### 成功响应

```javascript
{
    success: true,
    requestId: "运程测试_1641234567890_abc123",
    data: {
        analysisResult: "详细的运程分析内容...",
        fortuneScore: 85,
        luckyElements: ["金", "水"],
        suggestions: ["建议内容1", "建议内容2"],
        monthlyFortune: {
            "2025-01": { score: 88, description: "..." },
            "2025-02": { score: 76, description: "..." }
            // ... 其他月份
        }
    },
    cost: {
        totalCost: 15,
        breakdown: {
            baseCost: 10,
            depthMultiplier: 1.5
        }
    },
    timestamp: "2025-01-11T10:30:00.000Z"
}
```

### 错误响应

```javascript
{
    success: false,
    error: {
        code: "FORTUNE_001",
        message: "出生信息格式不正确",
        details: "出生日期必须为YYYY-MM-DD格式"
    },
    timestamp: "2025-01-11T10:30:00.000Z"
}
```

## 🔧 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| FORTUNE_001 | 出生信息格式不正确 | 检查日期时间格式 |
| FORTUNE_002 | 不支持的测试类型 | 使用支持的测试类型 |
| FORTUNE_003 | 分析深度无效 | 选择有效的分析深度 |
| FORTUNE_004 | 金币余额不足 | 充值或选择较低费用选项 |
| FORTUNE_005 | 用户未登录 | 请先登录 |
| FORTUNE_006 | 工作流执行失败 | 稍后重试或联系客服 |
| FORTUNE_007 | 请求超时 | 重新发起请求 |

## 🎨 前端集成

### Vue组件示例

```vue
<template>
  <view class="fortune-test">
    <form @submit="handleSubmit">
      <input v-model="formData.name" placeholder="请输入姓名" />
      <select v-model="formData.gender">
        <option value="male">男</option>
        <option value="female">女</option>
      </select>
      <input v-model="formData.birthDate" type="date" />
      <input v-model="formData.birthTime" type="time" />
      <select v-model="formData.testType">
        <option value="yearly">年度运程</option>
        <option value="monthly">月度运程</option>
        <option value="career">事业运程</option>
      </select>
      <button type="submit">开始测试</button>
    </form>
  </view>
</template>

<script>
import { 运程测试 } from '@/api/运程测试/index.js';

export default {
  data() {
    return {
      formData: {
        name: '',
        gender: 'male',
        birthDate: '',
        birthTime: '',
        testType: 'yearly',
        analysisDepth: 'detailed'
      }
    };
  },
  methods: {
    async handleSubmit() {
      try {
        const result = await 运程测试(this.formData);
        console.log('运程测试结果:', result);
        // 处理结果...
      } catch (error) {
        console.error('测试失败:', error);
        // 错误处理...
      }
    }
  }
};
</script>
```

## 🔗 相关文档

- [工作流基础类文档](../common/workflow-base.js)
- [API统一入口](../index.js)
- [功能板块架构总结](../../docs/功能板块架构总结.md)

## 📞 技术支持

如有问题，请联系开发团队或查看相关技术文档。

---

**创建时间**: 2025-01-11  
**版本**: v1.0  
**维护人员**: AI开发团队
