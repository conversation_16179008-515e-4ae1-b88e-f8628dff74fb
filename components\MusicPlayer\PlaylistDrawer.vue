<template>
	<view 
		class="playlist-drawer"
		:class="{ show: visible }"
		@click.self="handleClose"
	>
		<view class="drawer-content" :class="{ show: visible }" :style="themeStyles">
			<!-- 头部 -->
			<view class="drawer-header">
				<view class="header-left">
					<text class="header-title">推荐列表</text>
					<text class="header-count">({{ playlist.length }}首)</text>
				</view>
				<view class="header-actions">
					<!-- 刷新按钮 -->
					<view class="refresh-btn" @click="handleRefresh">
						<svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<polyline points="23 4 23 10 17 10"></polyline>
							<polyline points="1 20 1 14 7 14"></polyline>
							<path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
						</svg>
					</view>
					<!-- 关闭按钮 -->
					<view class="close-btn" @click="handleClose">
						<svg class="close-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<line x1="18" y1="6" x2="6" y2="18"></line>
							<line x1="6" y1="6" x2="18" y2="18"></line>
						</svg>
					</view>
				</view>
			</view>
			
			<!-- 列表 -->
			<scroll-view class="playlist-scroll" scroll-y>
				<view
					v-for="(item, index) in playlist"
					:key="item.id"
					class="playlist-item"
					:class="{ 
						active: index === currentIndex,
						ad: item.type === 'advertisement'
					}"
					@click="handleSelect(item, index)"
				>
					<!-- 序号/播放图标 -->
					<view class="item-index">
						<text v-if="index !== currentIndex" class="index-text">{{ index + 1 }}</text>
						<svg v-else class="playing-icon" viewBox="0 0 24 24" fill="currentColor">
							<rect x="6" y="4" width="4" height="16">
								<animate attributeName="height" values="16;8;16" dur="1s" repeatCount="indefinite" />
								<animate attributeName="y" values="4;8;4" dur="1s" repeatCount="indefinite" />
							</rect>
							<rect x="14" y="4" width="4" height="16">
								<animate attributeName="height" values="16;4;16" dur="1s" begin="0.2s" repeatCount="indefinite" />
								<animate attributeName="y" values="4;10;4" dur="1s" begin="0.2s" repeatCount="indefinite" />
							</rect>
						</svg>
					</view>
					
					<!-- 封面 -->
					<view class="item-cover">
						<image 
							v-if="item.type !== 'advertisement'"
							:src="item.cover || '/static/images/default-music-cover.jpg'" 
							mode="aspectFill"
							class="cover-img"
						/>
						<view v-else class="ad-cover">
							<text class="ad-icon">📺</text>
						</view>
					</view>
					
					<!-- 信息 -->
					<view class="item-info">
						<view class="item-title-row">
							<text class="item-title">{{ item.title }}</text>
							<!-- 付费标识 -->
							<view v-if="item.isPaid && !item.isPurchased" class="paid-tag">
								<text class="paid-tag-text">💰{{ item.price }}</text>
							</view>
							<!-- 广告标识 -->
							<view v-if="item.type === 'advertisement'" class="ad-tag">
								<text class="ad-tag-text">广告</text>
							</view>
						</view>
						<text class="item-artist">{{ item.artist || item.adType || '激励视频广告' }}</text>
					</view>
					
					<!-- 删除按钮 -->
					<view class="item-delete" @click.stop="handleDelete(item, index)">
						<svg class="delete-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<line x1="18" y1="6" x2="6" y2="18"></line>
							<line x1="6" y1="6" x2="18" y2="18"></line>
						</svg>
					</view>
				</view>
			</scroll-view>
			
			<!-- 底部操作 -->
			<view class="drawer-footer">
				<view class="footer-btn" @click="handleClearAll">
					<svg class="footer-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<polyline points="3 6 5 6 21 6"></polyline>
						<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
					</svg>
					<text class="footer-text">清空列表</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getTheme } from '@/utils/playerThemes.js';

export default {
	name: 'PlaylistDrawer',
	props: {
		// 是否显示
		visible: {
			type: Boolean,
			default: false
		},
		// 播放列表
		playlist: {
			type: Array,
			default: () => []
		},
		// 当前播放索引
		currentIndex: {
			type: Number,
			default: 0
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	computed: {
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--background-solid': theme.colors.backgroundSolid,
				'--text-primary': theme.colors.textPrimary,
				'--text-secondary': theme.colors.textSecondary,
				'--card-bg': theme.colors.cardBg,
				'--card-bg-hover': theme.colors.cardBgHover,
				'--icon-color': theme.colors.iconColor,
				'--icon-color-active': theme.colors.iconColorActive
			};
		}
	},
	methods: {
		// 关闭抽屉
		handleClose() {
			this.$emit('close');
		},

		// 刷新推荐
		handleRefresh() {
			this.$emit('refresh');
			uni.showToast({
				title: '正在刷新推荐...',
				icon: 'loading',
				duration: 1000
			});
		},

		// 选择歌曲
		handleSelect(item, index) {
			if (index === this.currentIndex) return;
			this.$emit('select', { item, index });
		},
		
		// 删除歌曲
		handleDelete(item, index) {
			uni.showModal({
				title: '确认删除',
				content: `确定要从列表中删除《${item.title}》吗?`,
				success: (res) => {
					if (res.confirm) {
						this.$emit('delete', { item, index });
					}
				}
			});
		},
		
		// 清空列表
		handleClearAll() {
			if (this.playlist.length === 0) {
				uni.showToast({
					title: '列表已为空',
					icon: 'none'
				});
				return;
			}
			
			uni.showModal({
				title: '确认清空',
				content: '确定要清空整个播放列表吗?',
				success: (res) => {
					if (res.confirm) {
						this.$emit('clear-all');
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.playlist-drawer {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10000;
	background: rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(10rpx);
	opacity: 0;
	pointer-events: none;
	transition: opacity 0.3s;
	
	&.show {
		opacity: 1;
		pointer-events: auto;
	}
}

.drawer-content {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	max-height: 80vh;
	background: var(--background-solid, #1a1a2e);
	border-radius: 30rpx 30rpx 0 0;
	display: flex;
	flex-direction: column;
	transform: translateY(100%);
	transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
	
	&.show {
		transform: translateY(0);
	}
}

.drawer-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.header-left {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
}

.header-title {
	color: var(--text-primary, #FFFFFF);
	font-size: 32rpx;
	font-weight: bold;
}

.header-count {
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
	font-size: 24rpx;
}

.header-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.refresh-btn,
.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;

	&:active {
		transform: scale(0.9);
	}
}

.refresh-icon,
.close-icon {
	width: 32rpx;
	height: 32rpx;
	color: var(--icon-color, #FFFFFF);
}

.refresh-btn:active .refresh-icon {
	animation: rotate 0.5s ease;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.playlist-scroll {
	flex: 1;
	overflow: hidden;
}

.playlist-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx 40rpx;
	transition: all 0.3s;
	
	&:active {
		background: var(--card-bg, rgba(255, 255, 255, 0.1));
	}
	
	&.active {
		background: var(--card-bg-hover, rgba(255, 255, 255, 0.15));
	}
	
	&.ad {
		opacity: 0.8;
	}
}

.item-index {
	width: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.index-text {
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
	font-size: 24rpx;
}

.playing-icon {
	width: 32rpx;
	height: 32rpx;
	color: var(--icon-color-active, #50E3C2);
}

.item-cover {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
	overflow: hidden;
	flex-shrink: 0;
}

.cover-img {
	width: 100%;
	height: 100%;
}

.ad-cover {
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.ad-icon {
	font-size: 40rpx;
}

.item-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	min-width: 0;
}

.item-title-row {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.item-title {
	flex: 1;
	color: var(--text-primary, #FFFFFF);
	font-size: 28rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.paid-tag,
.ad-tag {
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	flex-shrink: 0;
}

.paid-tag {
	background: rgba(255, 215, 0, 0.2);
}

.paid-tag-text {
	color: #FFD700;
	font-size: 20rpx;
}

.ad-tag {
	background: rgba(255, 165, 0, 0.2);
}

.ad-tag-text {
	color: #FFA500;
	font-size: 20rpx;
}

.item-artist {
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
	font-size: 22rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.item-delete {
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.delete-icon {
	width: 28rpx;
	height: 28rpx;
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

.drawer-footer {
	padding: 20rpx 40rpx;
	padding-bottom: calc(20rpx + var(--safe-area-inset-bottom, 0));
	border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.footer-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	padding: 20rpx;
	background: var(--card-bg, rgba(255, 255, 255, 0.1));
	border-radius: 15rpx;
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.98);
		background: var(--card-bg-hover, rgba(255, 255, 255, 0.15));
	}
}

.footer-icon {
	width: 32rpx;
	height: 32rpx;
	color: var(--icon-color, #FFFFFF);
}

.footer-text {
	color: var(--text-primary, #FFFFFF);
	font-size: 28rpx;
}
</style>

