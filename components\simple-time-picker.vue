<template>
	<view v-if="visible" class="simple-picker-overlay" @click="close">
		<view class="simple-picker-content" @click.stop>
			<view class="picker-header">
				<text class="picker-title">选择出生时间</text>
			</view>
			
			<view class="time-inputs">
				<view class="input-item">
					<text class="input-label">时</text>
					<input 
						class="time-input" 
						type="number" 
						v-model="selectedHour" 
						@input="validateHour"
						:min="0" 
						:max="23"
					/>
				</view>
				
				<view class="input-item">
					<text class="input-label">分</text>
					<input 
						class="time-input" 
						type="number" 
						v-model="selectedMinute" 
						@input="validateMinute"
						:min="0" 
						:max="59"
					/>
				</view>
			</view>
			
			<view class="picker-buttons">
				<button class="btn btn-cancel" @click="close">取消</button>
				<button class="btn btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'SimpleTimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const now = new Date();
		return {
			selectedHour: now.getHours(),
			selectedMinute: now.getMinutes()
		}
	},
	methods: {
		validateHour() {
			if (this.selectedHour < 0) this.selectedHour = 0;
			if (this.selectedHour > 23) this.selectedHour = 23;
		},
		validateMinute() {
			if (this.selectedMinute < 0) this.selectedMinute = 0;
			if (this.selectedMinute > 59) this.selectedMinute = 59;
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const timeStr = `${String(this.selectedHour).padStart(2, '0')}:${String(this.selectedMinute).padStart(2, '0')}`;
			this.$emit('confirm', {
				hour: this.selectedHour,
				minute: this.selectedMinute,
				timeString: timeStr
			});
		}
	}
}
</script>

<style scoped>
.simple-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.simple-picker-content {
	background: #F5E6D3;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 40rpx;
	max-width: 400rpx;
	width: 90%;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.picker-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.picker-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #8B4513;
}

.time-inputs {
	display: flex;
	justify-content: space-between;
	gap: 30rpx;
	margin-bottom: 40rpx;
}

.input-item {
	flex: 1;
	text-align: center;
}

.input-label {
	display: block;
	font-size: 28rpx;
	color: #8B4513;
	margin-bottom: 10rpx;
	font-weight: bold;
}

.time-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #8B4513;
	border-radius: 12rpx;
	text-align: center;
	font-size: 32rpx;
	color: #8B4513;
	background: white;
	padding: 0 10rpx;
}

.picker-buttons {
	display: flex;
	gap: 30rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
}

.btn-cancel {
	background: #F5E6D3;
	color: #8B4513;
	border: 2rpx solid #8B4513;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}
</style>
