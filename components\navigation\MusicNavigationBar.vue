<template>
  <view class="music-navigation-bar" 
        style="position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; height: 70px !important; background-color: #FFFFFF !important; box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important; border-top: 1px solid rgba(0, 0, 0, 0.05) !important; z-index: 999999 !important; display: flex !important; align-items: center !important; visibility: visible !important; opacity: 1 !important; pointer-events: auto !important;"
        :style="{ paddingBottom: safeAreaInsetBottom + 'px' }">
    
    <!-- 导航项容器 -->
    <view class="navigation-container">
      <!-- 主页 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 0 }"
        @tap="navigate(0, '/pages/music/home/<USER>')">
        <text class="nav-text">主页</text>
        <view v-if="currentTab === 0" class="active-indicator"></view>
      </view>

      <!-- 交易 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 1 }"
        @tap="navigate(1, '/pages/music/trade/index')">
        <text class="nav-text">交易</text>
        <view v-if="currentTab === 1" class="active-indicator"></view>
      </view>

      <!-- 创作 - 特殊样式 -->
      <view
        class="nav-item create-item"
        :class="{ active: currentTab === 2 }"
        @tap="navigate(2, '/pages/create/music/index?direct=true')">
        <view class="create-button">
          <view class="create-circle">
            <text class="create-text">创作</text>
          </view>
        </view>
        <view v-if="currentTab === 2" class="active-indicator create-indicator"></view>
      </view>

      <!-- 发现 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 3 }"
        @tap="navigate(3, '/pages/music/discover/index')">
        <text class="nav-text">发现</text>
        <view v-if="currentTab === 3" class="active-indicator"></view>
      </view>

      <!-- 创作者 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 4 }"
        @tap="navigate(4, '/pages/music/creator/index')">
        <text class="nav-text">创作者</text>
        <view v-if="currentTab === 4" class="active-indicator"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MusicNavigationBar',
  data() {
    return {
      currentTab: 2, // 默认选中创作页面
      safeAreaInsetBottom: 0,
      navigating: false // 导航状态标志
    }
  },
  created() {
    this.getSafeAreaInset();
    this.initCurrentTab();
    
    // 监听页面显示事件
    uni.$on('music-page-show', this.handlePageShow);
    console.log('MusicNavigationBar: 已创建并监听music-page-show事件');
  },
  mounted() {
    this.initCurrentTab();
    console.log('MusicNavigationBar: 已挂载并初始化当前标签');
  },
  beforeDestroy() {
    uni.$off('music-page-show', this.handlePageShow);
    console.log('MusicNavigationBar: 已移除music-page-show事件监听');
  },
  methods: {
    // 获取底部安全区高度
    getSafeAreaInset() {
      // #ifdef APP-PLUS || H5
      try {
        const safeArea = uni.getSystemInfoSync().safeAreaInsets;
        if (safeArea && safeArea.bottom > 0) {
          this.safeAreaInsetBottom = safeArea.bottom;
        }
      } catch (e) {
        console.error('获取安全区域失败', e);
        this.safeAreaInsetBottom = 0;
      }
      // #endif
    },
    
    // 导航到指定页面
    navigate(index, url) {
      console.log('🚀 MusicNavigationBar navigate方法被调用:', index, url);

      // 防止重复导航
      if (this.navigating) {
        console.log('正在导航中，忽略重复请求');
        return;
      }

      // 检查当前页面路径
      const currentPath = window.location.hash || '';
      const normalizedUrl = url.startsWith('/') ? url : '/' + url;

      console.log('音乐导航检查 - 当前路径:', currentPath, '目标路径:', normalizedUrl);

      // 如果当前已经在目标页面，则不需要导航
      if (currentPath.includes(normalizedUrl.substring(1))) {
        console.log('已在目标页面，无需导航:', url);
        this.currentTab = index;
        return;
      }

      console.log('MusicNavigationBar: 导航到', index, url);
      this.currentTab = index;
      this.navigating = true;

      // 使用简单的导航方式，避免冲突
      try {
        uni.navigateTo({
          url: url,
          success: () => {
            console.log('音乐导航成功:', url);
            this.navigating = false;
          },
          fail: (err) => {
            console.error('音乐导航失败:', err);
            this.navigating = false;
            // 简单的错误处理，不再尝试redirectTo避免冲突
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } catch (e) {
        console.error('音乐导航异常:', e);
        this.navigating = false;
      }
    },
    
    // 处理页面显示事件
    handlePageShow(event) {
      console.log('MusicNavigationBar: 收到music-page-show事件', event);
      if (event && event.path) {
        this.updateCurrentTab(event.path);
      }
    },
    
    // 初始化当前标签
    initCurrentTab() {
      const currentPath = window.location.hash || uni.getCurrentPages()[uni.getCurrentPages().length - 1]?.route || '';
      console.log('初始化音乐导航当前页面路径:', currentPath);
      this.updateCurrentTab(currentPath);
    },
    
    // 更新当前标签
    updateCurrentTab(currentPath) {
      console.log('音乐导航当前路径:', currentPath);
      
      // 更精确的路径匹配
      if (currentPath.includes('/pages/music/home/<USER>') || currentPath.includes('music/home')) {
        this.currentTab = 0;
        console.log('当前音乐标签: 主页');
      } else if (currentPath.includes('/pages/music/trade/index') || currentPath.includes('music/trade')) {
        this.currentTab = 1;
        console.log('当前音乐标签: 交易');
      } else if (currentPath.includes('/pages/create/music/index') || currentPath.includes('create/music')) {
        this.currentTab = 2;
        console.log('当前音乐标签: 创作');
      } else if (currentPath.includes('/pages/music/discover/index') || currentPath.includes('music/discover')) {
        this.currentTab = 3;
        console.log('当前音乐标签: 发现');
      } else if (currentPath.includes('/pages/music/creator/index') || currentPath.includes('music/creator')) {
        this.currentTab = 4;
        console.log('当前音乐标签: 创作者');
      }
    }
  }
}
</script>

<style scoped>
.music-navigation-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.navigation-container {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: space-around;
  padding: 0 10px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  transform: translateY(-2px);
}

.nav-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 600;
  transition: all 0.3s ease;
}

.nav-item.active .nav-text {
  color: #FF4444;
  font-weight: 700;
  font-size: 32rpx;
}

/* 创作按钮特殊样式 */
.create-item {
  position: relative;
}

.create-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.create-circle {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background: linear-gradient(135deg, #FF4444, #FF6666);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
  transition: all 0.3s ease;
}

.create-item:hover .create-circle {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

.create-text {
  color: white;
  font-size: 26rpx;
  font-weight: bold;
}

.create-item .nav-text {
  margin-top: -4px;
  font-size: 28rpx;
  font-weight: 600;
}

/* 激活指示器 */
.active-indicator {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: linear-gradient(90deg, #FF4444, #FF6666);
  border-radius: 2px;
  animation: slideIn 0.3s ease;
}

.create-indicator {
  bottom: 8px;
  background: linear-gradient(90deg, #FF4444, #FF6666);
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 20px;
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .nav-icon {
    font-size: 40rpx;
  }

  .nav-text {
    font-size: 24rpx;
  }

  .create-circle {
    width: 90rpx;
    height: 90rpx;
  }

  .create-text {
    font-size: 22rpx;
  }
}
</style>
