<template>
  <view class="stable-textarea-container" :class="{ 'selecting': isSelectionMode }">
    <!-- 主textarea区域 -->
    <CursorPositioner
      :textarea-element="$refs.textareaRef"
      :value="inputValue"
      :enabled="!isSelectionMode"
      @cursor-position-change="handleCursorPositionChange"
      ref="cursorPositioner"
    >
      <view class="textarea-wrapper"
            @click="handleClick">
        <!-- 官方textarea组件 -->
        <textarea
        ref="textareaRef"
        class="stable-textarea"
        :class="{
          'scroll-mode': !isFocused,
          'edit-mode': isFocused,
          'mouse-dragging': isDragging
        }"
        v-model="inputValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :auto-height="autoHeight"
        :cursor-spacing="cursorSpacing"
        :show-confirm-bar="showConfirmBar"
        :adjust-position="adjustPosition"
        :selection-start="selectionStart"
        :selection-end="selectionEnd"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
        @paste="handlePaste"
        @linechange="handleLineChange"
        @selectionchange="handleSelectionChange"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="handleTextareaClick"
      />

      <!-- 发送按钮插槽，放在右下角 -->
      <view class="send-button-slot" v-if="$slots.sendButton">
        <slot name="sendButton"></slot>
      </view>

      <!-- 选择模式提示 -->
      <view class="selection-tip" v-if="isSelectionMode && !showSelectionMenu">
        双击选择单词，长按选择文本
      </view>

      <!-- 高级文本选择器 -->
      <AdvancedTextSelector
        :textarea-element="$refs.textareaRef"
        :value="inputValue"
        :selection-start="selectionStart"
        :selection-end="selectionEnd"
        :is-selection-mode="isSelectionMode"
        @selection-change="handleAdvancedSelectionChange"
        @cursor-position-change="handleCursorPositionChange"
        @selection-cancel="handleSelectionCancel"
        ref="advancedSelector"
      />

      <!-- 文本选择菜单 -->
      <TextSelectionMenu
        :visible="showSelectionMenu"
        :selected-text="selectedText"
        :selection-start="selectionStart"
        :selection-end="selectionEnd"
        :position="selectionMenuPosition"
        @delete-text="handleDeleteText"
        @replace-text="handleReplaceText"
        @enhance-text="handleEnhanceText"
        @action-complete="handleSelectionActionComplete"
      />

      <!-- 回到底部按钮 -->
      <view
        v-if="showScrollToBottomBtn"
        class="scroll-to-bottom-btn"
        @click="scrollToBottomAndFollow"
      >
        <view class="scroll-btn-icon">↓</view>
        <view class="scroll-btn-text">回到底部</view>
      </view>
      </view>
    </CursorPositioner>

    <!-- 多媒体预览区域 -->
    <view class="media-preview" v-if="mediaItems.length > 0">
      <view class="media-item" v-for="(item, index) in mediaItems" :key="index">
        <image v-if="item.type === 'image'" :src="item.src" class="media-image" @click="previewMedia(item)" />
        <video v-if="item.type === 'video'" :src="item.src" class="media-video" controls />
        <view class="media-remove" @click="removeMedia(index)">×</view>
      </view>
    </view>
  </view>
</template>

<script>
import TextSelectionMenu from '@/components/text-editor/tools/TextSelectionMenu.vue';
import AdvancedTextSelector from '@/components/text-editor/tools/AdvancedTextSelector.vue';
import CursorPositioner from '@/components/text-editor/tools/CursorPositioner.vue';
import { TextareaStateManager } from '@/components/text-editor/tools/TextareaStateManager.js';

export default {
  name: 'StableTextArea',
  components: {
    TextSelectionMenu,
    AdvancedTextSelector,
    CursorPositioner
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    fixed: {
      type: Boolean,
      default: false
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    minHeight: {
      type: Number,
      default: 40
    },
    mediaItems: {
      type: Array,
      default: () => []
    }

  },
  emits: [
    'update:modelValue',
    'focus',
    'blur',
    'confirm',
    'paste',
    'keyboardheightchange',
    'linechange',
    'height-change',
    'input',
    'selectionchange',
    'media-add',
    'media-remove'
  ],
  data() {
    return {
      isFocused: false,
      platformClass: '',
      // 文字选择相关
      isSelectionMode: false,
      selectionStart: 0,
      selectionEnd: 0,
      lastClickTime: 0,
      longPressTimer: null,
      showSelectionMenu: false,
      selectionMenuPosition: { x: 0, y: 0 },

      // 状态管理器
      stateManager: null,
      // 鼠标拖拽模拟手势滚动
      isDragging: false,
      dragStartY: 0,
      dragStartX: 0,
      dragStartScrollTop: 0,
      // 智能跟随滚动
      isAutoFollowing: true,        // 是否自动跟随
      userHasScrolled: false,       // 用户是否手动滚动过
      lastUserScrollTime: 0,        // 最后一次用户滚动时间
      showScrollToBottomBtn: false, // 是否显示回到底部按钮
      scrollToBottomTimer: null     // 回到底部按钮显示定时器
    };
  },
  computed: {
    inputValue: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    },
    textareaStyle() {
      const style = {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`
      };

      // 为发送按钮留出空间
      if (this.$slots.sendButton) {
        style.paddingRight = '120px'; // 增加空间给两个按钮
      }

      return style;
    },
    // 检查是否滚动到底部
    isAtBottom() {
      if (!this.$refs || !this.$refs.textareaRef) {
        return true;
      }

      const textarea = this.getTextareaElement();
      if (!textarea) return true;

      try {
        const scrollTop = textarea.scrollTop;
        const scrollHeight = textarea.scrollHeight;
        const clientHeight = textarea.clientHeight;

        // 允许5px的误差
        return scrollTop + clientHeight >= scrollHeight - 5;
      } catch (error) {
        return true;
      }
    },
    // 检查是否有内容溢出 - 移到computed中
    hasOverflow() {
      // 防止在组件未挂载时访问$refs导致错误
      if (!this.$refs || !this.$refs.textareaRef) {
        return false;
      }

      const textarea = this.$refs.textareaRef;
      if (textarea) {
        try {
          // H5环境
          // #ifdef H5
          const element = textarea.$el || textarea;
          if (element && element.scrollHeight !== undefined && element.clientHeight !== undefined) {
            return element.scrollHeight > element.clientHeight;
          }
          // #endif

          // App和小程序环境
          // #ifndef H5
          if (textarea.scrollHeight !== undefined && textarea.clientHeight !== undefined) {
            return textarea.scrollHeight > textarea.clientHeight;
          }
          // #endif
        } catch (error) {
          console.warn('检查hasOverflow时出错:', error);
          return false;
        }
      }
      return false;
    },

    // 当前选中的文本
    selectedText() {
      if (this.selectionStart === this.selectionEnd || !this.inputValue) {
        return '';
      }
      return this.inputValue.substring(this.selectionStart, this.selectionEnd);
    }
  },
  watch: {
    // 监听内容变化，实现智能跟随滚动
    modelValue: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          // 只有在自动跟随模式下才滚动到底部
          if (this.isAutoFollowing && newVal && oldVal && newVal.length > oldVal.length) {
            this.scrollToBottom();
          }

          // 检查是否需要显示回到底部按钮
          this.checkScrollToBottomButton();
        });
      },
      immediate: false
    }
  },
  created() {
    this.detectPlatform();
  },
  mounted() {
    // 组件挂载完成，使用uni-app官方textarea的原生滚动功能
    console.log('StableTextArea mounted, 使用官方textarea原生滚动');

    // 初始化状态管理器
    this.initStateManager();

    // 初始化鼠标拖拽模拟手势滚动
    this.initMouseGestureSimulation();

    // 初始化滚动监听
    this.initScrollListener();
  },

  beforeUnmount() {
    // 清理所有资源
    this.cleanupAllTextareaResources();
  },
  methods: {
    // 初始化状态管理器
    initStateManager() {
      this.stateManager = new TextareaStateManager();

      // 监听状态管理器事件
      this.stateManager.on('longpress', (data) => {
        this.enableAdvancedSelectionMode();
        this.selectTextAtTouch(data.event);
      });

      this.stateManager.on('doubleclick', (data) => {
        this.enableAdvancedSelectionMode();
        this.selectCurrentWord();
      });

      this.stateManager.on('click', (data) => {
        if (data.state.mode === 'positioning') {
          // 处理光标定位
          this.handleTextareaClick(data.event);
        }
      });

      this.stateManager.on('modechange', (data) => {
        console.log(`文本框模式变化: ${data.oldMode} -> ${data.newMode}`);

        // 根据模式调整组件状态
        switch (data.newMode) {
          case 'selection':
            this.isSelectionMode = true;
            break;
          case 'normal':
            this.isSelectionMode = false;
            this.hideSelectionMenu();
            break;
        }
      });

      console.log('✅ 状态管理器初始化完成');
    },

    detectPlatform() {
      // 检测当前平台
      // #ifdef H5
      this.platformClass = 'h5-platform';
      // #endif

      // #ifdef APP-PLUS
      this.platformClass = 'app-platform';
      // #endif

      // #ifdef MP
      this.platformClass = 'mp-platform';
      // #endif
    },

    
    handleInput(e) {
      // 确保正确处理输入事件
      this.$emit('update:modelValue', e.detail.value);
      this.$emit('input', e);
    },
    
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },
    
    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },
    
    handleConfirm(e) {
      this.$emit('confirm', e);
    },

    handlePaste(e) {
      this.$emit('paste', e);
    },



    // 点击处理（双击选择）
    handleClick(e) {
      const now = Date.now();
      const isDoubleClick = now - this.lastClickTime < 300;
      this.lastClickTime = now;

      if (isDoubleClick) {
        this.enableSelectionMode();
        this.selectCurrentWord();
      }
    },

    // 触摸开始处理（使用状态管理器）
    handleTouchStart(e) {
      if (this.stateManager) {
        this.stateManager.handleTouchStart(e);

        // 根据状态管理器决定是否阻止默认行为
        if (this.stateManager.shouldPreventDefault('touchstart')) {
          e.preventDefault();
        }
      }
    },

    // 触摸移动处理（使用状态管理器）
    handleTouchMove(e) {
      if (this.stateManager) {
        this.stateManager.handleTouchMove(e);

        // 根据状态管理器决定是否阻止默认行为
        if (this.stateManager.shouldPreventDefault('touchmove')) {
          e.preventDefault();
        }
      }
    },

    // 触摸结束处理（使用状态管理器）
    handleTouchEnd(e) {
      if (this.stateManager) {
        this.stateManager.handleTouchEnd(e);

        // 根据状态管理器决定是否阻止默认行为
        if (this.stateManager.shouldPreventDefault('touchend')) {
          e.preventDefault();
        }
      }
    },

    // 在触摸位置选择文本
    selectTextAtTouch(e) {
      if (!e.touches || !e.touches[0]) return;

      try {
        const textarea = this.$refs.textareaRef;
        if (!textarea || !this.inputValue) return;

        // 获取触摸位置对应的文本位置
        const touch = e.touches[0];
        const rect = textarea.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;

        // 简单的位置估算（这里可以根据需要优化）
        const lineHeight = 20; // 估算行高
        const charWidth = 8; // 估算字符宽度
        const line = Math.floor(y / lineHeight);
        const char = Math.floor(x / charWidth);

        const lines = this.inputValue.split('\n');
        let position = 0;

        for (let i = 0; i < line && i < lines.length; i++) {
          position += lines[i].length + 1; // +1 for newline
        }

        if (line < lines.length) {
          position += Math.min(char, lines[line].length);
        }

        // 选择当前位置的单词
        this.selectWordAtPosition(position);
      } catch (error) {
        console.error('选择触摸位置文本失败:', error);
        // 如果位置计算失败，就选择当前光标位置的单词
        this.selectCurrentWord();
      }
    },

    // 在指定位置选择单词
    selectWordAtPosition(position) {
      if (!this.inputValue) return;

      const text = this.inputValue;
      let start = position;
      let end = position;

      // 定义单词边界字符
      const wordBoundary = /[\s\n\t,.;:'"!?()[\]{}\/\\|<>-+*=]/;

      // 向前找单词起始
      while (start > 0 && !wordBoundary.test(text[start - 1])) {
        start--;
      }

      // 向后找单词结束
      while (end < text.length && !wordBoundary.test(text[end])) {
        end++;
      }

      // 如果没有找到单词，选择整行
      if (start === end) {
        const lines = text.split('\n');
        let lineStart = 0;
        let currentLine = 0;

        for (let i = 0; i < lines.length; i++) {
          const lineEnd = lineStart + lines[i].length;
          if (position >= lineStart && position <= lineEnd) {
            start = lineStart;
            end = lineEnd;
            break;
          }
          lineStart = lineEnd + 1; // +1 for newline
        }
      }

      // 设置选择范围
      this.selectionStart = start;
      this.selectionEnd = end;
    },

    // 在指定位置显示选择菜单
    showSelectionMenuAtPosition(e) {
      try {
        const textarea = this.$refs.textareaRef;
        if (!textarea) return;

        // 获取textarea的位置
        const rect = textarea.getBoundingClientRect();

        // 计算菜单位置（在选择区域上方）
        this.selectionMenuPosition = {
          x: rect.left + rect.width / 2,
          y: rect.top
        };

        this.showSelectionMenu = true;
      } catch (error) {
        console.error('显示选择菜单失败:', error);
      }
    },

    // 隐藏选择菜单
    hideSelectionMenu() {
      this.showSelectionMenu = false;
    },

    // 处理删除文本
    handleDeleteText(data) {
      const { start, end } = data;
      const newValue = this.inputValue.substring(0, start) + this.inputValue.substring(end);
      this.inputValue = newValue;

      // 重置选择
      this.selectionStart = start;
      this.selectionEnd = start;
    },

    // 处理替换文本
    handleReplaceText(data) {
      const { start, end, newText } = data;
      const newValue = this.inputValue.substring(0, start) + newText + this.inputValue.substring(end);
      this.inputValue = newValue;

      // 设置新的选择范围
      this.selectionStart = start;
      this.selectionEnd = start + newText.length;
    },

    // 处理润色文本
    handleEnhanceText(data) {
      const { text, start, end } = data;

      // 这里可以调用AI润色API
      uni.showToast({
        title: '润色功能开发中',
        icon: 'none',
        duration: 2000
      });

      // 示例：简单的文本处理
      // const enhancedText = text.replace(/\s+/g, ' ').trim();
      // this.handleReplaceText({ start, end, newText: enhancedText });
    },

    // 处理选择操作完成
    handleSelectionActionComplete(action) {
      this.hideSelectionMenu();
      this.isSelectionMode = false;

      console.log(`文本选择操作完成: ${action}`);
    },

    // 处理高级选择器的选择变化
    handleAdvancedSelectionChange(selection) {
      this.selectionStart = selection.start;
      this.selectionEnd = selection.end;

      // 更新textarea的选择状态
      this.updateTextareaSelection();

      // 显示选择菜单
      if (selection.start !== selection.end) {
        this.showSelectionMenuAtPosition();
      } else {
        this.hideSelectionMenu();
      }
    },

    // 处理光标位置变化
    handleCursorPositionChange(position) {
      try {
        const textarea = this.$refs.textareaRef;
        if (textarea && textarea.setSelectionRange) {
          textarea.setSelectionRange(position, position);
          textarea.focus();
        }

        // 更新选择状态
        this.selectionStart = position;
        this.selectionEnd = position;

        console.log(`光标定位到位置: ${position}`);
      } catch (error) {
        console.error('设置光标位置失败:', error);
      }
    },

    // 更新textarea的选择状态
    updateTextareaSelection() {
      try {
        const textarea = this.$refs.textareaRef;
        if (textarea && textarea.setSelectionRange) {
          textarea.setSelectionRange(this.selectionStart, this.selectionEnd);
        }
      } catch (error) {
        console.error('更新textarea选择状态失败:', error);
      }
    },

    // 启用高级选择模式
    enableAdvancedSelectionMode() {
      this.isSelectionMode = true;

      // 更新高级选择器的文本框信息
      this.$nextTick(() => {
        if (this.$refs.advancedSelector) {
          this.$refs.advancedSelector.updateTextareaInfo();
        }
      });

      // 显示提示
      uni.showToast({
        title: '拖拽手柄调整选择范围',
        icon: 'none',
        duration: 2000
      });
    },

    // 处理文本框点击（用于光标定位）
    handleTextareaClick(e) {
      if (this.isSelectionMode) return;

      // 延迟处理，确保textarea已经处理了点击事件
      this.$nextTick(() => {
        const textarea = this.$refs.textareaRef;
        if (textarea) {
          const cursorPos = textarea.selectionStart;
          this.selectionStart = cursorPos;
          this.selectionEnd = cursorPos;

          // 通知高级选择器
          if (this.$refs.advancedSelector) {
            this.$refs.advancedSelector.handleTextareaClick(e);
          }
        }
      });
    },

    // 处理选择取消
    handleSelectionCancel() {
      console.log('收到选择取消事件');

      // 退出选择模式
      this.isSelectionMode = false;

      // 清除选择状态
      this.selectionStart = 0;
      this.selectionEnd = 0;

      // 隐藏选择菜单
      this.hideSelectionMenu();

      // 通知状态管理器
      if (this.stateManager) {
        this.stateManager.exitSelectionMode();
      }

      // 用户反馈
      uni.showToast({
        title: '已取消选择',
        icon: 'none',
        duration: 1000
      });

      console.log('✅ 文本选择已取消');
    },

    // 行数变化处理（自动滚动）
    handleLineChange(e) {
      this.$emit('linechange', e);

      // 当内容超过3行时，自动滚动到底部
      if (e.detail.lineCount > 3) {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 选择变化处理
    handleSelectionChange(e) {
      this.selectionStart = e.detail.selectionStart;
      this.selectionEnd = e.detail.selectionEnd;

      if (this.selectionStart !== this.selectionEnd) {
        const selectedText = this.inputValue.substring(this.selectionStart, this.selectionEnd);

        // 显示选择菜单
        this.showSelectionMenuAtPosition(e);

        uni.showToast({
          title: `已选择 ${selectedText.length} 个字符`,
          icon: 'none',
          duration: 1000
        });
      } else {
        // 隐藏选择菜单
        this.hideSelectionMenu();
      }

      this.$emit('selectionchange', e);
    },

    // 启用选择模式
    enableSelectionMode() {
      this.isSelectionMode = true;

      // 10秒后自动关闭选择模式
      setTimeout(() => {
        this.isSelectionMode = false;
      }, 10000);
    },

    // 选择当前单词
    selectCurrentWord() {
      const textarea = this.$refs.textareaRef;
      if (!textarea || !this.inputValue) return;

      try {
        const cursorPos = textarea.selectionStart || 0;
        const text = this.inputValue;

        // 查找单词边界
        let start = cursorPos;
        let end = cursorPos;

        const wordBoundary = /[\s\n\t,.;:'"!?()[\]{}\/\\|<>\-+*/]/;

        // 向前找单词起始
        while (start > 0 && !wordBoundary.test(text[start - 1])) {
          start--;
        }

        // 向后找单词结束
        while (end < text.length && !wordBoundary.test(text[end])) {
          end++;
        }

        // 设置选择范围
        this.selectionStart = start;
        this.selectionEnd = end;
      } catch (e) {
        console.error('选择单词失败:', e);
      }
    },

    // 滚动到底部
    scrollToBottom() {
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    },

    // 多媒体功能
    addMediaItem(item) {
      this.$emit('media-add', item);
    },

    removeMedia(index) {
      this.$emit('media-remove', index);
    },

    previewMedia(item) {
      if (item.type === 'image') {
        uni.previewImage({
          urls: [item.src],
          current: item.src
        });
      }
    },

    // 处理图片上传
    handleImageUpload() {
      uni.chooseImage({
        count: 9,
        success: (res) => {
          res.tempFilePaths.forEach(path => {
            this.addMediaItem({
              type: 'image',
              src: path,
              name: this.getFileName(path)
            });
          });

          uni.showToast({
            title: '图片添加成功',
            icon: 'success'
          });
        }
      });
    },

    // 处理视频上传
    handleVideoUpload() {
      uni.chooseVideo({
        count: 1,
        success: (res) => {
          this.addMediaItem({
            type: 'video',
            src: res.tempFilePath,
            name: this.getFileName(res.tempFilePath)
          });

          uni.showToast({
            title: '视频添加成功',
            icon: 'success'
          });
        }
      });
    },

    getFileName(path) {
      return path.split('/').pop() || 'file';
    },

    // 设置光标到文本末尾
    setCursorToEnd() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('setCursorToEnd: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.setSelectionRange) {
              const textLength = this.inputValue.length;
              element.setSelectionRange(textLength, textLength);
              element.focus();
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.setSelectionRange) {
              const textLength = this.inputValue.length;
              textarea.setSelectionRange(textLength, textLength);
            }
            // #endif
          } catch (error) {
            console.warn('setCursorToEnd 执行失败:', error);
          }
        }
      });
    },

    // 设置文本内容
    setText(text) {
      this.inputValue = text;
      this.$nextTick(() => {
        this.setCursorToEnd();
      });
    },

    // 聚焦输入框
    focus() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('focus: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.focus) {
              element.focus();
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.focus) {
              textarea.focus();
            }
            // #endif
          } catch (error) {
            console.warn('focus 执行失败:', error);
          }
        }
      });
    },

    // 失焦输入框
    blur() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('blur: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.blur) {
              element.blur();
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.blur) {
              textarea.blur();
            }
            // #endif
          } catch (error) {
            console.warn('blur 执行失败:', error);
          }
        }
      });
    },



    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('scrollToBottom: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.scrollHeight !== undefined) {
              element.scrollTop = element.scrollHeight;
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.scrollTop !== undefined) {
              textarea.scrollTop = textarea.scrollHeight || 999999;
            }
            // #endif
          } catch (error) {
            console.warn('scrollToBottom 执行失败:', error);
          }
        }
      });
    },

    // 检查溢出状态
    checkOverflow() {
      // 这个方法主要是为了兼容旧代码调用
      try {
        return this.hasOverflow;
      } catch (error) {
        console.warn('checkOverflow 执行失败:', error);
        return false;
      }
    },

    // 获取textarea元素
    getTextareaElement() {
      if (!this.$refs || !this.$refs.textareaRef) {
        return null;
      }

      const textarea = this.$refs.textareaRef;
      if (textarea) {
        try {
          // H5环境
          // #ifdef H5
          return textarea.$el || textarea;
          // #endif

          // App和小程序环境
          // #ifndef H5
          return textarea;
          // #endif
        } catch (error) {
          console.warn('getTextareaElement 执行失败:', error);
          return null;
        }
      }
      return null;
    },

    // 清理事件监听器，防止内存泄漏
    cleanupEventListeners() {
      if (this._touchHandlers) {
        const { element, handleTouchStart, handleTouchMove, handleTouchEnd } = this._touchHandlers;
        if (element) {
          element.removeEventListener('touchstart', handleTouchStart);
          element.removeEventListener('touchmove', handleTouchMove);
          element.removeEventListener('touchend', handleTouchEnd);
          console.log('StableTextArea: 已清理触摸事件监听器');
        }
        this._touchHandlers = null;
      }

      // 清理惯性滚动动画
      if (this._inertiaAnimation) {
        cancelAnimationFrame(this._inertiaAnimation);
        this._inertiaAnimation = null;
      }

      // 清理鼠标拖拽事件监听器
      this.cleanupMouseGestureSimulation();

      // 清理滚动监听器
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.removeEventListener('scroll', this.handleScroll);
      }
    },

    // 初始化鼠标拖拽模拟手势滚动
    initMouseGestureSimulation() {
      // 只在H5平台启用鼠标拖拽模拟
      // #ifdef H5
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      // 鼠标按下事件
      const handleMouseDown = (e) => {
        // 只处理左键
        if (e.button !== 0) return;

        // 记录初始状态，但不立即开始拖拽
        this.dragStartY = e.clientY;
        this.dragStartX = e.clientX;
        this.dragStartScrollTop = textarea.scrollTop;
        this.isDragging = false; // 初始不设为拖拽状态

        console.log('鼠标按下，准备拖拽');
      };

      // 鼠标移动事件
      const handleMouseMove = (e) => {
        // 只有在鼠标按下且移动距离超过阈值时才开始拖拽
        if (!this.dragStartY) return;

        const deltaY = Math.abs(e.clientY - this.dragStartY);
        const deltaX = Math.abs(e.clientX - this.dragStartX);

        // 移动距离超过5px且主要是垂直移动时，才开始拖拽滚动
        if (deltaY > 5 && deltaY > deltaX && !this.isDragging) {
          this.isDragging = true;

          // 清除任何现有的文字选择
          if (window.getSelection) {
            window.getSelection().removeAllRanges();
          }

          console.log('开始鼠标拖拽滚动');
        }

        // 只有在拖拽状态下才执行滚动
        if (this.isDragging) {
          const totalDeltaY = e.clientY - this.dragStartY;
          const sensitivity = 1.5; // 拖拽灵敏度

          // 计算新的滚动位置
          const maxScroll = textarea.scrollHeight - textarea.clientHeight;
          const newScrollTop = Math.max(0, Math.min(
            maxScroll,
            this.dragStartScrollTop - totalDeltaY * sensitivity
          ));

          textarea.scrollTop = newScrollTop;

          // 持续清除文字选择
          if (window.getSelection) {
            window.getSelection().removeAllRanges();
          }

          // 只在拖拽时阻止默认行为
          e.preventDefault();
          e.stopPropagation();
        }
      };

      // 鼠标释放事件
      const handleMouseUp = (e) => {
        // 重置所有拖拽状态
        if (this.isDragging) {
          this.isDragging = false;
          console.log('结束鼠标拖拽滚动');
        }
        this.dragStartY = 0;
        this.dragStartX = 0;
        this.dragStartScrollTop = 0;
      };

      // 添加事件监听器
      textarea.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      // 保存事件处理器引用，用于清理
      this._mouseHandlers = {
        element: textarea,
        handleMouseDown,
        handleMouseMove,
        handleMouseUp
      };

      console.log('StableTextArea: 已初始化鼠标拖拽模拟手势滚动');
      // #endif
    },

    // 清理鼠标拖拽事件监听器
    cleanupMouseGestureSimulation() {
      // #ifdef H5
      if (this._mouseHandlers) {
        const { element, handleMouseDown, handleMouseMove, handleMouseUp } = this._mouseHandlers;
        if (element) {
          element.removeEventListener('mousedown', handleMouseDown);
        }
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        this._mouseHandlers = null;
        console.log('StableTextArea: 已清理鼠标拖拽事件监听器');
      }
      // #endif
    },

    // 初始化滚动监听
    initScrollListener() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      // 监听滚动事件
      textarea.addEventListener('scroll', this.handleScroll, { passive: true });

      console.log('StableTextArea: 已初始化滚动监听');
    },

    // 处理滚动事件
    handleScroll(e) {
      const now = Date.now();
      this.lastUserScrollTime = now;

      // 检查是否是用户主动滚动（非程序触发）
      if (!this.isDragging) {
        this.userHasScrolled = true;

        // 如果用户滚动到底部，恢复自动跟随
        if (this.isAtBottom) {
          this.enableAutoFollow();
        } else {
          // 用户滚动到其他位置，停止自动跟随
          this.disableAutoFollow();
        }
      }

      // 检查是否需要显示回到底部按钮
      this.checkScrollToBottomButton();
    },

    // 启用自动跟随
    enableAutoFollow() {
      this.isAutoFollowing = true;
      this.showScrollToBottomBtn = false;
      console.log('启用自动跟随滚动');
    },

    // 禁用自动跟随
    disableAutoFollow() {
      this.isAutoFollowing = false;
      console.log('禁用自动跟随滚动');
    },

    // 检查是否需要显示回到底部按钮
    checkScrollToBottomButton() {
      // 清除之前的定时器
      if (this.scrollToBottomTimer) {
        clearTimeout(this.scrollToBottomTimer);
      }

      // 如果不在底部且有内容溢出，显示回到底部按钮
      if (!this.isAtBottom && this.hasOverflow && !this.isAutoFollowing) {
        this.scrollToBottomTimer = setTimeout(() => {
          this.showScrollToBottomBtn = true;
        }, 1000); // 1秒后显示按钮
      } else {
        this.showScrollToBottomBtn = false;
      }
    },

    // 手动回到底部并恢复跟随
    scrollToBottomAndFollow() {
      this.scrollToBottom();
      this.enableAutoFollow();
      this.userHasScrolled = false;
    }

  },

  // 清理所有文本框资源
  cleanupAllTextareaResources() {
    try {
      console.log('🧹 开始清理StableTextArea资源...');

      // 清理事件监听器
      this.cleanupEventListeners();

      // 清理鼠标拖拽事件监听器
      this.cleanupMouseGestureSimulation();

      // 清理触摸事件监听器
      if (this._touchHandlers) {
        const { element, handleTouchStart, handleTouchMove, handleTouchEnd } = this._touchHandlers;
        if (element) {
          element.removeEventListener('touchstart', handleTouchStart);
          element.removeEventListener('touchmove', handleTouchMove);
          element.removeEventListener('touchend', handleTouchEnd);
        }
        this._touchHandlers = null;
      }

      // 清理惯性滚动动画
      if (this._inertiaAnimation) {
        cancelAnimationFrame(this._inertiaAnimation);
        this._inertiaAnimation = null;
      }

      // 清理所有定时器
      if (this.scrollToBottomTimer) {
        clearTimeout(this.scrollToBottomTimer);
        this.scrollToBottomTimer = null;
      }

      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 隐藏选择菜单
      this.hideSelectionMenu();

      // 重置选择状态
      this.isSelectionMode = false;
      this.selectionStart = 0;
      this.selectionEnd = 0;

      // 清理textarea的滚动监听
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.removeEventListener('scroll', this.handleScroll);
      }

      // 清理状态管理器
      if (this.stateManager) {
        this.stateManager.destroy();
        this.stateManager = null;
        console.log('✅ 状态管理器已清理');
      }

      console.log('✅ StableTextArea资源清理完成');
    } catch (error) {
      console.error('❌ 清理StableTextArea资源时发生错误:', error);
    }
  }
}
</script>

<style scoped>
.stable-textarea-container {
  position: relative;
  width: 100%;
  /* 添加容器边框，确保输入框区域明显可见 */
  border-radius: 10px;
  background: linear-gradient(145deg, rgba(60, 60, 80, 0.1), rgba(40, 40, 60, 0.1));
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stable-textarea-container.selecting {
  border: 2px solid rgba(120, 100, 220, 0.8);
  box-shadow: 0 0 12px rgba(120, 100, 220, 0.4);
}

.textarea-wrapper {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.stable-textarea {
  width: 100%;
  background-color: rgba(40, 40, 60, 0.9);
  color: #e8e8e8;
  border-radius: 8px;
  padding: 12px 60px 12px 12px;
  font-size: 14px;
  line-height: 1.5;
  box-sizing: border-box;
  border: 1px solid rgba(120, 100, 220, 0.3);
  outline: none;
  transition: all 0.3s ease;
  resize: none;
  min-height: 40px;
  max-height: 200px;
  overflow-y: auto;
}

.stable-textarea:focus {
  background-color: rgba(50, 50, 70, 0.95);
  border-color: rgba(120, 100, 220, 0.8);
  box-shadow: 0 0 8px rgba(120, 100, 220, 0.4);
}

.stable-textarea::placeholder {
  color: rgba(232, 232, 232, 0.6);
  font-size: 14px;
}

.send-button-slot {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

/* 平台特定样式 */
.h5-platform .stable-textarea {
  transition: height 0.2s ease;
  /* H5平台增强边框效果 */
  border: 2px solid rgba(120, 100, 220, 0.4);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

  /* H5移动端手势滚动优化 */
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  touch-action: pan-y; /* 允许垂直滑动 */
  overscroll-behavior-y: contain; /* 防止滚动链接到父元素 */

  /* 移动端优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;

  /* 关键：非焦点状态下禁用文字选择，启用滚动 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.h5-platform .stable-textarea:focus {
  border-color: rgba(120, 100, 220, 1);
  box-shadow: 0 0 12px rgba(120, 100, 220, 0.6), inset 0 1px 3px rgba(0, 0, 0, 0.1);

  /* 焦点状态下启用文字选择 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.ios-platform .stable-textarea {
  font-family: -apple-system, SF Pro, sans-serif;
  border: 1px solid rgba(120, 100, 220, 0.5);
}

.android-platform .stable-textarea {
  font-family: Roboto, sans-serif;
  border: 1px solid rgba(120, 100, 220, 0.5);
}

.app-platform .stable-textarea {
  /* App平台特定样式 */
  border: 1px solid rgba(120, 100, 220, 0.6);
  background-color: rgba(40, 40, 60, 0.95);
}

.mp-platform .stable-textarea {
  /* 小程序平台特定样式 */
  border: 1px solid rgba(120, 100, 220, 0.5);
  background-color: rgba(40, 40, 60, 0.9);
}

.mp-platform .stable-textarea {
  /* 小程序平台特定样式 */
}

/* 选择模式提示 */
.selection-tip {
  position: absolute;
  top: -30px;
  right: 0;
  background: rgba(120, 100, 220, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0.8;
  z-index: 10;
}

/* 回到底部按钮 */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: linear-gradient(135deg, rgba(120, 100, 220, 0.9), rgba(100, 80, 200, 0.9));
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  z-index: 15;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: slideInUp 0.3s ease-out;
}

.scroll-to-bottom-btn:hover {
  background: linear-gradient(135deg, rgba(120, 100, 220, 1), rgba(100, 80, 200, 1));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.scroll-to-bottom-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.scroll-btn-icon {
  font-size: 14px;
  font-weight: bold;
  animation: bounce 2s infinite;
}

.scroll-btn-text {
  font-size: 11px;
  font-weight: 500;
}

/* 按钮动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* 多媒体预览区域 */
.media-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.media-item {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(40, 40, 60, 0.5);
}

.media-image, .media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-remove {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  z-index: 10;
}

/* uni-app官方textarea滚动优化 */
.stable-textarea {
  /* 启用平滑滚动 */
  scroll-behavior: smooth;
  /* 优化触摸滚动 - uni-app官方支持 */
  -webkit-overflow-scrolling: touch;
  /* 确保滚动区域可见 */
  overflow-y: auto;
  /* 防止过度滚动 */
  overscroll-behavior: contain;
}

/* H5移动端滚动模式和编辑模式切换 */
.h5-platform .stable-textarea.scroll-mode {
  /* 滚动模式：禁用文字选择，启用手势滚动 */
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  touch-action: pan-y !important;
  -webkit-touch-callout: none !important;

  /* 真机手势滚动优化 - 这些属性在真机上效果更好 */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

.h5-platform .stable-textarea.edit-mode {
  /* 编辑模式：启用文字选择，保持滚动功能 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  touch-action: manipulation !important;

  /* 编辑模式下的触摸优化 */
  -webkit-tap-highlight-color: rgba(102, 126, 234, 0.2) !important;
}

/* 鼠标拖拽模拟手势滚动状态 */
.h5-platform .stable-textarea.mouse-dragging {
  cursor: grabbing !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  /* 开发测试用的鼠标拖拽优化 */
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 选择状态下的文本高亮 */
.stable-textarea::selection {
  background: rgba(120, 100, 220, 0.3);
  color: #fff;
}

.stable-textarea::-moz-selection {
  background: rgba(120, 100, 220, 0.3);
  color: #fff;
}
</style>
