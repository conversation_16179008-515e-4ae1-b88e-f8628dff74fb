# 起名功能 - 接口规范

## 📋 **功能概述**

起名功能基于传统易经八字学说和现代姓名学理论，为新生儿提供专业的姓名推荐服务。

## 🔄 **数据流向**

```
前端表单数据 → 后端构建结构化参数 → 大模型处理 → 后端解析清洗 → 前端标准化数据
```

## 📤 **发送给大模型的结构化参数**

### **请求格式**
```javascript
{
    "requestId": "起名功能_1641234567890_def456",
    "userId": "user_123",
    "timestamp": 1641234567890,
    "moduleName": "起名功能",
    "workflowId": "name_generation_workflow_001",
    "workflowType": "baby_naming",
    "structuredParams": {
        "surname": {
            "type": "text",
            "value": "张",
            "placeholder": "{{surname}}",
            "description": "姓氏"
        },
        "gender": {
            "type": "text",
            "value": "male",
            "placeholder": "{{gender}}",
            "description": "性别：male|female"
        },
        "birthDate": {
            "type": "text",
            "value": "2024-01-15",
            "placeholder": "{{birthDate}}",
            "description": "出生日期"
        },
        "birthTime": {
            "type": "text",
            "value": "14:30",
            "placeholder": "{{birthTime}}",
            "description": "出生时间"
        },
        "birthPlace": {
            "type": "text",
            "value": "北京市",
            "placeholder": "{{birthPlace}}",
            "description": "出生地点"
        },
        "nameCount": {
            "type": "config",
            "value": "10",
            "placeholder": "{{nameCount}}",
            "description": "推荐名字数量：5|10|15|20"
        },
        "nameStyle": {
            "type": "config",
            "value": "traditional",
            "placeholder": "{{nameStyle}}",
            "description": "起名风格：traditional|modern|poetic|auspicious"
        },
        "avoidWords": {
            "type": "text",
            "value": "",
            "placeholder": "{{avoidWords}}",
            "description": "避免使用的字（可选）"
        },
        "preferWords": {
            "type": "text",
            "value": "",
            "placeholder": "{{preferWords}}",
            "description": "希望包含的字（可选）"
        },
        "returnFormat": {
            "type": "config",
            "value": {
                "format": "json",
                "structure": {
                    "baziAnalysis": "object (八字分析)",
                    "names": "array<object> (推荐名字列表)",
                    "summary": "string (总结说明)"
                },
                "name_structure": {
                    "name": "string (完整姓名)",
                    "givenName": "string (名字部分)",
                    "score": "number (0-100)",
                    "meaning": "string (寓意说明)",
                    "pronunciation": "string (拼音)",
                    "elements": "array<string> (五行属性)",
                    "strokes": "object (笔画分析)",
                    "numerology": "object (数理分析)"
                },
                "required_fields": ["baziAnalysis", "names", "summary"]
            },
            "placeholder": "{{returnFormat}}",
            "description": "指定返回数据的格式和结构"
        }
    }
}
```

### **提示词模板**
```
请基于以下信息进行专业的起名分析：

## 基本信息
姓氏：{{surname}}
性别：{{gender}}
出生日期：{{birthDate}}
出生时间：{{birthTime}}
出生地点：{{birthPlace}}

## 起名要求
推荐数量：{{nameCount}}个
起名风格：{{nameStyle}}
避免用字：{{avoidWords}}
偏好用字：{{preferWords}}

## 分析要求
请提供：
1. 八字分析（年月日时的天干地支、五行分析、喜用神）
2. 推荐名字列表（每个名字包含完整信息）
3. 总体起名建议

## 返回格式要求
请严格按照以下JSON格式返回结果：

{
  "baziAnalysis": {
    "bazi": "年柱 月柱 日柱 时柱",
    "elements": {
      "metal": 数字,
      "wood": 数字,
      "water": 数字,
      "fire": 数字,
      "earth": 数字
    },
    "favorableElements": ["喜用神"],
    "analysis": "八字分析说明"
  },
  "names": [
    {
      "name": "完整姓名",
      "givenName": "名字部分",
      "score": 分数(0-100),
      "meaning": "寓意说明",
      "pronunciation": "拼音",
      "elements": ["五行属性"],
      "strokes": {
        "total": 总笔画,
        "surname": 姓氏笔画,
        "givenName": 名字笔画
      },
      "numerology": {
        "tianGe": 天格数,
        "diGe": 地格数,
        "renGe": 人格数,
        "waiGe": 外格数,
        "zongGe": 总格数,
        "analysis": "数理分析"
      }
    }
  ],
  "summary": "总体起名建议和说明"
}
```

## 📥 **后端返回给前端的标准格式**

### **成功响应**
```javascript
{
    "success": true,
    "requestId": "起名功能_1641234567890_def456",
    "data": {
        "core": {
            "baziAnalysis": {
                "bazi": "甲辰 丙寅 戊申 甲午",
                "elements": {
                    "metal": 1,
                    "wood": 3,
                    "water": 0,
                    "fire": 2,
                    "earth": 2
                },
                "favorableElements": ["水", "金"],
                "analysis": "此命五行木旺缺水，日主天干为土，生于春季..."
            },
            "names": [
                {
                    "name": "张浩然",
                    "givenName": "浩然",
                    "score": 95,
                    "meaning": "浩然正气，胸怀宽广，寓意孩子将来品格高尚",
                    "pronunciation": "zhāng hào rán",
                    "elements": ["水", "金"],
                    "strokes": {
                        "total": 23,
                        "surname": 11,
                        "givenName": 12
                    },
                    "numerology": {
                        "tianGe": 12,
                        "diGe": 13,
                        "renGe": 22,
                        "waiGe": 3,
                        "zongGe": 23,
                        "analysis": "天格12为凶，地格13为吉，人格22为凶..."
                    }
                }
                // ... 更多名字
            ],
            "summary": "根据八字分析，此命五行缺水，建议选用带水属性的字..."
        },
        
        "display": {
            "overview": {
                "title": "张姓男孩起名",
                "subtitle": "基于生辰八字的智能起名",
                "score": 95,
                "level": "优秀",
                "summary": "为您推荐10个高分名字，综合考虑五行、寓意、音律",
                "icon": "star",
                "color": "#faad14"
            },
            
            "details": {
                "sections": [
                    {
                        "title": "推荐名字",
                        "type": "name-list",
                        "content": [
                            {
                                "name": "张浩然",
                                "score": 95,
                                "meaning": "浩然正气，胸怀宽广",
                                "pronunciation": "zhāng hào rán",
                                "elements": ["水", "金"],
                                "highlight": true
                            }
                            // ... 更多名字
                        ],
                        "style": "name-cards"
                    },
                    {
                        "title": "八字分析",
                        "type": "bazi-chart",
                        "content": {
                            "bazi": "甲辰 丙寅 戊申 甲午",
                            "elements": {
                                "metal": 1,
                                "wood": 3,
                                "water": 0,
                                "fire": 2,
                                "earth": 2
                            },
                            "favorableElements": ["水", "金"],
                            "analysis": "此命五行木旺缺水..."
                        },
                        "style": "chart-display"
                    },
                    {
                        "title": "起名建议",
                        "type": "text",
                        "content": "根据八字分析，此命五行缺水，建议选用带水属性的字...",
                        "style": "advice-text"
                    }
                ]
            }
        },
        
        "meta": {
            "processingTime": 3500,
            "aiModel": "gpt-4",
            "confidence": 0.94
        }
    },
    
    "cost": {
        "totalCost": 25,
        "breakdown": {
            "baseCost": 25,
            "extraCost": 0
        },
        "userBalance": 75
    },
    
    "timestamp": "2025-01-11T10:30:00.000Z"
}
```

## 🔧 **API接口定义**

### **执行起名**
```
POST /api/v1/name-generation/execute
Content-Type: application/json

请求体：
{
    "userId": "用户ID",
    "formData": {
        "surname": "张",
        "gender": "male",
        "birthDate": "2024-01-15",
        "birthTime": "14:30",
        "birthPlace": "北京市",
        "nameCount": "10",
        "nameStyle": "traditional",
        "avoidWords": "",
        "preferWords": ""
    }
}
```

### **查询起名状态**
```
GET /api/v1/name-generation/status/{requestId}
```

### **收藏名字**
```
POST /api/v1/name-generation/favorite
{
    "userId": "用户ID",
    "name": "张浩然",
    "requestId": "请求ID"
}
```

## 📊 **参数说明**

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| surname | string | ✅ | 姓氏 | "张" |
| gender | string | ✅ | 性别 | "male" / "female" |
| birthDate | string | ✅ | 出生日期 | "2024-01-15" |
| birthTime | string | ✅ | 出生时间 | "14:30" |
| birthPlace | string | ✅ | 出生地点 | "北京市" |
| nameCount | string | ❌ | 推荐数量 | "5" / "10" / "15" / "20" |
| nameStyle | string | ❌ | 起名风格 | "traditional" / "modern" / "poetic" |
| avoidWords | string | ❌ | 避免用字 | "忌讳的字" |
| preferWords | string | ❌ | 偏好用字 | "希望的字" |

## 💰 **费用说明**

| 推荐数量 | 基础费用 | 会员折扣 | 说明 |
|---------|---------|---------|------|
| 5个名字 | 25金币 | 8折 | 基础起名服务 |
| 10个名字 | 35金币 | 8折 | 标准起名服务 |
| 15个名字 | 45金币 | 8折 | 高级起名服务 |
| 20个名字 | 55金币 | 8折 | 专业起名服务 |

## 🚨 **错误码说明**

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| NAME_001 | 姓氏格式不正确 | 检查姓氏格式 |
| NAME_002 | 出生日期无效 | 使用正确的日期格式 |
| NAME_003 | 出生时间无效 | 使用正确的时间格式 |
| NAME_004 | 性别参数无效 | 使用有效的性别值 |
| NAME_005 | 推荐数量超出限制 | 选择有效的数量范围 |
| NAME_006 | 用户未登录 | 请先登录 |
| NAME_007 | 金币余额不足 | 充值或选择较低费用选项 |

---

**文档版本**: v1.0  
**创建时间**: 2025-01-11  
**维护团队**: 起名功能组
