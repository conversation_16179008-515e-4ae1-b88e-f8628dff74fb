<template>
	<view class="custom-time-picker" v-if="visible" @touchmove.stop.prevent @wheel.stop.prevent>
		<view class="picker-mask" @click="close" @touchmove.stop.prevent @wheel.stop.prevent></view>
		<view class="picker-content" @touchmove.stop.prevent @wheel.stop.prevent>
			<view class="time-selector" @touchmove.stop.prevent @wheel.stop.prevent>
				<!-- 小时选择 -->
				<view class="selector-column">
					<text class="column-title">时</text>
					<view 
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="hourScroll"
					>
						<view 
							v-for="hour in hours" 
							:key="hour"
							:id="'hour-' + hour"
							class="scroll-item"
							:class="{ active: hour === selectedHour }"
							@click="selectHour(hour)"
						>
							{{ String(hour).padStart(2, '0') }}
						</view>
					</view>
				</view>
				
				<!-- 分钟选择 -->
				<view class="selector-column">
					<text class="column-title">分</text>
					<view 
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="minuteScroll"
					>
						<view 
							v-for="minute in minutes" 
							:key="minute"
							:id="'minute-' + minute"
							class="scroll-item"
							:class="{ active: minute === selectedMinute }"
							@click="selectMinute(minute)"
						>
							{{ String(minute).padStart(2, '0') }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedHour: new Date().getHours(),
			selectedMinute: new Date().getMinutes(),
			hours: Array.from({ length: 24 }, (_, i) => i),
			minutes: Array.from({ length: 60 }, (_, i) => i),
			touchStartY: 0,
			isScrolling: false
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [hour, minute] = newVal.split(':');
					this.selectedHour = parseInt(hour);
					this.selectedMinute = parseInt(minute);
				}
			},
			immediate: true
		}
	},
	methods: {
		selectHour(hour) {
			this.selectedHour = hour;
			this.autoConfirm();
		},
		selectMinute(minute) {
			this.selectedMinute = minute;
			this.autoConfirm();
		},
		autoConfirm() {
			// 延迟一点时间让用户看到选择效果
			setTimeout(() => {
				this.confirm();
			}, 300);
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');
			const timeStr = `${hour}:${minute}`;
			this.$emit('confirm', timeStr);
		},
		onTouchStart(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.touchStartY = e.touches[0].clientY;
			this.isScrolling = true;
		},
		onTouchMove(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			if (!this.isScrolling) return;

			const deltaY = e.touches[0].clientY - this.touchStartY;
			const target = e.currentTarget;
			target.scrollTop -= deltaY * 0.8; // 调整滑动灵敏度，0.8比较合适
			this.touchStartY = e.touches[0].clientY;
		},
		onTouchEnd(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.isScrolling = false;
		},
		onWheel(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			const target = e.currentTarget;
			const scrollAmount = e.deltaY > 0 ? 60 : -60; // 固定滚动距离
			target.scrollTop += scrollAmount;
		}
	}
}
</script>

<style scoped>
.custom-time-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 500rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
	border-radius: 20rpx;
	overflow: hidden;
	animation: scaleIn 0.3s ease;
	box-shadow: 0 10rpx 40rpx rgba(220, 20, 60, 0.3);
	border: 2rpx solid #DC143C;
	z-index: 2;
}

@keyframes scaleIn {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}



.time-selector {
	display: flex;
	height: 400rpx;
	padding: 30rpx 15rpx;
	overflow: hidden;
}

.selector-column {
	flex: 1;
	display: flex;
	flex-direction: column;
	margin: 0 10rpx;
}

.column-title {
	text-align: center;
	font-size: 26rpx;
	color: #DC143C;
	font-weight: bold;
	margin-bottom: 15rpx;
	background: linear-gradient(135deg, rgba(220, 20, 60, 0.1), rgba(255, 182, 193, 0.2));
	padding: 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(220, 20, 60, 0.2);
}

.scroll-list {
	flex: 1;
	height: 320rpx;
	overflow-y: auto;
	overflow-x: hidden;
	scroll-behavior: smooth;
}

.scroll-list::-webkit-scrollbar {
	width: 8rpx;
}

.scroll-list::-webkit-scrollbar-track {
	background: rgba(220, 20, 60, 0.1);
	border-radius: 4rpx;
}

.scroll-list::-webkit-scrollbar-thumb {
	background: linear-gradient(135deg, #FF6B6B, #DC143C);
	border-radius: 4rpx;
	box-shadow: 0 2rpx 4rpx rgba(220, 20, 60, 0.3);
}

.scroll-list::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(135deg, #DC143C, #B22222);
}

.scroll-item {
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #DC143C;
	margin: 4rpx 8rpx;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1rpx solid transparent;
}

.scroll-item:hover {
	background: linear-gradient(135deg, rgba(220, 20, 60, 0.1), rgba(255, 182, 193, 0.2));
	transform: scale(1.02);
	border-color: rgba(220, 20, 60, 0.3);
}

.scroll-item.active {
	background: linear-gradient(135deg, #DC143C, #B22222);
	color: white;
	font-weight: bold;
	transform: scale(1.05);
	box-shadow: 0 4rpx 12rpx rgba(220, 20, 60, 0.4);
	border-color: #DC143C;
}
</style>
