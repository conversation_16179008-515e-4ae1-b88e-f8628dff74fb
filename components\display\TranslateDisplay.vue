<template>
  <view class="translate-display">
    <view class="translate-header">
      <view class="language-info">
        <text class="source-language">{{ getLanguageName(content.sourceLanguage) }}</text>
        <text class="arrow">→</text>
        <text class="target-language">{{ getLanguageName(content.targetLanguage) }}</text>
      </view>
    </view>
    
    <view class="translate-content">
      <view class="source-text-container">
        <text class="source-label">原文</text>
        <text class="source-text">{{ content.sourceText }}</text>
      </view>
      
      <view class="translated-text-container">
        <text class="translated-label">译文</text>
        <text class="translated-text">{{ content.translatedText }}</text>
      </view>
    </view>
    
    <view class="translate-actions">
      <view class="action-btn copy-btn" @tap="copyText(content.translatedText)">
        <text class="action-icon">📋</text>
        <text class="action-text">复制译文</text>
      </view>
      
      <view class="action-btn speak-btn" @tap="speakText(content.translatedText, content.targetLanguage)">
        <text class="action-icon">🔊</text>
        <text class="action-text">朗读译文</text>
      </view>
      
      <view class="action-btn retry-btn" @tap="retryTranslation">
        <text class="action-icon">🔄</text>
        <text class="action-text">重新翻译</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TranslateDisplay',
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  methods: {
    // 获取语言名称
    getLanguageName(code) {
      const languageMap = {
        'zh': '中文',
        'en': '英语',
        'ja': '日语',
        'ko': '韩语',
        'fr': '法语',
        'de': '德语',
        'es': '西班牙语',
        'it': '意大利语',
        'ru': '俄语',
        'pt': '葡萄牙语',
        'ar': '阿拉伯语'
      };
      
      return languageMap[code] || code;
    },
    
    // 复制文本
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        }
      });
    },
    
    // 朗读文本
    speakText(text, language) {
      // 触发朗读事件
      this.$emit('action', 'speak', { text, language });
      
      uni.showToast({
        title: '正在朗读...',
        icon: 'none'
      });
    },
    
    // 重新翻译
    retryTranslation() {
      this.$emit('action', 'retry', this.content);
    }
  }
};
</script>

<style scoped>
.translate-display {
  padding: 15px;
  background-color: #1a1a1a;
  border-radius: 8px;
  margin-bottom: 20px;
}

.translate-header {
  margin-bottom: 15px;
}

.language-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.source-language, .target-language {
  padding: 5px 12px;
  background-color: #333333;
  border-radius: 15px;
  color: #ffffff;
  font-size: 14px;
}

.arrow {
  margin: 0 10px;
  color: #4FD1C5;
  font-size: 16px;
}

.translate-content {
  margin-bottom: 20px;
}

.source-text-container, .translated-text-container {
  margin-bottom: 15px;
}

.source-label, .translated-label {
  display: block;
  font-size: 12px;
  color: #888888;
  margin-bottom: 5px;
}

.source-text, .translated-text {
  display: block;
  font-size: 16px;
  line-height: 1.5;
  color: #ffffff;
  padding: 10px;
  background-color: #333333;
  border-radius: 5px;
  word-break: break-all;
}

.translate-actions {
  display: flex;
  justify-content: space-around;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.action-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.action-text {
  font-size: 12px;
  color: #ffffff;
}

.copy-btn .action-icon {
  color: #4299E1;
}

.speak-btn .action-icon {
  color: #4FD1C5;
}

.retry-btn .action-icon {
  color: #F6AD55;
}
</style> 