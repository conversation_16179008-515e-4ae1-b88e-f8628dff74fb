/**
 * 姓名配对功能工作流执行接口
 * 基于通用工作流基础类的姓名配对功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 姓名配对工作流配置, 姓名配对参数验证规则, 姓名配对错误码, 姓名配对状态 } from './工作流配置.js';

/**
 * 姓名配对工作流执行类
 */
class Workflow extends WorkflowBase {
    constructor() {
        super('姓名配对', 姓名配对工作流配置);
        this.validationRules = 姓名配对参数验证规则;
        this.errorCodes = 姓名配对错误码;
        this.statusCodes = 姓名配对状态;
    }

    /**
     * 执行姓名配对工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async execute(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`姓名配对进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '姓名配对',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('姓名配对工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证姓名配对参数
     * @param {Object} formData - 表单数据
     */
    validateParams(formData) {
        this.validateStructuredParams(formData, this.validationRules.required);
        return true;
    }

    /**
     * 构建姓名配对结构化参数
     * @param {Object} formData - 表单数据
     */
    buildParams(formData) {
        const builder = new StructuredParamsBuilder();

        
        if (formData.name1) {
            builder.addTextParam('name1', formData.name1);
        }
        if (formData.gender1) {
            builder.addTextParam('gender1', formData.gender1);
        }
        if (formData.birthDate1) {
            builder.addTextParam('birthDate1', formData.birthDate1);
        }
        if (formData.birthTime1) {
            builder.addTextParam('birthTime1', formData.birthTime1);
        }
        if (formData.name2) {
            builder.addTextParam('name2', formData.name2);
        }
        if (formData.gender2) {
            builder.addTextParam('gender2', formData.gender2);
        }
        if (formData.birthDate2) {
            builder.addTextParam('birthDate2', formData.birthDate2);
        }
        if (formData.birthTime2) {
            builder.addTextParam('birthTime2', formData.birthTime2);
        }

        return builder.build();
    }
}

// 创建姓名配对工作流实例
const Workflow = new Workflow();

// 导出接口方法
export async function 执行姓名配对工作流(formData, options = {}) {
    return await Workflow.execute(formData, options);
}

export async function 查询姓名配对状态(requestId) {
    return await Workflow.queryWorkflowStatus(requestId);
}

export async function 取消姓名配对工作流(requestId) {
    return await Workflow.cancelWorkflow(requestId);
}

export default {
    执行姓名配对工作流,
    查询姓名配对状态,
    取消姓名配对工作流
};