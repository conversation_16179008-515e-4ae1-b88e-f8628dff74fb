<template>
  <view class="optimized-textarea-container" :class="[platformClass, {
    'has-overflow': hasOverflow,
    'selecting': isLongPress || selectionEnabled || isTextSelectionActive()
  }]">
    <!-- ���ı��� -->
    <textarea
      ref="textareaRef"
      class="optimized-textarea"
      :value="modelValue"
      :placeholder="showAnimatedPlaceholder ? '' : placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :auto-height="true"
      :fixed="false"
      :cursor-spacing="cursorSpacing"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @paste="handlePaste"
      @keyboardheightchange="handleKeyboardHeightChange"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @click="handleClick"
    ></textarea>
    
    <!-- ���Ͱ�ť��ۣ��������½� -->
    <view class="send-button-slot">
      <slot name="sendButton"></slot>
    </view>
    
    <!-- ��̬��ʾ������� -->
    <view 
      v-if="showAnimatedPlaceholder && !modelValue" 
      class="animated-placeholder"
      :class="{ 'is-focused': isFocused }"
    >
      <text class="placeholder-text">{{ currentPlaceholder }}</text>
    </view>

    <!-- H5ƽ̨�Ĺ���ָʾ�� -->
    <view 
      v-if="isH5 && hasOverflow" 
      class="scroll-indicator"
      :class="{'show-indicator': hasOverflow && !isTouchingTextarea}"
    ></view>
    
    <!-- �����ã�����λ��ָʾ�������� -->
    <view
      v-if="false && debugMode"
      class="debug-scroll-indicator"
      :style="{
        top: `${20 + (currentScrollTop / 10)}px`, 
        height: `${Math.min(50, contentLines * 3)}px`
      }"
    ></view>
    
    <!-- �����ã���ʾ�ײ���ǣ����� -->
    <text
      v-if="false && (isDebug || debugMode)" 
      class="debug-bottom-marker"
    >��</text>
  </view>
</template>

<script>
export default {
  name: 'OptimizedTextArea',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '����������'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200 // ����Ĭ�����߶�Ϊ200px���ṩ���õ���Ұ
    },
    minHeight: {
      type: Number,
      default: 40 // Ĭ����С�߶�40px
    },
    isDebug: {
      type: Boolean,
      default: false
    },
    isSending: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'confirm', 'paste', 'keyboardheightchange', 'height-change', 'send', 'paste-image'],
  computed: {
    textareaStyle() {
      return {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`,
        overflowY: this.hasOverflow ? 'auto' : 'hidden',
        paddingRight: '60px', /* Ϊ���Ͱ�ť�����ռ� */
      };
    }
  },
  data() {
    return {
      isFocused: false,
      platformClass: '',
      currentPlaceholder: '',
      placeholderIndex: 0,
      placeholderTimer: null,
      hasOverflow: false,
      textareaHeight: 0,
      lastContentHeight: 0,
      isComposing: false,
      isTouchingTextarea: false,
      touchStartY: 0,
      touchStartX: 0,
      touchStartTime: 0,
      isScrolling: false,
      initialScrollTop: 0,
      momentum: 0,
      animationFrameId: null,
      isH5: false,
      isApp: false,
      isMiniProgram: false,
      isIOS: false,
      isAndroid: false,
      contentLines: 1,
      scrollAnimation: null,
      observerAttached: false,
      scrollTimers: [],
      mutationObserver: null,
      lastScrollAttemptTime: 0,
      autoHeightActive: true,
      isUpdatingHeight: false,
      userHasScrolled: false,
      scrollAttempts: 0,
      lastUserInteraction: 0,
      debugInterval: null,
      debugMode: this.isDebug,
      isLongPress: false,
      longPressTimer: null,
      lastClickTime: 0,  // �������ڼ��˫���ı���
      selectionEnabled: false, // ����Ƿ��������ı�ѡ��ģʽ
      selectionLogShown: false, // �������ڼ���ı�ѡ��ģʽ�ı���
      resetScrollFlagTimer: null,
      initialTextLoaded: false, // ���ӱ�ǣ���ʾ��ʼ�ı��Ƿ��Ѽ���
      lastDeltaY: 0,     // ���ڼ�¼��һ��Y��ı仯��
      lastDeltaTime: 0,  // ���ڼ����ٶȵ�ʱ���
      isHBuilder: false, // ����HBuilder��������־
      _hasSelectedAll: false, // ���ӱ�ǣ���ʾ�Ƿ��Ѿ�ִ�й�ȫѡ����
    };
  },
  watch: {
    placeholders: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.currentPlaceholder = newVal[0];
          this.startPlaceholderAnimation();
        } else {
          this.currentPlaceholder = this.placeholder;
        }
      }
    },
    modelValue: {
      immediate: true,
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.checkOverflow();
          
          // ֻ�������״μ��ػ���������ʱ�Զ�����
          const isSubstantialAdd = !oldVal || (newVal && oldVal && newVal.length > oldVal.length + 10);
          // ����û��Ѿ��ֶ��������ҪԶ
          if (isSubstantialAdd && !this.userHasScrolled) {
            if (this.scrollAttempts < 3) {
              this.scrollAttempts++;
              this.scrollToBottom();
              
              setTimeout(() => {
                this.scrollAttempts = 0;
              }, 500);
            }
          }
        });
      }
    },
    maxHeight() {
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    }
  },
  created() {
    this.detectPlatform();
    this.setupAppFeatures();
  },
  mounted() {
    this.$nextTick(() => {
      this.initTextarea();
      
      this.setupMutationObserver();
      
      // ���¼��������¼�
      const textarea = this.getTextareaElement();
      if (textarea && this.isH5) {
        // ������ȷ�Ĺ����¼�����
        textarea.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
        
        // ���Ӽ����¼���֧��Ctrl+Aȫѡ��Delete/Backspaceɾ��
        textarea.addEventListener('keydown', this.handleKeyDown);
        
        // �����ı�ѡ������
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
      }
      
      // ��ʼ��ͼƬճ������
      console.log('��ʼ��ͼƬճ������');
      
      try {
        // ��document��������ճ���¼�����
        if (this.isH5 && typeof document !== 'undefined') {
          // ����ȫ��ճ���¼�������ȷ���ܲ�������ճ���¼�
          document.addEventListener('paste', (e) => {
            console.log('document����ճ���¼�������');
            this.handlePaste(e);
          });
          
          // ���Ӷ����ճ���¼�������window����
          if (typeof window !== 'undefined') {
            window.addEventListener('paste', (e) => {
              console.log('window����ճ���¼�������');
              this.handlePaste(e);
            });
          }
          
          // �������ص��ļ�����Ԫ����Ϊ��ѡ����
          this.createHiddenFileInput();
        }
        
        // ��HBuilder�������ر���
        if (this.isHBuilder) {
          console.log('��HBuilder�����г�ʼ��ͼƬճ������');
          
          // ����ȫ��ճ���¼�����
          if (typeof window !== 'undefined') {
            window.addEventListener('paste', (e) => {
              console.log('window����ճ���¼�������');
              this.handlePaste(e);
            });
          }
          
          // �����plus�������������⴦��
          if (typeof plus !== 'undefined') {
            // ��������������plus�����������ʼ������
            console.log('��⵽plus��������������ճ������');
          }
        }
        
        // ��APP�������ر���
        if (this.isApp) {
          console.log('��APP�����г�ʼ��ͼƬճ������');
          // APP���������⴦������enhanceAppTextSelection������ʵ��
        }
        
        // ��С���򻷾����ر���
        if (this.isMiniProgram) {
          console.log('��С���򻷾��г�ʼ��ͼƬճ������');
          // С���򻷾������⴦������setupMiniProgramImagePaste������ʵ��
        }
      } catch (err) {
        console.error('��ʼ��ͼƬճ������ʧ��:', err);
      }
      
      // ��ʼ����������
      if (this.isDebug || false) { // ��Ϊfalse�����ò����ı�
        this.enableDebugging(false); // ���õ��������ʾ
        
        setTimeout(() => {
          const testText = "����һ�������ı�\n������֤���������Ƿ���������\n������\n������\n������\n������\n������\n�ڰ���\n�ھ���\n��ʮ��";
          this.setText(testText);
          this.initialTextLoaded = true;
          console.log('���Ӳ����ı��Բ��Թ���');
        }, 1000);
      }
    });
  },
  
  // ��ʼ��ͼƬճ������
  beforeUnmount() {
    this.clearPlaceholderTimer();
    this.cleanupScrollAnimation();
    this.cleanupResizeObserver();
    this.cleanupMutationObserver();
    this.clearAllTimers();
    
    const textarea = this.getTextareaElement();
    if (textarea) {
      // �Ƴ������¼�������
      try {
        textarea.removeEventListener('scroll', this.handleScroll);
        textarea.removeEventListener('wheel', this.handleMouseWheel);
        textarea.removeEventListener('mousewheel', this.handleMouseWheel);
        textarea.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
        textarea.removeEventListener('keydown', this.handleKeyDown);
        textarea.removeEventListener('mouseup', this.handleMouseUp);
        textarea.removeEventListener('selectstart', this.handleSelectStart);
        textarea.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('�Ƴ��¼�������ʧ��:', e);
      }
    }
    
    // �Ƴ�window������¼�������
    if (this.isH5 && window) {
      try {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('�Ƴ�window�¼�������ʧ��:', e);
      }
    }
    
    // �Ƴ�document������¼�������
    if (this.isH5 && document) {
      try {
        document.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('�Ƴ�document�¼�������ʧ��:', e);
      }
    }
    
    if (this.debugMode) {
      this.disableDebugging();
    }
  },
  methods: {
    detectPlatform() {
      try {
        // ����Ƿ�ΪH5����
        this.isH5 = typeof window !== 'undefined' && typeof document !== 'undefined';
        
        // ���HBuilder����
        this.isHBuilder = typeof navigator !== 'undefined' && 
                         (navigator.userAgent.indexOf('HBuilder') > -1 || 
                          navigator.userAgent.indexOf('Html5Plus') > -1 ||
                          (typeof window !== 'undefined' && typeof window.plus !== 'undefined') ||
                          (typeof plus !== 'undefined'));
        
        if (this.isHBuilder) {
          console.log('��⵽HBuilder��������������ճ���������ı�ѡ��');
        }
        
        // ���App����
        try {
          // HBuilder/uni-app�������
          this.isApp = (typeof uni !== 'undefined' && typeof plus !== 'undefined') || 
                       (typeof window !== 'undefined' && window.plus) ||
                       !!navigator?.userAgent?.match(/Html5Plus/i);
        } catch (e) {
          this.isApp = false;
        }
        
        // С���򻷾����
        try {
          this.isMiniProgram = typeof wx !== 'undefined' && typeof wx.getSystemInfoSync === 'function';
          
          // ������uni-app�����е�С����
          if (!this.isMiniProgram && typeof uni !== 'undefined' && uni.getSystemInfoSync) {
            const sysInfo = uni.getSystemInfoSync();
            this.isMiniProgram = sysInfo.mp && (sysInfo.mp.weixin || sysInfo.mp.alipay || sysInfo.mp.baidu);
          }
          
          // �����С���򻷾�����������ͼƬճ������
          if (this.isMiniProgram) {
            console.log('��⵽С���򻷾�����������ͼƬճ������');
            this.setupMiniProgramImagePaste();
          }
        } catch (e) {
          this.isMiniProgram = false;
        }
        
        // �豸���ͼ��
        if (this.isH5 && typeof navigator !== 'undefined') {
          this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
          this.isAndroid = /Android/i.test(navigator.userAgent);
        } else if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
          try {
            const sysInfo = uni.getSystemInfoSync();
            this.isIOS = sysInfo.platform === 'ios';
            this.isAndroid = sysInfo.platform === 'android';
          } catch (e) {
            // Ĭ�ϼ��
            if (typeof navigator !== 'undefined') {
              this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
              this.isAndroid = /Android/i.test(navigator.userAgent);
            }
          }
        }
        
        // ���ݲ�ͬƽ̨��������
        if (this.isH5) {
          this.platformClass = 'h5-platform';
        } else if (this.isApp) {
          this.platformClass = 'app-platform';
        } else if (this.isMiniProgram) {
          this.platformClass = 'mp-platform';
        }
        
        if (this.debugMode) {
          console.log('��ǰƽ̨:', this.platformClass, 
                      '�豸:', this.isIOS ? 'iOS' : this.isAndroid ? 'Android' : '����');
        }
      } catch (e) {
        console.error('ƽ̨���ʧ��:', e);
        // Ĭ��ΪH5
        this.platformClass = 'h5-platform';
      }
    },
    
    // ����С���򻷾��µ�ͼƬճ������
    setupMiniProgramImagePaste() {
      if (!this.isMiniProgram) return;
      
      try {
        // ΢��С����
        if (typeof wx !== 'undefined') {
          // ����������仯
          wx.onClipboardData && wx.onClipboardData((res) => {
            if (res && res.data) {
              // ����Ƿ���ͼƬ·��
              if (res.data.indexOf('wxfile://') === 0 || 
                  res.data.indexOf('http') === 0 && 
                  (res.data.indexOf('.png') > -1 || 
                   res.data.indexOf('.jpg') > -1 || 
                   res.data.indexOf('.jpeg') > -1 || 
                   res.data.indexOf('.gif') > -1)) {
                
                // ������ͼƬ·��������ͼƬճ���¼�
                this.$emit('paste-image', {
                  path: res.data,
                  type: 'image/png' // Ĭ������
                });
              } else {
                // ��ͨ�ı������뵽���λ��
                this.insertTextAtCursor(res.data);
              }
            }
          });
        }
        
        // uni-app����
        if (typeof uni !== 'undefined') {
          // �����Զ����ͼƬճ����ť������С���򻷾���
          this.$nextTick(() => {
            // ��������������һ��ѡ��ͼƬ�İ�ť����Ϊ�˱��ֽ����࣬���ǲ����Ӷ���UI
            // ��������������ṩ��ͼƬ�ϴ�����
          });
        }
      } catch (e) {
        console.error('����С����ͼƬճ������ʧ��:', e);
      }
    },

    clearAllTimers() {
      for (const timer of this.scrollTimers) {
        clearTimeout(timer);
      }
      this.scrollTimers = [];
    },

    initTextarea() {
      this.textareaHeight = this.minHeight;
      
      // ȷ������DOM�Ѿ�����
      this.$nextTick(() => {
        this.updateHeightAndCheck();
        this.initEvents();
        
        if (this.debugMode) {
          this.enableDebugging();
        }
      });
    },
    
    initEvents() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // �������ܵľɼ�����
      try {
        textarea.removeEventListener('scroll', this.handleScroll);
        textarea.removeEventListener('wheel', this.handleMouseWheel);
        textarea.removeEventListener('mousewheel', this.handleMouseWheel);
        textarea.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
        textarea.removeEventListener('keydown', this.handleKeyDown);
        textarea.removeEventListener('mouseup', this.handleMouseUp);
        textarea.removeEventListener('selectstart', this.handleSelectStart);
        textarea.removeEventListener('paste', this.handlePaste);
      } catch(e) {}
      
      // �����µļ�����
      textarea.addEventListener('scroll', this.handleScroll);
      
      // �ر����H5���������������¼�
      if (this.isH5) {
        textarea.addEventListener('wheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
        
        // ���Ӽ����¼���֧��Ctrl+Aȫѡ��������ݼ�
        textarea.addEventListener('keydown', this.handleKeyDown);
        
        // ����ѡ���¼�����
        textarea.addEventListener('selectstart', this.handleSelectStart);
        textarea.addEventListener('mouseup', this.handleMouseUp);
        
        // ���û������ı�ѡ������
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
        
        // ȷ��iOS�Ĵ����¼���Ӧ������
        if (this.isIOS) {
          textarea.style.webkitOverflowScrolling = 'touch';
        }
        
        // �����Զ��������ײ�����Ϊ�����û����ƹ���
        if (typeof this.userHasScrolled === 'undefined') {
          this.userHasScrolled = false;
        }
      }
      
      // ΪHBuilder������������֧��
      if (this.isHBuilder) {
        console.log('HBuilder�������⴦��');
        
        // ��ǿѡ������
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
        
        // ȷ���ܽ���ճ���¼�
        textarea.setAttribute('contenteditable', 'true');
        
        // ����ճ���¼�����
        window.addEventListener('paste', this.handlePaste);
        textarea.addEventListener('paste', this.handlePaste);
        
        // ���Ӽ����¼�֧��
        textarea.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keydown', this.handleKeyDown);
        
        // ����֧�ֿ�ѡ
        textarea.addEventListener('selectstart', this.handleSelectStart);
        textarea.addEventListener('mouseup', this.handleMouseUp);
        
        // ����������ʽ
        textarea.classList.add('hbuilder-textarea');
      }
      
      // Ӧ�ù����Ż�
      this.enhanceScrolling();
    },
    
    enableDebugging(showPanel = true) {
      console.log('TextArea����ģʽ������');
      
      if (this.isH5 && showPanel) {
        const debugPanel = document.createElement('div');
        debugPanel.style.position = 'fixed';
        debugPanel.style.bottom = '10px';
        debugPanel.style.right = '10px';
        debugPanel.style.backgroundColor = 'rgba(0,0,0,0.7)';
        debugPanel.style.color = '#fff';
        debugPanel.style.padding = '5px 10px';
        debugPanel.style.borderRadius = '5px';
        debugPanel.style.fontSize = '12px';
        debugPanel.style.zIndex = '9999';
        debugPanel.style.maxWidth = '200px';
        debugPanel.id = 'textarea-debug-panel';
        
        const updateDebugInfo = () => {
          const textarea = this.getTextareaElement();
          if (!textarea) return;
          
          debugPanel.innerHTML = `
            <div>ƽ̨: ${this.platformClass}</div>
            <div>����λ��: ${textarea.scrollTop || 0}px</div>
            <div>���ݸ߶�: ${textarea.scrollHeight || 0}px</div>
            <div>���Ӹ߶�: ${textarea.clientHeight || 0}px</div>
            <div>�����߶�: ${this.textareaHeight}px</div>
            <div>���߶�: ${this.maxHeight}px</div>
            <div>���: ${this.hasOverflow ? '��' : '��'}</div>
            <div>����: ${this.contentLines}</div>
          `;
        };
        
        document.body.appendChild(debugPanel);
        this.debugInterval = setInterval(updateDebugInfo, 500);
        updateDebugInfo();
      }
    },
    
    disableDebugging() {
      if (this.isH5) {
        clearInterval(this.debugInterval);
        const panel = document.getElementById('textarea-debug-panel');
        if (panel) {
          document.body.removeChild(panel);
        }
      }
    },

    getTextareaElement() {
      if (!this.$refs.textareaRef) return null;
      
      if (this.isH5) {
        if (this.$refs.textareaRef.$el) {
          return this.$refs.textareaRef.$el;
        }
        return this.$refs.textareaRef;
      } 
      
      return this.$refs.textareaRef;
    },
    
    updateHeightAndCheck() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      this.isUpdatingHeight = true;
      
      let contentHeight = this.minHeight;
      
      if (this.isH5) {
        try {
          const originalHeight = textarea.style.height;
          const originalMaxHeight = textarea.style.maxHeight;
          
          textarea.style.height = 'auto';
          textarea.style.maxHeight = 'none';
          
          contentHeight = Math.max(this.minHeight, textarea.scrollHeight);
          
          textarea.style.height = originalHeight;
          textarea.style.maxHeight = originalMaxHeight;
          
          if (this.debugMode) {
            console.log('H5 - ���ݸ߶ȼ���:', contentHeight, 'scrollHeight:', textarea.scrollHeight);
          }
        } catch (e) {
          console.error('����߶�ʧ��:', e);
        }
      } else {
        const lineCount = this.countLines(this.modelValue);
        const lineHeight = 20;
        contentHeight = Math.max(this.minHeight, lineCount * lineHeight);
        
        if (this.debugMode) {
          console.log('��H5 - ����:', lineCount, '����߶�:', contentHeight);
        }
      }
      
      this.lastContentHeight = contentHeight;
      
      const newHeight = Math.min(Math.max(contentHeight, this.minHeight), this.maxHeight);
      
      if (this.textareaHeight !== newHeight) {
        this.textareaHeight = newHeight;
        this.$emit('height-change', newHeight);
        
        if (this.debugMode) {
          console.log('�߶ȸ���Ϊ:', newHeight);
        }
      }
      
      const hasOverflow = contentHeight > this.maxHeight;
      if (hasOverflow !== this.hasOverflow) {
        this.hasOverflow = hasOverflow;
        if (this.debugMode) {
          console.log('���״̬:', hasOverflow ? '���' : 'δ���');
        }
      }
      
      this.isUpdatingHeight = false;
      
      this.$forceUpdate();
    },
    
    countLines(text) {
      if (!text) return 1;
      
      const lines = (text.match(/\n/g) || []).length + 1;
      this.contentLines = lines;
      return lines;
    },
    
    cleanupMutationObserver() {
      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
        this.mutationObserver = null;
      }
    },
    
    cleanupResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },

    startPlaceholderAnimation() {
      this.clearPlaceholderTimer();
      
      if (!this.placeholders || this.placeholders.length <= 1) return;
      
      this.placeholderTimer = setInterval(() => {
        this.placeholderIndex = (this.placeholderIndex + 1) % this.placeholders.length;
        this.currentPlaceholder = this.placeholders[this.placeholderIndex];
      }, 3000);
    },

    clearPlaceholderTimer() {
      if (this.placeholderTimer) {
        clearInterval(this.placeholderTimer);
        this.placeholderTimer = null;
      }
    },
    
    cleanupScrollAnimation() {
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
        this.scrollAnimation = null;
      }
    },

    handleClick(e) {
      // ����Ƿ���˫����Ϊ
      const now = Date.now();
      if (now - this.lastClickTime < 300) {
        // ˫���������ı�ѡ��ģʽ
        this.selectionEnabled = true;
        
        // ȷ���ı���ѡ��
        const textarea = this.getTextareaElement();
        if (textarea) {
          textarea.style.userSelect = 'text';
          textarea.style.webkitUserSelect = 'text';
          textarea.style.MozUserSelect = 'text';
          textarea.style.msUserSelect = 'text';
          
          // ����ƽ̨������
          textarea.style.webkitTapHighlightColor = 'rgba(77, 157, 255, 0.3)';
          
          // �����ֶ�������ʽ����ǿѡ���Ӿ�Ч��
          if (this.isHBuilder) {
            textarea.classList.add('selecting-text');
          }
        }
        
        // ��APP�����£�����ʹ��ȫѡ
        if (this.isApp) {
          this.$nextTick(() => {
            try {
              // ����ԭ��ȫѡ
              if (textarea && typeof textarea.setSelectionRange === 'function') {
                textarea.setSelectionRange(0, textarea.value.length);
              } else if (uni && uni.createSelectorQuery) {
                // ����ͨ��uni API����
                uni.createSelectorQuery()
                  .in(this)
                  .select('.optimized-textarea')
                  .context((res) => {
                    if (res && res.context) {
                      res.context.setSelectionRange(0, this.modelValue.length);
                    }
                  }).exec();
              }
            } catch(e) {
              console.error('ȫѡ�ı�ʧ��:', e);
            }
          });
        }
        
        // �񶯷���
        try {
          if (navigator && navigator.vibrate) {
            navigator.vibrate(50);
          }
        } catch(e) {}
        
        if (this.debugMode) console.log('��⵽˫���������ı�ѡ��ģʽ');
      } else {
        // ��������¼ʱ��
        this.lastClickTime = now;
      }
    },

    handleInput(e) {
      const value = e.detail?.value || e.target?.value || '';
      this.$emit('update:modelValue', value);
      
      // ʹ��handleContent�����������ݱ仯
      this.$nextTick(() => {
        this.handleContent();
      });
    },

    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
      
      // ֻ����δ��⵽�û�����ʱ�Ź������ײ�
      if (!this.userHasScrolled) {
        this.$nextTick(() => {
          this.enhanceScrolling();
          // ʹ�õ��ι�������Ҫǿ��
          this.scrollToBottom();
        });
      }
    },

    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    handleConfirm(e) {
      // �ȴ���ԭ�е�confirm�¼�
      this.$emit('confirm', e);
      
      // ����������Զ����ͣ���ֱ�ӷ�����Ϣ
      if (this.isSending) {
        this.sendMessage();
      } else {
        // ����ֻ�������ײ�
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    handlePaste(e) {
      console.log('ճ���¼�������', e ? '�¼��������' : '�¼����󲻴���');
      
      try {
        // ���ԴӼ������¼���ȡ����
        if (e && e.clipboardData) {
          console.log('���������ݴ���', 
                     'items:', e.clipboardData.items ? e.clipboardData.items.length : '��items', 
                     'files:', e.clipboardData.files ? e.clipboardData.files.length : '��files');
          
          // ���ȼ��files
          if (e.clipboardData.files && e.clipboardData.files.length > 0) {
            const file = e.clipboardData.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('��clipboardData.files��ȡ��ͼƬ:', file.type);
              e.preventDefault();
              
              // ����ͼƬճ���¼�
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                source: 'clipboard-files'
              });
              
              return false;
            }
          }
          
          // ���items
          if (e.clipboardData.items) {
            // ��������ճ�������ͼƬ
            for (let i = 0; i < e.clipboardData.items.length; i++) {
              const item = e.clipboardData.items[i];
              console.log('��������item:', item.kind, item.type);
              
              if (item.kind === 'file' && item.type.indexOf('image') !== -1) {
                try {
                  const blob = item.getAsFile();
                  if (blob) {
                    console.log('��clipboardData.items��ȡ��ͼƬ:', item.type);
                    e.preventDefault();
                    
                    // ����ͼƬճ���¼�
                    this.$emit('paste-image', {
                      blob: blob,
                      type: item.type,
                      source: 'clipboard-items'
                    });
                    
                    return false;
                  }
                } catch (itemErr) {
                  console.error('��ȡ���������ļ�ʧ��:', itemErr);
                }
              }
            }
            
            // ���û��ͼƬ�����Ի�ȡ�ı�
            let pastedText = '';
            try {
              pastedText = e.clipboardData.getData('text/plain');
              console.log('�Ӽ������ȡ���ı�:', pastedText ? '�ɹ�' : 'ʧ��');
            } catch (textErr) {
              console.error('��ȡ�������ı�ʧ��:', textErr);
            }
            
            if (pastedText) {
              // ����ı��Ƿ������ͼƬURL
              if (this.isImageUrl(pastedText)) {
                console.log('��⵽������ͼƬURL:', pastedText);
                e.preventDefault();
                
                // ����ͼƬճ���¼�
                this.$emit('paste-image', {
                  path: pastedText,
                  type: 'image/url',
                  source: 'clipboard-text-url'
                });
                
                return false;
              }
              
              // ��ͨ�ı�����ֹĬ����Ϊ���ֶ�����ճ��
              e.preventDefault();
              
              // ��ȡ��ǰֵ�͹��λ��
              const textarea = this.getTextareaElement();
              let currentValue = this.modelValue || '';
              let selectionStart = 0;
              let selectionEnd = 0;
              
              try {
                if (textarea && textarea.selectionStart !== undefined) {
                  selectionStart = textarea.selectionStart;
                  selectionEnd = textarea.selectionEnd;
                }
              } catch (err) {
                console.error('��ȡѡ��Χʧ��:', err);
                // �����ȡʧ�ܣ�Ĭ��׷�ӵ�ĩβ
                selectionStart = currentValue.length;
                selectionEnd = currentValue.length;
              }
              
              // �����ı�
              const newValue = currentValue.substring(0, selectionStart) + 
                              pastedText + 
                              currentValue.substring(selectionEnd);
              
              // ����ֵ
              this.$emit('update:modelValue', newValue);
              
              // ���¹��λ��
              this.$nextTick(() => {
                try {
                  if (textarea && typeof textarea.setSelectionRange === 'function') {
                    const newPosition = selectionStart + pastedText.length;
                    textarea.focus();
                    textarea.setSelectionRange(newPosition, newPosition);
                  }
                } catch (err) {
                  console.error('���ù��λ��ʧ��:', err);
                }
                
                // ���¸߶Ⱥ͹���
                this.updateHeightAndCheck();
                this.scrollToBottom();
              });
              
              return false;
            }
          }
        }
        
        // H5�����£�����޷�ͨ���������ȡͼƬ�����ٵ�����ʾ��
        if (this.isH5 && (!e || !e.clipboardData || (!e.clipboardData.files?.length && !e.clipboardData.items?.length))) {
          console.log('H5�������޷�ͨ���������¼���ȡͼƬ��������ʾ��ʾ');
          // ���ٳ���ʹ��navigator.clipboard API
          // ������ʾ�κ���ʾ
          // ��ϵͳ��������Ĭ�ϵ�ճ����Ϊ
        }
        
        // ���⴦��HBuilder����
        if (this.isHBuilder) {
          console.log('HBuilder�������⴦��ճ��');
          
          // ��HBuilder�����У���Ҫ����ʹ��navigator.clipboard API
          // ��Ϊ���ᵼ��"Read permission denied"����
          
          // Ҳ��Ҫ����ʹ��document.execCommand('paste')
          // ��Ϊ���ᵼ��"callback is not a function"����
          
          // ֱ����ϵͳ����ճ���¼�������ֹĬ����Ϊ
          return true;
        }
      } catch (err) {
        console.error('����ճ���¼�ʧ��:', err);
        
        // ���ٳ���ʹ���ļ�ѡ������Ϊ��ѡ����
      }
      
      // �����������ʧ�ܣ�����ֹĬ����Ϊ����ϵͳ����ճ��
      return true;
    },
    
    // ����ı��Ƿ���ͼƬURL
    isImageUrl(text) {
      if (!text) return false;
      
      // �򵥼���Ƿ���ͼƬURL
      const trimmed = text.trim().toLowerCase();
      return (trimmed.startsWith('http') || trimmed.startsWith('https') || 
              trimmed.startsWith('data:image') || trimmed.startsWith('file:') || 
              trimmed.startsWith('wxfile:')) && 
             (trimmed.endsWith('.png') || trimmed.endsWith('.jpg') || 
              trimmed.endsWith('.jpeg') || trimmed.endsWith('.gif') || 
              trimmed.endsWith('.webp') || trimmed.endsWith('.bmp') ||
              trimmed.indexOf('data:image/') !== -1);
    },
    
    // ������HBuilder������ճ��
    tryHBuilderPaste() {
      try {
        // ������ʹ��plus.pasteboard API����Ϊ�����ܲ����û��´���
        console.log('HBuilder�����¼�ճ������');
        
        // ֱ�ӳ��Ի�ȡ�������ı�
        if (typeof uni !== 'undefined' && uni.getClipboardData) {
          uni.getClipboardData({
            success: (res) => {
              if (res && res.data) {
                console.log('�ɹ�ͨ��uni.getClipboardData��ȡ����');
                
                // ����Ƿ���ͼƬURL
                if (this.isImageUrl(res.data)) {
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/url',
                    source: 'hbuilder-clipboard'
                  });
                } else {
                  // ��ͨ�ı������뵽��ǰλ��
                  this.insertTextAtCursor(res.data);
                }
              } else {
                console.log('����������Ϊ��');
              }
            },
            fail: (err) => {
              console.error('uni.getClipboardDataʧ��:', err);
              
              // ���û���ʾ
              if (typeof uni !== 'undefined' && uni.showToast) {
                uni.showToast({
                  title: '�޷����ʼ����壬���ֶ�ճ��',
                  icon: 'none',
                  duration: 2000
                });
              }
            }
          });
        } else {
          console.log('uni.getClipboardData������');
        }
      } catch (err) {
        console.error('HBuilderճ������ʧ��:', err);
      }
    },

    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e);
      
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    
    handleTouchStart(e) {
      const touch = e.touches[0];
      this.touchStartY = touch.clientY;
      this.touchStartX = touch.clientX;
      this.touchStartTime = Date.now();
      this.isTouchingTextarea = true;
      
      // ������������ı�ѡ��
      if (this.longPressTimer) clearTimeout(this.longPressTimer);
      this.longPressTimer = setTimeout(() => {
        this.isLongPress = true;
        this.selectionEnabled = true;
        
        // ��App�����´���ȫѡ
        if (this.isApp) {
          this.selectAllText();
        }
        
        // �񶯷���
        try {
          if (navigator && navigator.vibrate) {
            navigator.vibrate(50);
          }
        } catch(e) {}
      }, 300);
    },
    
    handleTouchMove(e) {
      // ȷ���ǵ��㴥��
      if (e.touches.length !== 1) return;
      
      // �ı�ѡ��ģʽ�����й�������
      if (this.isLongPress || this.selectionEnabled) return;
      
      const touch = e.touches[0];
      const currentY = touch.clientY;
      const currentX = touch.clientX;
      const deltaY = currentY - this.touchStartY;
      const deltaX = currentX - this.touchStartX;
      
      // ��¼ʱ���λ�ƣ����ڼ������
      const now = Date.now();
      const deltaTime = now - this.lastUserInteraction;
      this.lastDeltaY = deltaY;
      this.lastDeltaTime = deltaTime;
      
      // ����Ƿ�����ȷ�Ĵ�ֱ�����������ж��ż�
      if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 3) {
        // ȡ��������ʱ��
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
        
        // ����û�����
        this.userHasScrolled = true;
        this.lastUserInteraction = now;
        
        // ֱ��ִ�й��������������
        const textarea = this.getTextareaElement();
        if (textarea) {
          // �����������ȣ��ر�����HBuilder����
          let sensitivity = this.isH5 ? 2.2 : 3.0;
          // APP�����ر��Ż�
          if (this.isApp) sensitivity = 3.5;
          
          // ֱ�Ӹ�����ָλ���ƶ���ʹ�ø��ߵ�������
          textarea.scrollTop -= deltaY * sensitivity;
          
          // ������ʼ�㣬ʵ����������Ч��
          this.touchStartY = currentY;
          this.touchStartX = currentX;
        }
      } else if (Math.abs(deltaX) > 10) {
        // ˮƽ�������������ı�ѡ��ȡ������
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      }
    },
    
    handleTouchEnd(e) {
      // ȡ��������ʱ��
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }
      
      // ������Թ���
      const now = Date.now();
      const timeElapsed = now - this.lastUserInteraction;
      
      // ֻ���ڶ�ʱ���ڽ����������������Ի�����Ӧ�ù���
      if (timeElapsed < 100 && Math.abs(this.lastDeltaY) > 5) {
        // ���㻬�����ٶ�
        const velocity = this.lastDeltaY / Math.max(10, this.lastDeltaTime);
        if (Math.abs(velocity) > 0.1) {
          const textarea = this.getTextareaElement();
          if (textarea) {
            this.applyInertialScroll(textarea, velocity);
          }
        }
      }
      
      // ����״̬
      this.isTouchingTextarea = false;
      
      // �ӳ������ı�ѡ��״̬
      if (!this.isTextSelectionActive()) {
        setTimeout(() => {
          this.isLongPress = false;
          this.selectionEnabled = false;
        }, 1000);
      }
    },
    
    // ����Ƿ����ڽ����ı�ѡ��
    isTextSelectionActive() {
      try {
        // ����H5�����м��
        if (this.isH5 && window) {
          // ���window.getSelection
          if (window.getSelection) {
            const selection = window.getSelection();
            if (selection && selection.type === 'Range' && selection.toString().length > 0) {
              return true;
            }
          }
          
          // ����ı���������ѡ��״̬
          const textarea = this.getTextareaElement();
          if (textarea && 
              typeof textarea.selectionStart !== 'undefined' && 
              textarea.selectionStart !== textarea.selectionEnd) {
            return true;
          }
        }
        
        // ��H5����������selectionEnabled��־
        return this.selectionEnabled;
      } catch (e) {
        console.error('����ı�ѡ��״̬ʱ����:', e);
      }
      
      // Ĭ�Ϸ���false
      return false;
    },

    applyScroll(element, scrollPosition) {
      if (!element) return;
      
      try {
        // ֱ�Ӹ�Ч���ù���λ�ã���������߼�
        const maxScroll = Math.max(0, element.scrollHeight - element.clientHeight);
        const limitedScroll = Math.max(0, Math.min(maxScroll, scrollPosition));
        
        // ����ʹ��ֱ�Ӹ�ֵ���ٶȸ���
        element.scrollTop = limitedScroll;
        
        // ���ض�ƽ̨�Ķ����Ż�
        if (this.isApp) {
          // APP���������޸����ܵĹ�������
          setTimeout(() => {
            if (Math.abs(element.scrollTop - limitedScroll) > 2) {
              element.scrollTop = limitedScroll;
            }
          }, 0);
        }
      } catch (err) {
        console.error('����Ӧ��ʧ��:', err);
      }
    },
    
    applyInertialScroll(element, velocity) {
      if (!element || Math.abs(velocity) < 0.05) return;
      
      // �����ʼ�ٶȣ�APP����ʹ�ø����ֵ
      const initialSpeed = velocity * (this.isApp ? 100 : 80);
      // ���������ʣ��ӳ���������
      const deceleration = this.isApp ? 0.95 : 0.92;
      
      let currentSpeed = initialSpeed;
      let currentPosition = element.scrollTop;
      
      // �����ɵĶ���
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
      }
      
      // �������Զ���
      const animate = () => {
        // Ӧ�õ�ǰ�ٶ�
        currentPosition -= currentSpeed;
        
        // �߽���
        if (currentPosition < 0) {
          currentPosition = 0;
          currentSpeed = 0;
        } else if (currentPosition > element.scrollHeight - element.clientHeight) {
          currentPosition = element.scrollHeight - element.clientHeight;
          currentSpeed = 0;
        }
        
        // Ӧ��λ��
        element.scrollTop = currentPosition;
        
        // ����
        currentSpeed *= deceleration;
        
        // ����������ֹͣ
        if (Math.abs(currentSpeed) > 0.5) {
          this.scrollAnimation = requestAnimationFrame(animate);
        } else {
          this.scrollAnimation = null;
        }
      };
      
      // ��ʼ����
      this.scrollAnimation = requestAnimationFrame(animate);
    },

    setCursorToEnd() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      try {
        const len = this.modelValue.length;
        
        if (this.isH5) {
          textarea.focus();
          
          if (typeof textarea.selectionStart !== 'undefined') {
            textarea.selectionStart = len;
            textarea.selectionEnd = len;
          }
        } else {
          if (uni && uni.createSelectorQuery) {
            const query = uni.createSelectorQuery().in(this);
            query.select('.optimized-textarea').context((res) => {
              if (res && res.context) {
                res.context.setSelectionRange(len, len);
              }
            }).exec();
          }
        }
        
        this.forceScrollToBottom();
      } catch (error) {
        console.error('���ù��λ��ʧ��:', error);
      }
    },

    setupMutationObserver() {
      if (this.isH5 && window.MutationObserver) {
        const textarea = this.getTextareaElement();
        if (!textarea) return;
        
        this.mutationObserver = new MutationObserver(mutations => {
          if (this.debugMode) {
            console.log('��⵽DOM�仯:', mutations.length);
          }
          
          setTimeout(() => {
            if (!this.userHasScrolled) {
              this.forceScrollToBottom();
            }
          }, 50);
        });
        
        const config = {
          childList: true,
          subtree: true,
          characterData: true,
          attributes: true
        };
        
        this.mutationObserver.observe(textarea, config);
      }
    },
    
    handleScroll(e) {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // ��¼�û�����������Ϊ
      if (Date.now() - this.lastUserInteraction < 1000) {
        this.userHasScrolled = true;
        
        // һ��ʱ��������û�������־�����ӳ�ʱ��
        clearTimeout(this.resetScrollFlagTimer);
        this.resetScrollFlagTimer = setTimeout(() => {
          this.userHasScrolled = false;
        }, 5000); // ���ӵ�5�룬���û�������Ķ��ͱ༭ʱ��
      }
      
      // ����Ƿ������������ײ�
      const isAtTop = textarea.scrollTop <= 0;
      const isAtBottom = Math.abs(textarea.scrollTop + textarea.clientHeight - textarea.scrollHeight) <= 2;
      
      if (isAtTop) {
        textarea.classList.add('at-top');
        textarea.classList.remove('at-bottom');
      } else if (isAtBottom) {
        textarea.classList.remove('at-top');
        textarea.classList.add('at-bottom');
      } else {
        textarea.classList.remove('at-top');
        textarea.classList.remove('at-bottom');
      }
    },
    
    updateScrollIndicator(textarea, isAtBottom) {
      if (!this.hasOverflow) return;
      
      const container = this.$el;
      if (!container) return;
      
      if (isAtBottom) {
        container.classList.remove('show-bottom-indicator');
      } else {
        container.classList.add('show-bottom-indicator');
      }
    },
    
    setText(text) {
      // �����ı�ʱ��Ӧ�������û�����״̬
      const wasInitialText = !this.initialTextLoaded;
      
      this.$emit('update:modelValue', text);
      
      this.$nextTick(() => {
        this.updateHeightAndCheck();
        
        // ֻ���״μ����ı�ʱ���Զ��������ײ�
        if (wasInitialText) {
          setTimeout(() => {
            this.userHasScrolled = false; // ��ʱ����״̬
            this.forceScrollToBottom();
            this.initialTextLoaded = true;
          }, 50);
          
          setTimeout(() => {
            this.userHasScrolled = false;
            this.forceScrollToBottom();
            this.setCursorToEnd();
          }, 200);
        }
      });
    },

    clearText() {
      this.$emit('update:modelValue', '');
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    },

    focus() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.focus();
        this.forceScrollToBottom();
      }
    },

    blur() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.blur();
      }
    },
    
    updateHeightAndScroll() {
      this.updateHeightAndCheck();
      this.forceScrollToBottom();
    },

    forceScrollToBottom() {
      // ǿ�ƹ���������userHasScrolled״̬��
      this.scrollToBottom(true);
    },
    
    scrollToBottom(force = false) {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // ����û��Ѿ��ֶ��������Ҳ���ǿ�ƹ�������ִ��
      if (this.userHasScrolled && !force) {
        return;
      }
      
      // ��ֱ�ӵ����ù���λ��
      try {
        textarea.scrollTop = textarea.scrollHeight;
      } catch (e) {
        console.error('����ʧ��:', e);
      }
    },

    checkOverflow() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // �ж������Ƿ񳬳����߶�
      const contentHeight = this.isH5 ? 
        textarea.scrollHeight : 
        this.countLines(this.modelValue) * 20;
      
      const hasOverflow = contentHeight > this.maxHeight;
      
      if (hasOverflow !== this.hasOverflow) {
        this.hasOverflow = hasOverflow;
        if (this.debugMode) {
          console.log('���״̬�仯:', hasOverflow ? '�����' : '�����');
        }
      }
    },

    // ���¼�鴦�����ݱ仯
    handleContent() {
      this.updateHeightAndCheck();
      
      // ��������ı�ѡ��������û�û���ֶ��������������ײ�
      if (!this.isTextSelectionActive() && !this.selectionEnabled && !this.userHasScrolled) {
        this.scrollToBottom();
      } else if (this.isTextSelectionActive() || this.selectionEnabled) {
        // �������ѡ���ı���ȷ��������ѡ��
        this.$nextTick(() => {
          if (!document.activeElement || document.activeElement !== this.getTextareaElement()) {
            // ����ı����ǻ�ԾԪ�أ����Ծ۽�
            const textarea = this.getTextareaElement();
            if (textarea) textarea.focus();
          }
        });
      }
    },

    // ��д��ǿ�����������Ƴ�һЩ���ܸ��ŵ��Ż�
    enhanceScrolling() {
      const textarea = this.getTextareaElement();
      if (!textarea || !this.isH5) return;
      
      // ȷ��������Ϊ��ֱ��
      textarea.style.overflowY = 'auto';
      
      if (this.isIOS) {
        // iOS���⴦��
        textarea.style.webkitOverflowScrolling = 'touch';
      } else {
        // ����ƽ̨������һЩ��ʽ
        textarea.style.scrollBehavior = 'auto';
        textarea.style.willChange = 'auto';
      }
    },

    // ��ʽ�����������¼�
    handleMouseWheel(e) {
      // һ����⵽�����֣���������û��ѹ���
      this.userHasScrolled = true;
      
      if (this.debugMode) {
        console.log('��⵽�������¼��������Զ�����');
      }
      
      // �ӳ��û�������ǵĳ���ʱ��
      clearTimeout(this.resetScrollFlagTimer);
      this.resetScrollFlagTimer = setTimeout(() => {
        this.userHasScrolled = false;
        if (this.debugMode) console.log('�����û��������');
      }, 10000); // 10���ڲ��Զ�����
    },

    // ��ǿ���������¼�
    handleKeyDown(e) {
      // ����Ctrl+Aȫѡ
      if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault(); // ��ֹĬ����Ϊȷ�����ǵ�ȫѡ��Ч
        this.selectionEnabled = true;
        
        // ����ִ��ȫѡ
        try {
          this.selectAllText();
          
          // �ر���HBuilder����
          if (this.isHBuilder) {
            // �����ȫѡ����
            const textarea = this.getTextareaElement();
            if (textarea) {
              // �������п��ܵ�ȫѡ����
              if (typeof textarea.select === 'function') {
                textarea.select();
              }
              
              // ��������ѡ��Χ��ȫ��
              if (typeof textarea.setSelectionRange === 'function') {
                textarea.setSelectionRange(0, this.modelValue.length || 0);
              }
              
              // ʹ��document.execCommand
              try {
                document.execCommand('selectAll');
              } catch(e) {}
            }
          }
        } catch(err) {
          console.error('ȫѡʧ��:', err);
        }
        
        return false;
      }
      
      // ����Ctrl+Vճ��
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        console.log('��⵽Ctrl+Vճ����ݼ�');
        
        // �ر���HBuilder����
        if (this.isHBuilder) {
          try {
            // ���������Ƿ���ͼƬ��HBuilder������
            if (typeof plus !== 'undefined' && plus.pasteboard) {
              plus.pasteboard.getImageItems(items => {
                if (items && items.length > 0) {
                  console.log('HBuilder������⵽ճ����ͼƬ');
                  
                  // ��ȡ��һ��ͼƬ
                  const imageItem = items[0];
                  
                  // ����ͼƬճ���¼�
                  this.$emit('paste-image', {
                    path: imageItem.path,
                    type: imageItem.type || 'image/png'
                  });
                  
                  return;
                } else {
                  // ���û��ͼƬ�����Ի�ȡ�ı�
                  this.tryGetClipboardText();
                }
              }, err => {
                console.error('��ȡ������ͼƬʧ��:', err);
                // ���Ի�ȡ�ı�
                this.tryGetClipboardText();
              });
              
              return false;
            } else {
              // ����ʹ��navigator.clipboard API��ȡ����������
              this.tryGetClipboardText();
            }
          } catch(err) {
            console.error('����ʹ��HBuilder������APIʧ��:', err);
          }
        }
        
        // ����ֹĬ����Ϊ����ϵͳ����ճ��
        return true;
      }
      
      // ����Ctrl+Delete��Ctrl+Backspace����ı���
      if ((e.ctrlKey || e.metaKey) && (e.key === 'Delete' || e.key === 'Backspace')) {
        // ��������ݣ�����ı���
        if (this.modelValue) {
          e.preventDefault(); // ��ֹĬ����Ϊ
          
          // ֱ������ı���������ȫѡ
          this.$emit('update:modelValue', '');
          
          return false;
        }
      }
      
      // ����Delete��Backspaceɾ��ѡ������
      if (e.key === 'Delete' || e.key === 'Backspace') {
        console.log('��⵽Delete��Backspace��');
        
        try {
          // ����Ƿ���ѡ���ı�
          const textarea = this.getTextareaElement();
          let hasSelection = false;
          
          // ����1�����textarea��ѡ��Χ
          if (textarea && 
              typeof textarea.selectionStart !== 'undefined' && 
              textarea.selectionStart !== textarea.selectionEnd) {
            hasSelection = true;
          }
          
          // ����2�����document.getSelection
          if (!hasSelection && document.getSelection) {
            const selection = document.getSelection();
            if (selection && selection.type === 'Range' && selection.toString().length > 0) {
              hasSelection = true;
            }
          }
          
          // ����3������Ƿ������ȫѡ״̬
          if (!hasSelection && document.activeElement === textarea && 
              this.selectionEnabled && this.modelValue) {
            console.log('������ȫѡ״̬������ɾ��ȫ������');
            e.preventDefault(); // ��ֹĬ����Ϊ
            this.$emit('update:modelValue', '');
            return false;
          }
          
          // �����ѡ���ı����ֶ�����ɾ��
          if (hasSelection) {
            console.log('��⵽��ѡ���ı���ִ��ɾ��');
            e.preventDefault(); // ��ֹĬ����Ϊ
            this.deleteSelectedText();
            return false;
          }
        } catch(err) {
          console.error('����Delete/Backspace��ʧ��:', err);
        }
      }
      
      // ����Ctrl+X����
      if ((e.ctrlKey || e.metaKey) && e.key === 'x') {
        // ȷ����HBuilder�����¼��й�����������
        if (this.isHBuilder) {
          try {
            // �ȸ���ѡ���ı�
            this.copySelectedText();
            // Ȼ��ɾ��ѡ���ı�
            setTimeout(() => {
              this.deleteSelectedText();
            }, 100);
          } catch(err) {
            console.error('���в���ʧ��:', err);
          }
        }
      }
      
      // ����Ctrl+C����
      if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        // ȷ����HBuilder�����¸��ƹ�����������
        if (this.isHBuilder) {
          try {
            this.copySelectedText();
          } catch(err) {
            console.error('���Ʋ���ʧ��:', err);
          }
        }
      }
      
      // ����Escape����ȡ��ѡ��
      if (e.key === 'Escape') {
        this.selectionEnabled = false;
        
        // ȡ��ѡ��
        try {
          const textarea = this.getTextareaElement();
          if (textarea && typeof textarea.selectionStart !== 'undefined') {
            const cursorPos = textarea.selectionEnd || 0;
            textarea.setSelectionRange(cursorPos, cursorPos);
          }
        } catch(err) {
          console.error('ȡ��ѡ��ʧ��:', err);
        }
      }
    },
    
    // ���Ի�ȡ�������ı�����
    tryGetClipboardText() {
      // ��HBuilder�����У�����ʹ��navigator.clipboard API
      if (this.isHBuilder) {
        console.log('HBuilder�����±���ʹ��navigator.clipboard API');
        
        // ����ʹ��uni API
        this.tryUniClipboardAPI();
        return;
      }
      
      // ����ʹ��navigator.clipboard API
      if (navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText()
          .then(text => {
            if (text) {
              console.log('��navigator.clipboard��ȡ���ı�');
              this.insertTextAtCursor(text);
            }
          })
          .catch(err => {
            console.error('navigator.clipboard.readTextʧ��:', err);
            
            // ����ʹ��uni API
            this.tryUniClipboardAPI();
          });
      } else {
        // ����ʹ��uni API
        this.tryUniClipboardAPI();
      }
    },
    
    // ����ʹ��uni API��ȡ����������
    tryUniClipboardAPI() {
      if (typeof uni !== 'undefined' && uni.getClipboardData) {
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              console.log('��uni.getClipboardData��ȡ���ı�');
              this.insertTextAtCursor(res.data);
            }
          },
          fail: (err) => {
            console.error('uni.getClipboardDataʧ��:', err);
          }
        });
      }
    },
    
    // ��ǿɾ��ѡ���ı�����
    deleteSelectedText() {
      console.log('ִ��ɾ��ѡ���ı�����');
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // ��ȡ��ǰ�ı���ѡ��Χ
        let text = this.modelValue || '';
        let start = 0;
        let end = text.length;
        
        // ���Ի�ȡѡ��Χ
        try {
          if (textarea.selectionStart !== undefined && textarea.selectionEnd !== undefined) {
            start = textarea.selectionStart;
            end = textarea.selectionEnd;
            console.log('��ȡ��ѡ��Χ:', start, end);
          }
        } catch (e) {
          console.error('��ȡѡ��Χʧ�ܣ���ɾ��ȫ���ı�:', e);
        }
        
        // ���û��ѡ��Χ��ѡ��Χ��Ч������Ƿ�Ϊȫѡ״̬
        if (start === end) {
          // ����Ƿ������ȫѡ״̬��δ����ȷ��ȡ��Χ
          if (document.activeElement === textarea && 
              (document.getSelection().toString() === text || 
               document.getSelection().type === 'Range')) {
            console.log('��⵽������ȫѡ״̬��ִ��ȫ�����');
            start = 0;
            end = text.length;
          } else {
            console.log('û��ѡ��Χ����ִ��ɾ��');
            return;
          }
        }
        
        console.log('ɾ��ѡ�в��֣���', start, '��', end);
        
        // �������ı���ɾ��ѡ�в��֣�
        const newText = text.substring(0, start) + text.substring(end);
        
        // �����ı�
        this.$emit('update:modelValue', newText);
        
        // �������ù��λ��
        this.$nextTick(() => {
          try {
            textarea.focus();
            if (typeof textarea.setSelectionRange === 'function') {
              textarea.setSelectionRange(start, start);
            }
          } catch (e) {
            console.error('���ù��λ��ʧ��:', e);
          }
        });
      } catch (e) {
        console.error('ɾ���ı�ʧ��:', e);
        
        // ���ɾ��ʧ�ܣ�����ֱ�����
        this.$emit('update:modelValue', '');
      }
    },

    // ���������ʱ����ƽ̨�ض��ĳ�ʼ��
    setupAppFeatures() {
      if (!this.isApp) return;
      
      // ������ǿAPP�������ı��༭����
      try {
        // ����uni��ready�¼�
        if (typeof uni !== 'undefined') {
          // ��Ҫʱ����ȫ��APP��ʽ
          if (typeof plus !== 'undefined' && plus.webview) {
            const currentWebview = plus.webview.currentWebview();
            if (currentWebview) {
              // ���ܵ�APP�ض��Ż�
              currentWebview.setStyle({
                softinputMode: 'adjustResize' // �����̵���ʱ�Զ�����ҳ���С
              });
            }
          }
          
          // ��APP���������ø���ѡ����
          this.$nextTick(() => {
            setTimeout(() => {
              this.enhanceAppTextSelection();
            }, 500);
          });
        }
      } catch (e) {
        console.error('APP������ǿʧ��:', e);
      }
    },

    // APP�����е�ѡ��ͱ༭��ǿ
    enhanceAppTextSelection() {
      if (!this.isApp) return;
      
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 1. ȷ����ʽ��ȷ
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.webkitTouchCallout = 'default';
        
        // 2. ����APP�����µ�ͼƬճ��֧��
        if (typeof plus !== 'undefined') {
          // ����plus�����µ�ճ���¼�
          document.addEventListener('paste', (e) => {
            this.handlePaste(e);
          });
          
          // ����ͼƬճ��֧��
          this.setupAppImagePaste();
        }
        
        // 3. ���������Զ���˵�
        if (typeof plus !== 'undefined' && plus.nativeUI) {
          // �����Զ��峤������
          textarea.addEventListener('longpress', (e) => {
            // ��ֹĬ����Ϊ
            e.preventDefault();
            
            // ����ѡ��ģʽ
            this.isLongPress = true;
            this.selectionEnabled = true;
            
            // ������
            if (plus.device && plus.device.vibrate) {
              plus.device.vibrate(50);
            }
            
            // ȫѡ�ı�
            this.selectAllText();
            
            // ���Կ�����ʾ�Զ���˵�
            plus.nativeUI.actionSheet({
              title: '�ı�����',
              cancel: 'ȡ��',
              buttons: [
                {title: 'ȫѡ'},
                {title: '����'},
                {title: '����'},
                {title: 'ճ��'},
                {title: 'ճ��ͼƬ'},
                {title: 'ɾ��'}
              ]
            }, (e) => {
              // �����˵�ѡ��
              switch(e.index) {
                case 1: // ȫѡ
                  this.selectAllText();
                  break;
                case 2: // ����
                  this.copySelectedText();
                  break;
                case 3: // ����
                  this.cutSelectedText();
                  break;
                case 4: // ճ��
                  // ���Ի�ȡ���������ݲ�ճ��
                  this.tryGetClipboardText();
                  break;
                case 5: // ճ��ͼƬ
                  this.tryPasteImage();
                  break;
                case 6: // ɾ��
                  this.deleteSelectedText();
                  break;
              }
            });
          });
        }
      } catch (e) {
        console.error('enhanceAppTextSelectionʧ��:', e);
      }
    },
    
    // ����APP�����µ�ͼƬճ������
    setupAppImagePaste() {
      if (!this.isApp) return;
      
      try {
        // ���plus����
        if (typeof plus !== 'undefined' && plus.pasteboard) {
          // ���������������ض��ĳ�ʼ������
          console.log('APP����������ͼƬճ��֧��');
        }
      } catch (e) {
        console.error('����APPͼƬճ������ʧ��:', e);
      }
    },
    
    // ����ճ��ͼƬ
    tryPasteImage() {
      console.log('ճͼƬǰ:', 
                 this.isH5 ? 'H5' : 
                 this.isApp ? 'APP' : 
                 this.isMiniProgram ? 'С' : 
                 this.isHBuilder ? 'HBuilder' : 'δ֪');
      
      try {
        // Բͬƽ̨ʵͼƬճ
        if (this.isHBuilder || (this.isApp && typeof plus !== 'undefined' && plus.pasteboard)) {
          // HBuilder/APP
          console.log('ʹplus.pasteboard APIȡͼƬ');
          
          plus.pasteboard.getImageItems(items => {
            if (items && items.length > 0) {
              console.log('ȡͼƬ:', items.length);
              
              // ȡһͼƬ
              const imageItem = items[0];
              console.log('ͼƬ·:', imageItem.path ? 'Ч' : 'Ч');
              
              // ͼƬճ¼
              this.$emit('paste-image', {
                path: imageItem.path,
                type: imageItem.type || 'image/png',
                source: 'tryPasteImage-plus'
              });
            } else {
              console.log('ûͼƬԻȡı');
              
              // ԻȡıǷͼƬURL
              plus.pasteboard.getString(text => {
                if (text && this.isImageUrl(text)) {
                  console.log('ıͼƬURL:', text);
                  
                  // ͼƬճ¼
                  this.$emit('paste-image', {
                    path: text,
                    type: 'image/url',
                    source: 'tryPasteImage-plus-text'
                  });
                } else {
                  console.log('ıͼƬURL');
                  
                  // H5ͬʵͼƬճ
                  if (this.isH5) {
                    this.showFileSelector();
                  }
                }
              });
            }
          }, err => {
            console.error('ȡͼƬʧ:', err);
            
            // H5ͬʵͼƬճ
            if (this.isH5) {
              this.showFileSelector();
            }
          });
        } else if (this.isH5) {
          // H5
          console.log('H5ԻȡͼƬ');
          
          // 1: ʹnavigator.clipboard API (ִ)
          if (navigator.clipboard && navigator.clipboard.read) {
            console.log('ʹnavigator.clipboard.read');
            
            navigator.clipboard.read()
              .then(clipboardItems => {
                console.log('ȡ:', clipboardItems.length);
                
                let foundImage = false;
                
                for (const clipboardItem of clipboardItems) {
                  console.log(':', clipboardItem.types);
                  
                  for (const type of clipboardItem.types) {
                    if (type.startsWith('image/')) {
                      console.log('⵽ͼƬ:', type);
                      foundImage = true;
                      
                      clipboardItem.getType(type)
                        .then(blob => {
                          console.log('ȡͼƬblob:', blob.size);
                          
                          // ͼƬճ¼
                          this.$emit('paste-image', {
                            blob: blob,
                            type: type,
                            source: 'navigator-clipboard-read'
                          });
                        })
                        .catch(err => {
                          console.error('ȡͼƬblobʧ:', err);
                          // ȡblobʧܣֱӴļѡ
                          this.showFileSelector();
                        });
                      
                      break;
                    }
                  }
                  
                  if (foundImage) break;
                }
                
                if (!foundImage) {
                  console.log('ûͼƬֱӴļѡ');
                  this.showFileSelector();
                }
              })
              .catch(err => {
                console.error('ȡ:', err);
                
                // Ȩ޴ļѡ
                console.log('ʼ:', 
                            this.isH5 ? 'H5' : 
                            this.isApp ? 'APP' : 
                            this.isMiniProgram ? 'С' : 
                            this.isHBuilder ? 'HBuilder' : 'δ֪');
                this.showFileSelector();
              });
          } else {
            // navigator.clipboard.read
            console.log('navigator.clipboard.read');
            this.showFileSelector();
          }
        } else if (this.isMiniProgram) {
          // С
          console.log('С򻷾ԻȡͼƬ');
          
          if (typeof wx !== 'undefined' && wx.getClipboardData) {
            wx.getClipboardData({
              success: (res) => {
                console.log('ȡ:', res.data ? '' : '');
                
                if (res.data && this.isImageUrl(res.data)) {
                  console.log('ͼƬURL:', res.data);
                  
                  // ͼƬ·
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/png', // Ĭ
                    source: 'wx-getClipboardData'
                  });
                } else {
                  console.log('ûͼƬURL');
                }
              },
              fail: (err) => {
                console.error('ȡ:', err);
              }
            });
          } else if (typeof uni !== 'undefined' && uni.getClipboardData) {
            // ʹuni API
            uni.getClipboardData({
              success: (res) => {
                console.log('ȡuni:', res.data ? '' : '');
                
                if (res.data && this.isImageUrl(res.data)) {
                  console.log('uniͼƬURL:', res.data);
                  
                  // ͼƬ·
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/png', // Ĭ
                    source: 'uni-getClipboardData'
                  });
                } else {
                  console.log('uniûͼƬURL');
                }
              }
            });
          } else {
            console.log('С򻷾޷ֱӷʼ');
          }
        } else {
          console.log('ǰ֧ͼƬճ');
        }
      } catch (e) {
        console.error('ճͼƬʧ:', e);
        
        // H5ͬʵͼƬճ
        if (this.isH5) {
          console.log('ճͼƬ:', 
                      this.isApp ? 'APP' : 
                      this.isMiniProgram ? 'С' : 
                      this.isHBuilder ? 'HBuilder' : 'δ֪');
          this.showFileSelector();
        }
      }
    },
    
    // ͨͼƬճ¼
    tryPasteImageViaEvent() {
      console.log('ͨͼƬճ¼');
      
      // HBuilderбʹdocument.execCommand
      if (this.isHBuilder) {
        console.log('HBuilderʹdocument.execCommandcallback');
        return;
      }
      
      try {
        // һʱĿɱ༭Ԫ
        const tempDiv = document.createElement('div');
        tempDiv.contentEditable = true;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '0';
        tempDiv.style.width = '1px';
        tempDiv.style.height = '1px';
        tempDiv.style.opacity = '0';
        tempDiv.style.pointerEvents = 'none';
        
        // ȷԪؿԽճ¼
        tempDiv.setAttribute('tabindex', '-1');
        
        // ӵĵ
        document.body.appendChild(tempDiv);
        
        // ۽Ԫ
        tempDiv.focus();
        
        // ճ¼
        let pasteDetected = false;
        const pasteHandler = (e) => {
          pasteDetected = true;
          console.log('ʱԪزճ¼');
          
          // ͼƬ
          if (e.clipboardData && e.clipboardData.files && e.clipboardData.files.length > 0) {
            const file = e.clipboardData.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('ʱԪشclipboardData.filesȡͼƬ:', file.type);
              e.preventDefault();
              e.stopPropagation();
              
              // ͼƬճ¼
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                source: 'temp-div-paste-files'
              });
            }
          }
        };
        
        tempDiv.addEventListener('paste', pasteHandler);
        
        // Դճ¼
        let pasteSuccess = false;
        try {
          pasteSuccess = document.execCommand('paste');
          console.log('document.execCommand("paste"):', pasteSuccess);
        } catch (execErr) {
          console.error('document.execCommand("paste")ʧ:', execErr);
        }
        
        // ͼƬ
        setTimeout(() => {
          // ճ¼
          tempDiv.removeEventListener('paste', pasteHandler);
          
          // ͼƬԪ
          const images = tempDiv.querySelectorAll('img');
          
          if (images.length > 0) {
            console.log('ճͼƬԪ:', images.length);
            
            // һͼƬ
            this.processImageElement(images[0]);
          } else {
            // ͼƬ
            const computedStyle = window.getComputedStyle(tempDiv);
            const backgroundImage = computedStyle.backgroundImage;
            
            if (backgroundImage && backgroundImage !== 'none' && backgroundImage.indexOf('url(') !== -1) {
              console.log('ճͼƬ:', backgroundImage);
              
              // ȡURL
              const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
              if (urlMatch && urlMatch[1]) {
                const imageUrl = urlMatch[1];
                
                // ͼƬճ¼
                this.$emit('paste-image', {
                  path: imageUrl,
                  type: 'image/url',
                  source: 'temp-div-background'
                });
              }
            } else if (!pasteDetected && !pasteSuccess) {
              console.log('δճͼƬʹļѡ');
              
              // ûмճ¼ͼƬ
              if (this.isH5) {
                setTimeout(() => {
                  // ûȷճ
                  if (typeof uni !== 'undefined' && uni.showModal) {
                    uni.showModal({
                      title: 'δճͼƬ',
                      content: 'δճͼƬǷʹļѡ',
                      confirmText: 'ѡͼƬ',
                      cancelText: 'ȡ',
                      success: (res) => {
                        if (res.confirm) {
                          this.showFileSelector();
                        }
                      }
                    });
                  } else {
                    // 
                    this.showFileSelector();
                  }
                }, 300);
              }
            }
          }
          
          // ʱԪ
          try {
            document.body.removeChild(tempDiv);
          } catch (cleanupErr) {
            console.error('ʱԪʧ:', cleanupErr);
          }
        }, 200);
      } catch (err) {
        console.error('ͨͼƬճ¼ʧ:', err);
        
        // ʧܣʹļѡ
        if (this.isH5) {
          setTimeout(() => {
            this.showFileSelector();
          }, 300);
        }
      }
    },
    
    // ճ¼лȡͼƬԪ
    processImageElement(imgElement) {
      if (!imgElement) return;
      
      try {
        // ȡͼƬsrc
        const imgSrc = imgElement.src;
        
        if (imgSrc) {
          console.log('ȡͼƬsrc:', imgSrc.substring(0, 30) + (imgSrc.length > 30 ? '...' : ''));
          
          // ͼƬdata URLתΪblob
          if (imgSrc.startsWith('data:')) {
            this.dataURLToBlob(imgSrc, (blob) => {
              if (blob) {
                // ͼƬճ¼
                this.$emit('paste-image', {
                  blob: blob,
                  type: blob.type || 'image/png',
                  source: 'paste-event-data-url'
                });
              } else {
                // תʧܣֱʹdata URL
                this.$emit('paste-image', {
                  path: imgSrc,
                  type: imgSrc.split(';')[0].replace('data:', ''),
                  source: 'paste-event-data-url-fallback'
                });
              }
            });
          } else {
            // ͼƬURL
            this.$emit('paste-image', {
              path: imgSrc,
              type: 'image/url',
              source: 'paste-event-url'
            });
          }
        } else {
          console.log('ͼƬԪûsrc');
          
          // ԴstyleȡͼƬ
          const style = imgElement.getAttribute('style');
          if (style && style.includes('background-image')) {
            const match = style.match(/background-image:\s*url\(['"]?(.*?)['"]?\)/i);
            if (match && match[1]) {
              this.$emit('paste-image', {
                path: match[1],
                type: 'image/url',
                source: 'paste-event-background'
              });
            }
          }
        }
      } catch (err) {
        console.error('ͼƬԪʧ:', err);
      }
    },

    // Data URLתΪBlob
    dataURLToBlob(dataURL, callback) {
      try {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        
        const blob = new Blob([u8arr], { type: mime });
        callback(blob);
      } catch (err) {
        console.error('תData URLʧ:', err);
        callback(null);
      }
    },

    // ͨѡı
    copySelectedText() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // ȡѡı
        let selectedText = '';
        
        if (this.isH5) {
          // H5
          if (window.getSelection) {
            selectedText = window.getSelection().toString();
          } else if (document.selection) {
            selectedText = document.selection.createRange().text;
          }
        }
        
        // ûͨwindowѡȡı
        if (!selectedText && textarea.selectionStart !== undefined) {
          const start = textarea.selectionStart;
          const end = textarea.selectionEnd;
          selectedText = this.modelValue.substring(start, end);
        }
        
        // ûѡı
        if (!selectedText) {
          selectedText = this.modelValue;
        }
        
        // Ƶ
        if (selectedText) {
          if (uni && uni.setClipboardData) {
            uni.setClipboardData({
              data: selectedText,
              success: () => {
                uni.showToast({
                  title: 'Ѹ',
                  icon: 'none',
                  duration: 1500
                });
              }
            });
          } else if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(selectedText)
              .then(() => {
                if (uni) {
                  uni.showToast({
                    title: 'Ѹ',
                    icon: 'none',
                    duration: 1500
                  });
                }
              });
          }
        }
      } catch (e) {
        console.error('ıʧ:', e);
      }
    },

    // ͨѡı
    cutSelectedText() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // ȸı
        this.copySelectedText();
        
        // Ȼɾѡı
        this.deleteSelectedText();
      } catch (e) {
        console.error('ıʧ:', e);
      }
    },

    // ӷ
    sendMessage() {
      // û
      if (!this.modelValue || this.modelValue.trim() === '') {
        return false;
      }
      
      // ǰ
      const content = this.modelValue;
      
      // 
      this.$emit('update:modelValue', '');
      
      // û
      this.userHasScrolled = false;
      
      // ¸߶
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
      
      // ¼
      this.$emit('send', content);
      
      return true;
    },

    // ǿı
    insertTextAtCursor(text) {
      if (!text) return;
      console.log('ִвı:', text.substring(0, 20) + (text.length > 20 ? '...' : ''));
      
      const textarea = this.getTextareaElement();
      let newText = this.modelValue || '';
      
      try {
        // ûȡǰѡλ
        let start = 0;
        let end = 0;
        
        if (textarea && typeof textarea.selectionStart !== 'undefined') {
          start = textarea.selectionStart || 0;
          end = textarea.selectionEnd || 0;
        } else {
          // û޷ȡѡλĬ׷ӵĩβ
          start = end = newText.length;
        }
        
        // ûı
        newText = newText.substring(0, start) + text + newText.substring(end);
        
        // ûı
        this.$emit('update:modelValue', newText);
        
        // UI͹λ
        this.$nextTick(() => {
          try {
            // ûԾ۽
            if (textarea) {
              textarea.focus();
              
              if (typeof textarea.setSelectionRange === 'function') {
                const newPosition = start + text.length;
                textarea.setSelectionRange(newPosition, newPosition);
              }
            }
          } catch (err) {
            console.error('ùλʧ:', err);
          }
          
          // ¸߶Ⱥ͹
          this.updateHeightAndCheck();
          this.forceScrollToBottom();
          
          // 200msٴιײȷ
          setTimeout(() => {
            this.forceScrollToBottom();
          }, 200);
        });
      } catch (err) {
        console.error('ıʧܣֱ׷:', err);
        
        // ʱı÷ֱ׷ӵĩβ
        try {
          newText = newText + text;
          this.$emit('update:modelValue', newText);
          
          this.$nextTick(() => {
            this.updateHeightAndCheck();
            this.forceScrollToBottom();
          });
        } catch (backupErr) {
          console.error('ò뷽Ҳʧ:', backupErr);
        }
      }
      
      // ճ¼
      this.$emit('paste', { detail: { value: text } });
    },

    // ȫѡ
    handleSelectAllButtonClick() {
      if (this.disabled) return;
      
      console.log('ȫѡ');
      this.selectAllText();
      
      // ѡʾ
      uni.showToast({
        title: 'ȫѡ',
        icon: 'none',
        duration: 1000
      });
    },

    // ȫѡư
    handleSelectAndCopyButtonClick() {
      if (this.disabled || !this.modelValue) return;
      
      console.log('ȫѡư');
      
      // ֱӽıƵ
      if (uni && uni.setClipboardData) {
        uni.setClipboardData({
          data: this.modelValue,
          success: () => {
            // Ӿ
            this.selectionEnabled = true;
            
            // ִȫѡ
            this.selectAllText();
            
            // û
            uni.showToast({
              title: 'ѸƵ',
              icon: 'none',
              duration: 1500
            });
            
            // 1.5
            setTimeout(() => {
              this.selectionEnabled = false;
            }, 1500);
          },
          fail: () => {
            uni.showToast({
              title: 'ʧܣֶ',
              icon: 'none',
              duration: 1500
            });
          }
        });
      } else {
        // ûȫѡ
        this.selectAllText();
        
        // û
        this.copySelectedText();
      }
    },
    
    // ѡʼ
    handleSelectStart(e) {
      // ѡģʽ
      this.selectionEnabled = true;
      
      if (this.debugMode) {
        console.log('ѡıʼ');
      }
    },
    
    // 
    handleMouseUp(e) {
      // ûıѡУ
      setTimeout(() => {
        if (this.isTextSelectionActive()) {
          this.selectionEnabled = true;
          
          if (this.debugMode) {
            console.log('⵽ıѡ');
          }
        } else {
          // ûıѡУ
          // Ϊ˲û
        }
      }, 100);
    },
    
    // ȫѡհ
    handleSelectAndClearButtonClick() {
      if (this.disabled) return;
      
      console.log('ȫѡհ');
      
      // ֱı
      this.$emit('update:modelValue', '');
      
      // û
      uni.showToast({
        title: '',
        icon: 'none',
        duration: 1500
      });
      
      // ѡ״̬
      this.selectionEnabled = false;
      
      // ¸߶
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    },

    // ǿȫѡ - ޸"this.selectAllText is not a function"
    selectAllText() {
      console.log('ִȫѡı');
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // ѡ״̬
        this.selectionEnabled = true;
        
        // ʹöַ
        // 1: ԭselect
        if (typeof textarea.select === 'function') {
          textarea.focus();
          textarea.select();
          console.log('ʹtextarea.select()ȫѡ');
        }
        
        // 2: ѡΧ
        if (typeof textarea.setSelectionRange === 'function') {
          textarea.focus();
          textarea.setSelectionRange(0, this.modelValue.length || 0);
          console.log('ʹsetSelectionRangeȫѡ');
        }
        
        // 3: ʹdocument.execCommand
        try {
          document.execCommand('selectAll');
          console.log('ʹdocument.execCommand("selectAll")ȫѡ');
        } catch(e) {
          console.log('document.execCommand("selectAll")ʧ');
        }
        
        // 4: 
        if (this.isApp && uni && uni.createSelectorQuery) {
          // ͨuni APIȫѡ
          uni.createSelectorQuery()
            .in(this)
            .select('.optimized-textarea')
            .context((res) => {
              if (res && res.context) {
                res.context.focus();
                res.context.setSelectionRange(0, this.modelValue.length || 0);
                console.log('ʹuni APIȫѡ');
              }
            }).exec();
        }
        
        // ֶı
        textarea.classList.add('selecting-text');
        
        // һ
        this._hasSelectedAll = true;
        
        // һʱȷѡ״̬
        setTimeout(() => {
          // ôȷѡ״̬
          if (document.activeElement === textarea) {
            this.selectionEnabled = true;
            console.log('ȷȫѡ״̬');
          }
        }, 100);
        
      } catch (e) {
        console.error('ȫѡıʧ:', e);
        
        // ȫѡʧܣ
        this.selectionEnabled = true;
      }
    },

    // ճ
    handlePasteButtonClick() {
      console.log('ճ');
      
      // ʾ
      uni.showLoading({
        title: 'ȡ',
        mask: true
      });
      
      // û
      this.getClipboardContentMultiMethod((text) => {
        // ûʾ
        uni.hideLoading();
        
        if (text) {
          // ֱıĩβ
          const newText = (this.modelValue || '') + text;
          this.$emit('update:modelValue', newText);
          
          // UI
          this.$nextTick(() => {
            this.updateHeightAndCheck();
            this.forceScrollToBottom();
            
            // û
            uni.showToast({
              title: 'ճ',
              icon: 'success',
              duration: 1500
            });
          });
        } else {
          // ûʧ
          uni.showToast({
            title: 'Ϊջ޷',
            icon: 'none',
            duration: 1500
          });
        }
      });
    },
    
    // ʹö
    getClipboardContentMultiMethod(callback) {
      let contentObtained = false;
      let timeoutId = setTimeout(() => {
        if (!contentObtained) {
          console.log('ȡ');
          callback(''); // ʱ
        }
      }, 3000); // 3볬ʱ
      
      // 1: ʹplus.clipboard API
      if (typeof plus !== 'undefined' && plus.clipboard) {
        try {
          plus.clipboard.get((text) => {
            clearTimeout(timeoutId);
            contentObtained = true;
            
            if (text) {
              console.log('ɹͨplus.clipboardȡ');
              callback(text);
            } else {
              // һ
              this.tryUniClipboardAPI(callback);
            }
          }, (err) => {
            console.error('plus.clipboard.getʧ:', err);
            // һ
            this.tryUniClipboardAPI(callback);
          });
        } catch (err) {
          console.error('ʹplus.clipboard:', err);
          // һ
          this.tryUniClipboardAPI(callback);
        }
      } else {
        // һ
        this.tryUniClipboardAPI(callback);
      }
    },
    
    // ʹuni APIȡ
    tryUniClipboardAPI(callback) {
      if (typeof uni !== 'undefined' && uni.getClipboardData) {
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              console.log('ɹͨuni.getClipboardDataȡ');
              callback(res.data);
            } else {
              // 
              this.tryNavigatorClipboard(callback);
            }
          },
          fail: (err) => {
            console.error('uni.getClipboardDataʧ:', err);
            // 
            this.tryNavigatorClipboard(callback);
          }
        });
      } else {
        // 
        this.tryNavigatorClipboard(callback);
      }
    },
    
    // ʹnavigator.clipboard API
    tryNavigatorClipboard(callback) {
      if (navigator && navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText()
          .then((text) => {
            if (text) {
              console.log('ɹͨnavigator.clipboardȡ');
              callback(text);
            } else {
              // 
              this.tryDocumentExecCommand(callback);
            }
          })
          .catch((err) => {
            console.error('navigator.clipboard.readTextʧ:', err);
            // 
            this.tryDocumentExecCommand(callback);
          });
      } else {
        // 
        this.tryDocumentExecCommand(callback);
      }
    },
    
    // ʹdocument.execCommand
    tryDocumentExecCommand(callback) {
      try {
        if (document && document.execCommand) {
          const textarea = this.getTextareaElement();
          if (textarea) {
            textarea.focus();
            const result = document.execCommand('paste');
            if (result) {
              console.log('ɹͨdocument.execCommandȡ');
              // ֱַճ
              callback(''); // û
            } else {
              console.log('document.execCommand("paste")ʧ');
              callback(''); // û
            }
          } else {
            callback(''); // û
          }
        } else {
          callback(''); // û
        }
      } catch (err) {
        console.error('document.execCommand("paste"):', err);
        callback(''); // û
      }
    },

    // ϵͳճ
    handleSystemPaste() {
      console.log('ϵͳճ');
      
      // ûıԪ
      const textarea = this.getTextareaElement();
      if (!textarea) {
        console.error('ҲûıԪ');
        return;
      }
      
      // ۽ı
      textarea.focus();
      
      try {
        // ʹdocument.execCommandճ
        if (document && document.execCommand) {
          const result = document.execCommand('paste');
          console.log('document.execCommand("paste"):', result);
          
          if (result) {
            // ճ
            uni.showToast({
              title: 'ճ',
              icon: 'success',
              duration: 1000
            });
          } else {
            // execCommandfalseûֶճ
            uni.showToast({
              title: 'ʹCtrl+Vճ',
              icon: 'none',
              duration: 1500
            });
            
            // һʾһʾûʹϵͳճ
            setTimeout(() => {
              uni.showModal({
                title: 'ճʾ',
                content: 'ʹϵͳճܣ\n1. ûı\n2. ѡ"ճ"\nʹü̿ݼCtrl+V',
                showCancel: false,
                confirmText: '֪'
              });
            }, 500);
          }
        } else {
          // document.execCommand
          uni.showToast({
            title: 'ʹCtrl+Vճ',
            icon: 'none',
            duration: 1500
          });
        }
      } catch (err) {
        console.error('ϵͳճʧ:', err);
        
        // ûֶճ
        uni.showToast({
          title: 'ʹϵͳճ',
          icon: 'none',
          duration: 1500
        });
      }
    },

    // û
    createHiddenFileInput() {
      if (!this.isH5 || typeof document === 'undefined') return;
      
      try {
        // û
        let fileInput = document.getElementById('hidden-image-input');
        if (fileInput) {
          document.body.removeChild(fileInput);
        }
        
        // ûļ
        fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'hidden-image-input';
        fileInput.accept = 'image/*';
        fileInput.style.position = 'absolute';
        fileInput.style.top = '-9999px';
        fileInput.style.left = '-9999px';
        fileInput.style.opacity = '0';
        fileInput.style.pointerEvents = 'none';
        fileInput.style.visibility = 'hidden';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
        
        // û¼
        fileInput.addEventListener('change', (e) => {
          console.log('ļѡ¼');
          if (fileInput.files && fileInput.files.length > 0) {
            const file = fileInput.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('ļȡͼƬ:', file.type, file.name, file.size);
              
              // ͼƬճ¼
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                name: file.name,
                size: file.size,
                source: 'file-input'
              });
              
              // ûļ
              fileInput.value = '';
            }
          }
        });
        
        // û
        document.body.appendChild(fileInput);
        
        console.log('ļصûı');
      } catch (err) {
        console.error('ļļ:', err);
      }
    },
    
    // û
    showFileSelector() {
      if (!this.isH5 || typeof document === 'undefined') return;
      
      try {
        let fileInput = document.getElementById('hidden-image-input');
        if (!fileInput) {
          // û
          this.createHiddenFileInput();
          fileInput = document.getElementById('hidden-image-input');
        }
        
        if (fileInput) {
          // ûvalueȷûı
          fileInput.value = '';
          
          // ûļ
          fileInput.style.position = 'fixed';
          fileInput.style.top = '50%';
          fileInput.style.left = '50%';
          fileInput.style.transform = 'translate(-50%, -50%)';
          fileInput.style.opacity = '0';
          fileInput.style.pointerEvents = 'auto';
          fileInput.style.visibility = 'visible';
          fileInput.style.width = '100%';
          fileInput.style.height = '100%';
          fileInput.style.zIndex = '9999';
          
          // ûļ
          console.log('ļѡ');
          fileInput.click();
          
          // ûָ
          setTimeout(() => {
            fileInput.style.position = 'absolute';
            fileInput.style.top = '-9999px';
            fileInput.style.left = '-9999px';
            fileInput.style.transform = 'none';
            fileInput.style.pointerEvents = 'none';
            fileInput.style.visibility = 'hidden';
            fileInput.style.width = '1px';
            fileInput.style.height = '1px';
            fileInput.style.zIndex = '-1';
          }, 500);
        } else {
          console.error('Ҳûļ');
          
          // ûֱӴûһʱļ
          const tempInput = document.createElement('input');
          tempInput.type = 'file';
          tempInput.accept = 'image/*';
          tempInput.style.position = 'fixed';
          tempInput.style.top = '50%';
          tempInput.style.left = '50%';
          tempInput.style.transform = 'translate(-50%, -50%)';
          tempInput.style.opacity = '0';
          tempInput.style.zIndex = '9999';
          
          tempInput.addEventListener('change', (e) => {
            if (tempInput.files && tempInput.files.length > 0) {
              const file = tempInput.files[0];
              if (file && file.type.indexOf('image') !== -1) {
                console.log('ʱļȡͼƬ:', file.type);
                
                // ͼƬճ¼
                this.$emit('paste-image', {
                  blob: file,
                  type: file.type,
                  source: 'temp-file-input'
                });
              }
              
              // ûʱԪ
              document.body.removeChild(tempInput);
            }
          });
          
          document.body.appendChild(tempInput);
          tempInput.click();
        }
      } catch (err) {
        console.error('ûļʧ:', err);
        
        // ûı
        if (typeof uni !== 'undefined' && uni.chooseImage) {
          uni.chooseImage({
            count: 1,
            success: (res) => {
              if (res.tempFilePaths && res.tempFilePaths.length > 0) {
                this.$emit('paste-image', {
                  path: res.tempFilePaths[0],
                  type: 'image/png',
                  source: 'uni-choose-image'
                });
              }
            }
          });
        }
      }
    },

    // ʹnavigator.clipboard APIճͼƬ
    tryPasteImageWithNavigatorClipboard() {
      if (!this.isH5 || typeof navigator === 'undefined' || !navigator.clipboard) {
        return;
      }
      
      console.log('ʹnavigator.clipboard APIճͼƬ');
      
      try {
        // û֧read
        if (navigator.clipboard.read) {
          navigator.clipboard.read()
            .then(clipboardItems => {
              console.log('ɹȡ:', clipboardItems.length);
              
              let imagePromises = [];
              
              // û
              for (const clipboardItem of clipboardItems) {
                // û
                for (const type of clipboardItem.types) {
                  if (type.startsWith('image/')) {
                    console.log('⵽ͼƬ:', type);
                    
                    // ûͼƬblob
                    const imagePromise = clipboardItem.getType(type)
                      .then(blob => {
                        console.log('ȡͼƬblob:', blob.size);
                        
                        // ͼƬճ¼
                        this.$emit('paste-image', {
                          blob: blob,
                          type: type,
                          source: 'navigator-clipboard-read'
                        });
                        
                        return true;
                      })
                      .catch(err => {
                        console.error('ȡͼƬblobʧ:', err);
                        return false;
                      });
                    
                    imagePromises.push(imagePromise);
                    break;
                  }
                }
              }
              
              // ûҵͼƬ
              if (imagePromises.length === 0) {
                console.log('ûмճͼƬ');
                this.tryPasteImageViaEvent();
              }
              
              return Promise.all(imagePromises);
            })
            .catch(err => {
              console.error('ȡ:', err);
              
              // û
              if (err.name === 'NotAllowedError' || err.message.includes('permission')) {
                console.log('ʱܾ');
                
                // ûʾ
                if (typeof uni !== 'undefined' && uni.showModal) {
                  uni.showModal({
                    title: 'Ҫ',
                    content: 'ʹļѡϴͼƬ',
                    confirmText: 'ѡͼƬ',
                    cancelText: 'ȡ',
                    success: (res) => {
                      if (res.confirm) {
                        this.showFileSelector();
                      }
                    }
                  });
                } else {
                  // û
                  if (confirm('ʹļѡϴͼƬ')) {
                    this.showFileSelector();
                  }
                }
              } else {
                // û
                this.tryPasteImageViaEvent();
              }
            });
        } else {
          // ûread
          console.log('navigator.clipboard.read');
          this.tryPasteImageViaEvent();
        }
      } catch (err) {
        console.error('ʹnavigator.clipboard APIʧ:', err);
        this.tryPasteImageViaEvent();
      }
    },

    // ͼƬճ
    handleImagePasteButtonClick() {
      console.log('ͼƬճ');
      
      // H5ֱӴļѡ
      if (this.isH5) {
        this.showFileSelector();
        return;
      }
      
      // û
      this.tryPasteImage();
    },
  }
};
</script>

<style>
.optimized-textarea-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: visible;
  box-sizing: border-box;
}

.optimized-textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 12px 16px;
  padding-right: 60px; /* ΪͰťռ */
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid rgba(77, 157, 255, 0.3);
  border-radius: 30px;
  background-color: rgba(45, 45, 55, 0.6);
  color: rgba(255, 255, 255, 0.9);
  outline: none;
  resize: none;
  transition: border-color 0.3s, background-color 0.3s;
  overflow-y: auto !important; 
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: auto; /*  */
  touch-action: auto; /* ԭ */
  position: relative;
  /* ȷıѡ */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  /* iOSʾ˵ */
  -webkit-touch-callout: default !important;
  /* ı */
  cursor: text;
  /* Ƴ */
  transform: none;
  -webkit-transform: none;
  will-change: auto;
  scroll-behavior: auto;
}

.optimized-textarea:focus {
  border-color: rgba(77, 157, 255, 0.8);
  box-shadow: 0 0 0 2px rgba(77, 157, 255, 0.3), 0 2px 12px rgba(77, 157, 255, 0.4);
  background-color: rgba(40, 40, 60, 0.6);
}

.animated-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  pointer-events: none;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  transition: opacity 0.3s;
}

.is-focused .placeholder-text {
  opacity: 0.6;
}

.h5-platform .optimized-textarea::-webkit-scrollbar {
  width: 3px;
  background-color: transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  transition: background-color 0.3s;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

.h5-platform .optimized-textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

/* Żʾָʾ */
.optimized-textarea-container.has-overflow::after {
  content: '';
  position: absolute;
  bottom: 10px;
  right: 55px;
  width: 4px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(77, 157, 255, 0.7), transparent);
  border-radius: 2px;
  pointer-events: none;
  opacity: 0.8;
  animation: scrollIndicator 1.5s infinite;
}

@keyframes scrollIndicator {
  0% { opacity: 0.4; height: 15px; }
  50% { opacity: 0.8; height: 20px; }
  100% { opacity: 0.4; height: 15px; }
}

/* iOS豸Ż */
@supports (-webkit-touch-callout: none) {
  .optimized-textarea {
    -webkit-overflow-scrolling: touch !important;
    padding-top: 13px; /* ΢iOSϵλ */
    -webkit-tap-highlight-color: rgba(77, 157, 255, 0.2);
    /* iOSıѡ˵ */
    -webkit-touch-callout: default !important;
    -webkit-user-select: text !important;
    /* Ƴ */
    -webkit-transform: none;
    transform: none;
    backface-visibility: visible;
  }
  
  /* iOS豸ϼıѡʽ */
  .optimized-textarea::selection {
    background-color: rgba(77, 157, 255, 0.4);
  }
}

.debug-bottom-marker {
  position: absolute;
  right: 8px;
  bottom: 8px;
  color: rgba(0, 255, 255, 0.7);
  font-size: 16px;
  pointer-events: none;
  z-index: 100;
}

.h5-platform .optimized-textarea {
  overflow-y: auto !important;
}

.mp-platform .optimized-textarea {
  position: relative;
  z-index: 1;
}

.app-platform .optimized-textarea {
  position: relative;
  z-index: 1;
}

.has-overflow .optimized-textarea {
  box-shadow: inset 0 -8px 8px -8px rgba(77, 157, 255, 0.3);
}

@media (pointer: coarse) {
  .optimized-textarea {
    font-size: 16px;
  }
  
  .h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
    width: 6px;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.show-indicator {
  opacity: 1;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.3; width: 30px; }
  100% { opacity: 0.7; width: 40px; }
}

.debug-scroll-indicator {
  position: absolute;
  right: 5px;
  width: 3px;
  background: rgba(100, 200, 255, 0.5);
  border-radius: 3px;
  pointer-events: none;
  z-index: 100;
}

@keyframes pulse-border {
  0% { border-color: rgba(77, 157, 255, 0.3); }
  50% { border-color: rgba(77, 157, 255, 0.6); }
  100% { border-color: rgba(77, 157, 255, 0.3); }
}

.optimized-textarea.at-top,
.optimized-textarea.at-bottom {
  animation: pulse-border 1s ease infinite;
}

.optimized-textarea.at-top {
  box-shadow: inset 0 4px 8px -4px rgba(77, 157, 255, 0.3);
}

.optimized-textarea.at-bottom {
  box-shadow: inset 0 -4px 8px -4px rgba(77, 157, 255, 0.3);
}

.send-button-slot {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optimized-textarea-container.show-bottom-indicator::after {
  content: '';
  position: absolute;
  bottom: 8px;
  right: 45px;
  width: 20px;
  height: 20px;
  background-color: rgba(77, 157, 255, 0.7);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  animation: bounce 1s infinite alternate;
  pointer-events: none;
  z-index: 10;
}

@keyframes bounce {
  0% { transform: translateY(0); }
  100% { transform: translateY(-3px); }
}

.h5-platform .optimized-textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.4) transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar {
  width: 5px;
  background-color: transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 10px;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

/* ıѡʽ */
.optimized-textarea::selection {
  background: rgba(77, 157, 255, 0.4);
  color: #ffffff;
}

/* Android豸Ż */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  .optimized-textarea {
    overflow-y: auto;
    scrollbar-width: thin;
    /* ȷAndroidϵĹ */
    touch-action: auto;
    -webkit-transform: none;
    transform: none;
  }
}

/* ״̬Ż */
.optimized-textarea-container.selecting .optimized-textarea {
  /* ıѡģʽŻʽ */
  touch-action: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -webkit-touch-callout: default !important;
  /*  */
  background-color: rgba(50, 50, 70, 0.7) !important;
  border-color: rgba(77, 157, 255, 0.7) !important;
  box-shadow: inset 0 0 0 1px rgba(77, 157, 255, 0.8) !important;
}

/* APPıѡǿ */
.app-platform .optimized-textarea {
  caret-color: rgba(77, 157, 255, 0.9);
  -webkit-user-select: auto;
  user-select: auto;
  -webkit-touch-callout: default;
  touch-callout: default;
  transition-property: border-color, background-color;
  transition-duration: 0.3s;
}

/* ǿı
.optimized-textarea::selection {
  background-color: rgba(77, 157, 255, 0.5) !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* HBuilder
.optimized-textarea.selecting-text {
  background-color: rgba(50, 50, 70, 0.7) !important;
  border-color: rgba(77, 157, 255, 0.7) !important;
  box-shadow: inset 0 0 0 1px rgba(77, 157, 255, 0.8) !important;
}

/* ճťʽ */
.paste-button {
  display: none;
}

.paste-button:active {
  display: none;
}

.paste-icon {
  display: none;
}

.paste-text {
  display: none;
}

/* ճťʽ -  */
.paste-button-large {
  display: none;
}

.paste-button-large:active {
  display: none;
}

@keyframes pulse-button {
  0% { box-shadow: 0 0 0 0 rgba(77, 157, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(77, 157, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(77, 157, 255, 0); }
}

/* ճťʽ - С */
.paste-button-small {
  display: none;
}

.paste-button-small:active {
  display: none;
}

.paste-button-hbuilder {
  display: none;
}

.paste-button-hbuilder:active {
  display: none;
}

.paste-icon-text {
  display: none;
}

/* HBuilder环境特殊样式 */
.hbuilder-textarea {
  -webkit-user-select: text !important;
  user-select: text !important;
  -webkit-touch-callout: default !important;
  cursor: text !important;
  -webkit-tap-highlight-color: rgba(77, 157, 255, 0.3) !important;
}
</style>
</template>
