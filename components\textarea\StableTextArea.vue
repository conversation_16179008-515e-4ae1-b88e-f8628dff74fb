<template>
  <view class="stable-textarea-container" :class="{ 'selecting': isSelectionMode }">
    <!-- 主textarea区域 -->
    <CursorPositioner
      :textarea-element="$refs.textareaRef"
      :value="inputValue"
      :enabled="false"
      @cursor-position-change="handleCursorPositionChange"
      ref="cursorPositioner"
    >
      <view class="textarea-wrapper"
            @click="handleClick">
        <!-- 官方textarea组件 -->
        <textarea
        ref="textareaRef"
        class="stable-textarea"
        :class="{
          'scroll-mode': !isFocused,
          'edit-mode': isFocused,
          'mouse-dragging': isDragging
        }"
        v-model="inputValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :auto-height="autoHeight"
        :cursor-spacing="cursorSpacing"
        :show-confirm-bar="showConfirmBar"
        :adjust-position="adjustPosition"
        :selection-start="selectionStart"
        :selection-end="selectionEnd"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
        @paste="handlePaste"
        @linechange="handleLineChange"
        @selectionchange="handleSelectionChange"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="handleTextareaClick"
      />

      <!-- 发送按钮插槽，放在右下角 -->
      <view class="send-button-slot" v-if="$slots.sendButton">
        <slot name="sendButton"></slot>
      </view>

      <!-- 选择模式提示 -->
      <view class="selection-tip" v-if="isSelectionMode && !showSelectionMenu">
        双击选择单词，长按选择文本
      </view>

      <!-- 高级文本选择器 - 暂时禁用，让原生处理 -->
      <AdvancedTextSelector
        :textarea-element="$refs.textareaRef"
        :value="inputValue"
        :selection-start="selectionStart"
        :selection-end="selectionEnd"
        :is-selection-mode="false"
        @selection-change="handleAdvancedSelectionChange"
        @cursor-position-change="handleCursorPositionChange"
        @selection-cancel="handleSelectionCancel"
        ref="advancedSelector"
      />

      <!-- 文本选择菜单 -->
      <TextSelectionMenu
        :visible="showSelectionMenu"
        :selected-text="selectedText"
        :selection-start="selectionStart"
        :selection-end="selectionEnd"
        :position="selectionMenuPosition"
        @delete-text="handleDeleteText"
        @replace-text="handleReplaceText"
        @enhance-text="handleEnhanceText"
        @action-complete="handleSelectionActionComplete"
      />

      <!-- 回到底部按钮 -->
      <view
        v-if="showScrollToBottomBtn"
        class="scroll-to-bottom-btn"
        @click="scrollToBottomAndFollow"
      >
        <view class="scroll-btn-icon">↓</view>
        <view class="scroll-btn-text">回到底部</view>
      </view>
      </view>
    </CursorPositioner>

    <!-- 多媒体预览区域 -->
    <view class="media-preview" v-if="mediaItems.length > 0">
      <view class="media-item" v-for="(item, index) in mediaItems" :key="index">
        <image v-if="item.type === 'image'" :src="item.src" class="media-image" @click="previewMedia(item)" />
        <video v-if="item.type === 'video'" :src="item.src" class="media-video" controls />
        <view class="media-remove" @click="removeMedia(index)">×</view>
      </view>
    </view>
  </view>
</template>

<script>
import TextSelectionMenu from '@/components/text-editor/tools/TextSelectionMenu.vue';
import AdvancedTextSelector from '@/components/text-editor/tools/AdvancedTextSelector.vue';
import CursorPositioner from '@/components/text-editor/tools/CursorPositioner.vue';
import { TextareaStateManager } from '@/components/text-editor/tools/TextareaStateManager.js';

export default {
  name: 'StableTextArea',
  components: {
    TextSelectionMenu,
    AdvancedTextSelector,
    CursorPositioner
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    fixed: {
      type: Boolean,
      default: false
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    minHeight: {
      type: Number,
      default: 40
    },
    mediaItems: {
      type: Array,
      default: () => []
    }

  },
  emits: [
    'update:modelValue',
    'focus',
    'blur',
    'confirm',
    'paste',
    'keyboardheightchange',
    'linechange',
    'height-change',
    'input',
    'selectionchange',
    'media-add',
    'media-remove'
  ],
  data() {
    return {
      isFocused: false,
      platformClass: '',
      // 文字选择相关
      isSelectionMode: false,
      selectionStart: 0,
      selectionEnd: 0,
      lastClickTime: 0,
      longPressTimer: null,
      showSelectionMenu: false,
      selectionMenuPosition: { x: 0, y: 0 },

      // 触摸相关状态
      touchStartTime: 0,
      touchStartPos: null,
      touchMoved: false,

      // 状态管理器
      stateManager: null,
      // 鼠标拖拽模拟手势滚动
      isDragging: false,
      dragStartY: 0,
      dragStartX: 0,
      dragStartScrollTop: 0,
      // 智能跟随滚动
      isAutoFollowing: true,        // 是否自动跟随
      userHasScrolled: false,       // 用户是否手动滚动过
      lastUserScrollTime: 0,        // 最后一次用户滚动时间
      showScrollToBottomBtn: false, // 是否显示回到底部按钮
      scrollToBottomTimer: null     // 回到底部按钮显示定时器
    };
  },
  computed: {
    inputValue: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    },
    textareaStyle() {
      const style = {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`
      };

      // 为发送按钮留出空间
      if (this.$slots.sendButton) {
        style.paddingRight = '120px'; // 增加空间给两个按钮
      }

      return style;
    },
    // 检查是否滚动到底部
    isAtBottom() {
      if (!this.$refs || !this.$refs.textareaRef) {
        return true;
      }

      const textarea = this.getTextareaElement();
      if (!textarea) return true;

      try {
        const scrollTop = textarea.scrollTop;
        const scrollHeight = textarea.scrollHeight;
        const clientHeight = textarea.clientHeight;

        // 允许5px的误差
        return scrollTop + clientHeight >= scrollHeight - 5;
      } catch (error) {
        return true;
      }
    },
    // 检查是否有内容溢出 - 移到computed中
    hasOverflow() {
      // 防止在组件未挂载时访问$refs导致错误
      if (!this.$refs || !this.$refs.textareaRef) {
        return false;
      }

      const textarea = this.$refs.textareaRef;
      if (textarea) {
        try {
          // H5环境
          // #ifdef H5
          const element = textarea.$el || textarea;
          if (element && element.scrollHeight !== undefined && element.clientHeight !== undefined) {
            return element.scrollHeight > element.clientHeight;
          }
          // #endif

          // App和小程序环境
          // #ifndef H5
          if (textarea.scrollHeight !== undefined && textarea.clientHeight !== undefined) {
            return textarea.scrollHeight > textarea.clientHeight;
          }
          // #endif
        } catch (error) {
          console.warn('检查hasOverflow时出错:', error);
          return false;
        }
      }
      return false;
    },

    // 当前选中的文本
    selectedText() {
      if (this.selectionStart === this.selectionEnd || !this.inputValue) {
        return '';
      }
      return this.inputValue.substring(this.selectionStart, this.selectionEnd);
    }
  },
  watch: {
    // 监听内容变化，实现智能跟随滚动
    modelValue: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          // 只有在自动跟随模式下才滚动到底部
          if (this.isAutoFollowing && newVal && oldVal && newVal.length > oldVal.length) {
            this.scrollToBottom();
          }

          // 检查是否需要显示回到底部按钮
          this.checkScrollToBottomButton();
        });
      },
      immediate: false
    }
  },
  created() {
    this.detectPlatform();
  },
  mounted() {
    // 组件挂载完成，使用uni-app官方textarea的原生滚动功能
    console.log('StableTextArea mounted, 使用官方textarea原生滚动');

    // 初始化状态管理器
    this.initStateManager();

    // 初始化鼠标拖拽模拟手势滚动
    this.initMouseGestureSimulation();

    // 初始化滚动监听
    this.initScrollListener();
  },

  beforeUnmount() {
    // 清理所有资源
    this.cleanupAllTextareaResources();
  },
  methods: {
    // 初始化状态管理器（禁用版本 - 不干扰原生行为）
    initStateManager() {
      // 暂时不使用状态管理器，让原生textarea完全处理基础交互
      console.log('🚫 状态管理器已禁用，使用原生textarea处理');

      // 如果需要状态管理器，只用于非关键功能
      // this.stateManager = new TextareaStateManager();
    },

    detectPlatform() {
      // 检测当前平台
      // #ifdef H5
      this.platformClass = 'h5-platform';
      // #endif

      // #ifdef APP-PLUS
      this.platformClass = 'app-platform';
      // #endif

      // #ifdef MP
      this.platformClass = 'mp-platform';
      // #endif
    },

    
    handleInput(e) {
      // 确保正确处理输入事件
      this.$emit('update:modelValue', e.detail.value);
      this.$emit('input', e);
    },
    
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },
    
    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },
    
    handleConfirm(e) {
      this.$emit('confirm', e);
    },

    handlePaste(e) {
      this.$emit('paste', e);
    },



    // 点击处理（修复版本 - 单击立即定位光标）
    handleClick(e) {
      const now = Date.now();
      const isDoubleClick = now - this.lastClickTime < 300;
      this.lastClickTime = now;

      if (isDoubleClick) {
        console.log('触发双击选择');

        // 启用选择模式
        this.enableAdvancedSelectionMode();

        // 选择当前光标位置的单词
        this.selectCurrentWord();

        // 使用新的反馈系统
        this.provideTactileFeedback('light');
        this.showOperationTip('双击选择文本', 1000);
      } else {
        // 单击处理 - 立即定位光标，完全由原生处理
        console.log('单击处理 - 光标立即定位');

        // 如果在选择模式，退出选择模式
        if (this.isSelectionMode) {
          this.exitSelectionMode();
        }

        // 隐藏选择菜单
        this.hideSelectionMenu();

        // 轻微的触觉反馈（表示光标定位成功）
        if (uni.vibrateShort) {
          uni.vibrateShort();
        }

        // 不阻止默认行为，让原生textarea立即处理光标定位
        // 这样点击就能立即定位光标，无需等待
      }

      // 发射点击事件，不阻止默认行为
      this.$emit('click', e);
    },

    // 触摸开始处理（修复 - 单击立即定位光标）
    handleTouchStart(e) {
      // 记录触摸开始时间和位置
      this.touchStartTime = Date.now();
      this.touchStartPos = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      };
      this.touchMoved = false;

      // 清除之前的长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      console.log('触摸开始 - 准备单击定位光标或长按选择文本');

      // 启动长按检测（用于文本选择）
      this.longPressTimer = setTimeout(() => {
        // 只有在没有移动的情况下才触发长按选择
        if (!this.touchMoved) {
          console.log('触发长按 - 启动文本选择');
          this.handleLongPress(e);
        }
      }, 500); // 500ms后触发长按选择

      // 完全不阻止默认行为，让原生textarea立即处理触摸
      // 这样单击就能立即定位光标，无需等待
    },

    // 触摸移动处理（修复 - 区分滑动和单击）
    handleTouchMove(e) {
      if (!this.touchStartPos) return;

      const currentPos = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      };

      // 计算移动距离
      const deltaX = Math.abs(currentPos.x - this.touchStartPos.x);
      const deltaY = Math.abs(currentPos.y - this.touchStartPos.y);
      const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 提高阈值，区分真正的滑动和单击时的轻微移动
      // 单击时手指轻微移动是正常的，不应该被认为是滑动
      if (totalDelta > 20) { // 从8提高到20像素，避免误判单击为滑动
        this.touchMoved = true;
        console.log('检测到真正的滑动，取消长按');
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      }

      // 完全不阻止默认行为，让原生处理滚动和光标定位
    },

    // 触摸结束处理（修复 - 正确区分单击和滑动）
    handleTouchEnd(e) {
      const touchDuration = Date.now() - this.touchStartTime;

      // 清除长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 判断是否为单击：时间短且移动距离小
      const isClick = touchDuration < 500 && !this.touchMoved;

      if (isClick) {
        // 这是一个单击，让原生立即处理光标定位
        console.log('检测到单击 - 让原生立即定位光标');
        // 不调用 this.handleClick(e)，避免干扰原生行为
        // 原生textarea会自动处理光标定位
      } else if (this.touchMoved) {
        console.log('检测到滑动 - 让原生处理滚动');
        // 这是滑动，让原生处理滚动
      } else {
        console.log('检测到长按或其他操作');
      }

      // 重置触摸状态
      this.touchStartTime = 0;
      this.touchStartPos = null;
      this.touchMoved = false;

      // 完全不阻止默认行为，让原生textarea处理所有操作
      // 单击 → 原生立即定位光标
      // 滑动 → 原生处理滚动
    },

    // 处理长按事件（修复版本 - 不干扰光标定位）
    handleLongPress(e) {
      console.log('触发长按选择');

      // 启用选择模式
      this.enableAdvancedSelectionMode();

      // 选择当前光标位置的文本（不重新计算位置）
      // 因为光标已经由原生处理定位好了
      this.selectCurrentWord();

      // 使用新的反馈系统
      this.provideTactileFeedback('strong');
      this.showOperationTip('长按选择已启用', 1500);
    },

    // 在触摸位置选择文本
    selectTextAtTouch(e) {
      if (!e.touches || !e.touches[0]) return;

      try {
        const textarea = this.$refs.textareaRef;
        if (!textarea || !this.inputValue) return;

        // 获取触摸位置对应的文本位置
        const touch = e.touches[0];
        const rect = textarea.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;

        // 改进的位置计算
        const position = this.calculateTextPosition(x, y, textarea);

        // 选择当前位置的单词
        this.selectWordAtPosition(position);

        console.log(`在位置 ${position} 选择文本`);
      } catch (error) {
        console.error('选择触摸位置文本失败:', error);
        // 如果位置计算失败，就选择当前光标位置的单词
        this.selectCurrentWord();
      }
    },

    // 计算文本位置（新增方法）
    calculateTextPosition(x, y, textarea) {
      try {
        // 尝试使用更精确的方法
        if (document.caretPositionFromPoint) {
          const rect = textarea.getBoundingClientRect();
          const caretPos = document.caretPositionFromPoint(x + rect.left, y + rect.top);
          if (caretPos && caretPos.offsetNode && caretPos.offsetNode.nodeValue) {
            return caretPos.offset;
          }
        }

        // 回退到改进的估算方法
        const style = window.getComputedStyle(textarea);
        const lineHeight = parseInt(style.lineHeight) || 20;
        const fontSize = parseInt(style.fontSize) || 14;
        const charWidth = fontSize * 0.6; // 更准确的字符宽度估算

        const line = Math.floor(y / lineHeight);
        const char = Math.floor(x / charWidth);

        // 计算在文本中的位置
        const lines = this.inputValue.split('\n');
        let position = 0;

        for (let i = 0; i < line && i < lines.length; i++) {
          position += lines[i].length + 1; // +1 for newline
        }

        if (line < lines.length) {
          position += Math.min(char, lines[line].length);
        }

        return Math.max(0, Math.min(position, this.inputValue.length));
      } catch (error) {
        console.error('计算文本位置失败:', error);
        // 返回当前光标位置或文本开头
        const textarea = this.$refs.textareaRef;
        return (textarea && textarea.selectionStart) || 0;
      }
    },

    // 在指定位置选择单词（优化版本）
    selectWordAtPosition(position) {
      if (!this.inputValue) return;

      const text = this.inputValue;
      let start = position;
      let end = position;

      // 改进的单词边界定义，支持中文
      const wordBoundary = /[\s\n\t,.;:'"!?()[\]{}\/\\|<>\-+*=，。；：'"！？（）【】｛｝]/;
      const chineseChar = /[\u4e00-\u9fff]/;

      // 检查当前字符类型
      const currentChar = text[position];
      const isChineseChar = currentChar && chineseChar.test(currentChar);

      if (isChineseChar) {
        // 中文字符：选择单个字符或词组
        start = position;
        end = position + 1;

        // 尝试扩展选择相邻的中文字符
        while (start > 0 && chineseChar.test(text[start - 1])) {
          start--;
        }
        while (end < text.length && chineseChar.test(text[end])) {
          end++;
        }
      } else {
        // 英文字符：选择整个单词
        while (start > 0 && !wordBoundary.test(text[start - 1])) {
          start--;
        }
        while (end < text.length && !wordBoundary.test(text[end])) {
          end++;
        }
      }

      // 如果没有找到有效选择，选择当前行
      if (start === end) {
        const lines = text.split('\n');
        let lineStart = 0;

        for (let i = 0; i < lines.length; i++) {
          const lineEnd = lineStart + lines[i].length;
          if (position >= lineStart && position <= lineEnd) {
            start = lineStart;
            end = lineEnd;
            break;
          }
          lineStart = lineEnd + 1; // +1 for newline
        }
      }

      // 设置选择范围
      this.selectionStart = start;
      this.selectionEnd = end;

      // 更新textarea的选择状态
      this.updateTextareaSelection();

      // 显示选择菜单和反馈
      if (start !== end) {
        const selectedText = text.substring(start, end);
        this.showSelectionFeedback(selectedText);

        this.$nextTick(() => {
          this.showSelectionMenuAtPosition();
        });
      } else {
        this.showOperationTip('未找到可选择的文本', 1000);
      }

      console.log(`选择文本: "${text.substring(start, end)}" (${start}-${end})`);
    },

    // 在指定位置显示选择菜单
    showSelectionMenuAtPosition(e) {
      try {
        const textarea = this.$refs.textareaRef;
        if (!textarea) return;

        // 获取textarea的位置
        const rect = textarea.getBoundingClientRect();

        // 计算菜单位置（在选择区域上方）
        this.selectionMenuPosition = {
          x: rect.left + rect.width / 2,
          y: rect.top
        };

        this.showSelectionMenu = true;
      } catch (error) {
        console.error('显示选择菜单失败:', error);
      }
    },

    // 隐藏选择菜单
    hideSelectionMenu() {
      this.showSelectionMenu = false;
    },

    // 处理删除文本
    handleDeleteText(data) {
      const { start, end } = data;
      const newValue = this.inputValue.substring(0, start) + this.inputValue.substring(end);
      this.inputValue = newValue;

      // 重置选择
      this.selectionStart = start;
      this.selectionEnd = start;
    },

    // 处理替换文本
    handleReplaceText(data) {
      const { start, end, newText } = data;
      const newValue = this.inputValue.substring(0, start) + newText + this.inputValue.substring(end);
      this.inputValue = newValue;

      // 设置新的选择范围
      this.selectionStart = start;
      this.selectionEnd = start + newText.length;
    },

    // 处理润色文本
    handleEnhanceText(data) {
      const { text, start, end } = data;

      // 这里可以调用AI润色API
      uni.showToast({
        title: '润色功能开发中',
        icon: 'none',
        duration: 2000
      });

      // 示例：简单的文本处理
      // const enhancedText = text.replace(/\s+/g, ' ').trim();
      // this.handleReplaceText({ start, end, newText: enhancedText });
    },

    // 处理选择操作完成
    handleSelectionActionComplete(action) {
      this.hideSelectionMenu();
      this.isSelectionMode = false;

      console.log(`文本选择操作完成: ${action}`);
    },

    // 处理高级选择器的选择变化
    handleAdvancedSelectionChange(selection) {
      this.selectionStart = selection.start;
      this.selectionEnd = selection.end;

      // 更新textarea的选择状态
      this.updateTextareaSelection();

      // 显示选择菜单
      if (selection.start !== selection.end) {
        this.showSelectionMenuAtPosition();
      } else {
        this.hideSelectionMenu();
      }
    },

    // 处理光标位置变化
    handleCursorPositionChange(position) {
      try {
        const textarea = this.$refs.textareaRef;
        if (textarea && textarea.setSelectionRange) {
          textarea.setSelectionRange(position, position);
          textarea.focus();
        }

        // 更新选择状态
        this.selectionStart = position;
        this.selectionEnd = position;

        console.log(`光标定位到位置: ${position}`);
      } catch (error) {
        console.error('设置光标位置失败:', error);
      }
    },

    // 更新textarea的选择状态
    updateTextareaSelection() {
      try {
        const textarea = this.$refs.textareaRef;
        if (textarea && textarea.setSelectionRange) {
          textarea.setSelectionRange(this.selectionStart, this.selectionEnd);
        }
      } catch (error) {
        console.error('更新textarea选择状态失败:', error);
      }
    },

    // 启用高级选择模式
    enableAdvancedSelectionMode() {
      this.isSelectionMode = true;

      // 更新高级选择器的文本框信息
      this.$nextTick(() => {
        if (this.$refs.advancedSelector) {
          this.$refs.advancedSelector.updateTextareaInfo();
        }
      });

      // 显示提示
      uni.showToast({
        title: '拖拽手柄调整选择范围',
        icon: 'none',
        duration: 2000
      });
    },

    // 处理文本框点击（简化版本 - 让原生处理光标定位）
    handleTextareaClick(e) {
      // 如果在选择模式，不处理点击
      if (this.isSelectionMode) return;

      // 隐藏选择菜单
      this.hideSelectionMenu();

      // 简单的触觉反馈，不干扰原生光标定位
      if (uni.vibrateShort) {
        uni.vibrateShort();
      }

      // 让原生textarea处理光标定位，不阻止默认行为
      // 延迟更新状态，确保原生处理完成
      this.$nextTick(() => {
        const textarea = this.$refs.textareaRef;
        if (textarea) {
          this.selectionStart = textarea.selectionStart;
          this.selectionEnd = textarea.selectionEnd;
        }
      });
    },

    // 处理选择取消
    handleSelectionCancel() {
      console.log('收到选择取消事件');

      // 退出选择模式
      this.isSelectionMode = false;

      // 清除选择状态
      this.selectionStart = 0;
      this.selectionEnd = 0;

      // 隐藏选择菜单
      this.hideSelectionMenu();

      // 通知状态管理器
      if (this.stateManager) {
        this.stateManager.exitSelectionMode();
      }

      // 用户反馈
      uni.showToast({
        title: '已取消选择',
        icon: 'none',
        duration: 1000
      });

      console.log('✅ 文本选择已取消');
    },

    // 行数变化处理（自动滚动）
    handleLineChange(e) {
      this.$emit('linechange', e);

      // 当内容超过3行时，自动滚动到底部
      if (e.detail.lineCount > 3) {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 选择变化处理
    handleSelectionChange(e) {
      this.selectionStart = e.detail.selectionStart;
      this.selectionEnd = e.detail.selectionEnd;

      if (this.selectionStart !== this.selectionEnd) {
        const selectedText = this.inputValue.substring(this.selectionStart, this.selectionEnd);

        // 显示选择菜单
        this.showSelectionMenuAtPosition(e);

        uni.showToast({
          title: `已选择 ${selectedText.length} 个字符`,
          icon: 'none',
          duration: 1000
        });
      } else {
        // 隐藏选择菜单
        this.hideSelectionMenu();
      }

      this.$emit('selectionchange', e);
    },

    // 启用选择模式
    enableSelectionMode() {
      this.isSelectionMode = true;

      // 10秒后自动关闭选择模式
      setTimeout(() => {
        this.isSelectionMode = false;
      }, 10000);
    },

    // 选择当前单词
    selectCurrentWord() {
      const textarea = this.$refs.textareaRef;
      if (!textarea || !this.inputValue) return;

      try {
        const cursorPos = textarea.selectionStart || 0;
        const text = this.inputValue;

        // 查找单词边界
        let start = cursorPos;
        let end = cursorPos;

        const wordBoundary = /[\s\n\t,.;:'"!?()[\]{}\/\\|<>\-+*/]/;

        // 向前找单词起始
        while (start > 0 && !wordBoundary.test(text[start - 1])) {
          start--;
        }

        // 向后找单词结束
        while (end < text.length && !wordBoundary.test(text[end])) {
          end++;
        }

        // 设置选择范围
        this.selectionStart = start;
        this.selectionEnd = end;
      } catch (e) {
        console.error('选择单词失败:', e);
      }
    },

    // 滚动到底部
    scrollToBottom() {
      const textarea = this.$refs.textareaRef;
      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    },

    // 多媒体功能
    addMediaItem(item) {
      this.$emit('media-add', item);
    },

    removeMedia(index) {
      this.$emit('media-remove', index);
    },

    previewMedia(item) {
      if (item.type === 'image') {
        uni.previewImage({
          urls: [item.src],
          current: item.src
        });
      }
    },

    // 处理图片上传
    handleImageUpload() {
      uni.chooseImage({
        count: 9,
        success: (res) => {
          res.tempFilePaths.forEach(path => {
            this.addMediaItem({
              type: 'image',
              src: path,
              name: this.getFileName(path)
            });
          });

          uni.showToast({
            title: '图片添加成功',
            icon: 'success'
          });
        }
      });
    },

    // 处理视频上传
    handleVideoUpload() {
      uni.chooseVideo({
        count: 1,
        success: (res) => {
          this.addMediaItem({
            type: 'video',
            src: res.tempFilePath,
            name: this.getFileName(res.tempFilePath)
          });

          uni.showToast({
            title: '视频添加成功',
            icon: 'success'
          });
        }
      });
    },

    getFileName(path) {
      return path.split('/').pop() || 'file';
    },

    // 设置光标到文本末尾
    setCursorToEnd() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('setCursorToEnd: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.setSelectionRange) {
              const textLength = this.inputValue.length;
              element.setSelectionRange(textLength, textLength);
              element.focus();
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.setSelectionRange) {
              const textLength = this.inputValue.length;
              textarea.setSelectionRange(textLength, textLength);
            }
            // #endif
          } catch (error) {
            console.warn('setCursorToEnd 执行失败:', error);
          }
        }
      });
    },

    // 设置文本内容
    setText(text) {
      this.inputValue = text;
      this.$nextTick(() => {
        this.setCursorToEnd();
      });
    },

    // 聚焦输入框
    focus() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('focus: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.focus) {
              element.focus();
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.focus) {
              textarea.focus();
            }
            // #endif
          } catch (error) {
            console.warn('focus 执行失败:', error);
          }
        }
      });
    },

    // 失焦输入框
    blur() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('blur: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.blur) {
              element.blur();
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.blur) {
              textarea.blur();
            }
            // #endif
          } catch (error) {
            console.warn('blur 执行失败:', error);
          }
        }
      });
    },



    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (!this.$refs || !this.$refs.textareaRef) {
          console.warn('scrollToBottom: textarea ref 未找到');
          return;
        }

        const textarea = this.$refs.textareaRef;
        if (textarea) {
          try {
            // H5环境
            // #ifdef H5
            const element = textarea.$el || textarea;
            if (element && element.scrollHeight !== undefined) {
              element.scrollTop = element.scrollHeight;
            }
            // #endif

            // App和小程序环境
            // #ifndef H5
            if (textarea.scrollTop !== undefined) {
              textarea.scrollTop = textarea.scrollHeight || 999999;
            }
            // #endif
          } catch (error) {
            console.warn('scrollToBottom 执行失败:', error);
          }
        }
      });
    },

    // 检查溢出状态
    checkOverflow() {
      // 这个方法主要是为了兼容旧代码调用
      try {
        return this.hasOverflow;
      } catch (error) {
        console.warn('checkOverflow 执行失败:', error);
        return false;
      }
    },

    // 获取textarea元素
    getTextareaElement() {
      if (!this.$refs || !this.$refs.textareaRef) {
        return null;
      }

      const textarea = this.$refs.textareaRef;
      if (textarea) {
        try {
          // H5环境
          // #ifdef H5
          return textarea.$el || textarea;
          // #endif

          // App和小程序环境
          // #ifndef H5
          return textarea;
          // #endif
        } catch (error) {
          console.warn('getTextareaElement 执行失败:', error);
          return null;
        }
      }
      return null;
    },

    // 清理事件监听器，防止内存泄漏
    cleanupEventListeners() {
      if (this._touchHandlers) {
        const { element, handleTouchStart, handleTouchMove, handleTouchEnd } = this._touchHandlers;
        if (element) {
          element.removeEventListener('touchstart', handleTouchStart);
          element.removeEventListener('touchmove', handleTouchMove);
          element.removeEventListener('touchend', handleTouchEnd);
          console.log('StableTextArea: 已清理触摸事件监听器');
        }
        this._touchHandlers = null;
      }

      // 清理惯性滚动动画
      if (this._inertiaAnimation) {
        cancelAnimationFrame(this._inertiaAnimation);
        this._inertiaAnimation = null;
      }

      // 清理鼠标拖拽事件监听器
      this.cleanupMouseGestureSimulation();

      // 清理滚动监听器
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.removeEventListener('scroll', this.handleScroll);
      }
    },

    // 初始化鼠标拖拽模拟手势滚动
    initMouseGestureSimulation() {
      // 只在H5平台启用鼠标拖拽模拟
      // #ifdef H5
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      // 鼠标按下事件
      const handleMouseDown = (e) => {
        // 只处理左键
        if (e.button !== 0) return;

        // 记录初始状态，但不立即开始拖拽
        this.dragStartY = e.clientY;
        this.dragStartX = e.clientX;
        this.dragStartScrollTop = textarea.scrollTop;
        this.isDragging = false; // 初始不设为拖拽状态

        console.log('鼠标按下，准备拖拽');
      };

      // 鼠标移动事件
      const handleMouseMove = (e) => {
        // 只有在鼠标按下且移动距离超过阈值时才开始拖拽
        if (!this.dragStartY) return;

        const deltaY = Math.abs(e.clientY - this.dragStartY);
        const deltaX = Math.abs(e.clientX - this.dragStartX);

        // 移动距离超过5px且主要是垂直移动时，才开始拖拽滚动
        if (deltaY > 5 && deltaY > deltaX && !this.isDragging) {
          this.isDragging = true;

          // 清除任何现有的文字选择
          if (window.getSelection) {
            window.getSelection().removeAllRanges();
          }

          console.log('开始鼠标拖拽滚动');
        }

        // 只有在拖拽状态下才执行滚动
        if (this.isDragging) {
          const totalDeltaY = e.clientY - this.dragStartY;
          const sensitivity = 1.5; // 拖拽灵敏度

          // 计算新的滚动位置
          const maxScroll = textarea.scrollHeight - textarea.clientHeight;
          const newScrollTop = Math.max(0, Math.min(
            maxScroll,
            this.dragStartScrollTop - totalDeltaY * sensitivity
          ));

          textarea.scrollTop = newScrollTop;

          // 持续清除文字选择
          if (window.getSelection) {
            window.getSelection().removeAllRanges();
          }

          // 只在拖拽时阻止默认行为
          e.preventDefault();
          e.stopPropagation();
        }
      };

      // 鼠标释放事件
      const handleMouseUp = (e) => {
        // 重置所有拖拽状态
        if (this.isDragging) {
          this.isDragging = false;
          console.log('结束鼠标拖拽滚动');
        }
        this.dragStartY = 0;
        this.dragStartX = 0;
        this.dragStartScrollTop = 0;
      };

      // 添加事件监听器
      textarea.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      // 保存事件处理器引用，用于清理
      this._mouseHandlers = {
        element: textarea,
        handleMouseDown,
        handleMouseMove,
        handleMouseUp
      };

      console.log('StableTextArea: 已初始化鼠标拖拽模拟手势滚动');
      // #endif
    },

    // 清理鼠标拖拽事件监听器
    cleanupMouseGestureSimulation() {
      // #ifdef H5
      if (this._mouseHandlers) {
        const { element, handleMouseDown, handleMouseMove, handleMouseUp } = this._mouseHandlers;
        if (element) {
          element.removeEventListener('mousedown', handleMouseDown);
        }
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        this._mouseHandlers = null;
        console.log('StableTextArea: 已清理鼠标拖拽事件监听器');
      }
      // #endif
    },

    // 初始化滚动监听
    initScrollListener() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      // 监听滚动事件
      textarea.addEventListener('scroll', this.handleScroll, { passive: true });

      console.log('StableTextArea: 已初始化滚动监听');
    },

    // 处理滚动事件
    handleScroll(e) {
      const now = Date.now();
      this.lastUserScrollTime = now;

      // 检查是否是用户主动滚动（非程序触发）
      if (!this.isDragging) {
        this.userHasScrolled = true;

        // 如果用户滚动到底部，恢复自动跟随
        if (this.isAtBottom) {
          this.enableAutoFollow();
        } else {
          // 用户滚动到其他位置，停止自动跟随
          this.disableAutoFollow();
        }
      }

      // 检查是否需要显示回到底部按钮
      this.checkScrollToBottomButton();
    },

    // 启用自动跟随
    enableAutoFollow() {
      this.isAutoFollowing = true;
      this.showScrollToBottomBtn = false;
      console.log('启用自动跟随滚动');
    },

    // 禁用自动跟随
    disableAutoFollow() {
      this.isAutoFollowing = false;
      console.log('禁用自动跟随滚动');
    },

    // 检查是否需要显示回到底部按钮
    checkScrollToBottomButton() {
      // 清除之前的定时器
      if (this.scrollToBottomTimer) {
        clearTimeout(this.scrollToBottomTimer);
      }

      // 如果不在底部且有内容溢出，显示回到底部按钮
      if (!this.isAtBottom && this.hasOverflow && !this.isAutoFollowing) {
        this.scrollToBottomTimer = setTimeout(() => {
          this.showScrollToBottomBtn = true;
        }, 1000); // 1秒后显示按钮
      } else {
        this.showScrollToBottomBtn = false;
      }
    },

    // 手动回到底部并恢复跟随
    scrollToBottomAndFollow() {
      this.scrollToBottom();
      this.enableAutoFollow();
      this.userHasScrolled = false;
    },

    // 显示操作提示（新增方法）
    showOperationTip(message, duration = 1500) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: duration
      });
    },

    // 提供触觉反馈（新增方法）
    provideTactileFeedback(type = 'light') {
      try {
        if (uni.vibrateShort) {
          uni.vibrateShort();
        } else if (uni.vibrate) {
          uni.vibrate({ duration: type === 'light' ? 15 : 25 });
        }
      } catch (error) {
        console.warn('触觉反馈失败:', error);
      }
    },

    // 显示选择成功反馈（新增方法）
    showSelectionFeedback(selectedText) {
      const length = selectedText.length;
      let message = '';

      if (length === 0) {
        message = '未选择任何文本';
      } else if (length === 1) {
        message = `已选择字符: "${selectedText}"`;
      } else if (length <= 10) {
        message = `已选择: "${selectedText}"`;
      } else {
        message = `已选择 ${length} 个字符`;
      }

      this.showOperationTip(message, 1000);
      this.provideTactileFeedback('light');
    }

  },

  // 清理所有文本框资源
  cleanupAllTextareaResources() {
    try {
      console.log('🧹 开始清理StableTextArea资源...');

      // 清理事件监听器
      this.cleanupEventListeners();

      // 清理鼠标拖拽事件监听器
      this.cleanupMouseGestureSimulation();

      // 清理触摸事件监听器
      if (this._touchHandlers) {
        const { element, handleTouchStart, handleTouchMove, handleTouchEnd } = this._touchHandlers;
        if (element) {
          element.removeEventListener('touchstart', handleTouchStart);
          element.removeEventListener('touchmove', handleTouchMove);
          element.removeEventListener('touchend', handleTouchEnd);
        }
        this._touchHandlers = null;
      }

      // 清理惯性滚动动画
      if (this._inertiaAnimation) {
        cancelAnimationFrame(this._inertiaAnimation);
        this._inertiaAnimation = null;
      }

      // 清理所有定时器
      if (this.scrollToBottomTimer) {
        clearTimeout(this.scrollToBottomTimer);
        this.scrollToBottomTimer = null;
      }

      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 隐藏选择菜单
      this.hideSelectionMenu();

      // 重置选择状态
      this.isSelectionMode = false;
      this.selectionStart = 0;
      this.selectionEnd = 0;

      // 清理textarea的滚动监听
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.removeEventListener('scroll', this.handleScroll);
      }

      // 清理状态管理器
      if (this.stateManager) {
        this.stateManager.destroy();
        this.stateManager = null;
        console.log('✅ 状态管理器已清理');
      }

      console.log('✅ StableTextArea资源清理完成');
    } catch (error) {
      console.error('❌ 清理StableTextArea资源时发生错误:', error);
    }
  }
}
</script>

<style scoped>
.stable-textarea-container {
  position: relative;
  width: 100%;
  /* 添加容器边框，确保输入框区域明显可见 */
  border-radius: 10px;
  background: linear-gradient(145deg, rgba(60, 60, 80, 0.1), rgba(40, 40, 60, 0.1));
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stable-textarea-container.selecting {
  border: 2px solid rgba(120, 100, 220, 0.9);
  box-shadow: 0 0 16px rgba(120, 100, 220, 0.6);
  animation: selectingPulse 2s ease-in-out infinite;
}

@keyframes selectingPulse {
  0%, 100% {
    box-shadow: 0 0 16px rgba(120, 100, 220, 0.6);
  }
  50% {
    box-shadow: 0 0 20px rgba(120, 100, 220, 0.8);
  }
}

.textarea-wrapper {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.stable-textarea {
  width: 100%;
  background-color: rgba(40, 40, 60, 0.9);
  color: #e8e8e8;
  border-radius: 8px;
  padding: 12px 60px 12px 12px;
  font-size: 14px;
  line-height: 1.5;
  box-sizing: border-box;
  border: 1px solid rgba(120, 100, 220, 0.3);
  outline: none;
  transition: all 0.3s ease;
  resize: none;
  min-height: 40px;
  max-height: 200px;
  overflow-y: auto;
}

.stable-textarea:focus {
  background-color: rgba(50, 50, 70, 0.95);
  border-color: rgba(120, 100, 220, 0.8);
  box-shadow: 0 0 8px rgba(120, 100, 220, 0.4);
}

.stable-textarea::placeholder {
  color: rgba(232, 232, 232, 0.6);
  font-size: 14px;
}

.send-button-slot {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

/* 平台特定样式 */
.h5-platform .stable-textarea {
  transition: height 0.2s ease;
  /* H5平台增强边框效果 */
  border: 2px solid rgba(120, 100, 220, 0.4);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

  /* H5移动端手势滚动优化 */
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  touch-action: pan-y; /* 允许垂直滑动 */
  overscroll-behavior-y: contain; /* 防止滚动链接到父元素 */

  /* 移动端优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;

  /* 关键：非焦点状态下禁用文字选择，启用滚动 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.h5-platform .stable-textarea:focus {
  border-color: rgba(120, 100, 220, 1);
  box-shadow: 0 0 12px rgba(120, 100, 220, 0.6), inset 0 1px 3px rgba(0, 0, 0, 0.1);

  /* 焦点状态下启用文字选择 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.ios-platform .stable-textarea {
  font-family: -apple-system, SF Pro, sans-serif;
  border: 1px solid rgba(120, 100, 220, 0.5);
}

.android-platform .stable-textarea {
  font-family: Roboto, sans-serif;
  border: 1px solid rgba(120, 100, 220, 0.5);
}

.app-platform .stable-textarea {
  /* App平台特定样式 */
  border: 1px solid rgba(120, 100, 220, 0.6);
  background-color: rgba(40, 40, 60, 0.95);
}

.mp-platform .stable-textarea {
  /* 小程序平台特定样式 */
  border: 1px solid rgba(120, 100, 220, 0.5);
  background-color: rgba(40, 40, 60, 0.9);
}

.mp-platform .stable-textarea {
  /* 小程序平台特定样式 */
}

/* 选择模式提示（增强版本） */
.selection-tip {
  position: absolute;
  top: -35px;
  right: 0;
  background: linear-gradient(135deg, rgba(120, 100, 220, 0.95), rgba(100, 80, 200, 0.95));
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0.9;
  z-index: 15;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: tipFadeIn 0.3s ease-out;
}

@keyframes tipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}

/* 回到底部按钮 */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: linear-gradient(135deg, rgba(120, 100, 220, 0.9), rgba(100, 80, 200, 0.9));
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  z-index: 15;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: slideInUp 0.3s ease-out;
}

.scroll-to-bottom-btn:hover {
  background: linear-gradient(135deg, rgba(120, 100, 220, 1), rgba(100, 80, 200, 1));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.scroll-to-bottom-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.scroll-btn-icon {
  font-size: 14px;
  font-weight: bold;
  animation: bounce 2s infinite;
}

.scroll-btn-text {
  font-size: 11px;
  font-weight: 500;
}

/* 按钮动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* 多媒体预览区域 */
.media-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.media-item {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(40, 40, 60, 0.5);
}

.media-image, .media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-remove {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  z-index: 10;
}

/* uni-app官方textarea滚动优化 */
.stable-textarea {
  /* 启用平滑滚动 */
  scroll-behavior: smooth;
  /* 优化触摸滚动 - uni-app官方支持 */
  -webkit-overflow-scrolling: touch;
  /* 确保滚动区域可见 */
  overflow-y: auto;
  /* 防止过度滚动 */
  overscroll-behavior: contain;
}

/* H5移动端滚动模式和编辑模式切换 */
.h5-platform .stable-textarea.scroll-mode {
  /* 滚动模式：禁用文字选择，启用手势滚动 */
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  touch-action: pan-y !important;
  -webkit-touch-callout: none !important;

  /* 真机手势滚动优化 - 这些属性在真机上效果更好 */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

.h5-platform .stable-textarea.edit-mode {
  /* 编辑模式：启用文字选择，保持滚动功能 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  touch-action: manipulation !important;

  /* 编辑模式下的触摸优化 */
  -webkit-tap-highlight-color: rgba(102, 126, 234, 0.2) !important;
}

/* 鼠标拖拽模拟手势滚动状态 */
.h5-platform .stable-textarea.mouse-dragging {
  cursor: grabbing !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  /* 开发测试用的鼠标拖拽优化 */
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 选择状态下的文本高亮 */
.stable-textarea::selection {
  background: rgba(120, 100, 220, 0.3);
  color: #fff;
}

.stable-textarea::-moz-selection {
  background: rgba(120, 100, 220, 0.3);
  color: #fff;
}
</style>
