/**
 * 创作者中心接口
 * 管理创作者信息、作品管理、收益统计等功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 👨‍🎨 创作者信息接口
// ================================

/**
 * 获取创作者信息
 */
export async function 获取创作者信息() {
	return await apiRequest('music/creator/profile');
}

/**
 * 更新创作者信息
 * @param {Object} profileData - 个人信息数据
 */
export async function 更新创作者信息(profileData) {
	return await apiRequest('music/creator/profile', {
		method: 'PUT',
		body: profileData
	});
}

/**
 * 上传头像
 * @param {File} avatarFile - 头像文件
 */
export async function 上传头像(avatarFile) {
	const formData = new FormData();
	formData.append('avatar', avatarFile);
	
	return await apiRequest('music/creator/upload-avatar', {
		method: 'POST',
		body: formData,
		headers: {
			// 不设置Content-Type，让浏览器自动设置
		}
	});
}

/**
 * 获取创作者统计
 */
export async function 获取创作者统计() {
	return await apiRequest('music/creator/stats');
}

/**
 * 获取创作者徽章
 */
export async function 获取创作者徽章() {
	return await apiRequest('music/creator/badges');
}

// ================================
// 🎵 作品管理接口
// ================================

/**
 * 获取创作者作品列表
 * @param {Object} params - 查询参数
 */
export async function 获取创作者作品列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/works?${queryParams}`);
}

/**
 * 获取作品详情
 * @param {string} musicId - 音乐ID
 */
export async function 获取作品详情(musicId) {
	return await apiRequest(`music/creator/work-detail?musicId=${musicId}`);
}

/**
 * 更新作品信息
 * @param {string} musicId - 音乐ID
 * @param {Object} updateData - 更新数据
 */
export async function 更新作品信息(musicId, updateData) {
	return await apiRequest('music/creator/update-work', {
		method: 'PUT',
		body: { musicId, ...updateData }
	});
}

/**
 * 删除作品
 * @param {string} musicId - 音乐ID
 */
export async function 删除作品(musicId) {
	return await apiRequest('music/creator/delete-work', {
		method: 'DELETE',
		body: { musicId }
	});
}

/**
 * 批量管理作品
 * @param {Object} params - 批量操作参数
 */
export async function 批量管理作品(params) {
	return await apiRequest('music/creator/batch-manage-works', {
		method: 'POST',
		body: params
	});
}

/**
 * 发布作品
 * @param {string} musicId - 音乐ID
 * @param {Object} publishOptions - 发布选项
 */
export async function 发布作品(musicId, publishOptions) {
	return await apiRequest('music/creator/publish-work', {
		method: 'POST',
		body: { musicId, ...publishOptions }
	});
}

/**
 * 下架作品
 * @param {string} musicId - 音乐ID
 * @param {string} reason - 下架原因
 */
export async function 下架作品(musicId, reason) {
	return await apiRequest('music/creator/unpublish-work', {
		method: 'POST',
		body: { musicId, reason }
	});
}

// ================================
// 💰 收益管理接口
// ================================

/**
 * 获取收益统计
 * @param {Object} params - 统计参数
 */
export async function 获取收益统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/revenue-stats?${queryParams}`);
}

/**
 * 获取收益明细
 * @param {Object} params - 查询参数
 */
export async function 获取收益明细(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/revenue-details?${queryParams}`);
}

/**
 * 申请提现
 * @param {Object} withdrawData - 提现数据
 */
export async function 申请提现(withdrawData) {
	return await apiRequest('music/creator/withdraw', {
		method: 'POST',
		body: withdrawData
	});
}

/**
 * 获取提现记录
 * @param {Object} params - 查询参数
 */
export async function 获取提现记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/withdraw-history?${queryParams}`);
}

/**
 * 设置收款信息
 * @param {Object} paymentInfo - 收款信息
 */
export async function 设置收款信息(paymentInfo) {
	return await apiRequest('music/creator/payment-info', {
		method: 'POST',
		body: paymentInfo
	});
}

// ================================
// 📊 数据分析接口
// ================================

/**
 * 获取作品数据分析
 * @param {string} musicId - 音乐ID
 * @param {Object} params - 分析参数
 */
export async function 获取作品数据分析(musicId, params = {}) {
	const queryParams = new URLSearchParams({ musicId, ...params }).toString();
	return await apiRequest(`music/creator/work-analytics?${queryParams}`);
}

/**
 * 获取粉丝数据分析
 * @param {Object} params - 分析参数
 */
export async function 获取粉丝数据分析(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/fan-analytics?${queryParams}`);
}

/**
 * 获取收益趋势分析
 * @param {Object} params - 分析参数
 */
export async function 获取收益趋势分析(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/revenue-trend?${queryParams}`);
}

// ================================
// 👥 粉丝管理接口
// ================================

/**
 * 获取粉丝列表
 * @param {Object} params - 查询参数
 */
export async function 获取粉丝列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/fans?${queryParams}`);
}

/**
 * 获取关注列表
 * @param {Object} params - 查询参数
 */
export async function 获取关注列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music/creator/following?${queryParams}`);
}

/**
 * 关注创作者
 * @param {string} creatorId - 创作者ID
 */
export async function 关注创作者(creatorId) {
	return await apiRequest('music/creator/follow', {
		method: 'POST',
		body: { creatorId }
	});
}

/**
 * 取消关注
 * @param {string} creatorId - 创作者ID
 */
export async function 取消关注(creatorId) {
	return await apiRequest('music/creator/unfollow', {
		method: 'POST',
		body: { creatorId }
	});
}

// ================================
// 🏆 成就系统接口
// ================================

/**
 * 获取成就列表
 */
export async function 获取成就列表() {
	return await apiRequest('music/creator/achievements');
}

/**
 * 领取成就奖励
 * @param {string} achievementId - 成就ID
 */
export async function 领取成就奖励(achievementId) {
	return await apiRequest('music/creator/claim-achievement', {
		method: 'POST',
		body: { achievementId }
	});
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整发布作品流程
 * @param {string} musicId - 音乐ID
 * @param {Object} publishOptions - 发布选项
 */
export async function 完整发布作品流程(musicId, publishOptions) {
	try {
		// 1. 更新作品信息
		if (publishOptions.workInfo) {
			await 更新作品信息(musicId, publishOptions.workInfo);
		}
		
		// 2. 发布作品
		const publishResult = await 发布作品(musicId, publishOptions);
		
		// 3. 如果需要销售，发布销售信息
		if (publishOptions.forSale) {
			// 这里需要调用音乐交易接口
			// await 发布音乐销售({ musicId, ...publishOptions.saleInfo });
		}
		
		return {
			success: true,
			data: publishResult.data
		};
		
	} catch (error) {
		console.error('完整发布作品流程失败:', error);
		throw error;
	}
}

/**
 * 获取创作者完整信息
 */
export async function 获取创作者完整信息() {
	try {
		const [
			profile,
			stats,
			badges,
			revenueStats
		] = await Promise.all([
			获取创作者信息(),
			获取创作者统计(),
			获取创作者徽章(),
			获取收益统计()
		]);
		
		return {
			success: true,
			data: {
				profile: profile.data,
				stats: stats.data,
				badges: badges.data,
				revenueStats: revenueStats.data
			}
		};
		
	} catch (error) {
		console.error('获取创作者完整信息失败:', error);
		throw error;
	}
}

export default {
	获取创作者信息,
	更新创作者信息,
	上传头像,
	获取创作者统计,
	获取创作者徽章,
	获取创作者作品列表,
	获取作品详情,
	更新作品信息,
	删除作品,
	批量管理作品,
	发布作品,
	下架作品,
	获取收益统计,
	获取收益明细,
	申请提现,
	获取提现记录,
	设置收款信息,
	获取作品数据分析,
	获取粉丝数据分析,
	获取收益趋势分析,
	获取粉丝列表,
	获取关注列表,
	关注创作者,
	取消关注,
	获取成就列表,
	领取成就奖励,
	完整发布作品流程,
	获取创作者完整信息
};
