<template>
	<view class="custom-date-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
			</view>

			<picker-view
				class="picker-view"
				:value="pickerValue"
				@change="onPickerChange"
			>
				<!-- 年份列 -->
				<picker-view-column>
					<view
						v-for="(year, index) in years"
						:key="index"
						class="picker-item"
					>
						{{ year }}年
					</view>
				</picker-view-column>

				<!-- 月份列 -->
				<picker-view-column>
					<view
						v-for="(month, index) in months"
						:key="index"
						class="picker-item"
					>
						{{ month }}月
					</view>
				</picker-view-column>

				<!-- 日期列 -->
				<picker-view-column>
					<view
						v-for="(day, index) in days"
						:key="index"
						class="picker-item"
					>
						{{ day }}日
					</view>
				</picker-view-column>
			</picker-view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		const now = new Date();
		return {
			selectedYear: now.getFullYear(),
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate(),
			years: [],
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
			pickerValue: [0, 0, 0]
		}
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			return Array.from({ length: daysInMonth }, (_, i) => i + 1);
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [year, month, day] = newVal.split('-');
					this.selectedYear = parseInt(year);
					this.selectedMonth = parseInt(month);
					this.selectedDay = parseInt(day);
					this.updatePickerValue();
				}
			},
			immediate: true
		},
		selectedMonth() {
			// 当月份改变时，检查日期是否有效
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
			}
			this.updatePickerValue();
		},
		visible: {
			handler(newVal) {
				if (newVal) {
					// 当选择器显示时，确保正确居中
					this.$nextTick(() => {
						setTimeout(() => {
							this.updatePickerValue();
						}, 100);
					});
				}
			},
			immediate: true
		}
	},
	created() {
		this.initYears();
		this.initCurrentDate();
		this.updatePickerValue();
	},
	methods: {
		initYears() {
			const currentYear = new Date().getFullYear();
			console.log('当前年份:', currentYear);
			// 扩展年份范围从1800年到未来10年，满足更广泛的出生年份需求
			for (let i = 1800; i <= currentYear + 10; i++) {
				this.years.push(i);
			}
			console.log('年份范围:', this.years.length, '从', this.years[0], '到', this.years[this.years.length - 1]);
		},

		initCurrentDate() {
			// 确保初始化为当前日期
			const now = new Date();
			this.selectedYear = now.getFullYear();
			this.selectedMonth = now.getMonth() + 1;
			this.selectedDay = now.getDate();

			console.log('初始化当前日期:', this.selectedYear, this.selectedMonth, this.selectedDay);
		},
		updatePickerValue() {
			// 更新picker-view的选中索引
			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.selectedMonth - 1;
			const dayIndex = this.selectedDay - 1;

			console.log('更新picker值:');
			console.log('- 选中年份:', this.selectedYear, '索引:', yearIndex);
			console.log('- 选中月份:', this.selectedMonth, '索引:', monthIndex);
			console.log('- 选中日期:', this.selectedDay, '索引:', dayIndex);

			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0
			];

			console.log('最终picker值:', this.pickerValue);

			// 强制更新picker-view的显示
			this.$nextTick(() => {
				this.forcePickerUpdate();
			});
		},
		forcePickerUpdate() {
			// 通过微调pickerValue来强制picker-view重新渲染和居中
			const currentValue = [...this.pickerValue];
			console.log('强制更新picker-view:', currentValue);

			// 使用轻微的重置来强制更新，但立即恢复
			this.pickerValue = [
				currentValue[0] || 0,
				currentValue[1] || 0,
				currentValue[2] || 0
			];

			this.$nextTick(() => {
				// 再次确保值正确
				this.pickerValue = [
					currentValue[0] || 0,
					currentValue[1] || 0,
					currentValue[2] || 0
				];
			});
		},
		onPickerChange(e) {
			console.log('picker change:', e.detail.value);
			let newValue = e.detail.value;

			// 确保数组长度正确（年、月、日 = 3个元素）
			if (newValue.length !== 3) {
				console.warn('picker value length mismatch:', newValue.length, 'expected: 3');
				newValue = newValue.slice(0, 3); // 只取前3个元素
				if (newValue.length < 3) {
					// 如果不足3个元素，补充默认值
					while (newValue.length < 3) {
						newValue.push(0);
					}
				}
			}

			// 直接更新值，不进行复杂的计算
			this.pickerValue = newValue;
			this.updateDateFromPickerValue(newValue);
		},

		updateDateFromPickerValue(value) {
			const [yearIndex, monthIndex, dayIndex] = value;

			this.selectedYear = this.years[yearIndex];
			this.selectedMonth = monthIndex + 1;
			this.selectedDay = dayIndex + 1;

			console.log('更新日期:', this.selectedYear, this.selectedMonth, this.selectedDay);

			// 检查日期有效性，但不立即调用updatePickerValue避免递归
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				console.log('日期无效，调整为最大日期:', maxDay);
				this.selectedDay = maxDay;
				// 延迟更新，避免干扰当前滚动
				this.$nextTick(() => {
					if (!this.isPickerScrolling) {
						this.updatePickerValue();
					}
				});
			}
		},
		onPickStart() {
			this.isPickerScrolling = true;
		},
		onPickEnd() {
			this.isPickerScrolling = false;
			console.log('滚动结束，当前值:', this.pickerValue);
			// 重新启用简化的磁性吸附
			this.$nextTick(() => {
				this.simpleSnap();
			});
		},

		simpleSnap() {
			// 简化的磁性吸附，只做整数修正
			const currentValue = [...this.pickerValue];

			// 确保索引在有效范围内
			const correctedValue = [
				Math.max(0, Math.min(Math.round(currentValue[0]), this.years.length - 1)),
				Math.max(0, Math.min(Math.round(currentValue[1]), this.months.length - 1)),
				Math.max(0, Math.min(Math.round(currentValue[2]), this.days.length - 1))
			];

			console.log('简单吸附:');
			console.log('- 当前值:', currentValue);
			console.log('- 修正值:', correctedValue);
			console.log('- 数据长度:', this.years.length, this.months.length, this.days.length);

			// 如果需要修正，直接设置
			if (currentValue[0] !== correctedValue[0] ||
			    currentValue[1] !== correctedValue[1] ||
			    currentValue[2] !== correctedValue[2]) {
				console.log('执行吸附修正');
				this.pickerValue = correctedValue;
			} else {
				console.log('位置正确，无需修正');
			}
		},
		magneticSnap() {
			// 基于位置计算的精确磁性吸附
			const currentValue = [...this.pickerValue];
			console.log('磁性吸附 - 当前值:', currentValue);
			console.log('数据长度 - 年:', this.years.length, '月:', this.months.length, '日:', this.days.length);

			const correctedValue = this.calculateSnapPosition(currentValue);
			console.log('磁性吸附 - 修正值:', correctedValue);

			// 如果需要修正位置，则进行吸附
			if (this.needsSnap(currentValue, correctedValue)) {
				console.log('需要吸附，执行修正');
				this.performPreciseSnap(correctedValue);
			} else {
				console.log('位置正确，无需吸附');
			}
		},

		calculateSnapPosition(currentValue) {
			// 确保输入数组长度正确
			const safeValue = [...currentValue];
			while (safeValue.length < 3) {
				safeValue.push(0);
			}
			if (safeValue.length > 3) {
				safeValue.splice(3); // 只保留前3个元素
			}

			// 计算每个列的精确吸附位置
			const correctedValue = [0, 0, 0];

			// 年份吸附计算
			const yearIndex = safeValue[0] || 0;
			const yearCount = this.years.length;
			correctedValue[0] = this.snapToNearestIndex(yearIndex, yearCount);

			// 月份吸附计算
			const monthIndex = safeValue[1] || 0;
			const monthCount = this.months.length;
			correctedValue[1] = this.snapToNearestIndex(monthIndex, monthCount);

			// 日期吸附计算
			const dayIndex = safeValue[2] || 0;
			const dayCount = this.days.length;
			correctedValue[2] = this.snapToNearestIndex(dayIndex, dayCount);

			return correctedValue;
		},

		snapToNearestIndex(currentIndex, maxCount) {
			console.log('吸附计算 - 输入:', currentIndex, '最大数量:', maxCount);

			// 确保索引在有效范围内
			if (currentIndex < 0) {
				console.log('索引小于0，返回0');
				return 0;
			}
			if (currentIndex >= maxCount) {
				console.log('索引超出范围，返回最大值:', maxCount - 1);
				return maxCount - 1;
			}

			// 精确的吸附规则计算
			const decimal = currentIndex - Math.floor(currentIndex);
			console.log('小数部分:', decimal);

			let result;
			// 如果偏移量小于0.3，向下吸附
			if (decimal < 0.3) {
				result = Math.floor(currentIndex);
				console.log('向下吸附:', result);
			}
			// 如果偏移量大于0.7，向上吸附
			else if (decimal > 0.7) {
				result = Math.ceil(currentIndex);
				console.log('向上吸附:', result);
			}
			// 否则吸附到最近的整数
			else {
				result = Math.round(currentIndex);
				console.log('就近吸附:', result);
			}

			return result;
		},

		needsSnap(currentValue, correctedValue) {
			// 确保两个数组都是正确长度
			if (currentValue.length !== 3 || correctedValue.length !== 3) {
				return true; // 长度不对就需要修正
			}

			// 检查是否需要进行位置修正
			return currentValue[0] !== correctedValue[0] ||
			       currentValue[1] !== correctedValue[1] ||
			       currentValue[2] !== correctedValue[2];
		},

		performPreciseSnap(targetValue) {
			// 确保目标值是正确的3元素数组
			const safeTargetValue = [...targetValue];
			while (safeTargetValue.length < 3) {
				safeTargetValue.push(0);
			}
			if (safeTargetValue.length > 3) {
				safeTargetValue.splice(3);
			}

			// 执行精确的位置吸附
			console.log('执行精确吸附:', this.pickerValue, '->', safeTargetValue);

			// 直接设置到计算出的精确位置
			this.pickerValue = [...safeTargetValue];

			// 确保DOM更新后再次确认位置
			this.$nextTick(() => {
				this.pickerValue = [...safeTargetValue];
			});
		},

		ensureAlignment() {
			// 兼容性方法，调用磁性吸附
			this.magneticSnap();
		},

		close() {
			this.$emit('close');
		},
		confirm() {
			const year = this.selectedYear;
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateStr = `${year}-${month}-${day}`;
			this.$emit('confirm', dateStr);
		}
	}
}
</script>

<style scoped>
.custom-date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 600rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
	border-radius: 20rpx;
	overflow: hidden;
	animation: scaleIn 0.3s ease;
	box-shadow: 0 10rpx 40rpx rgba(220, 20, 60, 0.3);
	border: 2rpx solid #DC143C;
	z-index: 2;
}

@keyframes scaleIn {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

.picker-header {
	padding: 30rpx;
	text-align: center;
	background: linear-gradient(135deg, #DC143C, #B22222);
	color: white;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
}

.picker-view {
	height: 400rpx;
	padding: 20rpx;
	/* 确保picker-view有足够的空间进行滚动 */
	overflow: visible;
	/* 移除可能影响滚动的属性 */
}

/* 确保picker-view-column正确显示 */
.picker-view ::v-deep .uni-picker-view-group {
	height: 80rpx;
}

.picker-view ::v-deep .uni-picker-view-content {
	padding: 0;
}

.picker-view ::v-deep .uni-picker-view-indicator {
	height: 80rpx;
}

.picker-view ::v-deep .uni-picker-view-mask {
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(255, 255, 255, 0.6) 40%,
		transparent 50%,
		transparent 50%,
		rgba(255, 255, 255, 0.6) 60%,
		rgba(255, 255, 255, 0.95) 100%);
}

.picker-item {
	height: 80rpx;
	line-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #DC143C;
	font-weight: 500;
	text-align: center;
	box-sizing: border-box;
	/* 移除可能影响滚动的CSS属性 */
}

.picker-footer {
	display: flex;
	padding: 30rpx;
	gap: 30rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: rgba(220, 20, 60, 0.1);
	color: #DC143C;
	border: 1rpx solid rgba(220, 20, 60, 0.3);
}

.btn-cancel:hover {
	background: rgba(220, 20, 60, 0.2);
	transform: scale(1.02);
}

.btn-confirm {
	background: linear-gradient(135deg, #DC143C, #B22222);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(220, 20, 60, 0.3);
}

.btn-confirm:hover {
	background: linear-gradient(135deg, #B22222, #8B0000);
	transform: scale(1.02);
	box-shadow: 0 6rpx 16rpx rgba(220, 20, 60, 0.4);
}
</style>
