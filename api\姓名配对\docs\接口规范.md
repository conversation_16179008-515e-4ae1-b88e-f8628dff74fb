# 姓名配对功能 - 接口规范

## 📋 **功能概述**

姓名配对功能基于传统易经学和现代数据分析，为用户提供两个人姓名的配对分析服务。

## 🔄 **数据流向**

```
前端表单数据 → 后端构建结构化参数 → 大模型处理 → 后端解析清洗 → 前端标准化数据
```

## 📤 **发送给大模型的结构化参数**

### **请求格式**
```javascript
{
    "requestId": "姓名配对_1641234567890_abc123",
    "userId": "user_123",
    "timestamp": 1641234567890,
    "moduleName": "姓名配对",
    "workflowId": "name_matching_workflow_001",
    "workflowType": "name_compatibility",
    "structuredParams": {
        "name1": {
            "type": "text",
            "value": "张三",
            "placeholder": "{{name1}}",
            "description": "第一个人的姓名"
        },
        "gender1": {
            "type": "text",
            "value": "male",
            "placeholder": "{{gender1}}",
            "description": "第一个人的性别"
        },
        "name2": {
            "type": "text",
            "value": "李四",
            "placeholder": "{{name2}}",
            "description": "第二个人的姓名"
        },
        "gender2": {
            "type": "text",
            "value": "female",
            "placeholder": "{{gender2}}",
            "description": "第二个人的性别"
        },
        "analysisType": {
            "type": "config",
            "value": "detailed",
            "placeholder": "{{analysisType}}",
            "description": "分析类型：basic|detailed|professional"
        },
        "returnFormat": {
            "type": "config",
            "value": {
                "format": "json",
                "structure": {
                    "matchScore": "number (0-100)",
                    "compatibility": "string (优秀|良好|一般|较差)",
                    "analysis": "string (详细分析文本)",
                    "strengths": "array<string> (优势列表)",
                    "challenges": "array<string> (挑战列表)",
                    "advice": "array<string> (建议列表)",
                    "elementAnalysis": "object (五行分析)",
                    "numerologyAnalysis": "object (数字学分析)"
                },
                "required_fields": ["matchScore", "compatibility", "analysis", "strengths", "advice"],
                "optional_fields": ["challenges", "elementAnalysis", "numerologyAnalysis"]
            },
            "placeholder": "{{returnFormat}}",
            "description": "指定返回数据的格式和结构"
        }
    }
}
```

### **提示词模板**
```
请基于以下信息进行专业的姓名配对分析：

## 配对信息
第一个人：
- 姓名：{{name1}}
- 性别：{{gender1}}

第二个人：
- 姓名：{{name2}}
- 性别：{{gender2}}

## 分析要求
分析类型：{{analysisType}}
请提供详细的姓名配对分析，包括：
1. 配对指数（0-100分）
2. 配对等级（优秀/良好/一般/较差）
3. 详细分析说明
4. 配对优势（至少3个）
5. 潜在挑战（至少2个）
6. 关系建议（至少3个）

## 返回格式要求
请严格按照以下JSON格式返回结果，不要包含任何其他文本：

{
  "matchScore": 数字(0-100),
  "compatibility": "字符串(优秀|良好|一般|较差)",
  "analysis": "详细分析文本",
  "strengths": ["优势1", "优势2", "优势3"],
  "challenges": ["挑战1", "挑战2"],
  "advice": ["建议1", "建议2", "建议3"],
  "elementAnalysis": {
    "name1Elements": ["五行元素"],
    "name2Elements": ["五行元素"],
    "elementCompatibility": "五行相配性分析"
  }
}
```

## 📥 **后端返回给前端的标准格式**

### **成功响应**
```javascript
{
    "success": true,
    "requestId": "姓名配对_1641234567890_abc123",
    "data": {
        "core": {
            "matchScore": 85,
            "compatibility": "良好",
            "analysis": "张三与李四的姓名配对分析显示两人具有很好的互补性...",
            "strengths": [
                "性格互补，张三的稳重与李四的活泼形成良好平衡",
                "五行相配，金水相生，有利于感情发展",
                "数字学显示两人有共同的人生目标"
            ],
            "challenges": [
                "沟通方式可能存在差异，需要多加理解",
                "生活节奏不同，需要相互适应"
            ],
            "advice": [
                "多进行深入的心灵沟通，增进相互了解",
                "在重要决策时要充分考虑对方的意见",
                "保持各自的独立性，同时培养共同兴趣"
            ],
            "elementAnalysis": {
                "name1Elements": ["金", "水"],
                "name2Elements": ["木", "火"],
                "elementCompatibility": "金水相生，木火相助，整体五行平衡"
            }
        },
        
        "display": {
            "overview": {
                "title": "张三 ❤️ 李四",
                "subtitle": "姓名配对分析",
                "score": 85,
                "level": "良好",
                "summary": "两人姓名配对显示良好的互补性，建议多加沟通...",
                "icon": "heart",
                "color": "#52c41a"
            },
            
            "details": {
                "sections": [
                    {
                        "title": "配对分析",
                        "type": "text",
                        "content": "张三与李四的姓名配对分析显示...",
                        "style": "paragraph"
                    },
                    {
                        "title": "配对优势",
                        "type": "list",
                        "content": [
                            {"text": "性格互补", "icon": "check", "color": "green"},
                            {"text": "五行相配", "icon": "check", "color": "green"},
                            {"text": "数字和谐", "icon": "check", "color": "green"}
                        ],
                        "style": "positive-list"
                    },
                    {
                        "title": "需要注意",
                        "type": "list",
                        "content": [
                            {"text": "沟通差异", "icon": "warning", "color": "orange"},
                            {"text": "生活节奏", "icon": "warning", "color": "orange"}
                        ],
                        "style": "warning-list"
                    },
                    {
                        "title": "关系建议",
                        "type": "list",
                        "content": [
                            {"text": "多沟通", "icon": "bulb", "color": "blue"},
                            {"text": "相互理解", "icon": "bulb", "color": "blue"},
                            {"text": "培养共同兴趣", "icon": "bulb", "color": "blue"}
                        ],
                        "style": "advice-list"
                    }
                ]
            }
        },
        
        "meta": {
            "processingTime": 2500,
            "aiModel": "gpt-4",
            "confidence": 0.92
        }
    },
    
    "cost": {
        "totalCost": 12,
        "breakdown": {
            "baseCost": 12,
            "extraCost": 0
        },
        "userBalance": 88
    },
    
    "timestamp": "2025-01-11T10:30:00.000Z"
}
```

### **错误响应**
```javascript
{
    "success": false,
    "requestId": "姓名配对_1641234567890_abc123",
    "error": {
        "code": "MATCH_001",
        "message": "姓名格式不正确",
        "details": "姓名必须为2-4个中文字符"
    },
    "timestamp": "2025-01-11T10:30:00.000Z"
}
```

## 🔧 **API接口定义**

### **执行姓名配对**
```
POST /api/v1/name-matching/execute
Content-Type: application/json

请求体：
{
    "userId": "用户ID",
    "formData": {
        "name1": "张三",
        "gender1": "male",
        "name2": "李四",
        "gender2": "female",
        "analysisType": "detailed"
    }
}
```

### **查询配对状态**
```
GET /api/v1/name-matching/status/{requestId}
```

### **取消配对请求**
```
DELETE /api/v1/name-matching/cancel/{requestId}
```

## 📊 **参数说明**

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| name1 | string | ✅ | 第一个人的姓名 | "张三" |
| gender1 | string | ❌ | 第一个人的性别 | "male" / "female" |
| name2 | string | ✅ | 第二个人的姓名 | "李四" |
| gender2 | string | ❌ | 第二个人的性别 | "male" / "female" |
| analysisType | string | ❌ | 分析类型 | "basic" / "detailed" / "professional" |

## 💰 **费用说明**

| 分析类型 | 基础费用 | 会员折扣 | 说明 |
|---------|---------|---------|------|
| basic | 12金币 | 8折 | 基础配对分析 |
| detailed | 17金币 | 8折 | 详细配对分析 |
| professional | 22金币 | 8折 | 专业配对分析 |

## 🚨 **错误码说明**

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| MATCH_001 | 姓名格式不正确 | 检查姓名格式 |
| MATCH_002 | 不能与自己配对 | 确保两个姓名不同 |
| MATCH_003 | 性别参数无效 | 使用有效的性别值 |
| MATCH_006 | 用户未登录 | 请先登录 |
| MATCH_007 | 金币余额不足 | 充值或选择较低费用选项 |

---

**文档版本**: v1.0  
**创建时间**: 2025-01-11  
**维护团队**: 姓名配对功能组
