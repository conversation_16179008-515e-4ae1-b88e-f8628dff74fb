<template>
  <view class="loading-container" v-if="visible">
    <view class="loading-mask" v-if="mask"></view>
    <view class="loading-content" :class="{ 'with-text': text }">
      <view class="loading-spinner">
        <view class="loading-dot" v-for="(_, index) in 12" :key="index" :style="{ animationDelay: index * 0.1 + 's' }"></view>
      </view>
      <text v-if="text" class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    text: {
      type: String,
      default: '加载中...'
    },
    mask: {
      type: <PERSON><PERSON>an,
      default: true
    }
  }
}
</script>

<style lang="scss">
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 10rpx;
  padding: 30rpx;
  width: 140rpx;
  height: 140rpx;
  
  &.with-text {
    width: 200rpx;
    height: 200rpx;
  }
}

.loading-spinner {
  position: relative;
  width: 70rpx;
  height: 70rpx;
}

.loading-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 14rpx;
  height: 14rpx;
  border-radius: 50%;
  background-color: #ffffff;
  margin-top: -7rpx;
  margin-left: -7rpx;
  animation: loading-fade 1.2s linear infinite;
  transform-origin: center -20rpx;
  
  @for $i from 1 through 12 {
    &:nth-child(#{$i}) {
      transform: rotate(($i - 1) * 30deg);
    }
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
}

@keyframes loading-fade {
  0%, 100% {
    opacity: 0.15;
  }
  50% {
    opacity: 1;
  }
}
</style> 