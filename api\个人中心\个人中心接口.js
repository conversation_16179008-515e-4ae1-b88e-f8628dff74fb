/**
 * 个人中心接口
 * 管理用户个人信息、设置、订单、历史记录等功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 👤 个人信息接口
// ================================

/**
 * 获取个人中心数据
 */
export async function 获取个人中心数据() {
	return await apiRequest('user/personal-center');
}

/**
 * 获取用户完整档案
 */
export async function 获取用户完整档案() {
	return await apiRequest('user/complete-profile');
}

/**
 * 更新个人资料
 * @param {Object} profileData - 个人资料数据
 */
export async function 更新个人资料(profileData) {
	return await apiRequest('user/update-profile', {
		method: 'PUT',
		body: profileData
	});
}

// ================================
// 📋 订单管理接口
// ================================

/**
 * 获取用户订单列表
 * @param {Object} params - 查询参数
 */
export async function 获取用户订单列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/orders?${queryParams}`);
}

/**
 * 获取订单详情
 * @param {string} orderId - 订单ID
 */
export async function 获取用户订单详情(orderId) {
	return await apiRequest(`user/order-detail?orderId=${orderId}`);
}

/**
 * 取消订单
 * @param {string} orderId - 订单ID
 * @param {string} reason - 取消原因
 */
export async function 取消用户订单(orderId, reason) {
	return await apiRequest('user/cancel-order', {
		method: 'POST',
		body: { orderId, reason }
	});
}

/**
 * 申请退款
 * @param {Object} refundData - 退款数据
 */
export async function 申请用户退款(refundData) {
	return await apiRequest('user/apply-refund', {
		method: 'POST',
		body: refundData
	});
}

// ================================
// 📊 消费记录接口
// ================================

/**
 * 获取消费记录
 * @param {Object} params - 查询参数
 */
export async function 获取消费记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/consumption-records?${queryParams}`);
}

/**
 * 获取消费统计
 * @param {Object} params - 统计参数
 */
export async function 获取消费统计(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/consumption-stats?${queryParams}`);
}

/**
 * 获取充值记录
 * @param {Object} params - 查询参数
 */
export async function 获取充值记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/recharge-records?${queryParams}`);
}

// ================================
// 📚 历史记录接口
// ================================

/**
 * 获取创作历史
 * @param {Object} params - 查询参数
 */
export async function 获取创作历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/creation-history?${queryParams}`);
}

/**
 * 获取浏览历史
 * @param {Object} params - 查询参数
 */
export async function 获取浏览历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/browse-history?${queryParams}`);
}

/**
 * 清空历史记录
 * @param {string} type - 历史类型
 */
export async function 清空历史记录(type) {
	return await apiRequest('user/clear-history', {
		method: 'POST',
		body: { type }
	});
}

/**
 * 删除单条历史记录
 * @param {string} historyId - 历史记录ID
 */
export async function 删除历史记录(historyId) {
	return await apiRequest('user/delete-history', {
		method: 'DELETE',
		body: { historyId }
	});
}

// ================================
// ⚙️ 设置管理接口
// ================================

/**
 * 获取用户设置
 */
export async function 获取用户设置() {
	return await apiRequest('user/settings');
}

/**
 * 更新用户设置
 * @param {Object} settings - 设置数据
 */
export async function 更新用户设置(settings) {
	return await apiRequest('user/settings', {
		method: 'PUT',
		body: settings
	});
}

/**
 * 获取隐私设置
 */
export async function 获取隐私设置() {
	return await apiRequest('user/privacy-settings');
}

/**
 * 更新隐私设置
 * @param {Object} privacySettings - 隐私设置
 */
export async function 更新隐私设置(privacySettings) {
	return await apiRequest('user/privacy-settings', {
		method: 'PUT',
		body: privacySettings
	});
}

// ================================
// 🎫 会员管理接口
// ================================

/**
 * 获取会员信息
 */
export async function 获取会员信息() {
	return await apiRequest('user/membership-info');
}

/**
 * 获取会员权益
 */
export async function 获取会员权益() {
	return await apiRequest('user/membership-benefits');
}

/**
 * 获取会员消费记录
 * @param {Object} params - 查询参数
 */
export async function 获取会员消费记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/membership-consumption?${queryParams}`);
}

// ================================
// 🎁 邀请推荐接口
// ================================

/**
 * 获取邀请信息
 */
export async function 获取邀请信息() {
	return await apiRequest('user/invitation-info');
}

/**
 * 获取邀请记录
 * @param {Object} params - 查询参数
 */
export async function 获取邀请记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/invitation-records?${queryParams}`);
}

/**
 * 生成邀请码
 */
export async function 生成邀请码() {
	return await apiRequest('user/generate-invitation-code', {
		method: 'POST'
	});
}

/**
 * 获取邀请奖励
 */
export async function 获取邀请奖励() {
	return await apiRequest('user/invitation-rewards');
}

// ================================
// 💬 反馈建议接口
// ================================

/**
 * 提交反馈
 * @param {Object} feedbackData - 反馈数据
 */
export async function 提交反馈(feedbackData) {
	return await apiRequest('user/submit-feedback', {
		method: 'POST',
		body: feedbackData
	});
}

/**
 * 获取反馈历史
 * @param {Object} params - 查询参数
 */
export async function 获取反馈历史(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/feedback-history?${queryParams}`);
}

/**
 * 获取反馈详情
 * @param {string} feedbackId - 反馈ID
 */
export async function 获取反馈详情(feedbackId) {
	return await apiRequest(`user/feedback-detail?feedbackId=${feedbackId}`);
}

// ================================
// 🔔 通知消息接口
// ================================

/**
 * 获取通知列表
 * @param {Object} params - 查询参数
 */
export async function 获取通知列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/notifications?${queryParams}`);
}

/**
 * 标记通知已读
 * @param {string} notificationId - 通知ID
 */
export async function 标记通知已读(notificationId) {
	return await apiRequest('user/mark-notification-read', {
		method: 'POST',
		body: { notificationId }
	});
}

/**
 * 批量标记已读
 * @param {Array} notificationIds - 通知ID数组
 */
export async function 批量标记通知已读(notificationIds) {
	return await apiRequest('user/batch-mark-notifications-read', {
		method: 'POST',
		body: { notificationIds }
	});
}

/**
 * 删除通知
 * @param {string} notificationId - 通知ID
 */
export async function 删除通知(notificationId) {
	return await apiRequest('user/delete-notification', {
		method: 'DELETE',
		body: { notificationId }
	});
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 获取个人中心完整数据
 */
export async function 获取个人中心完整数据() {
	try {
		const [
			personalData,
			orders,
			consumptionStats,
			membershipInfo,
			invitationInfo
		] = await Promise.all([
			获取个人中心数据(),
			获取用户订单列表({ page: 1, pageSize: 5, status: 'recent' }),
			获取消费统计({ period: 'month' }),
			获取会员信息(),
			获取邀请信息()
		]);
		
		return {
			success: true,
			data: {
				personalData: personalData.data,
				recentOrders: orders.data,
				consumptionStats: consumptionStats.data,
				membershipInfo: membershipInfo.data,
				invitationInfo: invitationInfo.data
			}
		};
		
	} catch (error) {
		console.error('获取个人中心完整数据失败:', error);
		throw error;
	}
}

export default {
	获取个人中心数据,
	获取用户完整档案,
	更新个人资料,
	获取用户订单列表,
	获取用户订单详情,
	取消用户订单,
	申请用户退款,
	获取消费记录,
	获取消费统计,
	获取充值记录,
	获取创作历史,
	获取浏览历史,
	清空历史记录,
	删除历史记录,
	获取用户设置,
	更新用户设置,
	获取隐私设置,
	更新隐私设置,
	获取会员信息,
	获取会员权益,
	获取会员消费记录,
	获取邀请信息,
	获取邀请记录,
	生成邀请码,
	获取邀请奖励,
	提交反馈,
	获取反馈历史,
	获取反馈详情,
	获取通知列表,
	标记通知已读,
	批量标记通知已读,
	删除通知,
	获取个人中心完整数据
};
