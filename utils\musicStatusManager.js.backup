/**
 * 音乐状态管理工具
 * 统一管理音乐的显示逻辑和状态判断
 */

// 音乐状态常量定义
export const MUSIC_STATUS = {
	DRAFT: 'draft',           // 草稿 - 创作中，不公开显示
	PUBLISHED: 'published',   // 已发行 - 可在主页、发现等公开区域显示
	SELLING: 'selling',       // 出售中 - 可在主页、发现、交易等区域显示
	SOLD: 'sold',            // 已售出 - 不在公开区域显示，只在创作者个人中心显示
	REMOVED: 'removed',       // 已下架 - 不显示
	PENDING: 'pending'        // 审核中 - 不公开显示
};

// 可在公开区域显示的状态（现在包含已售出，激发用户积极性）
export const DISPLAYABLE_STATUSES = [
	MUSIC_STATUS.PUBLISHED,
	MUSIC_STATUS.SELLING,
	MUSIC_STATUS.SOLD
];

// 可在交易区域显示的状态
export const TRADEABLE_STATUSES = [
	MUSIC_STATUS.SELLING
];

/**
 * 音乐状态管理类
 */
export class MusicStatusManager {
	
	/**
	 * 判断音乐是否可以在公开区域显示
	 * @param {Object} music - 音乐对象
	 * @returns {boolean} - 是否可以显示
	 */
	static isMusicAvailableForDisplay(music) {
		if (!music || !music.status) {
			console.warn('音乐对象或状态无效:', music);
			return false;
		}

		// 检查状态是否在可显示列表中（现在包含已售出状态）
		const isStatusValid = DISPLAYABLE_STATUSES.includes(music.status);

		// 移除售出检查，让已售出的歌曲也能显示，激发用户积极性
		// const isNotSold = !music.isSold && music.status !== MUSIC_STATUS.SOLD;

		// 检查是否被删除或下架
		const isNotRemoved = music.status !== MUSIC_STATUS.REMOVED;

		const canDisplay = isStatusValid && isNotRemoved;

		console.log(`音乐 "${music.title}" 状态检查:`, {
			status: music.status,
			isSold: music.isSold,
			isStatusValid,
			isNotRemoved,
			canDisplay
		});

		return canDisplay;
	}

	/**
	 * 判断音乐是否可以在交易区域显示
	 * @param {Object} music - 音乐对象
	 * @returns {boolean} - 是否可以交易
	 */
	static isMusicAvailableForTrade(music) {
		if (!music || !music.status) {
			return false;
		}

		// 只有出售中的音乐才能在交易区域显示
		const isTradeable = TRADEABLE_STATUSES.includes(music.status);
		const isNotSold = !music.isSold && music.status !== MUSIC_STATUS.SOLD;
		
		return isTradeable && isNotSold;
	}

	/**
	 * 判断音乐是否可以在发现页面显示
	 * @param {Object} music - 音乐对象
	 * @returns {boolean} - 是否可以在发现页面显示
	 */
	static isMusicAvailableForDiscover(music) {
		// 发现页面的显示逻辑与主页相同
		return this.isMusicAvailableForDisplay(music);
	}

	/**
	 * 过滤音乐列表，只返回可显示的音乐
	 * @param {Array} musicList - 音乐列表
	 * @param {string} context - 上下文 ('display', 'trade', 'discover')
	 * @returns {Array} - 过滤后的音乐列表
	 */
	static filterAvailableMusic(musicList, context = 'display') {
		if (!Array.isArray(musicList)) {
			console.warn('音乐列表不是数组:', musicList);
			return [];
		}

		let filterMethod;
		switch (context) {
			case 'trade':
				filterMethod = this.isMusicAvailableForTrade.bind(this);
				break;
			case 'discover':
				filterMethod = this.isMusicAvailableForDiscover.bind(this);
				break;
			case 'display':
			default:
				filterMethod = this.isMusicAvailableForDisplay.bind(this);
				break;
		}

		const filteredList = musicList.filter(filterMethod);
		
		console.log(`音乐列表过滤 (${context}):`, {
			原始数量: musicList.length,
			过滤后数量: filteredList.length,
			过滤掉的数量: musicList.length - filteredList.length
		});

		return filteredList;
	}

	/**
	 * 获取音乐状态的显示文本
	 * @param {string} status - 音乐状态
	 * @returns {string} - 状态显示文本
	 */
	static getStatusDisplayText(status) {
		const statusTextMap = {
			[MUSIC_STATUS.DRAFT]: '草稿',
			[MUSIC_STATUS.PUBLISHED]: '已发行',
			[MUSIC_STATUS.SELLING]: '出售中',
			[MUSIC_STATUS.SOLD]: '已售出',
			[MUSIC_STATUS.REMOVED]: '已下架',
			[MUSIC_STATUS.PENDING]: '审核中'
		};

		return statusTextMap[status] || '未知状态';
	}

	/**
	 * 获取音乐状态的样式类名
	 * @param {string} status - 音乐状态
	 * @returns {string} - CSS类名
	 */
	static getStatusStyleClass(status) {
		const statusClassMap = {
			[MUSIC_STATUS.DRAFT]: 'status-draft',
			[MUSIC_STATUS.PUBLISHED]: 'status-published',
			[MUSIC_STATUS.SELLING]: 'status-selling',
			[MUSIC_STATUS.SOLD]: 'status-sold',
			[MUSIC_STATUS.REMOVED]: 'status-removed',
			[MUSIC_STATUS.PENDING]: 'status-pending'
		};

		return statusClassMap[status] || 'status-unknown';
	}

	/**
	 * 标记音乐为已售出
	 * @param {Object} music - 音乐对象
	 * @param {Object} saleInfo - 销售信息
	 * @returns {Object} - 更新后的音乐对象
	 */
	static markMusicAsSold(music, saleInfo = {}) {
		return {
			...music,
			status: MUSIC_STATUS.SOLD,
			isSold: true,
			soldAt: saleInfo.soldAt || new Date().toISOString(),
			buyer: saleInfo.buyer || null,
			salePrice: saleInfo.salePrice || music.price,
			saleId: saleInfo.saleId || null
		};
	}

	/**
	 * 推荐算法 - 获取可推荐的音乐
	 * @param {Array} musicList - 音乐列表
	 * @param {Object} options - 推荐选项
	 * @returns {Array} - 推荐的音乐列表
	 */
	static getRecommendedMusic(musicList, options = {}) {
		const {
			limit = 10,
			shuffle = true,
			excludeIds = [],
			userPreferences = {}
		} = options;

		// 先过滤出可显示的音乐
		let availableMusic = this.filterAvailableMusic(musicList, 'display');

		// 排除指定的音乐ID
		if (excludeIds.length > 0) {
			availableMusic = availableMusic.filter(music => 
				!excludeIds.includes(music.id)
			);
		}

		// 如果需要打乱顺序（Fisher-Yates洗牌算法）
		if (shuffle) {
			const shuffled = [...availableMusic];
			for (let i = shuffled.length - 1; i > 0; i--) {
				const j = Math.floor(Math.random() * (i + 1));
				[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
			}
			availableMusic = shuffled;
		}

		// 应用用户偏好（简单实现，可以扩展）
		if (userPreferences.preferredGenres && userPreferences.preferredGenres.length > 0) {
			// 优先推荐用户喜欢的风格
			const preferred = availableMusic.filter(music => 
				userPreferences.preferredGenres.includes(music.genre)
			);
			const others = availableMusic.filter(music => 
				!userPreferences.preferredGenres.includes(music.genre)
			);
			availableMusic = [...preferred, ...others];
		}

		// 限制返回数量
		return availableMusic.slice(0, limit);
	}

	/**
	 * 检查音乐状态变更的合法性
	 * @param {string} currentStatus - 当前状态
	 * @param {string} newStatus - 新状态
	 * @returns {boolean} - 是否允许变更
	 */
	static isStatusChangeAllowed(currentStatus, newStatus) {
		// 定义状态变更规则
		const allowedTransitions = {
			[MUSIC_STATUS.DRAFT]: [MUSIC_STATUS.PENDING, MUSIC_STATUS.PUBLISHED, MUSIC_STATUS.SELLING],
			[MUSIC_STATUS.PENDING]: [MUSIC_STATUS.PUBLISHED, MUSIC_STATUS.DRAFT, MUSIC_STATUS.REMOVED],
			[MUSIC_STATUS.PUBLISHED]: [MUSIC_STATUS.SELLING, MUSIC_STATUS.REMOVED],
			[MUSIC_STATUS.SELLING]: [MUSIC_STATUS.SOLD, MUSIC_STATUS.REMOVED, MUSIC_STATUS.PUBLISHED],
			[MUSIC_STATUS.SOLD]: [], // 已售出的音乐不能变更状态
			[MUSIC_STATUS.REMOVED]: [MUSIC_STATUS.DRAFT] // 已下架的可以重新编辑
		};

		const allowed = allowedTransitions[currentStatus] || [];
		return allowed.includes(newStatus);
	}
}

// 默认导出
export default MusicStatusManager;
