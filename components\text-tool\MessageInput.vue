<template>
  <view class="message-input-container">
    <view class="input-wrapper">
      <!-- 已选标签区域 -->
    <view class="selected-tags-container" v-if="hasSelectedValues">
      <view 
          v-for="(itemKey, index) in selectedParamKeys"
          :key="'tag-'+index"
        class="selected-tag"
      >
          <text>{{getParamLabel(itemKey)}}</text><text>：</text><text>{{displayParamValues[itemKey]}}</text>
      </view>
    </view>
    
      <!-- 输入框区域 - 使用动态高度调整 -->
      <view class="prompt-input-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
      <textarea 
          ref="inputTextarea"
        class="prompt-input" 
          :placeholder="placeholder"
          v-model="text"
        :maxlength="maxLength"
        @input="onInput"
          :style="dynamicTextareaStyle"
      ></textarea>
      </view>
      
        <!-- 清除按钮 -->
      <view class="input-clear-btn" v-if="text" @tap="clearText">
          <text class="clear-icon">×</text>
        </view>
        
      <!-- 字数统计 -->
      <view class="char-counter">{{ text.length }}/{{ maxLength }}</view>
      
      <!-- 发送按钮 -->
      <view 
        class="input-send-btn" 
        :class="{'input-send-btn-active': canSubmit}"
        @tap="submit"
      >
        <text class="send-icon">➤</text>
      </view>
    </view>
    
    <!-- 预设词区域 - 只显示可选择的预设词，不显示任何已选标签 -->
    <view class="suggestion-container" v-if="suggestions && suggestions.length > 0">
      <view class="suggestion-title">{{ suggestionTitle }}</view>
      <scroll-view 
        scroll-x 
        class="suggestion-list"
      >
        <view 
          v-for="(suggestion, index) in suggestions" 
          :key="index"
          class="suggestion-item"
          :class="{ 'active': isSelected(suggestion) }"
          @tap="useSuggestion(suggestion)"
        >
          {{ suggestion.title }}
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { debounce, calculateTextareaHeight } from '../../utils/textareaHelper.js';

export default {
  name: 'MessageInput',
  props: {
    // 输入框占位文字
    placeholder: {
      type: String,
      default: '请输入您想要生成的内容...'
    },
    // 最大字数限制
    maxLength: {
      type: Number,
      default: 500
    },
    // 提示标题
    suggestionTitle: {
      type: String,
      default: '快捷提示'
    },
    // 提示词列表
    suggestions: {
      type: Array,
      default: () => []
    },
    // 参数标签信息
    params: {
      type: Array,
      default: () => []
    },
    // 输入框是否应与其他元素联动
    linkedWithParams: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      text: '',
      selectedSuggestions: [],
      selectedSuggestionItems: [],
      selectedTags: {},
      paramValues: {},
      // 输入框高度相关 - 进一步降低初始高度和最大高度
      textareaHeight: 50,
      minHeight: 50, // 降低最小高度
      maxHeight: 180, // 大幅降低最大高度限制
      // 滑动相关
      startY: 0,
      scrolling: false,
      touchStartTime: 0,
      lastTranslateY: 0,
      scrollSpeed: 0,
      inertiaFrame: null,
      // 防抖处理函数
      debouncedAdjustHeight: null
    }
  },
  computed: {
    // 动态文本域样式
    dynamicTextareaStyle() {
      return {
        height: `${this.textareaHeight}rpx`,
        minHeight: `${this.minHeight}rpx`,
        resize: 'none',
        overflow: 'hidden'
      }
    },
    // 是否有选中的标签
    hasSelectedValues() {
      return this.selectedParamKeys.length > 0;
    },
    // 是否可以提交
    canSubmit() {
      return this.text.trim().length > 0;
    },
    // 获取已选参数显示值
    displayParamValues() {
      const result = {};
      
      try {
        // 处理常规参数值
        (this.params || []).forEach(param => {
          const paramKey = param.key;
          const paramValue = this.paramValues[paramKey];
          
          if (paramValue) {
            let displayValue = String(paramValue);
            
            // 如果是预设，显示预设的标签
            if (param.presets && Array.isArray(param.presets)) {
              const preset = param.presets.find(p => 
                (p.value !== undefined && String(p.value) === String(paramValue)) || 
                (p.label !== undefined && String(p.label) === String(paramValue))
              );
              
              if (preset && preset.label) {
                displayValue = String(preset.label);
              }
            }
            
            result[paramKey] = displayValue;
          }
          
          // 标签参数处理
          if (param.type === 'tags' && this.selectedTags) {
            const tags = this.selectedTags[paramKey];
            if (Array.isArray(tags) && tags.length > 0) {
              result[paramKey] = tags.map(tag => String(tag)).join(', ');
        }
      }
        });
      } catch (err) {
        console.warn('处理显示参数值时出错:', err);
      }
      
      return result;
    },
    // 获取已选参数的键列表
    selectedParamKeys() {
      try {
        return Object.keys(this.displayParamValues || {}).filter(key => {
          const value = this.displayParamValues[key];
          return typeof value === 'string' && value.trim() !== '';
        });
      } catch (err) {
        console.warn('获取已选参数键失败:', err);
        return [];
      }
    }
  },
  watch: {
    // 监听输入框高度变化，通知父组件
    textareaHeight(newHeight) {
      if (this.linkedWithParams) {
        // 通知父组件输入框高度变化
        this.$emit('height-change', newHeight);
      }
    }
  },
  mounted() {
    // 创建防抖处理函数
    this.debouncedAdjustHeight = debounce(this.adjustTextareaHeight, 100);
    
    this.$nextTick(() => {
      this.adjustTextareaHeight();
    });
  },
  beforeDestroy() {
    // 清理惯性滚动
    if (this.inertiaFrame) {
      cancelAnimationFrame(this.inertiaFrame);
      this.inertiaFrame = null;
    }
  },
  methods: {
    // 输入框内容变化
    onInput() {
      this.$emit('input', this.text);
      this.debouncedAdjustHeight(); // 使用防抖函数优化性能
    },
    
    // 动态调整textarea高度
    adjustTextareaHeight() {
      const textarea = this.$refs.inputTextarea;
      if (!textarea) return;
      
      // 使用工具函数计算高度
      const newHeight = calculateTextareaHeight(
        textarea, 
        this.minHeight, 
        this.maxHeight
      );
      
      // 进一步控制高度增长速度，每次最多增加8rpx
      const currentHeight = this.textareaHeight;
      const heightDiff = newHeight - currentHeight;
      
      if (heightDiff > 8) {
        // 如果需要增长超过8rpx，则限制增长速度
        this.textareaHeight = currentHeight + 8;
      } else if (heightDiff < -8) {
        // 如果需要减少超过8rpx，则限制减少速度
        this.textareaHeight = currentHeight - 8;
      } else if (Math.abs(heightDiff) > 3) {
        // 只有当高度变化超过3rpx时才更新，减少不必要的重绘
        this.textareaHeight = newHeight;
      }
    },
    
    // 触摸滑动相关方法
    handleTouchStart(e) {
      if (!e.touches || !e.touches[0]) return;
      
      this.startY = e.touches[0].clientY;
      this.touchStartTime = Date.now();
      this.scrolling = true;
      
      // 停止惯性滚动
      if (this.inertiaFrame) {
        cancelAnimationFrame(this.inertiaFrame);
        this.inertiaFrame = null;
      }
    },
    
    handleTouchMove(e) {
      if (!this.scrolling || !e.touches || !e.touches[0]) return;
      
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - this.startY;
      
      if (Math.abs(deltaY) > 5) {
        const textarea = this.$refs.inputTextarea;
        if (textarea) {
          // 计算速度
          const now = Date.now();
          const elapsed = now - this.touchStartTime;
          if (elapsed > 0) {
            this.scrollSpeed = deltaY / elapsed * 15;
          }
          
          // 应用滚动
          textarea.scrollTop -= deltaY;
          
          // 更新位置和时间
          this.startY = currentY;
          this.touchStartTime = now;
        }
      }
    },
    
    handleTouchEnd() {
      this.scrolling = false;
      
      // 启动惯性滚动
      if (Math.abs(this.scrollSpeed) > 0.5) {
        this.startInertialScroll();
      }
    },
    
    // 惯性滚动
    startInertialScroll() {
      const textarea = this.$refs.inputTextarea;
      if (!textarea) return;
      
      let speed = this.scrollSpeed;
      const deceleration = 0.95; // 减速因子
      
      const scroll = () => {
        speed *= deceleration;
        
        if (Math.abs(speed) < 0.5) {
          cancelAnimationFrame(this.inertiaFrame);
          this.inertiaFrame = null;
          return;
        }
        
        textarea.scrollTop -= speed;
        this.inertiaFrame = requestAnimationFrame(scroll);
      };
      
      this.inertiaFrame = requestAnimationFrame(scroll);
    },
    
    // 使用提示词
    useSuggestion(suggestion) {
      // 获取完整提示词文本
      const suggestionText = suggestion.prompt || suggestion.title;
      
      // 直接将提示词添加到文本内容中
      this.text = this.text ? `${this.text} ${suggestionText}` : suggestionText;
      
      // 更新文本高度
      this.$nextTick(() => {
        this.adjustTextareaHeight();
      });
      
      // 通知父组件
      this.$emit('use-suggestion', suggestion);
      this.$emit('input', this.text);
    },
    
    // 清空输入框
    clearText() {
      this.text = '';
      this.selectedSuggestions = [];
      this.selectedSuggestionItems = [];
      this.$emit('input', this.text);
      
      // 重置高度
      this.textareaHeight = this.minHeight;
    },
    
    // 外部调用的清空方法（兼容性接口）
    clearInput() {
      this.clearText();
    },
    
    // 聚焦输入框
    focusInput() {
      const textarea = this.$refs.inputTextarea;
      if (textarea) {
        textarea.focus();
      }
    },
    
    // 添加文本
    appendText(text) {
      if (!text) return;
      
      if (this.text) {
        this.text += ' ' + text;
      } else {
        this.text = text;
      }
      
      this.$emit('input', this.text);
      this.$nextTick(() => {
        this.adjustTextareaHeight();
      });
    },
    
    // 设置文本
    setText(text) {
      this.text = text || '';
      this.$emit('input', this.text);
      this.$nextTick(() => {
        this.adjustTextareaHeight();
      });
    },
    
    // 提交内容
    submit() {
      if (!this.canSubmit) return;
      
      this.$emit('submit', {
        text: this.text,
        params: this.paramValues,
        tags: this.selectedTags,
        suggestions: this.selectedSuggestions,
        suggestionItems: this.selectedSuggestionItems
      });
    },
    
    // 获取参数标签名
    getParamLabel(key) {
      if (!key) return key;
      
      const param = this.params ? this.params.find(p => p.key === key) : null;
      return param ? param.label || key : key;
    },
    
    // 检查提示词是否已被选中（用于高亮显示）
    isSelected(suggestion) {
      return this.text.includes(suggestion.prompt || suggestion.title);
    },
    
    // 外部调用的方法，用于根据参数区域状态调整布局
    adjustLayoutForParams(isParamsPanelExpanded) {
      // 外部可以调用此方法来告知输入框参数面板状态改变
      if (isParamsPanelExpanded) {
        // 参数面板展开时，可能需要额外处理
        this.$nextTick(() => {
          this.adjustTextareaHeight();
        });
  }
    }
  }
}
</script>

<style>
/* 基础容器样式 */
.message-input-container {
  width: 100%;
  background-color: rgba(40, 40, 56, 0.6);
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 输入框包装容器 */
.input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  z-index: 5;
}

/* 已选标签容器 */
.selected-tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8rpx; /* 减少与输入框的间距 */
  gap: 8rpx;
}

.selected-tag {
  background-color: rgba(90, 80, 140, 0.3);
  border-radius: 6rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  color: rgba(240, 240, 245, 0.9);
}

/* 输入框样式 */
.prompt-input-container {
  position: relative;
  width: 100%;
  margin-bottom: 10rpx; /* 减少底部间距 */
  overflow: hidden;
}

.prompt-input {
  width: 100%;
  min-height: 60rpx; /* 减小最小高度 */
  background-color: rgba(60, 60, 80, 0.3);
  border-radius: 8rpx;
  padding: 12rpx; /* 减小内边距 */
  padding-right: 100rpx; /* 为按钮留出空间 */
  font-size: 28rpx;
  color: rgba(240, 240, 245, 0.9);
  z-index: 10;
  position: relative;
  transition: height 0.2s;
  -webkit-overflow-scrolling: touch;
  line-height: 1.3; /* 添加适当的行高 */
}

/* 清除按钮 */
.input-clear-btn {
  position: absolute;
  right: 80rpx;
  top: 12rpx; /* 调整位置 */
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(100, 100, 120, 0.3);
  border-radius: 50%;
  z-index: 10;
}

.clear-icon {
  font-size: 24rpx;
  color: rgba(240, 240, 245, 0.7);
}

/* 字数统计 */
.char-counter {
  position: absolute;
  right: 150rpx;
  top: 16rpx; /* 调整位置 */
  font-size: 22rpx;
  color: rgba(240, 240, 245, 0.5);
}

/* 发送按钮 */
.input-send-btn {
  position: absolute;
  right: 16rpx;
  top: 12rpx; /* 调整位置 */
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: rgba(80, 80, 100, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.input-send-btn-active {
  background-color: rgba(110, 86, 207, 0.8);
}

.send-icon {
  font-size: 24rpx;
  color: rgba(240, 240, 245, 0.9);
  transform: translateX(2rpx);
}

/* 提示词区域 */
.suggestion-container {
  margin-top: 12rpx; /* 减少上边距 */
  position: relative;
  z-index: 1;
}

.suggestion-title {
  font-size: 26rpx;
  color: rgba(240, 240, 245, 0.7);
  margin-bottom: 8rpx;
}

.suggestion-list {
  width: 100%;
  white-space: nowrap;
  display: flex;
  overflow-x: auto;
}

.suggestion-item {
  display: inline-block;
  padding: 6rpx 16rpx;
  margin-right: 12rpx;
  background-color: rgba(70, 70, 90, 0.3);
  border-radius: 6rpx;
  font-size: 24rpx;
  color: rgba(240, 240, 245, 0.9);
  cursor: pointer;
}

.suggestion-item.active {
  background-color: rgba(110, 86, 207, 0.8);
  color: #ffffff;
}

/* 媒体查询适配小屏幕设备 */
@media screen and (max-height: 600px) {
  .prompt-input {
    min-height: 50rpx; /* 在小屏幕上进一步减小输入框高度 */
    padding: 10rpx;
    padding-right: 100rpx;
  }
  
  .suggestion-container {
    margin-top: 8rpx;
  }
}
</style> 