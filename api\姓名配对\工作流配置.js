/**
 * 姓名配对功能工作流配置
 * 定义姓名配对功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 姓名配对工作流配置
 */
export const 姓名配对工作流配置 = {
    // 工作流基础信息
    workflowId: 'name_matching_workflow_001',
    workflowType: 'name_compatibility',
    moduleName: '姓名配对',
    
    // 工作流描述
    description: '基于传统易经学文化的姓名配对分析工作流',

    // 结构化参数定义
    structuredParams: {
        
        name1: {
            type: 'text',
            required: true,
            placeholder: '{{name1}}',
            description: 'name1'
        },
        
        gender1: {
            type: 'text',
            required: true,
            placeholder: '{{gender1}}',
            description: 'gender1'
        },
        
        birthDate1: {
            type: 'text',
            required: true,
            placeholder: '{{birthDate1}}',
            description: 'birthDate1'
        },
        
        birthTime1: {
            type: 'text',
            required: true,
            placeholder: '{{birthTime1}}',
            description: 'birthTime1'
        },
        
        name2: {
            type: 'text',
            required: true,
            placeholder: '{{name2}}',
            description: 'name2'
        },
        
        gender2: {
            type: 'text',
            required: true,
            placeholder: '{{gender2}}',
            description: 'gender2'
        },
        
        birthDate2: {
            type: 'text',
            required: true,
            placeholder: '{{birthDate2}}',
            description: 'birthDate2'
        },
        
        birthTime2: {
            type: 'text',
            required: true,
            placeholder: '{{birthTime2}}',
            description: 'birthTime2'
        }
    },

    // 提示词模板 - 关键：包含返回格式要求
    promptTemplate: `
请基于以下信息进行专业的姓名配对分析：

## 配对信息
第一个人：
- 姓名：{{name1}}
- 性别：{{gender1}}
- 出生日期：{{birthDate1}}
- 出生时间：{{birthTime1}}

第二个人：
- 姓名：{{name2}}
- 性别：{{gender2}}
- 出生日期：{{birthDate2}}
- 出生时间：{{birthTime2}}

## 分析要求
请提供详细的姓名配对分析，包括：
1. 配对指数（0-100分）
2. 配对等级（优秀/良好/一般/较差）
3. 详细分析说明
4. 配对优势（至少3个）
5. 潜在挑战（至少2个）
6. 关系建议（至少3个）
7. 五行分析（如果适用）
8. 数字学分析

## 返回格式要求
请严格按照以下JSON格式返回结果，不要包含任何其他文本：

{
  "matchScore": 数字(0-100),
  "compatibility": "字符串(优秀/良好/一般/较差)",
  "analysis": "详细分析文本",
  "strengths": ["优势1", "优势2", "优势3"],
  "challenges": ["挑战1", "挑战2"],
  "advice": ["建议1", "建议2", "建议3"],
  "elementAnalysis": {
    "name1Elements": ["五行元素"],
    "name2Elements": ["五行元素"],
    "elementCompatibility": "五行相配性分析"
  },
  "numerologyAnalysis": {
    "name1Number": 数字,
    "name2Number": 数字,
    "combinedNumber": 数字,
    "numerologyMeaning": "数字学含义"
  }
}
`,

    // 输出格式定义 - 详细的返回格式标准
    outputFormat: {
        type: 'json',
        description: '标准化的姓名配对分析结果',
        schema: {
            matchScore: {
                type: 'number',
                range: [0, 100],
                description: '配对分数，0-100分'
            },
            compatibility: {
                type: 'string',
                enum: ['优秀', '良好', '一般', '较差'],
                description: '配对等级'
            },
            analysis: {
                type: 'string',
                minLength: 100,
                description: '详细的配对分析文本'
            },
            strengths: {
                type: 'array',
                items: 'string',
                minItems: 3,
                description: '配对优势列表'
            },
            challenges: {
                type: 'array',
                items: 'string',
                minItems: 2,
                description: '潜在挑战列表'
            },
            advice: {
                type: 'array',
                items: 'string',
                minItems: 3,
                description: '关系建议列表'
            },
            elementAnalysis: {
                type: 'object',
                description: '五行分析结果',
                properties: {
                    name1Elements: {
                        type: 'array',
                        items: 'string',
                        description: '第一个人的五行属性'
                    },
                    name2Elements: {
                        type: 'array',
                        items: 'string',
                        description: '第二个人的五行属性'
                    },
                    elementCompatibility: {
                        type: 'string',
                        description: '五行相配性分析'
                    }
                }
            },
            numerologyAnalysis: {
                type: 'object',
                description: '数字学分析结果',
                properties: {
                    name1Number: {
                        type: 'number',
                        description: '第一个人的姓名数字'
                    },
                    name2Number: {
                        type: 'number',
                        description: '第二个人的姓名数字'
                    },
                    combinedNumber: {
                        type: 'number',
                        description: '组合数字'
                    },
                    numerologyMeaning: {
                        type: 'string',
                        description: '数字学含义解释'
                    }
                }
            }
        }
    },

    // 费用配置
    pricing: {
        basePrice: 12,
        memberDiscount: 0.8
    },

    // 执行配置
    execution: {
        timeout: 300000,
        maxRetries: 3,
        pollInterval: 2000,
        enableCache: true
    }
};

/**
 * 姓名配对参数验证规则
 */
export const 姓名配对参数验证规则 = {
    required: ['name1', 'gender1', 'birthDate1', 'birthTime1', 'name2', 'gender2', 'birthDate2', 'birthTime2'],
    formats: {},
    ranges: {},
    enums: {}
};

/**
 * 姓名配对错误码定义
 */
export const 姓名配对错误码 = {
    INVALID_PARAMS: { code: 'NAME_COMPATIBILITY_001', message: '参数格式不正确' },
    INSUFFICIENT_COINS: { code: 'NAME_COMPATIBILITY_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'NAME_COMPATIBILITY_008', message: '工作流执行超时' },
    WORKFLOW_FAILED: { code: 'NAME_COMPATIBILITY_009', message: '工作流执行失败' },
    UNKNOWN_ERROR: { code: 'NAME_COMPATIBILITY_999', message: '未知错误' }
};

/**
 * 姓名配对状态定义
 */
export const 姓名配对状态 = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

export default {
    姓名配对工作流配置,
    姓名配对参数验证规则,
    姓名配对错误码,
    姓名配对状态
};