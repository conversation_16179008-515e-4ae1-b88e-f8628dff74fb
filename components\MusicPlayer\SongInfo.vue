<template>
	<view class="song-info" :style="themeStyles">
		<!-- 歌曲名称 + 歌词切换按钮 -->
		<view class="song-title-row">
			<text class="song-title">{{ music.title || '未知歌曲' }}</text>
			<view class="lyrics-toggle-btn" @click="handleToggleLyrics">
				<text class="lyrics-icon">{{ showLyrics ? '🎵' : '🎵' }}</text>
				<text class="lyrics-text">{{ showLyrics ? '封面' : '歌词' }}</text>
			</view>
		</view>
		
		<!-- 作者信息 + 关注按钮 -->
		<view class="author-row">
			<view class="author-info" @click="handleAuthorClick">
				<text class="author-name">{{ music.artist || '未知艺术家' }}</text>
			</view>
			<view 
				class="follow-btn" 
				:class="{ followed: isFollowed }"
				@click="handleFollow"
			>
				<text class="follow-icon">{{ isFollowed ? '✓' : '+' }}</text>
				<text class="follow-text">{{ isFollowed ? '已关注' : '关注' }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getTheme, getCurrentTheme } from '@/utils/playerThemes.js';

export default {
	name: 'SongInfo',
	props: {
		// 音乐信息
		music: {
			type: Object,
			required: true,
			default: () => ({
				title: '未知歌曲',
				artist: '未知艺术家',
				authorId: null
			})
		},
		// 是否显示歌词
		showLyrics: {
			type: Boolean,
			default: false
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	data() {
		return {
			isFollowed: false
		};
	},
	computed: {
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--text-primary': theme.colors.textPrimary,
				'--text-secondary': theme.colors.textSecondary,
				'--button-bg': theme.colors.buttonBg,
				'--button-bg-active': theme.colors.buttonBgActive,
				'--icon-color-active': theme.colors.iconColorActive
			};
		}
	},
	mounted() {
		// 检查是否已关注该作者
		this.checkFollowStatus();
	},
	watch: {
		'music.authorId'() {
			this.checkFollowStatus();
		}
	},
	methods: {
		// 切换歌词显示
		handleToggleLyrics() {
			this.$emit('toggle-lyrics');
		},
		
		// 点击作者名称
		handleAuthorClick() {
			if (!this.music.authorId) {
				uni.showToast({
					title: '作者信息不可用',
					icon: 'none'
				});
				return;
			}
			
			this.$emit('author-click', {
				authorId: this.music.authorId,
				authorName: this.music.artist
			});
		},
		
		// 关注/取消关注
		handleFollow() {
			if (!this.music.authorId) {
				uni.showToast({
					title: '作者信息不可用',
					icon: 'none'
				});
				return;
			}
			
			this.isFollowed = !this.isFollowed;
			
			this.$emit('follow', {
				authorId: this.music.authorId,
				authorName: this.music.artist,
				isFollowed: this.isFollowed
			});
			
			uni.showToast({
				title: this.isFollowed ? '关注成功' : '已取消关注',
				icon: 'success'
			});
		},
		
		// 检查关注状态
		checkFollowStatus() {
			if (!this.music.authorId) {
				this.isFollowed = false;
				return;
			}
			
			// 从本地存储或API检查关注状态
			try {
				const followedAuthors = uni.getStorageSync('followed_authors') || [];
				this.isFollowed = followedAuthors.includes(this.music.authorId);
			} catch (e) {
				console.error('检查关注状态失败:', e);
				this.isFollowed = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.song-info {
	padding: 30rpx 40rpx;
	background: rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(20rpx);
}

.song-title-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.song-title {
	flex: 1;
	color: var(--text-primary, #FFFFFF);
	font-size: 36rpx;
	font-weight: bold;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: 20rpx;
}

.lyrics-toggle-btn {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 10rpx 20rpx;
	background: var(--button-bg, rgba(255, 255, 255, 0.2));
	backdrop-filter: blur(10rpx);
	border-radius: 30rpx;
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.95);
		background: var(--button-bg-active, rgba(255, 255, 255, 0.3));
	}
}

.lyrics-icon {
	font-size: 28rpx;
}

.lyrics-text {
	color: var(--text-primary, #FFFFFF);
	font-size: 24rpx;
}

.author-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.author-info {
	flex: 1;
	display: flex;
	align-items: center;
	cursor: pointer;
	
	&:active {
		opacity: 0.7;
	}
}

.author-name {
	color: var(--text-secondary, rgba(255, 255, 255, 0.7));
	font-size: 28rpx;
	text-decoration: underline;
	text-decoration-style: dotted;
}

.follow-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 8rpx 20rpx;
	background: var(--button-bg, rgba(255, 255, 255, 0.2));
	backdrop-filter: blur(10rpx);
	border-radius: 30rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.95);
	}
	
	&.followed {
		background: var(--icon-color-active, #50E3C2);
		border-color: var(--icon-color-active, #50E3C2);
		
		.follow-icon,
		.follow-text {
			color: #FFFFFF;
		}
	}
}

.follow-icon {
	font-size: 24rpx;
	font-weight: bold;
	color: var(--text-primary, #FFFFFF);
}

.follow-text {
	color: var(--text-primary, #FFFFFF);
	font-size: 24rpx;
}

/* H5端优化 */
/* #ifdef H5 */
.author-info {
	cursor: pointer;
	
	&:hover {
		.author-name {
			color: var(--icon-color-active, #50E3C2);
		}
	}
}

.follow-btn {
	cursor: pointer;
	
	&:hover {
		background: var(--button-bg-active, rgba(255, 255, 255, 0.3));
	}
	
	&.followed:hover {
		opacity: 0.9;
	}
}
/* #endif */
</style>

