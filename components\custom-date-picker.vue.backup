<template>
	<view class="custom-date-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
				<view class="header-actions">
					<text class="action-btn cancel" @click="close">取消</text>
					<text class="action-btn confirm" @click="confirm">确定</text>
				</view>
			</view>
			
			<view class="date-selector" @touchmove.stop @wheel.stop>
				<!-- 年份选择 -->
				<view class="selector-column">
					<text class="column-title">年</text>
					<view
						class="scroll-list"
						@touchstart="onTouchStart"
						@touchmove.stop="onTouchMove"
						@touchend="onTouchEnd"
						@wheel.stop="onWheel"
						ref="yearScroll"
					>
						<view
							v-for="year in years"
							:key="year"
							:id="'year-' + year"
							class="scroll-item"
							:class="{ active: year === selectedYear }"
							@click="selectYear(year)"
						>
							{{ year }}
						</view>
					</view>
				</view>

				<!-- 月份选择 -->
				<view class="selector-column">
					<text class="column-title">月</text>
					<view
						class="scroll-list"
						@touchstart="onTouchStart"
						@touchmove.stop="onTouchMove"
						@touchend="onTouchEnd"
						@wheel.stop="onWheel"
						ref="monthScroll"
					>
						<view
							v-for="month in months"
							:key="month"
							:id="'month-' + month"
							class="scroll-item"
							:class="{ active: month === selectedMonth }"
							@click="selectMonth(month)"
						>
							{{ month }}月
						</view>
					</view>
				</view>

				<!-- 日期选择 -->
				<view class="selector-column">
					<text class="column-title">日</text>
					<view
						class="scroll-list"
						@touchstart="onTouchStart"
						@touchmove.stop="onTouchMove"
						@touchend="onTouchEnd"
						@wheel.stop="onWheel"
						ref="dayScroll"
					>
						<view
							v-for="day in days"
							:key="day"
							:id="'day-' + day"
							class="scroll-item"
							:class="{ active: day === selectedDay }"
							@click="selectDay(day)"
						>
							{{ day }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedYear: new Date().getFullYear(),
			selectedMonth: new Date().getMonth() + 1,
			selectedDay: new Date().getDate(),
			years: [],
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
			touchStartY: 0,
			scrollTop: 0,
			isScrolling: false
		}
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			return Array.from({ length: daysInMonth }, (_, i) => i + 1);
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [year, month, day] = newVal.split('-');
					this.selectedYear = parseInt(year);
					this.selectedMonth = parseInt(month);
					this.selectedDay = parseInt(day);
				}
			},
			immediate: true
		},
		selectedMonth() {
			// 当月份改变时，检查日期是否有效
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
			}
		}
	},
	created() {
		this.initYears();
	},
	methods: {
		initYears() {
			const currentYear = new Date().getFullYear();
			for (let i = 1900; i <= currentYear; i++) {
				this.years.push(i);
			}
		},
		selectYear(year) {
			this.selectedYear = year;
		},
		selectMonth(month) {
			this.selectedMonth = month;
		},
		selectDay(day) {
			this.selectedDay = day;
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const year = this.selectedYear;
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateStr = `${year}-${month}-${day}`;
			this.$emit('confirm', dateStr);
		},
		onTouchStart(e) {
			this.touchStartY = e.touches[0].clientY;
			this.isScrolling = true;
		},
		onTouchMove(e) {
			if (!this.isScrolling) return;
			e.preventDefault();
			e.stopPropagation();

			const deltaY = this.touchStartY - e.touches[0].clientY;
			const target = e.currentTarget;
			target.scrollTop += deltaY;
			this.touchStartY = e.touches[0].clientY;
		},
		onTouchEnd() {
			this.isScrolling = false;
		},
		onWheel(e) {
			e.preventDefault();
			e.stopPropagation();

			const target = e.currentTarget;
			target.scrollTop += e.deltaY;
		}
	}
}
</script>

<style scoped>
.custom-date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: flex-end;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	width: 100%;
	max-width: 600rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #FFF8E7, #F0E68C);
	border-radius: 30rpx 30rpx 0 0;
	overflow: hidden;
	animation: slideUp 0.3s ease;
	box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.2);
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: linear-gradient(135deg, #FFD700, #FFA500);
	color: #8B4513;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
}

.header-actions {
	display: flex;
	gap: 30rpx;
}

.action-btn {
	font-size: 28rpx;
	padding: 8rpx 16rpx;
	border-radius: 6rpx;
	cursor: pointer;
}

.cancel {
	color: rgba(139, 69, 19, 0.7);
}

.confirm {
	background: rgba(139, 69, 19, 0.1);
	color: #8B4513;
	font-weight: bold;
}

.date-selector {
	display: flex;
	height: 400rpx;
	padding: 20rpx 15rpx;
	overflow: hidden;
}

.selector-column {
	flex: 1;
	display: flex;
	flex-direction: column;
	margin: 0 5rpx;
}

.column-title {
	text-align: center;
	font-size: 24rpx;
	color: #8B4513;
	font-weight: bold;
	margin-bottom: 15rpx;
	background: rgba(255, 215, 0, 0.3);
	padding: 8rpx;
	border-radius: 8rpx;
}

.scroll-list {
	flex: 1;
	height: 320rpx;
	overflow-y: auto;
	overflow-x: hidden;
	scroll-behavior: smooth;
}

.scroll-list::-webkit-scrollbar {
	width: 6rpx;
}

.scroll-list::-webkit-scrollbar-track {
	background: rgba(255, 215, 0, 0.2);
	border-radius: 3rpx;
}

.scroll-list::-webkit-scrollbar-thumb {
	background: #FFD700;
	border-radius: 3rpx;
}

.scroll-item {
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #8B4513;
	margin: 3rpx 8rpx;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.2s ease;
}

.scroll-item:hover {
	background: rgba(255, 215, 0, 0.3);
	transform: scale(1.02);
}

.scroll-item.active {
	background: linear-gradient(135deg, #FFD700, #FFA500);
	color: #8B4513;
	font-weight: bold;
	transform: scale(1.05);
	box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.4);
}
</style>
