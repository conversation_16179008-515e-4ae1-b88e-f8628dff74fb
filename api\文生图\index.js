/**
 * 文生图功能统一入口文件
 * 整合文生图功能的所有接口和业务逻辑
 * 创建时间：2025-01-11
 */

import { 执行文生图工作流, 查询文生图状态, 取消文生图工作流 } from './工作流执行接口.js';
import { 文生图工作流配置, 文生图参数验证规则, 文生图错误码, 文生图状态 } from './工作流配置.js';

// 文生图功能统一API
export const 文生图功能API = {
    执行文生图工作流,
    查询文生图状态,
    取消文生图工作流,
    
    配置: 文生图工作流配置,
    验证规则: 文生图参数验证规则,
    错误码: 文生图错误码,
    状态码: 文生图状态
};

// 快捷方法
export async function 文生图(formData, options = {}) {
    return await 执行文生图工作流(formData, options);
}

export default 文生图功能API;