<template>
	<view class="lyrics-service-container">
		<!-- 歌词生成服务组件 -->
		<view class="service-header">
			<text class="service-title">🎵 AI歌词创作助手</text>
			<text class="service-subtitle">让创意变成动听的歌词</text>
		</view>

		<!-- 免费试用区域 -->
		<view class="free-trial-section" v-if="!userPaid">
			<view class="trial-header">
				<text class="trial-title">🎁 免费体验</text>
				<text class="trial-count">剩余 {{ freeTrialCount }}/{{ maxFreeTrials }} 次</text>
			</view>
			
			<view class="trial-limitations">
				<view class="limitation-item">
					<text class="limitation-icon">📝</text>
					<text class="limitation-text">每次最多生成2段歌词</text>
				</view>
				<view class="limitation-item">
					<text class="limitation-icon">⏱️</text>
					<text class="limitation-text">歌词预览30秒后模糊</text>
				</view>
				<view class="limitation-item">
					<text class="limitation-icon">🔒</text>
					<text class="limitation-text">需生成音乐才能完整查看</text>
				</view>
			</view>
		</view>

		<!-- 付费解锁区域 -->
		<view class="premium-section" v-if="showPremiumPrompt">
			<view class="premium-header">
				<text class="premium-title">🌟 解锁完整功能</text>
				<text class="premium-price">仅需 ¥{{ premiumPrice }}</text>
			</view>
			
			<view class="premium-benefits">
				<view class="benefit-item">
					<text class="benefit-icon">✨</text>
					<text class="benefit-text">无限次歌词生成</text>
				</view>
				<view class="benefit-item">
					<text class="benefit-icon">📖</text>
					<text class="benefit-text">完整歌词立即查看</text>
				</view>
				<view class="benefit-item">
					<text class="benefit-icon">🎨</text>
					<text class="benefit-text">多种风格模板</text>
				</view>
				<view class="benefit-item">
					<text class="benefit-icon">💾</text>
					<text class="benefit-text">歌词库永久保存</text>
				</view>
			</view>
			
			<button class="premium-btn" @tap="purchasePremium">
				立即解锁 ¥{{ premiumPrice }}
			</button>
		</view>

		<!-- 歌词生成表单 -->
		<view class="lyrics-form">
			<view class="form-item">
				<text class="form-label">歌曲主题</text>
				<input 
					class="form-input" 
					v-model="lyricsForm.theme"
					placeholder="例如：爱情、友情、青春、梦想..."
					maxlength="50"
				/>
			</view>
			
			<view class="form-item">
				<text class="form-label">情感基调</text>
				<view class="mood-options">
					<view 
						v-for="mood in moodOptions"
						:key="mood.value"
						class="mood-option"
						:class="{ 'selected': lyricsForm.mood === mood.value }"
						@tap="selectMood(mood.value)"
					>
						<text class="mood-emoji">{{ mood.emoji }}</text>
						<text class="mood-name">{{ mood.name }}</text>
					</view>
				</view>
			</view>
			
			<view class="form-item">
				<text class="form-label">歌词风格</text>
				<picker 
					:range="styleOptions" 
					:value="selectedStyleIndex"
					@change="onStyleChange"
				>
					<view class="picker-display">
						{{ styleOptions[selectedStyleIndex] || '请选择风格' }}
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<button 
				class="generate-btn" 
				@tap="generateLyrics"
				:disabled="!canGenerate"
			>
				{{ generateButtonText }}
			</button>
		</view>

		<!-- 生成结果 -->
		<view class="lyrics-result" v-if="generatedLyrics">
			<view class="result-header">
				<text class="result-title">🎼 生成的歌词</text>
				<view class="result-actions">
					<button class="action-btn copy-btn" @tap="copyLyrics">复制</button>
					<button class="action-btn use-btn" @tap="useInMusic">用于创作</button>
				</view>
			</view>
			
			<!-- 歌词内容 -->
			<view class="lyrics-content" :class="{ 'blurred': isBlurred }">
				<text class="lyrics-text">{{ displayLyrics }}</text>
			</view>
			
			<!-- 模糊遮罩 -->
			<view class="blur-overlay" v-if="isBlurred">
				<view class="blur-message">
					<text class="blur-title">🔒 完整歌词已生成</text>
					<text class="blur-subtitle">{{ blurMessage }}</text>
					<button class="unlock-btn" @tap="handleUnlock">
						{{ unlockButtonText }}
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LyricsGenerationService',
	
	data() {
		return {
			// 用户状态
			userPaid: false,
			freeTrialCount: 3,
			maxFreeTrials: 3,
			
			// 付费相关
			premiumPrice: 9.9,
			showPremiumPrompt: false,
			
			// 表单数据
			lyricsForm: {
				theme: '',
				mood: '',
				style: ''
			},
			
			// 选项数据
			moodOptions: [
				{ value: 'happy', name: '欢快', emoji: '😊' },
				{ value: 'sad', name: '忧伤', emoji: '😢' },
				{ value: 'romantic', name: '浪漫', emoji: '💕' },
				{ value: 'energetic', name: '激昂', emoji: '🔥' },
				{ value: 'peaceful', name: '平静', emoji: '🌙' }
			],
			
			styleOptions: ['流行', '民谣', '摇滚', '说唱', '古风', '英文'],
			selectedStyleIndex: 0,
			
			// 生成结果
			generatedLyrics: '',
			fullLyrics: '',
			isBlurred: false,
			blurTimer: null,
			
			// 状态控制
			isGenerating: false
		}
	},
	
	computed: {
		canGenerate() {
			return this.lyricsForm.theme.trim().length > 0 && 
				   this.lyricsForm.mood && 
				   !this.isGenerating &&
				   (this.userPaid || this.freeTrialCount > 0);
		},
		
		generateButtonText() {
			if (this.isGenerating) return '生成中...';
			if (!this.userPaid && this.freeTrialCount === 0) return '试用次数已用完';
			return this.userPaid ? '生成歌词' : `免费生成 (${this.freeTrialCount}/${this.maxFreeTrials})`;
		},
		
		displayLyrics() {
			if (this.userPaid || !this.isBlurred) {
				return this.fullLyrics;
			}
			// 免费用户显示部分歌词
			return this.fullLyrics.split('\n').slice(0, 4).join('\n') + '\n...';
		},
		
		blurMessage() {
			if (this.userPaid) return '';
			return '升级到高级版本查看完整歌词，或直接用于音乐创作';
		},
		
		unlockButtonText() {
			return this.userPaid ? '查看完整' : '升级解锁 ¥' + this.premiumPrice;
		}
	},
	
	onLoad() {
		this.checkUserStatus();
	},
	
	methods: {
		// 检查用户状态
		checkUserStatus() {
			const userInfo = uni.getStorageSync('userInfo') || {};
			const lyricsService = uni.getStorageSync('lyricsService') || {};
			
			this.userPaid = lyricsService.isPremium || false;
			this.freeTrialCount = lyricsService.freeTrialCount || this.maxFreeTrials;
		},
		
		// 选择情感
		selectMood(mood) {
			this.lyricsForm.mood = mood;
		},
		
		// 选择风格
		onStyleChange(e) {
			this.selectedStyleIndex = e.detail.value;
			this.lyricsForm.style = this.styleOptions[e.detail.value];
		},
		
		// 生成歌词
		async generateLyrics() {
			if (!this.canGenerate) return;
			
			this.isGenerating = true;
			
			try {
				// 模拟API调用
				const result = await this.callLyricsAPI();
				
				this.fullLyrics = result.lyrics;
				this.generatedLyrics = result.lyrics;
				
				// 免费用户处理
				if (!this.userPaid) {
					this.freeTrialCount--;
					this.saveFreeTrialCount();
					
					// 30秒后模糊
					this.startBlurTimer();
					
					if (this.freeTrialCount === 0) {
						this.showPremiumPrompt = true;
					}
				}
				
			} catch (error) {
				uni.showToast({
					title: '生成失败，请重试',
					icon: 'error'
				});
			} finally {
				this.isGenerating = false;
			}
		},
		
		// 模拟歌词生成API
		async callLyricsAPI() {
			return new Promise((resolve) => {
				setTimeout(() => {
					const sampleLyrics = `第一段：
在这个${this.lyricsForm.theme}的季节里
心情如${this.getMoodDescription()}般美好
每一个瞬间都值得珍藏
让我们一起歌唱

副歌：
${this.lyricsForm.theme}的故事永远不会结束
就像这首歌一样动人
在${this.lyricsForm.style}的旋律中
找到属于我们的回忆

第二段：
时光荏苒岁月如歌
${this.lyricsForm.theme}依然在心中闪烁
无论走到哪里都不会忘记
这份美好的感动

副歌：
${this.lyricsForm.theme}的故事永远不会结束
就像这首歌一样动人
在${this.lyricsForm.style}的旋律中
找到属于我们的回忆`;
					
					resolve({ lyrics: sampleLyrics });
				}, 2000);
			});
		},
		
		// 获取情感描述
		getMoodDescription() {
			const moodMap = {
				'happy': '阳光',
				'sad': '细雨',
				'romantic': '花香',
				'energetic': '火焰',
				'peaceful': '月光'
			};
			return moodMap[this.lyricsForm.mood] || '美好';
		},
		
		// 开始模糊计时器
		startBlurTimer() {
			if (this.userPaid) return;
			
			this.blurTimer = setTimeout(() => {
				this.isBlurred = true;
				uni.showToast({
					title: '预览时间结束',
					icon: 'none'
				});
			}, 30000); // 30秒
		},
		
		// 处理解锁
		handleUnlock() {
			if (this.userPaid) {
				this.isBlurred = false;
			} else {
				this.purchasePremium();
			}
		},
		
		// 购买高级版
		async purchasePremium() {
			uni.showModal({
				title: '升级高级版',
				content: `升级到高级版本，享受无限歌词生成服务，仅需¥${this.premiumPrice}`,
				success: (res) => {
					if (res.confirm) {
						this.processPremiumPurchase();
					}
				}
			});
		},
		
		// 处理高级版购买
		async processPremiumPurchase() {
			uni.showLoading({ title: '处理中...' });
			
			try {
				// 模拟支付
				await new Promise(resolve => setTimeout(resolve, 2000));
				
				this.userPaid = true;
				this.isBlurred = false;
				this.showPremiumPrompt = false;
				
				// 保存状态
				uni.setStorageSync('lyricsService', {
					isPremium: true,
					purchaseTime: Date.now()
				});
				
				uni.showToast({
					title: '升级成功！',
					icon: 'success'
				});
				
			} catch (error) {
				uni.showToast({
					title: '支付失败',
					icon: 'error'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 保存试用次数
		saveFreeTrialCount() {
			const lyricsService = uni.getStorageSync('lyricsService') || {};
			lyricsService.freeTrialCount = this.freeTrialCount;
			uni.setStorageSync('lyricsService', lyricsService);
		},
		
		// 复制歌词
		copyLyrics() {
			if (this.isBlurred && !this.userPaid) {
				uni.showToast({
					title: '请先解锁完整歌词',
					icon: 'none'
				});
				return;
			}
			
			uni.setClipboardData({
				data: this.fullLyrics,
				success: () => {
					uni.showToast({
						title: '已复制到剪贴板',
						icon: 'success'
					});
				}
			});
		},
		
		// 用于音乐创作
		useInMusic() {
			// 跳转到音乐创作页面并传递歌词
			uni.navigateTo({
				url: `/pages/create/music/index?lyrics=${encodeURIComponent(this.fullLyrics)}`
			});
		}
	},
	
	onUnload() {
		if (this.blurTimer) {
			clearTimeout(this.blurTimer);
		}
	}
}
</script>

<style lang="scss">
.lyrics-service-container {
	padding: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}

.service-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.service-title {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 10rpx;
	}
	
	.service-subtitle {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
}

.free-trial-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	
	.trial-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.trial-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.trial-count {
			font-size: 24rpx;
			color: #666;
			background: #f0f0f0;
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
		}
	}
	
	.trial-limitations {
		.limitation-item {
			display: flex;
			align-items: center;
			margin-bottom: 15rpx;
			
			.limitation-icon {
				font-size: 32rpx;
				margin-right: 15rpx;
			}
			
			.limitation-text {
				font-size: 26rpx;
				color: #666;
			}
		}
	}
}

.premium-section {
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	
	.premium-header {
		text-align: center;
		margin-bottom: 25rpx;
		
		.premium-title {
			display: block;
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 10rpx;
		}
		
		.premium-price {
			font-size: 48rpx;
			font-weight: bold;
			color: #e74c3c;
		}
	}
	
	.premium-benefits {
		margin-bottom: 30rpx;
		
		.benefit-item {
			display: flex;
			align-items: center;
			margin-bottom: 15rpx;
			
			.benefit-icon {
				font-size: 32rpx;
				margin-right: 15rpx;
			}
			
			.benefit-text {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}
	
	.premium-btn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
		color: white;
		border: none;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
	}
}

.lyrics-form {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	
	.form-item {
		margin-bottom: 30rpx;
		
		.form-label {
			display: block;
			font-size: 28rpx;
			color: #333;
			margin-bottom: 15rpx;
			font-weight: 500;
		}
		
		.form-input {
			width: 100%;
			height: 80rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 12rpx;
			padding: 0 20rpx;
			font-size: 28rpx;
		}
	}
	
	.mood-options {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
		
		.mood-option {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 20rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 12rpx;
			cursor: pointer;
			transition: all 0.3s;
			
			&.selected {
				border-color: #667eea;
				background: #f0f4ff;
			}
			
			.mood-emoji {
				font-size: 40rpx;
				margin-bottom: 8rpx;
			}
			
			.mood-name {
				font-size: 24rpx;
				color: #666;
			}
		}
	}
	
	.picker-display {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		
		.picker-arrow {
			color: #999;
		}
	}
	
	.generate-btn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border: none;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
		
		&[disabled] {
			background: #ccc;
			color: #999;
		}
	}
}

.lyrics-result {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	position: relative;
	
	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.result-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.result-actions {
			display: flex;
			gap: 10rpx;
			
			.action-btn {
				padding: 12rpx 24rpx;
				border: none;
				border-radius: 20rpx;
				font-size: 24rpx;
				
				&.copy-btn {
					background: #f0f0f0;
					color: #666;
				}
				
				&.use-btn {
					background: #667eea;
					color: white;
				}
			}
		}
	}
	
	.lyrics-content {
		position: relative;
		
		&.blurred {
			filter: blur(3px);
			pointer-events: none;
		}
		
		.lyrics-text {
			font-size: 28rpx;
			line-height: 1.8;
			color: #333;
			white-space: pre-line;
		}
	}
	
	.blur-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		
		.blur-message {
			text-align: center;
			
			.blur-title {
				display: block;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.blur-subtitle {
				display: block;
				font-size: 24rpx;
				color: #666;
				margin-bottom: 20rpx;
			}
			
			.unlock-btn {
				padding: 15rpx 30rpx;
				background: #667eea;
				color: white;
				border: none;
				border-radius: 25rpx;
				font-size: 28rpx;
			}
		}
	}
}
</style>
