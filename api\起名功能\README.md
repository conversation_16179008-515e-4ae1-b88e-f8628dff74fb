# 起名功能 API 文档

## 📋 概述

起名功能是基于传统易经学文化和现代AI技术的智能起名系统，为用户提供个性化的姓名推荐服务。

## 🏗️ 架构设计

### 文件结构
```
api/起名功能/
├── index.js                 # 统一入口文件
├── 工作流配置.js             # 工作流配置和参数定义
├── 工作流执行接口.js         # 工作流执行相关接口
├── 业务逻辑接口.js           # 业务逻辑处理接口
└── README.md                # 文档说明
```

### 核心组件

1. **工作流基础类** (`WorkflowBase`)
   - 提供统一的工作流对接机制
   - 支持参数验证、状态轮询、错误处理

2. **起名工作流类** (`NameGeneratorWorkflow`)
   - 继承工作流基础类
   - 实现起名特定的业务逻辑

3. **业务逻辑类** (`NameGeneratorBusiness`)
   - 处理费用计算、用户验证
   - 管理完整的业务流程

## 🚀 快速开始

### 基础用法

```javascript
import { 智能起名 } from '@/api/起名功能/index.js';

// 基础起名
const result = await 智能起名({
    surname: '张',
    gender: 'male',
    birthDate: '2024-01-01',
    birthTime: '08:30',
    nameLength: 2,
    nameStyle: 'traditional'
});
```

### 高级用法

```javascript
import { 高级起名, 预览起名费用 } from '@/api/起名功能/index.js';

// 预览费用
const costInfo = await 预览起名费用(formData);

// 高级起名
const result = await 高级起名({
    surname: '李',
    gender: 'female',
    birthDate: '2024-01-01',
    birthTime: '14:20',
    nameLength: 3,
    nameStyle: 'poetic',
    expectedMeaning: '聪明智慧',
    generateCount: 12
});
```

## 📝 API 接口

### 核心接口

#### `智能起名(formData, options)`
主要的起名接口，包含完整的业务流程。

**参数：**
- `formData` (Object): 表单数据
  - `surname` (string): 姓氏 *必需*
  - `gender` (string): 性别 ('male'|'female') *必需*
  - `birthDate` (string): 出生日期 (YYYY-MM-DD) *必需*
  - `birthTime` (string): 出生时间 (HH:MM) *必需*
  - `nameLength` (number): 名字长度 (2|3)
  - `nameStyle` (string): 起名风格
  - `expectedMeaning` (string): 期望寓意
  - `generateCount` (number): 生成数量

**返回：**
```javascript
{
    success: true,
    data: {
        recommendations: [
            {
                givenName: "子轩",
                fullName: "张子轩",
                score: 95,
                meaning: "品德高尚，气宇轩昂",
                wuxing: "水土",
                strokes: "11+3+10"
            }
        ],
        wuxingAnalysis: {
            elements: { 金: 30, 木: 40, 水: 50, 火: 20, 土: 35 },
            weakest: "火",
            strongest: "水"
        },
        costInfo: {
            totalCost: 10,
            transactionId: "tx_123456"
        }
    }
}
```

#### `快速起名(basicInfo)`
使用默认配置的快速起名。

#### `预览起名费用(formData)`
计算起名费用，不执行实际起名。

#### `我的起名记录(params)`
获取用户的起名历史记录。

### 辅助接口

#### `验证起名表单(formData)`
验证表单数据的完整性和格式。

#### `格式化起名结果(result)`
格式化起名结果，便于前端显示。

## ⚙️ 配置说明

### 工作流配置

```javascript
export const 起名工作流配置 = {
    workflowId: 'name_generator_workflow_001',
    workflowType: 'name_generation',
    
    // 结构化参数定义
    structuredParams: {
        surname: {
            type: 'text',
            required: true,
            placeholder: '{{surname}}'
        }
        // ... 其他参数
    },
    
    // 费用配置
    pricing: {
        basePrice: 10,
        pricePerName: 1,
        memberDiscount: 0.8
    }
};
```

### 参数验证规则

```javascript
export const 起名参数验证规则 = {
    required: ['surname', 'gender', 'birthDate', 'birthTime'],
    formats: {
        birthDate: /^\d{4}-\d{2}-\d{2}$/,
        birthTime: /^\d{2}:\d{2}$/
    },
    enums: {
        gender: ['male', 'female'],
        nameStyle: ['traditional', 'modern', 'poetic']
    }
};
```

## 🔧 工作流对接

### 结构化参数格式

发送给后端工作流的参数格式：

```javascript
{
    requestId: "name_1641234567890_abc123",
    userId: "user_123",
    timestamp: 1641234567890,
    moduleName: "起名功能",
    workflowId: "name_generator_workflow_001",
    workflowType: "name_generation",
    structuredParams: {
        surname: {
            type: "text",
            value: "张",
            placeholder: "{{surname}}"
        },
        gender: {
            type: "text", 
            value: "male",
            placeholder: "{{gender}}"
        }
        // ... 其他参数
    }
}
```

### 提示词模板

```
请基于以下信息为用户起名：

基础信息：
- 姓氏：{{surname}}
- 性别：{{gender}}
- 出生日期：{{birthDate}}
- 出生时间：{{birthTime}}

起名要求：
- 名字长度：{{nameLength}}字
- 起名风格：{{nameStyle}}
- 期望寓意：{{expectedMeaning}}

请生成符合要求的名字推荐...
```

## 🚨 错误处理

### 错误码定义

```javascript
export const 起名错误码 = {
    INVALID_SURNAME: { code: 'NAME_001', message: '姓氏格式不正确' },
    INVALID_GENDER: { code: 'NAME_002', message: '性别参数无效' },
    INSUFFICIENT_COINS: { code: 'NAME_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'NAME_008', message: '起名工作流执行超时' }
};
```

### 错误处理示例

```javascript
try {
    const result = await 智能起名(formData);
} catch (error) {
    if (error.code === 'NAME_007') {
        // 处理余额不足
        uni.showModal({
            title: '余额不足',
            content: '请先充值后再使用起名功能'
        });
    } else {
        // 处理其他错误
        uni.showToast({
            title: error.message || '起名失败',
            icon: 'none'
        });
    }
}
```

## 💰 费用计算

### 计费规则

1. **基础费用**: 10金币（包含8个名字）
2. **额外名字**: 每个额外名字1金币
3. **会员折扣**: 会员享受8折优惠

### 费用计算示例

```javascript
// 普通用户生成12个名字
// 基础费用: 10金币
// 额外费用: (12-8) * 1 = 4金币
// 总费用: 14金币

// 会员用户生成12个名字
// 总费用: 14 * 0.8 = 12金币（向上取整）
```

## 🔄 状态管理

### 工作流状态

- `pending`: 等待中
- `processing`: 处理中  
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消
- `timeout`: 超时

### 状态轮询

系统会自动轮询工作流状态，直到完成或超时。

## 📊 使用统计

### 统计信息

```javascript
const stats = await 获取起名统计信息();
// 返回：
{
    totalCount: 15,        // 总起名次数
    favoriteCount: 3,      // 收藏名字数量
    totalSpent: 150,       // 总花费金币
    lastUsed: "2024-01-01" // 最后使用时间
}
```

## 🔗 相关链接

- [工作流基础类文档](../common/workflow-base.js)
- [支付系统接口](../支付系统/)
- [用户系统接口](../用户系统/)

## 📞 技术支持

如有问题，请联系开发团队或查看相关文档。
