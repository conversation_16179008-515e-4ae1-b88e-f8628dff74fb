<template>
  <view class="preset-prompts-container">
    <view class="section-title">
      <text class="title-text">推荐提示词</text>
      <text class="see-more" @tap="showAllPresets" v-if="!showAll && presets.length > visibleCount">查看更多</text>
    </view>
    
    <view class="prompts-scrollview" :style="{ height: showAll ? 'auto' : presets.length <= visibleCount ? 'auto' : '240rpx' }">
      <view class="prompts-grid">
        <view 
          class="prompt-item" 
          v-for="(item, index) in showAll ? presets : presets.slice(0, visibleCount)" 
          :key="index"
          @tap="selectPreset(item)"
          :class="{ 'active': selectedPreset && selectedPreset.id === item.id }"
        >
          <text class="prompt-text">{{ item.title }}</text>
        </view>
      </view>
    </view>
    
    <view class="collapse-btn" @tap="toggleCollapse" v-if="showAll && presets.length > visibleCount">
      <text>收起</text>
      <image src="/static/icons/arrow-up.png" mode="aspectFit"></image>
    </view>
    
    <!-- 预设详情弹窗 -->
    <view class="preset-detail-modal" v-if="showDetail">
      <view class="modal-mask" @tap="closeDetail"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{ currentPreset.title }}</text>
          <view class="close-btn" @tap="closeDetail">
            <image src="/static/icons/close.png" mode="aspectFit"></image>
          </view>
        </view>
        
        <scroll-view class="modal-body" scroll-y>
          <view class="preset-description">{{ currentPreset.description }}</view>
          <view class="preset-prompt-content">{{ currentPreset.prompt }}</view>
          
          <!-- 预览图（仅图片和视频类型有） -->
          <view class="preset-preview" v-if="['image', 'video'].includes(type) && currentPreset.previewUrl">
            <image :src="currentPreset.previewUrl" mode="aspectFill"></image>
          </view>
        </scroll-view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @tap="closeDetail">取消</button>
          <button class="use-btn" @tap="usePreset">使用此提示词</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PresetPrompts',
  props: {
    type: {
      type: String,
      default: 'text', // 'text', 'image', 'music', 'video'
      validator: value => ['text', 'image', 'music', 'video'].includes(value)
    },
    presets: {
      type: Array,
      default: () => []
    },
    visibleCount: {
      type: Number,
      default: 8
    }
  },
  data() {
    return {
      showAll: false,
      selectedPreset: null,
      showDetail: false,
      currentPreset: null
    }
  },
  created() {
    // 如果没有提供预设，则根据类型加载默认预设
    if (this.presets.length === 0) {
      this.loadDefaultPresets()
    }
  },
  methods: {
    loadDefaultPresets() {
      // 这里可以根据不同类型加载默认的预设提示词
      // 实际项目中可能会从API获取
      const defaultPresets = {
        text: [
          { id: 't1', title: '创意故事', prompt: '写一个关于未来科技的短篇故事', description: '根据提示生成一个有创意的短篇故事' },
          { id: 't2', title: '商业文案', prompt: '编写一个推广新产品的商业广告文案', description: '生成专业的商业推广文案' },
          { id: 't3', title: '诗歌创作', prompt: '以春天为主题创作一首现代诗', description: '创作富有意境的现代诗歌' },
          { id: 't4', title: '技术文档', prompt: '编写一个关于如何使用React Hooks的教程', description: '生成专业的技术文档或教程' },
          { id: 't5', title: '剧本对话', prompt: '创作一段两个陌生人在咖啡馆相遇的对话', description: '生成生动有趣的人物对话' },
          { id: 't6', title: '产品描述', prompt: '为一款智能手表编写产品特点和卖点', description: '突出产品优势的详细描述' }
        ],
        image: [
          { id: 'i1', title: '科幻场景', prompt: '未来城市，飞行汽车，高科技建筑，8K超高清', description: '生成未来科幻风格的城市场景', previewUrl: '/static/presets/image-scifi.jpg' },
          { id: 'i2', title: '自然风光', prompt: '宁静的湖泊，雪山倒影，黄昏，照片风格', description: '生成写实风格的自然风景', previewUrl: '/static/presets/image-nature.jpg' },
          { id: 'i3', title: '角色设计', prompt: '年轻女战士，银色铠甲，魔法剑，幻想风格插图', description: '生成幻想风格的角色设计', previewUrl: '/static/presets/image-character.jpg' },
          { id: 'i4', title: '插画风格', prompt: '可爱的小猫咪，卡通风格，柔和色彩，儿童插画', description: '生成卡通风格的插画', previewUrl: '/static/presets/image-cartoon.jpg' },
          { id: 'i5', title: '写实人像', prompt: '专业商务人士，办公环境，自然光线，摄影风格', description: '生成写实风格的人像', previewUrl: '/static/presets/image-portrait.jpg' },
          { id: 'i6', title: '概念艺术', prompt: '废弃的宇宙飞船，外星风景，概念设计，电影场景', description: '生成科幻概念艺术作品', previewUrl: '/static/presets/image-concept.jpg' }
        ],
        music: [
          { id: 'm1', title: '轻松流行', prompt: '创作一首轻松愉快的流行歌曲，钢琴和吉他伴奏', description: '生成轻松愉快的流行风格音乐' },
          { id: 'm2', title: '史诗配乐', prompt: '创作一首宏大的电影配乐，适合冒险场景', description: '生成电影级的史诗配乐' },
          { id: 'm3', title: '舒缓冥想', prompt: '创作一首舒缓的环境音乐，适合冥想和放松', description: '生成舒缓放松的冥想音乐' },
          { id: 'm4', title: '电子舞曲', prompt: '创作一首现代电子舞曲，强劲的节奏和合成器声音', description: '生成节奏感强的电子舞曲' },
          { id: 'm5', title: '古典钢琴', prompt: '创作一首浪漫主义风格的钢琴独奏曲', description: '生成古典风格的钢琴曲' },
          { id: 'm6', title: '爵士即兴', prompt: '创作一段爵士四重奏的即兴演奏，以萨克斯为主导', description: '生成爵士风格的音乐' }
        ],
        video: [
          { id: 'v1', title: '自然风光', prompt: '海浪拍打海岸，阳光照射，鸟瞰视角，慢动作', description: '生成自然风光的高质量视频', previewUrl: '/static/presets/video-nature.jpg' },
          { id: 'v2', title: '科技展示', prompt: '未来智能手机展示，特写镜头，工作室灯光', description: '生成专业的产品展示视频', previewUrl: '/static/presets/video-tech.jpg' },
          { id: 'v3', title: '抽象动画', prompt: '彩色流体动画，抽象形状变形，平滑过渡', description: '生成抽象艺术风格的动画', previewUrl: '/static/presets/video-abstract.jpg' },
          { id: 'v4', title: '城市延时', prompt: '繁忙城市的延时摄影，从白天到黑夜，车流灯光', description: '生成城市延时摄影效果的视频', previewUrl: '/static/presets/video-city.jpg' },
          { id: 'v5', title: '角色动画', prompt: '3D卡通角色行走循环动画，公园背景', description: '生成3D角色动画', previewUrl: '/static/presets/video-character.jpg' },
          { id: 'v6', title: '营销广告', prompt: '15秒产品广告，动态文字，明亮色彩，快节奏剪辑', description: '生成适合社交媒体的短视频广告', previewUrl: '/static/presets/video-ad.jpg' }
        ]
      }
      
      this.$emit('update:presets', defaultPresets[this.type] || [])
    },
    
    showAllPresets() {
      this.showAll = true
    },
    
    toggleCollapse() {
      this.showAll = !this.showAll
    },
    
    selectPreset(preset) {
      this.selectedPreset = preset
      this.currentPreset = preset
      this.showDetail = true
    },
    
    closeDetail() {
      this.showDetail = false
      this.currentPreset = null
    },
    
    usePreset() {
      this.$emit('select', this.currentPreset)
      this.showDetail = false
    }
  }
}
</script>

<style lang="scss" scoped>
.preset-prompts-container {
  width: 100%;
  margin: 20rpx 0;
  
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .title-text {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
    
    .see-more {
      font-size: 26rpx;
      color: #6e7fff;
    }
  }
  
  .prompts-scrollview {
    width: 100%;
    transition: height 0.3s ease;
    
    .prompts-grid {
      display: flex;
      flex-wrap: wrap;
      
      .prompt-item {
        width: calc((100% - 60rpx) / 4);
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        border-radius: 12rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        
        &:nth-child(4n) {
          margin-right: 0;
        }
        
        .prompt-text {
          font-size: 24rpx;
          color: #666;
          text-align: center;
          padding: 0 10rpx;
        }
        
        &.active {
          background: linear-gradient(135deg, #6e7fff, #4e5af4);
          
          .prompt-text {
            color: #fff;
          }
        }
      }
    }
  }
  
  .collapse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    
    text {
      font-size: 26rpx;
      color: #666;
      margin-right: 10rpx;
    }
    
    image {
      width: 24rpx;
      height: 24rpx;
    }
  }
  
  .preset-detail-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    
    .modal-mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.6);
    }
    
    .modal-content {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: #fff;
      border-radius: 24rpx 24rpx 0 0;
      padding: 30rpx;
      box-sizing: border-box;
      max-height: 80vh;
      display: flex;
      flex-direction: column;
      animation: slideUp 0.3s ease-out;
      
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30rpx;
        
        .modal-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
        
        .close-btn {
          width: 40rpx;
          height: 40rpx;
          
          image {
            width: 100%;
            height: 100%;
          }
        }
      }
      
      .modal-body {
        flex: 1;
        max-height: 60vh;
        
        .preset-description {
          font-size: 28rpx;
          color: #666;
          margin-bottom: 20rpx;
          line-height: 1.5;
        }
        
        .preset-prompt-content {
          font-size: 28rpx;
          color: #333;
          background-color: #f5f7fa;
          padding: 20rpx;
          border-radius: 12rpx;
          margin-bottom: 20rpx;
          line-height: 1.5;
        }
        
        .preset-preview {
          width: 100%;
          height: 400rpx;
          border-radius: 12rpx;
          overflow: hidden;
          margin-top: 20rpx;
          
          image {
            width: 100%;
            height: 100%;
          }
        }
      }
      
      .modal-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 30rpx;
        
        button {
          width: 48%;
          height: 80rpx;
          line-height: 80rpx;
          text-align: center;
          font-size: 28rpx;
          border-radius: 40rpx;
        }
        
        .cancel-btn {
          color: #666;
          background-color: #f5f7fa;
        }
        
        .use-btn {
          color: #fff;
          background: linear-gradient(135deg, #6e7fff, #4e5af4);
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style> 