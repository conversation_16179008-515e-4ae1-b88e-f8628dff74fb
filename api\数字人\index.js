/**
 * 数字人功能统一入口文件
 * 整合数字人功能的所有接口和业务逻辑
 * 创建时间：2025-01-11
 */

import { 执行数字人工作流, 查询数字人状态, 取消数字人工作流 } from './工作流执行接口.js';
import { 数字人工作流配置, 数字人参数验证规则, 数字人错误码, 数字人状态 } from './工作流配置.js';

// 数字人功能统一API
export const 数字人功能API = {
    执行数字人工作流,
    查询数字人状态,
    取消数字人工作流,
    
    配置: 数字人工作流配置,
    验证规则: 数字人参数验证规则,
    错误码: 数字人错误码,
    状态码: 数字人状态
};

// 快捷方法
export async function 数字人(formData, options = {}) {
    return await 执行数字人工作流(formData, options);
}

export default 数字人功能API;