<template>
  <view class="cursor-positioner" @click="handleContainerClick">
    <slot></slot>
    
    <!-- 光标定位辅助层 -->
    <view 
      class="cursor-overlay"
      v-if="showOverlay"
      @click="handleOverlayClick"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <!-- 光标预览指示器 -->
      <view 
        class="cursor-preview"
        v-if="showCursorPreview"
        :style="cursorPreviewStyle"
      ></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CursorPositioner',
  props: {
    textareaElement: {
      type: Object,
      default: null
    },
    value: {
      type: String,
      default: ''
    },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showOverlay: false,
      showCursorPreview: false,
      cursorPreviewPos: { x: 0, y: 0 },
      
      // 文本框信息
      textareaRect: null,
      lineHeight: 20,
      charWidth: 8,
      
      // 触摸状态
      isTouching: false,
      touchStartTime: 0,
      touchMoved: false
    };
  },
  computed: {
    cursorPreviewStyle() {
      return {
        left: this.cursorPreviewPos.x + 'px',
        top: this.cursorPreviewPos.y + 'px'
      };
    }
  },
  watch: {
    textareaElement: {
      handler() {
        this.updateTextareaInfo();
      },
      immediate: true
    }
  },
  mounted() {
    console.log('🚫 CursorPositioner已被完全禁用');
    // 完全不初始化，让原生textarea处理所有光标定位
    return;
  },
  methods: {
    // 初始化定位器
    initPositioner() {
      this.updateTextareaInfo();
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.updateTextareaInfo);
    },
    
    // 更新文本框信息
    updateTextareaInfo() {
      if (!this.textareaElement) return;
      
      try {
        this.textareaRect = this.textareaElement.getBoundingClientRect();
        
        // 计算字符尺寸
        const computedStyle = window.getComputedStyle(this.textareaElement);
        this.lineHeight = parseInt(computedStyle.lineHeight) || 20;
        
        // 估算字符宽度
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.font = computedStyle.font;
        this.charWidth = ctx.measureText('M').width || 8;
        
      } catch (error) {
        console.error('更新文本框信息失败:', error);
      }
    },
    
    // 处理容器点击（简化版本 - 不阻止原生行为）
    handleContainerClick(e) {
      if (!this.enabled || !this.textareaElement) return;

      // 如果点击的是文本框本身，完全让其正常处理
      if (e.target === this.textareaElement) {
        return;
      }

      // 不阻止事件冒泡，让原生处理
    },

    // 处理覆盖层点击（简化版本）
    handleOverlayClick(e) {
      if (!this.enabled) return;

      // 隐藏覆盖层，让用户直接点击textarea
      this.hideOverlay();

      // 不阻止默认行为，不干扰原生点击处理
    },
    
    // 处理触摸开始（简化版本 - 不阻止原生行为）
    handleTouchStart(e) {
      if (!this.enabled) return;

      this.isTouching = true;
      this.touchStartTime = Date.now();
      this.touchMoved = false;

      // 不显示预览，不干扰原生行为
      // 不阻止默认行为，让原生textarea处理触摸
    },
    
    // 处理触摸移动
    handleTouchMove(e) {
      if (!this.enabled || !this.isTouching) return;
      
      this.touchMoved = true;
      
      const position = this.getPositionFromEvent(e);
      this.cursorPreviewPos = position;
      
      e.preventDefault();
    },
    
    // 处理触摸结束（简化版本）
    handleTouchEnd(e) {
      if (!this.enabled || !this.isTouching) return;

      this.isTouching = false;
      this.showCursorPreview = false;

      // 不设置光标位置，让原生textarea处理
      // 不阻止默认行为
    },
    
    // 从事件获取相对位置
    getPositionFromEvent(e) {
      const clientX = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : 0);
      const clientY = e.clientY || (e.touches && e.touches[0] ? e.touches[0].clientY : 0);
      
      return {
        x: clientX - this.textareaRect.left,
        y: clientY - this.textareaRect.top
      };
    },
    
    // 根据位置获取文本索引（优化版本）
    getIndexFromPosition(x, y) {
      if (!this.value) return 0;

      try {
        // 尝试使用浏览器原生API获取更精确的位置
        if (this.textareaElement && document.caretPositionFromPoint) {
          const rect = this.textareaElement.getBoundingClientRect();
          const caretPos = document.caretPositionFromPoint(x + rect.left, y + rect.top);
          if (caretPos && caretPos.offsetNode) {
            return Math.min(caretPos.offset, this.value.length);
          }
        }

        // 改进的估算方法
        const style = this.textareaElement ? window.getComputedStyle(this.textareaElement) : {};
        const lineHeight = parseInt(style.lineHeight) || this.lineHeight || 20;
        const fontSize = parseInt(style.fontSize) || 14;
        const charWidth = fontSize * 0.6; // 更准确的字符宽度估算

        const lineIndex = Math.max(0, Math.floor(y / lineHeight));
        const charIndex = Math.max(0, Math.floor(x / charWidth));

        const lines = this.value.split('\n');
        let index = 0;

        // 计算到指定行的字符数
        for (let i = 0; i < lineIndex && i < lines.length; i++) {
          index += lines[i].length + 1; // +1 for newline
        }

        // 添加行内字符数
        if (lineIndex < lines.length) {
          index += Math.min(charIndex, lines[lineIndex].length);
        }

        return Math.max(0, Math.min(index, this.value.length));
      } catch (error) {
        console.error('计算文本索引失败:', error);
        return 0;
      }
    },
    
    // 设置光标位置（优化版本）
    setCursorPosition(index) {
      try {
        if (this.textareaElement && this.textareaElement.setSelectionRange) {
          // 确保索引在有效范围内
          const safeIndex = Math.max(0, Math.min(index, this.value.length));

          this.textareaElement.setSelectionRange(safeIndex, safeIndex);
          this.textareaElement.focus();

          // 触觉反馈
          if (uni.vibrateShort) {
            uni.vibrateShort();
          }

          // 显示成功提示
          uni.showToast({
            title: `光标已定位到位置 ${safeIndex}`,
            icon: 'none',
            duration: 1000
          });

          // 发射事件
          this.$emit('cursor-position-change', safeIndex);

          console.log(`光标定位到位置: ${safeIndex}`);

          // 滚动到光标位置（如果需要）
          this.scrollToCursor();
        }
      } catch (error) {
        console.error('设置光标位置失败:', error);
        uni.showToast({
          title: '光标定位失败',
          icon: 'error',
          duration: 1500
        });
      }
    },

    // 滚动到光标位置（新增方法）
    scrollToCursor() {
      try {
        if (this.textareaElement) {
          // 简单的滚动到光标位置
          this.textareaElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      } catch (error) {
        console.warn('滚动到光标位置失败:', error);
      }
    },
    
    // 显示覆盖层
    showOverlay() {
      this.showOverlay = true;
      this.updateTextareaInfo();
    },
    
    // 隐藏覆盖层
    hideOverlay() {
      this.showOverlay = false;
      this.showCursorPreview = false;
    },
    
    // 启用精确定位模式
    enablePreciseMode() {
      this.showOverlay();
      
      uni.showToast({
        title: '点击定位光标位置',
        icon: 'none',
        duration: 2000
      });
    }
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.updateTextareaInfo);
  }
}
</script>

<style scoped>
.cursor-positioner {
  position: relative;
  width: 100%;
  height: 100%;
}

.cursor-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.05);
  cursor: crosshair;
}

.cursor-preview {
  position: absolute;
  width: 3px;
  height: 24px;
  background: #007AFF;
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(0, 122, 255, 0.6);
  animation: pulse 0.5s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes pulse {
  from {
    opacity: 0.7;
    transform: scaleY(0.9);
  }
  to {
    opacity: 1;
    transform: scaleY(1.1);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .cursor-overlay {
    background: rgba(0, 0, 0, 0.02);
  }

  .cursor-preview {
    width: 4px;
    height: 28px;
    border-radius: 2px;
    box-shadow: 0 0 12px rgba(0, 122, 255, 0.8);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .cursor-overlay {
    background: rgba(0, 0, 0, 0.03);
  }

  .cursor-preview {
    width: 5px;
    height: 32px;
    border-radius: 3px;
    box-shadow: 0 0 16px rgba(0, 122, 255, 1);
  }
}
</style>
