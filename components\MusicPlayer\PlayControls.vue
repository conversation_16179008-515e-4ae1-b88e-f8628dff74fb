<template>
	<view class="play-controls" :style="themeStyles">
		<!-- 上一曲 -->
		<view class="control-btn" @click="handlePrev">
			<svg class="control-icon" viewBox="0 0 24 24" fill="currentColor">
				<path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"></path>
			</svg>
		</view>
		
		<!-- 下一曲 -->
		<view class="control-btn" @click="handleNext">
			<svg class="control-icon" viewBox="0 0 24 24" fill="currentColor">
				<path d="M16 18h2V6h-2zM6 18l8.5-6L6 6z"></path>
			</svg>
		</view>
		
		<!-- 循环模式 -->
		<view class="control-btn" :class="{ active: isRepeat }" @click="handleRepeat">
			<svg v-if="!isRepeat" class="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
				<polyline points="17 1 21 5 17 9"></polyline>
				<path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
				<polyline points="7 23 3 19 7 15"></polyline>
				<path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
			</svg>
			<svg v-else class="control-icon" viewBox="0 0 24 24" fill="currentColor">
				<path d="M17 1l4 4-4 4V6H7a4 4 0 0 0-4 4v2h2v-2a2 2 0 0 1 2-2h10V5zM7 23l-4-4 4-4v3h10a4 4 0 0 0 4-4v-2h-2v2a2 2 0 0 1-2 2H7v3z"></path>
			</svg>
			<text v-if="isRepeat" class="repeat-badge">1</text>
		</view>
	</view>
</template>

<script>
import { getTheme } from '@/utils/playerThemes.js';

export default {
	name: 'PlayControls',
	props: {
		// 是否循环播放
		isRepeat: {
			type: Boolean,
			default: false
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	computed: {
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--icon-color': theme.colors.iconColor,
				'--icon-color-active': theme.colors.iconColorActive,
				'--button-bg': theme.colors.buttonBg,
				'--button-bg-active': theme.colors.buttonBgActive
			};
		}
	},
	methods: {
		// 上一曲
		handlePrev() {
			this.$emit('prev');
		},
		
		// 下一曲
		handleNext() {
			this.$emit('next');
		},
		
		// 切换循环模式
		handleRepeat() {
			this.$emit('repeat', !this.isRepeat);
		}
	}
};
</script>

<style lang="scss" scoped>
.play-controls {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 60rpx;
	padding: 30rpx 40rpx;
	background: rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.control-btn {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: var(--button-bg, rgba(255, 255, 255, 0.1));
	backdrop-filter: blur(10rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.95);
		background: var(--button-bg-active, rgba(255, 255, 255, 0.2));
	}
	
	&.active {
		background: var(--icon-color-active, #50E3C2);
		
		.control-icon {
			color: #FFFFFF;
		}
	}
}

.control-icon {
	width: 40rpx;
	height: 40rpx;
	color: var(--icon-color, #FFFFFF);
	transition: all 0.3s;
}

.repeat-badge {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 24rpx;
	height: 24rpx;
	background: #FFFFFF;
	color: var(--icon-color-active, #50E3C2);
	border-radius: 50%;
	font-size: 18rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* H5端优化 */
/* #ifdef H5 */
.control-btn {
	cursor: pointer;
	
	&:hover {
		background: var(--button-bg-active, rgba(255, 255, 255, 0.2));
		transform: scale(1.05);
		
		.control-icon {
			transform: scale(1.1);
		}
	}
	
	&.active:hover {
		opacity: 0.9;
	}
}
/* #endif */
</style>

