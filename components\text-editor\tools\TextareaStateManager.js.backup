/**
 * 文本框状态管理器
 * 用于协调文本选择、滚动、点击等功能之间的冲突
 */

export class TextareaStateManager {
  constructor() {
    this.state = {
      // 当前模式
      mode: 'normal', // 'normal', 'selection', 'scrolling', 'positioning'
      
      // 功能状态
      isSelectionMode: false,
      isScrolling: false,
      isDragging: false,
      isLongPressing: false,
      
      // 时间戳
      lastTouchTime: 0,
      lastClickTime: 0,
      lastScrollTime: 0,
      
      // 位置信息
      touchStartPos: { x: 0, y: 0 },
      touchCurrentPos: { x: 0, y: 0 },
      
      // 阈值配置
      longPressDelay: 500,
      doubleClickDelay: 300,
      scrollThreshold: 10,
      dragThreshold: 5,
      
      // 定时器
      longPressTimer: null,
      modeResetTimer: null
    };
    
    this.listeners = new Map();
  }
  
  /**
   * 注册事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }
  
  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }
  
  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error);
        }
      });
    }
  }
  
  /**
   * 处理触摸开始
   */
  handleTouchStart(e) {
    const now = Date.now();
    const touch = e.touches[0];
    
    this.state.lastTouchTime = now;
    this.state.touchStartPos = { x: touch.clientX, y: touch.clientY };
    this.state.touchCurrentPos = { x: touch.clientX, y: touch.clientY };
    
    // 清除之前的长按定时器
    this.clearLongPressTimer();
    
    // 如果不在选择模式，启动长按检测
    if (!this.state.isSelectionMode) {
      this.state.longPressTimer = setTimeout(() => {
        this.handleLongPress(e);
      }, this.state.longPressDelay);
    }
    
    this.emit('touchstart', { event: e, state: this.getState() });
  }
  
  /**
   * 处理触摸移动
   */
  handleTouchMove(e) {
    const touch = e.touches[0];
    const currentPos = { x: touch.clientX, y: touch.clientY };
    
    // 计算移动距离
    const deltaX = Math.abs(currentPos.x - this.state.touchStartPos.x);
    const deltaY = Math.abs(currentPos.y - this.state.touchStartPos.y);
    const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    this.state.touchCurrentPos = currentPos;
    
    // 如果移动距离超过阈值，取消长按
    if (totalDelta > this.state.dragThreshold) {
      this.clearLongPressTimer();
      
      // 判断是否为滚动
      if (deltaY > this.state.scrollThreshold && deltaY > deltaX) {
        this.setMode('scrolling');
      } else if (this.state.isSelectionMode) {
        this.setMode('selection');
        this.state.isDragging = true;
      }
    }
    
    this.emit('touchmove', { event: e, state: this.getState() });
  }
  
  /**
   * 处理触摸结束
   */
  handleTouchEnd(e) {
    const now = Date.now();
    const touchDuration = now - this.state.lastTouchTime;
    
    // 清除长按定时器
    this.clearLongPressTimer();
    
    // 判断是否为点击
    const deltaX = Math.abs(this.state.touchCurrentPos.x - this.state.touchStartPos.x);
    const deltaY = Math.abs(this.state.touchCurrentPos.y - this.state.touchStartPos.y);
    const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    if (totalDelta < this.state.dragThreshold && touchDuration < this.state.longPressDelay) {
      this.handleClick(e);
    }
    
    // 重置状态
    this.state.isDragging = false;
    this.state.isScrolling = false;
    
    // 延迟重置模式
    this.scheduleModeReset();
    
    this.emit('touchend', { event: e, state: this.getState() });
  }
  
  /**
   * 处理点击
   */
  handleClick(e) {
    const now = Date.now();
    const isDoubleClick = now - this.state.lastClickTime < this.state.doubleClickDelay;
    
    this.state.lastClickTime = now;
    
    if (isDoubleClick) {
      this.handleDoubleClick(e);
    } else {
      // 单击处理
      if (this.state.mode === 'normal') {
        this.setMode('positioning');
        this.emit('click', { event: e, state: this.getState() });
      }
    }
  }
  
  /**
   * 处理双击
   */
  handleDoubleClick(e) {
    this.setMode('selection');
    this.state.isSelectionMode = true;
    this.emit('doubleclick', { event: e, state: this.getState() });
  }
  
  /**
   * 处理长按
   */
  handleLongPress(e) {
    this.setMode('selection');
    this.state.isSelectionMode = true;
    this.state.isLongPressing = true;
    this.emit('longpress', { event: e, state: this.getState() });
  }
  
  /**
   * 处理滚动
   */
  handleScroll(e) {
    this.state.lastScrollTime = Date.now();
    
    if (this.state.mode !== 'selection') {
      this.setMode('scrolling');
    }
    
    this.emit('scroll', { event: e, state: this.getState() });
  }
  
  /**
   * 设置模式
   */
  setMode(mode) {
    if (this.state.mode !== mode) {
      const oldMode = this.state.mode;
      this.state.mode = mode;
      
      this.emit('modechange', { 
        oldMode, 
        newMode: mode, 
        state: this.getState() 
      });
      
      console.log(`文本框模式切换: ${oldMode} -> ${mode}`);
    }
  }
  
  /**
   * 重置到正常模式
   */
  resetMode() {
    this.setMode('normal');
    this.state.isSelectionMode = false;
    this.state.isScrolling = false;
    this.state.isDragging = false;
    this.state.isLongPressing = false;
  }
  
  /**
   * 计划模式重置
   */
  scheduleModeReset() {
    if (this.state.modeResetTimer) {
      clearTimeout(this.state.modeResetTimer);
    }
    
    // 如果不在选择模式，延迟重置
    if (!this.state.isSelectionMode) {
      this.state.modeResetTimer = setTimeout(() => {
        this.resetMode();
      }, 1000);
    }
  }
  
  /**
   * 清除长按定时器
   */
  clearLongPressTimer() {
    if (this.state.longPressTimer) {
      clearTimeout(this.state.longPressTimer);
      this.state.longPressTimer = null;
    }
  }
  
  /**
   * 获取当前状态
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * 强制退出选择模式
   */
  exitSelectionMode() {
    this.state.isSelectionMode = false;
    this.resetMode();
    this.emit('selectionexit', { state: this.getState() });
  }
  
  /**
   * 判断是否应该阻止默认行为
   */
  shouldPreventDefault(eventType) {
    switch (eventType) {
      case 'touchstart':
        return this.state.isSelectionMode;
      case 'touchmove':
        return this.state.isDragging || this.state.mode === 'selection';
      case 'touchend':
        return this.state.isDragging;
      default:
        return false;
    }
  }
  
  /**
   * 清理资源
   */
  destroy() {
    this.clearLongPressTimer();
    
    if (this.state.modeResetTimer) {
      clearTimeout(this.state.modeResetTimer);
    }
    
    this.listeners.clear();
  }
}

export default TextareaStateManager;
