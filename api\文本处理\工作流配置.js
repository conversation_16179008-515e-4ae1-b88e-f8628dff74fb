/**
 * 文本处理功能工作流配置
 * 定义文本处理功能与后端工作流的对接配置
 * 创建时间：2025-01-11
 */

/**
 * 文本处理工作流配置
 */
export const 文本处理工作流配置 = {
    // 工作流基础信息
    workflowId: 'text_processing_workflow_001',
    workflowType: 'text_processing',
    moduleName: '文本处理',
    
    // 工作流描述
    description: '多功能文本处理工作流，支持写作、翻译、总结等',

    // 结构化参数定义
    structuredParams: {
        
        textInput: {
            type: 'text',
            required: true,
            placeholder: '{{textInput}}',
            description: 'textInput'
        },
        
        processType: {
            type: 'text',
            required: true,
            placeholder: '{{processType}}',
            description: 'processType'
        },
        
        outputFormat: {
            type: 'text',
            required: true,
            placeholder: '{{outputFormat}}',
            description: 'outputFormat'
        }
    },

    // 提示词模板
    promptTemplate: `
请基于以下参数执行文本处理任务：

- textInput：{{textInput}}
- processType：{{processType}}
- outputFormat：{{outputFormat}}

请提供详细的处理结果。
`,

    // 输出格式定义
    outputFormat: {
        type: 'json',
        schema: {
            success: 'boolean',
            data: 'object'
        }
    },

    // 费用配置
    pricing: {
        basePrice: 8,
        memberDiscount: 0.8
    },

    // 执行配置
    execution: {
        timeout: 300000,
        maxRetries: 3,
        pollInterval: 2000,
        enableCache: true
    }
};

/**
 * 文本处理参数验证规则
 */
export const 文本处理参数验证规则 = {
    required: ['textInput', 'processType', 'outputFormat'],
    formats: {},
    ranges: {},
    enums: {}
};

/**
 * 文本处理错误码定义
 */
export const 文本处理错误码 = {
    INVALID_PARAMS: { code: 'TEXT_PROCESSING_001', message: '参数格式不正确' },
    INSUFFICIENT_COINS: { code: 'TEXT_PROCESSING_007', message: '金币余额不足' },
    WORKFLOW_TIMEOUT: { code: 'TEXT_PROCESSING_008', message: '工作流执行超时' },
    WORKFLOW_FAILED: { code: 'TEXT_PROCESSING_009', message: '工作流执行失败' },
    UNKNOWN_ERROR: { code: 'TEXT_PROCESSING_999', message: '未知错误' }
};

/**
 * 文本处理状态定义
 */
export const 文本处理状态 = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout'
};

export default {
    文本处理工作流配置,
    文本处理参数验证规则,
    文本处理错误码,
    文本处理状态
};