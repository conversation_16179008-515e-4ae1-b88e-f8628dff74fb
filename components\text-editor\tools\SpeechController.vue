<template>
  <view class="speech-controller">
    <!-- 语音控制器不需要可视化界面，只提供功能 -->
  </view>
</template>

<script>
export default {
  name: 'SpeechController',
  data() {
    return {
      isSpeaking: false,
      currentSpeakingIndex: -1,
      speechSynthesis: null,
      currentUtterance: null
    };
  },
  mounted() {
    // 初始化语音合成
    this.initSpeechSynthesis();
  },
  methods: {
    // 初始化语音合成
    initSpeechSynthesis() {
      // #ifdef H5
      if ('speechSynthesis' in window) {
        this.speechSynthesis = window.speechSynthesis;
      }
      // #endif
    },

    // 开始语音播放
    startSpeaking(content, index) {
      if (!content) return;

      // 如果正在播放，先停止
      if (this.isSpeaking) {
        this.stopSpeaking();
      }

      this.isSpeaking = true;
      this.currentSpeakingIndex = index;

      // #ifdef H5
      if (this.speechSynthesis) {
        try {
          // 创建语音合成实例
          this.currentUtterance = new SpeechSynthesisUtterance(content);
          
          // 设置语音参数
          this.currentUtterance.lang = 'zh-CN';
          this.currentUtterance.rate = 1.0;
          this.currentUtterance.pitch = 1.0;
          this.currentUtterance.volume = 1.0;

          // 监听语音结束事件
          this.currentUtterance.onend = () => {
            this.isSpeaking = false;
            this.currentSpeakingIndex = -1;
            this.currentUtterance = null;
            this.$emit('speech-end');
          };

          // 监听语音错误事件
          this.currentUtterance.onerror = (event) => {
            console.error('语音播放错误:', event);
            this.isSpeaking = false;
            this.currentSpeakingIndex = -1;
            this.currentUtterance = null;
            this.$emit('speech-error', event);
          };

          // 开始播放
          this.speechSynthesis.speak(this.currentUtterance);
          this.$emit('speech-start', { content, index });

        } catch (error) {
          console.error('语音播放失败:', error);
          this.isSpeaking = false;
          this.currentSpeakingIndex = -1;
          this.$emit('speech-error', error);
        }
      }
      // #endif

      // #ifndef H5
      // 非H5环境的处理
      uni.showToast({
        title: '语音播放功能仅在H5环境下可用',
        icon: 'none'
      });
      this.isSpeaking = false;
      this.currentSpeakingIndex = -1;
      // #endif
    },

    // 停止语音播放
    stopSpeaking() {
      // #ifdef H5
      if (this.speechSynthesis) {
        this.speechSynthesis.cancel();
      }
      // #endif
      
      this.isSpeaking = false;
      this.currentSpeakingIndex = -1;
      this.currentUtterance = null;
      this.$emit('speech-stop');
    },

    // 暂停语音播放
    pauseSpeaking() {
      // #ifdef H5
      if (this.speechSynthesis && this.isSpeaking) {
        this.speechSynthesis.pause();
        this.$emit('speech-pause');
      }
      // #endif
    },

    // 恢复语音播放
    resumeSpeaking() {
      // #ifdef H5
      if (this.speechSynthesis && this.isSpeaking) {
        this.speechSynthesis.resume();
        this.$emit('speech-resume');
      }
      // #endif
    },

    // 获取当前播放状态
    getSpeakingState() {
      return {
        isSpeaking: this.isSpeaking,
        currentSpeakingIndex: this.currentSpeakingIndex
      };
    },

    // 检查是否支持语音合成
    isSpeechSynthesisSupported() {
      // #ifdef H5
      return 'speechSynthesis' in window;
      // #endif
      // #ifndef H5
      return false;
      // #endif
    },

    // 获取可用的语音列表
    getAvailableVoices() {
      // #ifdef H5
      if (this.speechSynthesis) {
        return this.speechSynthesis.getVoices();
      }
      // #endif
      return [];
    },

    // 设置语音参数
    setSpeechSettings(settings) {
      if (this.currentUtterance) {
        if (settings.rate !== undefined) {
          this.currentUtterance.rate = settings.rate;
        }
        if (settings.pitch !== undefined) {
          this.currentUtterance.pitch = settings.pitch;
        }
        if (settings.volume !== undefined) {
          this.currentUtterance.volume = settings.volume;
        }
        if (settings.lang !== undefined) {
          this.currentUtterance.lang = settings.lang;
        }
      }
    }
  },
  beforeDestroy() {
    // 组件销毁前停止语音播放
    this.stopSpeaking();
  }
}
</script>

<style scoped>
.speech-controller {
  display: none; /* 隐藏容器，因为这是一个功能性组件 */
}
</style>
