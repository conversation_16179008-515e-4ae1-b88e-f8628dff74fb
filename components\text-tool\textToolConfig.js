/**
 * 文本工具配置文件
 * 用于管理不同行业和用途的文本工具配置
 */

// 基础配置模板
export const baseConfig = {
  title: '',
  description: '',
  avatar: null,
  showHeader: false,
  showActions: false,
  inputPlaceholder: '',
  maxLength: 500,
  submitText: '',
  showSuggestions: false,
  suggestionTitle: '',
  params: [],
  suggestions: []
};

// 通用风格选项
export const commonStyles = [
  { label: '标准', value: 'standard' },
  { label: '专业', value: 'professional' },
  { label: '通用', value: 'general' },
  { label: '创意', value: 'creative' },
  { label: '幽默', value: 'humorous' },
  { label: '正式', value: 'formal' },
  { label: '简洁', value: 'concise' },
  { label: '详细', value: 'detailed' },
  { label: '热情', value: 'passionate' },
  { label: '技术', value: 'technical' }
];

// 通用语气选项
export const commonTones = [
  { label: '中性', value: 'neutral' },
  { label: '积极', value: 'positive' },
  { label: '严肃', value: 'serious' },
  { label: '友好', value: 'friendly' },
  { label: '权威', value: 'authoritative' },
  { label: '温暖', value: 'warm' },
  { label: '轻松', value: 'casual' }
];

// 通用文本工具配置
export const generalTextTool = {
  ...baseConfig,
  title: 'AI智能写作助手',
  description: '智能AI助手可以帮您创作各类文本内容',
  showHeader: true,
  showActions: true,
  inputPlaceholder: '请输入您想要生成的内容...',
  maxLength: 500,
  submitText: '立即生成',
  showSuggestions: true,
  params: [
    {
      key: 'style',
      label: '风格',
      type: 'style',
      presets: commonStyles,
      defaultValue: 'standard'
    },
    {
      key: 'type',
      label: '类型',
      type: 'style',
      presets: [
        { label: '文章', value: 'article' },
        { label: '广告文案', value: 'ad' },
        { label: '产品描述', value: 'product' },
        { label: '社交媒体', value: 'social' },
        { label: '邮件', value: 'email' }
      ]
    }
  ],
  suggestions: [
    {
      title: '创建产品介绍文案',
      prompt: '我需要一个产品介绍文案，该产品是一款智能家居设备，可以通过语音控制家中的电器。',
      params: {
        style: 'professional',
        type: 'product',
        length: 400
      }
    },
    {
      title: '编写营销邮件',
      prompt: '帮我写一封营销邮件，向客户推广我们最新的健身APP订阅服务。',
      params: {
        style: 'professional',
        type: 'email',
        length: 300
      }
    },
    {
      title: '撰写社交媒体帖子',
      prompt: '为一家新开的咖啡店创作一条引人注目的社交媒体宣传帖子。',
      params: {
        style: 'creative',
        type: 'social',
        length: 200
      }
    }
  ]
};

// 市场营销文本工具配置
export const marketingTextTool = {
  ...baseConfig,
  title: '营销文案助手',
  description: '专业营销文案生成工具，帮您制作吸引人的营销内容',
  params: [
    {
      key: 'style',
      label: '风格',
      type: 'style',
      presets: [
        { label: '吸引注意', value: 'attention-grabbing' },
        { label: '情感化', value: 'emotional' },
        { label: '说服力强', value: 'persuasive' },
        { label: '紧迫感', value: 'urgent' },
        { label: '信任建立', value: 'trust-building' }
      ],
      defaultValue: 'attention-grabbing'
    },
    {
      key: 'platform',
      label: '平台',
      type: 'style',
      presets: [
        { label: '微信', value: 'wechat' },
        { label: '微博', value: 'weibo' },
        { label: '抖音', value: 'douyin' },
        { label: '小红书', value: 'xiaohongshu' },
        { label: '知乎', value: 'zhihu' },
        { label: '邮件', value: 'email' },
        { label: '短信', value: 'sms' }
      ]
    },
    {
      key: 'target',
      label: '目标人群',
      type: 'tags',
      multiple: true,
      options: [
        { label: '年轻人', value: 'youth' },
        { label: '中年人', value: 'middle-aged' },
        { label: '老年人', value: 'seniors' },
        { label: '学生', value: 'students' },
        { label: '职场人', value: 'professionals' },
        { label: '父母', value: 'parents' }
      ]
    },
    {
      key: 'cta',
      label: '行动号召',
      type: 'input',
      placeholder: '例如：立即购买、了解更多'
    }
  ],
  suggestions: [
    {
      title: '促销活动宣传',
      prompt: '为我们即将到来的夏季大促销活动创建一条吸引人的社交媒体宣传文案，重点突出折扣力度和限时特惠。',
      params: {
        style: 'attention-grabbing',
        platform: 'xiaohongshu',
        target: ['youth', 'students'],
        cta: '限时抢购'
      }
    },
    {
      title: '新品上市宣传',
      prompt: '我们将推出一款新的智能手表，请创建一条吸引目标用户的产品介绍文案，突出其健康监测功能。',
      params: {
        style: 'persuasive',
        platform: 'wechat',
        target: ['professionals'],
        cta: '预约体验'
      }
    }
  ]
};

// 商业文件工具配置
export const businessDocumentTool = {
  ...baseConfig,
  title: '商务文档助手',
  description: '生成专业商务文档，包括报告、方案、邮件等',
  params: [
    {
      key: 'documentType',
      label: '文档类型',
      type: 'style',
      presets: [
        { label: '商业计划', value: 'business-plan' },
        { label: '项目提案', value: 'project-proposal' },
        { label: '市场分析', value: 'market-analysis' },
        { label: '会议纪要', value: 'meeting-minutes' },
        { label: '商务邮件', value: 'business-email' },
        { label: '合作协议', value: 'cooperation-agreement' }
      ]
    },
    {
      key: 'industry',
      label: '行业',
      type: 'style',
      presets: [
        { label: '科技', value: 'tech' },
        { label: '金融', value: 'finance' },
        { label: '医疗', value: 'healthcare' },
        { label: '教育', value: 'education' },
        { label: '零售', value: 'retail' },
        { label: '制造', value: 'manufacturing' },
        { label: '服务', value: 'service' }
      ]
    },
    {
      key: 'tone',
      label: '语气',
      type: 'style',
      presets: [
        { label: '正式', value: 'formal' },
        { label: '专业', value: 'professional' },
        { label: '权威', value: 'authoritative' },
        { label: '合作', value: 'collaborative' }
      ],
      defaultValue: 'professional'
    }
  ],
  suggestions: [
    {
      title: '创建商业计划书',
      prompt: '为一家新创建的SaaS初创公司编写商业计划书，重点介绍产品价值、市场分析和盈利模式。',
      params: {
        documentType: 'business-plan',
        industry: 'tech',
        tone: 'professional',
        detailLevel: 4
      }
    },
    {
      title: '起草合作提案',
      prompt: '为我司向潜在合作伙伴起草一份合作提案，重点展示双方合作的价值和未来可能的发展方向。',
      params: {
        documentType: 'project-proposal',
        industry: 'service',
        tone: 'collaborative',
        detailLevel: 3
      }
    }
  ]
};

// 教育培训文本工具配置
export const educationTextTool = {
  ...baseConfig,
  title: '教育内容助手',
  description: '创建教育培训内容，包括课程大纲、教学计划、学习材料等',
  params: [
    {
      key: 'contentType',
      label: '内容类型',
      type: 'style',
      presets: [
        { label: '课程大纲', value: 'course-outline' },
        { label: '教学计划', value: 'teaching-plan' },
        { label: '学习材料', value: 'study-material' },
        { label: '习题', value: 'exercises' },
        { label: '考试题目', value: 'exam-questions' },
        { label: '教师指南', value: 'teacher-guide' }
      ]
    },
    {
      key: 'subject',
      label: '学科',
      type: 'style',
      presets: [
        { label: '语文', value: 'chinese' },
        { label: '数学', value: 'math' },
        { label: '英语', value: 'english' },
        { label: '物理', value: 'physics' },
        { label: '化学', value: 'chemistry' },
        { label: '生物', value: 'biology' },
        { label: '历史', value: 'history' },
        { label: '地理', value: 'geography' },
        { label: '政治', value: 'politics' },
        { label: '计算机', value: 'computer-science' }
      ]
    },
    {
      key: 'educationLevel',
      label: '教育阶段',
      type: 'style',
      presets: [
        { label: '小学', value: 'elementary' },
        { label: '初中', value: 'junior-high' },
        { label: '高中', value: 'high-school' },
        { label: '大学', value: 'university' },
        { label: '职业培训', value: 'vocational' },
        { label: '成人教育', value: 'adult-education' }
      ]
    }
  ],
  suggestions: [
    {
      title: '创建数学课程大纲',
      prompt: '为初中一年级学生创建一个数学课程大纲，包括代数基础、几何入门和简单数据分析。',
      params: {
        contentType: 'course-outline',
        subject: 'math',
        educationLevel: 'junior-high',
        difficulty: 2
      }
    },
    {
      title: '编写英语教学计划',
      prompt: '为小学高年级学生编写一个为期10周的英语教学计划，重点培养学生的口语表达和阅读理解能力。',
      params: {
        contentType: 'teaching-plan',
        subject: 'english',
        educationLevel: 'elementary',
        difficulty: 3
      }
    }
  ]
};

// 工具配置缓存
const toolConfigCache = {};

// 默认配置 - 当API不可用时使用
const defaultToolConfigs = {
  'text': {
    id: 'text',
    name: '通用文本创作',
    description: '创建各种风格的文本内容',
    maxQuantity: 1,
    coinCost: 1,
    params: [
      {
        key: 'style',
        label: '风格',
        type: 'style',
        placeholder: '请输入或选择风格',
        presets: [
          { label: '标准', value: '标准' },
          { label: '专业', value: '专业' },
          { label: '幽默', value: '幽默' },
          { label: '故事化', value: '故事化' },
          { label: '正式', value: '正式' },
          { label: '简洁', value: '简洁' },
          { label: '创意', value: '创意' },
          { label: '诗意', value: '诗意' }
        ]
      },
      {
        key: 'category',
        label: '类型',
        type: 'category',
        placeholder: '请输入或选择内容类型',
        presets: [
          { label: '文章', value: '文章' },
          { label: '报告', value: '报告' },
          { label: '教程', value: '教程' },
          { label: '脚本', value: '脚本' },
          { label: '故事', value: '故事' },
          { label: '广告文案', value: '广告文案' },
          { label: '电子邮件', value: '电子邮件' },
          { label: '社交媒体', value: '社交媒体' },
          { label: '新闻稿', value: '新闻稿' },
          { label: '产品描述', value: '产品描述' },
          { label: '演讲稿', value: '演讲稿' },
          { label: '诗歌', value: '诗歌' }
        ]
      },
      {
        key: 'tone',
        label: '语调',
        type: 'select',
        placeholder: '请选择语调',
        options: [
          { label: '友好', value: '友好' },
          { label: '正式', value: '正式' },
          { label: '专业', value: '专业' },
          { label: '幽默', value: '幽默' },
          { label: '热情', value: '热情' },
          { label: '严肃', value: '严肃' },
          { label: '轻松', value: '轻松' }
        ]
      },
      {
        key: 'length',
        label: '长度',
        type: 'select',
        placeholder: '请选择长度',
        options: [
          { label: '短 (150字以内)', value: '短' },
          { label: '中 (300-500字)', value: '中' },
          { label: '长 (800字以上)', value: '长' }
        ]
      },
      {
        key: 'tags',
        label: '标签',
        type: 'tags',
        options: [
          { label: '商业', value: '商业' },
          { label: '技术', value: '技术' },
          { label: '教育', value: '教育' },
          { label: '营销', value: '营销' },
          { label: '创意', value: '创意' },
          { label: '科学', value: '科学' },
          { label: '健康', value: '健康' }
        ]
      }
    ],
    defaultTemplate: '请根据以下要求生成文本:\n风格：{style}\n类型：{category}\n语调：{tone}\n长度：{length}\n标签：{tags}\n\n内容主题：{prompt}'
  },
  'default': {
    id: 'default',
    name: '默认文本创作',
    description: '创建各种风格的文本内容',
    maxQuantity: 1,
    coinCost: 1,
    params: [
      {
        key: 'style',
        label: '风格',
        type: 'style',
        placeholder: '请输入或选择风格',
        presets: [
          { label: '标准', value: '标准' },
          { label: '专业', value: '专业' },
          { label: '幽默', value: '幽默' },
          { label: '故事化', value: '故事化' },
          { label: '正式', value: '正式' },
          { label: '简洁', value: '简洁' },
          { label: '创意', value: '创意' },
          { label: '诗意', value: '诗意' }
        ]
      },
      {
        key: 'category',
        label: '类型',
        type: 'category',
        placeholder: '请输入或选择内容类型',
        presets: [
          { label: '文章', value: '文章' },
          { label: '报告', value: '报告' },
          { label: '教程', value: '教程' },
          { label: '脚本', value: '脚本' },
          { label: '故事', value: '故事' },
          { label: '广告文案', value: '广告文案' },
          { label: '电子邮件', value: '电子邮件' },
          { label: '社交媒体', value: '社交媒体' },
          { label: '新闻稿', value: '新闻稿' },
          { label: '产品描述', value: '产品描述' },
          { label: '演讲稿', value: '演讲稿' },
          { label: '诗歌', value: '诗歌' }
        ]
      },
      {
        key: 'tone',
        label: '语调',
        type: 'select',
        placeholder: '请选择语调',
        options: [
          { label: '友好', value: '友好' },
          { label: '正式', value: '正式' },
          { label: '专业', value: '专业' },
          { label: '幽默', value: '幽默' },
          { label: '热情', value: '热情' },
          { label: '严肃', value: '严肃' },
          { label: '轻松', value: '轻松' }
        ]
      },
      {
        key: 'length',
        label: '长度',
        type: 'select',
        placeholder: '请选择长度',
        options: [
          { label: '短 (150字以内)', value: '短' },
          { label: '中 (300-500字)', value: '中' },
          { label: '长 (800字以上)', value: '长' }
        ]
      },
      {
        key: 'tags',
        label: '标签',
        type: 'tags',
        options: [
          { label: '商业', value: '商业' },
          { label: '技术', value: '技术' },
          { label: '教育', value: '教育' },
          { label: '营销', value: '营销' },
          { label: '创意', value: '创意' },
          { label: '科学', value: '科学' },
          { label: '健康', value: '健康' }
        ]
      }
    ],
    defaultTemplate: '请根据以下要求生成文本:\n风格：{style}\n类型：{category}\n语调：{tone}\n长度：{length}\n标签：{tags}\n\n内容主题：{prompt}'
  },
  'chat': {
    id: 'chat',
    name: '智能对话',
    description: '与AI助手进行智能对话',
    maxQuantity: 1,
    coinCost: 1,
    params: [
      {
        key: 'assistant_type',
        label: '助手类型',
        type: 'select',
        placeholder: '请选择助手类型',
        options: [
          { label: '通用助手', value: '通用助手' },
          { label: '专业顾问', value: '专业顾问' },
          { label: '创意搭档', value: '创意搭档' },
          { label: '学习导师', value: '学习导师' },
          { label: '技术专家', value: '技术专家' }
        ]
      },
      {
        key: 'knowledge_depth',
        label: '知识深度',
        type: 'select',
        placeholder: '请选择知识深度',
        options: [
          { label: '入门级', value: '入门级' },
          { label: '普通', value: '普通' },
          { label: '专业', value: '专业' },
          { label: '专家', value: '专家' }
        ]
      }
    ],
    defaultTemplate: '你现在是一个{assistant_type}，知识深度为{knowledge_depth}。请回答我的问题：{prompt}'
  },
  'lipsync': {
    id: 'lipsync',
    name: '口型同步',
    description: '为视频生成口型同步效果',
    maxQuantity: 1,
    coinCost: 2,
    params: [
      {
        key: 'style',
        label: '风格',
        type: 'style',
        placeholder: '请输入或选择风格',
        presets: [
          { label: '标准', value: '标准' },
          { label: '自然', value: '自然' },
          { label: '精确', value: '精确' }
        ]
      },
      {
        key: 'language',
        label: '语言',
        type: 'select',
        placeholder: '请选择语言',
        options: [
          { label: '中文', value: '中文' },
          { label: '英语', value: '英语' },
          { label: '日语', value: '日语' }
        ]
      }
    ],
    defaultTemplate: '请生成口型同步效果，风格：{style}，语言：{language}，内容：{prompt}'
  }
};

// 全局暴露配置
window.toolConfigs = defaultToolConfigs;

/**
 * 通过ID获取工具配置
 */
export function getToolConfigById(id) {
  // 如果缓存中有配置，直接返回
  if (toolConfigCache[id]) {
    return toolConfigCache[id];
  }
  
  // 如果有默认配置，使用默认配置
  if (defaultToolConfigs[id]) {
    toolConfigCache[id] = defaultToolConfigs[id];
    return toolConfigCache[id];
  }
  
  // 否则尝试从API加载
  return fetchToolConfig(id);
}

/**
 * 获取所有工具配置
 */
export function getAllToolConfigs() {
  // 如果缓存中已有所有配置，直接返回
  if (Object.keys(toolConfigCache).length > 0) {
    return toolConfigCache;
  }
  
  // 否则尝试从API加载
  return fetchAllToolConfigs();
}

/**
 * 从API加载单个工具配置
 */
async function fetchToolConfig(id) {
  // 如果没有后端API，返回默认配置
  if (!window.API || !window.API.getToolConfig) {
    console.warn(`[工具配置] 没有API可用，使用默认配置: ${id}`);
    // 尝试有效ID
    if (defaultToolConfigs[id]) {
      toolConfigCache[id] = defaultToolConfigs[id];
      return defaultToolConfigs[id];
    } 
    
    // 如果没有找到对应ID的配置，尝试使用text配置
    if (defaultToolConfigs['text']) {
      console.warn(`[工具配置] ID ${id} 不存在，使用text配置代替`);
      toolConfigCache[id] = defaultToolConfigs['text'];
      return defaultToolConfigs['text'];
    }
    
    // 最后兜底，返回任意一个可用配置
    const firstKey = Object.keys(defaultToolConfigs)[0];
    if (firstKey) {
      console.warn(`[工具配置] 找不到有效配置，使用${firstKey}配置`);
      toolConfigCache[id] = defaultToolConfigs[firstKey];
      return defaultToolConfigs[firstKey];
    }
    
    return null;
  }
  
  try {
    const config = await window.API.getToolConfig(id);
    toolConfigCache[id] = config;
    return config;
  } catch (error) {
    console.error(`[工具配置] 无法加载配置 ${id}:`, error);
    // 尝试使用默认配置
    if (defaultToolConfigs[id]) {
      return defaultToolConfigs[id];
    }
    // 尝试使用text配置
    if (defaultToolConfigs['text']) {
      return defaultToolConfigs['text'];
    }
    return null;
  }
}

/**
 * 从API加载所有工具配置
 */
async function fetchAllToolConfigs() {
  // 如果没有后端API，返回默认配置
  if (!window.API || !window.API.getAllToolConfigs) {
    console.warn('[工具配置] 没有API可用，使用默认配置');
    return defaultToolConfigs;
  }
  
  try {
    const configs = await window.API.getAllToolConfigs();
    
    // 更新缓存
    Object.keys(configs).forEach(key => {
      toolConfigCache[key] = configs[key];
    });
    
    return configs;
  } catch (error) {
    console.error('[工具配置] 无法加载所有配置:', error);
    return defaultToolConfigs;
  }
} 