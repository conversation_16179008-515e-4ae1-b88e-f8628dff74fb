/**
 * 图生视频功能工作流执行接口
 * 基于通用工作流基础类的图生视频功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 图生视频工作流配置, 图生视频参数验证规则, 图生视频错误码, 图生视频状态 } from './工作流配置.js';

/**
 * 图生视频工作流执行类
 */
class Workflow extends WorkflowBase {
    constructor() {
        super('图生视频', 图生视频工作流配置);
        this.validationRules = 图生视频参数验证规则;
        this.errorCodes = 图生视频错误码;
        this.statusCodes = 图生视频状态;
    }

    /**
     * 执行图生视频工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async execute(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`图生视频进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '图生视频',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('图生视频工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证图生视频参数
     * @param {Object} formData - 表单数据
     */
    validateParams(formData) {
        this.validateStructuredParams(formData, this.validationRules.required);
        return true;
    }

    /**
     * 构建图生视频结构化参数
     * @param {Object} formData - 表单数据
     */
    buildParams(formData) {
        const builder = new StructuredParamsBuilder();

        
        if (formData.imageInput) {
            builder.addTextParam('imageInput', formData.imageInput);
        }
        if (formData.duration) {
            builder.addTextParam('duration', formData.duration);
        }
        if (formData.motionType) {
            builder.addTextParam('motionType', formData.motionType);
        }
        if (formData.resolution) {
            builder.addTextParam('resolution', formData.resolution);
        }

        return builder.build();
    }
}

// 创建图生视频工作流实例
const Workflow = new Workflow();

// 导出接口方法
export async function 执行图生视频工作流(formData, options = {}) {
    return await Workflow.execute(formData, options);
}

export async function 查询图生视频状态(requestId) {
    return await Workflow.queryWorkflowStatus(requestId);
}

export async function 取消图生视频工作流(requestId) {
    return await Workflow.cancelWorkflow(requestId);
}

export default {
    执行图生视频工作流,
    查询图生视频状态,
    取消图生视频工作流
};