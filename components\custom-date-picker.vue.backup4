<template>
	<view class="custom-date-picker" v-if="visible" @touchmove.stop.prevent @wheel.stop.prevent>
		<view class="picker-mask" @click="close" @touchmove.stop.prevent @wheel.stop.prevent></view>
		<view class="picker-content" @touchmove.stop.prevent @wheel.stop.prevent>
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
				<view class="header-actions">
					<text class="action-btn cancel" @click="close">取消</text>
					<text class="action-btn confirm" @click="confirm">确定</text>
				</view>
			</view>

			<view class="date-selector" @touchmove.stop.prevent @wheel.stop.prevent>
				<!-- 年份选择 -->
				<view class="selector-column">
					<text class="column-title">年</text>
					<view
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="yearScroll"
					>
						<view
							v-for="year in years"
							:key="year"
							:id="'year-' + year"
							class="scroll-item"
							:class="{ active: year === selectedYear }"
							@click="selectYear(year)"
						>
							{{ year }}
						</view>
					</view>
				</view>

				<!-- 月份选择 -->
				<view class="selector-column">
					<text class="column-title">月</text>
					<view
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="monthScroll"
					>
						<view
							v-for="month in months"
							:key="month"
							:id="'month-' + month"
							class="scroll-item"
							:class="{ active: month === selectedMonth }"
							@click="selectMonth(month)"
						>
							{{ month }}月
						</view>
					</view>
				</view>

				<!-- 日期选择 -->
				<view class="selector-column">
					<text class="column-title">日</text>
					<view
						class="scroll-list"
						@touchstart.stop="onTouchStart"
						@touchmove.stop.prevent="onTouchMove"
						@touchend.stop="onTouchEnd"
						@wheel.stop.prevent="onWheel"
						ref="dayScroll"
					>
						<view
							v-for="day in days"
							:key="day"
							:id="'day-' + day"
							class="scroll-item"
							:class="{ active: day === selectedDay }"
							@click="selectDay(day)"
						>
							{{ day }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			selectedYear: new Date().getFullYear(),
			selectedMonth: new Date().getMonth() + 1,
			selectedDay: new Date().getDate(),
			years: [],
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
			touchStartY: 0,
			scrollTop: 0,
			isScrolling: false
		}
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			return Array.from({ length: daysInMonth }, (_, i) => i + 1);
		}
	},
	watch: {
		value: {
			handler(newVal) {
				if (newVal) {
					const [year, month, day] = newVal.split('-');
					this.selectedYear = parseInt(year);
					this.selectedMonth = parseInt(month);
					this.selectedDay = parseInt(day);
				}
			},
			immediate: true
		},
		selectedMonth() {
			// 当月份改变时，检查日期是否有效
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
			}
		}
	},
	created() {
		this.initYears();
	},
	methods: {
		initYears() {
			const currentYear = new Date().getFullYear();
			for (let i = 1900; i <= currentYear; i++) {
				this.years.push(i);
			}
		},
		selectYear(year) {
			this.selectedYear = year;
		},
		selectMonth(month) {
			this.selectedMonth = month;
		},
		selectDay(day) {
			this.selectedDay = day;
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const year = this.selectedYear;
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const dateStr = `${year}-${month}-${day}`;
			this.$emit('confirm', dateStr);
		},
		onTouchStart(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.touchStartY = e.touches[0].clientY;
			this.isScrolling = true;
		},
		onTouchMove(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			if (!this.isScrolling) return;

			const deltaY = e.touches[0].clientY - this.touchStartY;
			const target = e.currentTarget;
			target.scrollTop -= deltaY * 0.5; // 减慢滚动速度
			this.touchStartY = e.touches[0].clientY;
		},
		onTouchEnd(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}
			this.isScrolling = false;
		},
		onWheel(e) {
			e.preventDefault();
			e.stopPropagation();
			if (e.stopImmediatePropagation) {
				e.stopImmediatePropagation();
			}

			const target = e.currentTarget;
			target.scrollTop += e.deltaY * 0.5; // 减慢滚动速度
		}
	}
}
</script>

<style scoped>
.custom-date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 600rpx;
	margin: 0 auto;
	background: linear-gradient(135deg, #F5E6D3, #E8D5B7);
	border-radius: 20rpx;
	overflow: hidden;
	animation: scaleIn 0.3s ease;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
	z-index: 2;
}

@keyframes scaleIn {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

.picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #8B4513;
	color: white;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
}

.header-actions {
	display: flex;
	gap: 30rpx;
}

.action-btn {
	font-size: 28rpx;
	padding: 8rpx 16rpx;
	border-radius: 6rpx;
	cursor: pointer;
}

.cancel {
	color: rgba(255, 255, 255, 0.7);
}

.confirm {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	font-weight: bold;
}

.date-selector {
	display: flex;
	height: 400rpx;
	padding: 20rpx 15rpx;
	overflow: hidden;
}

.selector-column {
	flex: 1;
	display: flex;
	flex-direction: column;
	margin: 0 5rpx;
}

.column-title {
	text-align: center;
	font-size: 24rpx;
	color: #8B4513;
	font-weight: bold;
	margin-bottom: 15rpx;
	background: rgba(139, 69, 19, 0.1);
	padding: 8rpx;
	border-radius: 8rpx;
}

.scroll-list {
	flex: 1;
	height: 320rpx;
	overflow-y: auto;
	overflow-x: hidden;
	scroll-behavior: smooth;
}

.scroll-list::-webkit-scrollbar {
	width: 6rpx;
}

.scroll-list::-webkit-scrollbar-track {
	background: rgba(139, 69, 19, 0.1);
	border-radius: 3rpx;
}

.scroll-list::-webkit-scrollbar-thumb {
	background: #D2B48C;
	border-radius: 3rpx;
}

.scroll-item {
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #8B4513;
	margin: 3rpx 8rpx;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.2s ease;
}

.scroll-item:hover {
	background: rgba(139, 69, 19, 0.1);
	transform: scale(1.02);
}

.scroll-item.active {
	background: #8B4513;
	color: white;
	font-weight: bold;
	transform: scale(1.05);
	box-shadow: 0 2rpx 8rpx rgba(139, 69, 19, 0.3);
}
</style>
