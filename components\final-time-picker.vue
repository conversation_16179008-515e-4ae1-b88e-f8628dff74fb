<template>
	<view v-if="visible" class="time-picker-overlay">
		<view class="time-picker-modal">
			<view class="modal-header">
				<text class="modal-title">选择出生时间</text>
			</view>
			
			<view class="time-form">
				<view class="form-row">
					<view class="form-group">
						<text class="form-label">小时</text>
						<input 
							class="form-input" 
							type="number" 
							v-model="hour" 
							placeholder="14"
							@input="validateHour"
						/>
					</view>
					
					<view class="form-group">
						<text class="form-label">分钟</text>
						<input 
							class="form-input" 
							type="number" 
							v-model="minute" 
							placeholder="30"
							@input="validateMinute"
						/>
					</view>
				</view>
			</view>
			
			<view class="modal-footer">
				<button class="btn btn-cancel" @click="cancel">取消</button>
				<button class="btn btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'FinalTimePicker',
	props: {
		visible: {
			type: <PERSON>olean,
			default: false
		}
	},
	data() {
		const now = new Date();
		return {
			hour: now.getHours(),
			minute: now.getMinutes()
		}
	},
	methods: {
		validateHour() {
			if (this.hour < 0) this.hour = 0;
			if (this.hour > 23) this.hour = 23;
		},
		validateMinute() {
			if (this.minute < 0) this.minute = 0;
			if (this.minute > 59) this.minute = 59;
		},
		cancel() {
			this.$emit('close');
		},
		confirm() {
			const timeStr = `${String(this.hour).padStart(2, '0')}:${String(this.minute).padStart(2, '0')}`;
			this.$emit('confirm', {
				hour: this.hour,
				minute: this.minute,
				timeString: timeStr
			});
		}
	}
}
</script>

<style scoped>
.time-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99999;
}

.time-picker-modal {
	background: #F5E6D3;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	margin: 40rpx;
	width: 90%;
	max-width: 500rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
	text-align: center;
	margin-bottom: 50rpx;
}

.modal-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #8B4513;
}

.time-form {
	margin-bottom: 50rpx;
}

.form-row {
	display: flex;
	gap: 40rpx;
}

.form-group {
	flex: 1;
	text-align: center;
}

.form-label {
	display: block;
	font-size: 30rpx;
	color: #8B4513;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	height: 100rpx;
	border: 3rpx solid #8B4513;
	border-radius: 15rpx;
	text-align: center;
	font-size: 36rpx;
	color: #8B4513;
	background: white;
	font-weight: bold;
}

.modal-footer {
	display: flex;
	gap: 40rpx;
}

.btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	font-size: 36rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
}

.btn-cancel {
	background: white;
	color: #8B4513;
	border: 3rpx solid #8B4513;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}
</style>
