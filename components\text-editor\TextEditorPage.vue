<template>
  <view class="chat-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack" id="backButton">
        <text class="back-text">&lt;&lt; 返回</text>
      </view>
      <view class="nav-title">{{ pageConfig.title }}</view>
    </view>
    
    <!-- 聊天内容区域 -->
    <view class="chat-area">
      <MessageList 
        :messages="messages"
        :is-generating="isGenerating"
        :coin-cost="coinCost"
        :is-speaking="isSpeaking"
        :current-speaking-index="currentSpeakingIndex"
        @preview-image="previewImage"
        @open-document="openDocument"
        @copy-message="copyMessage"
        @speak-message="speakMessage"
        @regenerate-message="regenerateMessage"
      />
    </view>
    
    <!-- 参数面板 -->
    <ParamsPanel 
      :is-collapsed="isCollapsed" 
      :is-animating="isAnimating" 
      :is-h5-platform="isH5Platform"
      :media-items="mediaItems"
      @preview-image="previewImage"
      @image-long-press="handleImageLongPress"
      @open-document="openDocument"
      @remove-media="removeMediaItem"
    >
      <template #prompt-tags>
        <PromptTags 
          :preset-prompt-tags="presetPromptTags"
          :type-tags="typeTags"
          :style-tags="styleTags"
          @select-tag="addPromptToInput"
        />
      </template>
    </ParamsPanel>
    
    <!-- 输入区域 -->
    <InputArea
      :value="inputText"
      @update:value="inputText = $event"
      :is-collapsed="isCollapsed"
      :placeholder-texts="placeholderTexts"
      :current-placeholder="currentPlaceholder"
      :is-generating="isGenerating"
      :coin-cost="coinCost"
      :show-feature-menu="showFeatureMenu"
      @toggle-expand="toggleExpand"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      @paste="handlePaste"
      @height-change="handleTextareaHeightChange"
      @send="sendMessage"
      @toggle-feature-menu="toggleFeatureMenu"
      @stop-generating="stopGenerating"
      ref="inputArea"
    />
    
    <!-- 功能菜单 -->
    <FeatureMenu 
      :show="showFeatureMenu"
      :features="availableFeatures"
      @close="closeFeatureMenu"
      @select="handleFeatureSelect"
    />
  </view>
</template>

<script>
import MessageList from './message/MessageList.vue';
import ParamsPanel from './params/ParamsPanel.vue';
import PromptTags from './params/PromptTags.vue';
import InputArea from './input/InputArea.vue';
import FeatureMenu from './input/FeatureMenu.vue';

export default {
  name: 'TextEditorPage',
  components: {
    MessageList,
    ParamsPanel,
    PromptTags,
    InputArea,
    FeatureMenu
  },
  props: {
    pageConfig: {
      type: Object,
      default: () => ({
        title: 'AI智能写作助手'
      })
    }
  },
  data() {
    return {
      messages: [],
      inputText: '',
      isCollapsed: true,
      isAnimating: false,
      isH5Platform: false,
      mediaItems: [],
      presetPromptTags: [
        { title: '写一篇文章', content: '请写一篇关于[主题]的文章，字数在800字左右，要求内容充实，逻辑清晰。' },
        { title: '生成提纲', content: '请为[主题]生成一个详细的提纲，包括引言、正文和结论部分。' },
        { title: '润色文章', content: '请帮我润色以下文章，提升语言表达和文采，但保持原意：\n[文章内容]' },
        { title: '创意写作', content: '请以[主题]为灵感，创作一篇有创意的短文，字数在500字左右。' },
        { title: '内容扩写', content: '请将以下内容进行扩写，使其更加详细生动：\n[简短内容]' },
        { title: '总结内容', content: '请将以下文章进行总结，提炼出核心观点：\n[文章内容]' },
        { title: '改写段落', content: '请用不同的表达方式改写以下段落，保持原意但使用不同的词汇和句式：\n[段落内容]' },
        { title: '写作建议', content: '我正在写一篇关于[主题]的文章，请给我一些写作建议和注意事项。' },
        { title: '标题生成', content: '请为我的文章生成10个吸引人的标题，主题是：[主题]' },
        { title: '文案创作', content: '请为[产品/服务]创作一段吸引人的营销文案，突出其核心优势。' },
        { title: '问答生成', content: '请围绕[主题]生成5个常见问题及其详细解答。' },
        { title: '故事创作', content: '请以[主题/角色]为核心创作一个短篇故事，字数在1000字左右。' },
        { title: '观点阐述', content: '请分析[主题]的不同观点，并给出客观的评价。' },
        { title: '比较分析', content: '请比较[A]和[B]的异同点，并进行深入分析。' },
        { title: '步骤说明', content: '请详细说明如何完成[任务/过程]的步骤。' }
      ],
      typeTags: [
        { title: '论文', content: '学术论文' },
        { title: '散文', content: '文学散文' },
        { title: '报告', content: '工作报告' },
        { title: '教程', content: '教学教程' },
        { title: '小说', content: '小说创作' },
        { title: '诗歌', content: '诗歌创作' },
        { title: '剧本', content: '剧本创作' },
        { title: '演讲稿', content: '演讲稿撰写' },
        { title: '广告文案', content: '广告文案创作' },
        { title: '新闻稿', content: '新闻稿撰写' },
        { title: '产品说明', content: '产品说明文档' },
        { title: '技术文档', content: '技术文档编写' },
        { title: '博客文章', content: '博客文章创作' },
        { title: '社交媒体', content: '社交媒体内容' },
        { title: '电子邮件', content: '电子邮件内容' },
        { title: '研究报告', content: '研究报告编写' },
        { title: '简历', content: '个人简历编写' },
        { title: '提案', content: '项目提案文档' },
        { title: '用户手册', content: '用户手册编写' },
        { title: '评论', content: '评论文章创作' }
      ],
      styleTags: [
        { title: '正式', content: '正式学术风格' },
        { title: '幽默', content: '幽默诙谐风格' },
        { title: '简洁', content: '简洁明了风格' },
        { title: '文学', content: '文学艺术风格' },
        { title: '专业', content: '专业技术风格' },
        { title: '故事化', content: '故事化叙述风格' },
        { title: '对话式', content: '对话式交流风格' },
        { title: '感性', content: '感性抒情风格' },
        { title: '理性', content: '理性分析风格' },
        { title: '古风', content: '古典文言风格' },
        { title: '现代', content: '现代口语风格' },
        { title: '科技', content: '科技前沿风格' },
        { title: '商务', content: '商务专业风格' },
        { title: '新闻', content: '新闻报道风格' },
        { title: '教学', content: '教学指导风格' },
        { title: '励志', content: '励志鼓舞风格' },
        { title: '批判', content: '批判分析风格' },
        { title: '戏剧', content: '戏剧张力风格' },
        { title: '科普', content: '科普通俗风格' },
        { title: '童话', content: '童话故事风格' }
      ],
      placeholderTexts: [
        '请输入您的问题或描述...',
        '想要写作什么内容呢？',
        '需要AI怎么帮助您？',
        '有什么创作需求？'
      ],
      currentPlaceholder: '请输入您的问题或描述...',
      isGenerating: false,
      coinCost: 10,
      isSpeaking: false,
      currentSpeakingIndex: -1,
      showFeatureMenu: false,
      availableFeatures: [
        {
          name: '上传图片',
          icon: '🖼️',
          action: 'uploadImage',
          description: '上传图片进行分析或生成相关内容',
          color: 'rgba(64, 169, 151, 0.8)'
        },
        {
          name: '上传文档',
          icon: '📄',
          action: 'uploadDocument',
          description: '上传文档进行分析或摘要',
          color: 'rgba(82, 130, 201, 0.8)'
        },
        {
          name: '语音输入',
          icon: '🎤',
          action: 'voiceInput',
          description: '使用语音输入内容',
          color: 'rgba(110, 86, 207, 0.8)'
        },
        {
          name: '从历史选择',
          icon: '📋',
          action: 'selectFromHistory',
          description: '从历史记录中选择内容',
          color: 'rgba(130, 95, 185, 0.8)'
        },
        {
          name: '拍照识别',
          icon: '📸',
          action: 'takePhoto',
          description: '拍照并识别内容',
          color: 'rgba(59, 126, 164, 0.8)'
        }
      ]
    };
  },
  created() {
    this.detectPlatform();
    this.initMessages();
  },
  mounted() {
    // 初始化欢迎消息
    if (this.messages.length === 0) {
      this.addAIMessage('您好！我是您的AI写作助手，请问有什么可以帮助您的？');
    }
  },
  methods: {
    detectPlatform() {
      // 检测H5平台
      // #ifdef H5
      this.isH5Platform = true;
      // #endif
    },
    
    initMessages() {
      // 初始化消息列表
      this.messages = [];
    },
    
    goBack() {
      try {
        uni.navigateBack({
          fail: () => {
            uni.switchTab({
              url: '/pages/index/index'
            });
          }
        });
      } catch (e) {
        console.error('返回失败:', e);
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    },
    
    toggleExpand() {
      this.isAnimating = true;
      this.isCollapsed = !this.isCollapsed;
      
      // 400ms后关闭动画标志
      setTimeout(() => {
        this.isAnimating = false;
      }, 400);
    },
    
    addPromptToInput(tag) {
      if (!tag || !tag.content) return;
      
      if (this.inputText) {
        this.inputText += '\n' + tag.content;
      } else {
        this.inputText = tag.content;
      }
      
      this.$refs.inputArea?.focus();
    },
    
    handleInputFocus() {
      // 输入框获取焦点时，可以添加逻辑
    },
    
    handleInputBlur() {
      // 输入框失去焦点时，可以添加逻辑
    },
    
    handlePaste(e) {
      // 处理粘贴事件，可以检测图片等媒体内容
      const clipboardItems = e.clipboardData && e.clipboardData.items;
      if (clipboardItems) {
        for (let i = 0; i < clipboardItems.length; i++) {
          const item = clipboardItems[i];
          if (item.type.indexOf('image') !== -1) {
            // 处理粘贴的图片
            const blob = item.getAsFile();
            this.handleImageUpload(blob);
            // 阻止默认粘贴行为
            e.preventDefault();
            return;
          }
        }
      }
    },
    
    handleTextareaHeightChange() {
      // 文本区域高度变化时，可以调整UI布局
    },
    
    sendMessage() {
      if (!this.inputText.trim() && this.mediaItems.length === 0) return;
      
      // 添加用户消息
      const userMessage = {
        type: 'user',
        content: this.inputText,
        timestamp: Date.now(),
        media: [...this.mediaItems]
      };
      
      this.messages.push(userMessage);
      
      // 清空输入区域和媒体项
      this.inputText = '';
      this.mediaItems = [];
      
      // 模拟AI回复
      this.generateAIResponse(userMessage);
    },
    
    generateAIResponse(userMessage) {
      // 开始生成状态
      this.isGenerating = true;
      
      // 模拟AI思考时间
      setTimeout(() => {
        // 添加AI回复消息
        this.addAIMessage(`收到您的消息，这是对"${userMessage.content.substring(0, 20)}${userMessage.content.length > 20 ? '...' : ''}"的回复...`);
        
        // 结束生成状态
        this.isGenerating = false;
      }, 1500);
    },
    
    addAIMessage(content) {
      this.messages.push({
        type: 'ai',
        content,
        timestamp: Date.now()
      });
    },
    
    toggleFeatureMenu() {
      this.showFeatureMenu = !this.showFeatureMenu;
    },
    
    closeFeatureMenu() {
      this.showFeatureMenu = false;
    },
    
    handleFeatureSelect(feature) {
      if (!feature || !feature.action) return;
      
      switch (feature.action) {
        case 'uploadImage':
          this.chooseImage();
          break;
        case 'uploadDocument':
          this.chooseDocument();
          break;
        case 'voiceInput':
          this.startVoiceInput();
          break;
        case 'selectFromHistory':
          this.openHistorySelection();
          break;
        case 'takePhoto':
          this.takePhoto();
          break;
        default:
          console.log('未知的功能动作:', feature.action);
      }
    },
    
    chooseImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.handleImageUpload(tempFilePath);
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
        }
      });
    },
    
    handleImageUpload(file) {
      // 处理图片上传逻辑
      const imageItem = {
        type: 'image',
        src: typeof file === 'string' ? file : URL.createObjectURL(file),
        name: typeof file === 'string' ? 'image.png' : file.name || 'image.png'
      };
      
      this.mediaItems.push(imageItem);
    },
    
    chooseDocument() {
      // 在支持的平台上选择文档
      // #ifdef H5
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.pdf,.doc,.docx,.txt';
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          this.handleDocumentUpload(file);
        }
      };
      input.click();
      // #endif
      
      // #ifdef APP-PLUS
      uni.chooseFile({
        count: 1,
        extension: ['.pdf', '.doc', '.docx', '.txt'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.handleDocumentUpload({
            name: res.tempFiles[0].name,
            src: tempFilePath
          });
        }
      });
      // #endif
    },
    
    handleDocumentUpload(file) {
      // 处理文档上传逻辑
      const docItem = {
        type: 'document',
        src: typeof file === 'string' ? file : URL.createObjectURL(file),
        name: file.name || '文档.pdf',
        size: file.size ? this.formatFileSize(file.size) : ''
      };
      
      this.mediaItems.push(docItem);
    },
    
    formatFileSize(bytes) {
      if (bytes < 1024) return bytes + ' B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    },
    
    startVoiceInput() {
      // 开始语音输入
      uni.showToast({
        title: '语音输入功能暂未实现',
        icon: 'none'
      });
    },
    
    openHistorySelection() {
      // 打开历史记录选择
      uni.showToast({
        title: '历史记录功能暂未实现',
        icon: 'none'
      });
    },
    
    takePhoto() {
      // 拍照功能
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.handleImageUpload(tempFilePath);
        }
      });
    },
    
    previewImage(src) {
      if (!src) return;
      
      uni.previewImage({
        urls: [src],
        current: src
      });
    },
    
    handleImageLongPress(src) {
      // 处理图片长按事件
      uni.showActionSheet({
        itemList: ['保存图片', '删除'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 保存图片
            uni.saveImageToPhotosAlbum({
              filePath: src,
              success: () => {
                uni.showToast({
                  title: '已保存到相册',
                  icon: 'success'
                });
              }
            });
          } else if (res.tapIndex === 1) {
            // 删除图片
            this.mediaItems = this.mediaItems.filter(item => item.src !== src);
          }
        }
      });
    },
    
    openDocument(media) {
      // 打开文档
      if (!media || !media.src) return;
      
      // #ifdef H5
      window.open(media.src, '_blank');
      // #endif
      
      // #ifdef APP-PLUS
      uni.openDocument({
        filePath: media.src,
        success: () => {
          console.log('打开文档成功');
        },
        fail: () => {
          uni.showToast({
            title: '打开文档失败',
            icon: 'none'
          });
        }
      });
      // #endif
    },
    
    removeMediaItem(index) {
      if (index < 0 || index >= this.mediaItems.length) return;
      
      this.mediaItems.splice(index, 1);
    },
    
    copyMessage(content) {
      if (!content) return;
      
      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'none'
          });
        }
      });
    },
    
    speakMessage(event) {
      const { content, index } = event;
      if (!content) return;
      
      if (this.isSpeaking) {
        this.stopSpeaking();
        return;
      }
      
      // 开始语音合成
      this.isSpeaking = true;
      this.currentSpeakingIndex = index;
      
      // #ifdef H5
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(content);
        utterance.lang = 'zh-CN';
        utterance.onend = () => {
          this.isSpeaking = false;
          this.currentSpeakingIndex = -1;
        };
        speechSynthesis.speak(utterance);
      } else {
        uni.showToast({
          title: '当前环境不支持语音合成',
          icon: 'none'
        });
        this.isSpeaking = false;
        this.currentSpeakingIndex = -1;
      }
      // #endif
      
      // #ifdef APP-PLUS
      // App环境下需要使用原生API实现语音合成
      this.isSpeaking = false;
      this.currentSpeakingIndex = -1;
      uni.showToast({
        title: 'App环境暂不支持语音合成',
        icon: 'none'
      });
      // #endif
    },
    
    stopSpeaking() {
      // #ifdef H5
      if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
      }
      // #endif
      
      this.isSpeaking = false;
      this.currentSpeakingIndex = -1;
    },
    
    regenerateMessage(index) {
      // 重新生成消息
      if (index < 0 || index >= this.messages.length) return;
      
      // 找到用户最后一条消息
      let userMessageIndex = -1;
      for (let i = index - 1; i >= 0; i--) {
        if (this.messages[i].type === 'user') {
          userMessageIndex = i;
          break;
        }
      }
      
      if (userMessageIndex === -1) return;
      
      // 删除这条AI消息
      this.messages.splice(index, 1);
      
      // 重新生成回复
      this.generateAIResponse(this.messages[userMessageIndex]);
    },
    
    stopGenerating() {
      // 停止生成
      this.isGenerating = false;
    }
  }
}
</script>

<style scoped>
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #1a1a2e;
  color: #e8e8e8;
  position: relative;
}

.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: rgba(30, 30, 50, 0.8);
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(80, 80, 100, 0.2);
}

.nav-left {
  margin-right: 20rpx;
}

.back-text {
  font-size: 28rpx;
  color: rgba(200, 200, 210, 0.8);
}

.nav-title {
  flex: 1;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

.chat-area {
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: rgba(25, 25, 35, 0.95);
}

/* 媒体查询 - 适应小屏幕设备 */
@media screen and (max-height: 600px) {
  .nav-bar {
    padding: 10rpx;
  }
  
  .nav-title {
    font-size: 32rpx;
  }
}

/* 媒体查询 - 适应大屏幕设备 */
@media screen and (min-height: 1000px) {
  .chat-area {
    max-height: 70vh;
  }
}
</style> 