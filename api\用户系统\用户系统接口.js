/**
 * 用户系统接口
 * 管理用户登录注册、个人信息、设置等功能
 */

import { apiRequest, setAuthToken, clearAuthToken } from '../common/request.js';

// ================================
// 🔐 认证接口
// ================================

/**
 * 用户登录
 * @param {Object} loginData - 登录数据
 */
export async function 用户登录(loginData) {
	const result = await apiRequest('user/login', {
		method: 'POST',
		body: loginData
	});
	
	// 登录成功后保存token
	if (result.success && result.data.token) {
		setAuthToken(result.data.token);
		uni.setStorageSync('userId', result.data.userId);
		uni.setStorageSync('userInfo', result.data.userInfo);
	}
	
	return result;
}

/**
 * 用户注册
 * @param {Object} registerData - 注册数据
 */
export async function 用户注册(registerData) {
	return await apiRequest('user/register', {
		method: 'POST',
		body: registerData
	});
}

/**
 * 用户登出
 */
export async function 用户登出() {
	try {
		await apiRequest('user/logout', {
			method: 'POST'
		});
	} catch (error) {
		console.error('登出请求失败:', error);
	} finally {
		// 清除本地存储
		clearAuthToken();
		uni.removeStorageSync('userId');
		uni.removeStorageSync('userInfo');
	}
}

/**
 * 刷新token
 */
export async function 刷新token() {
	const result = await apiRequest('user/refresh-token', {
		method: 'POST'
	});
	
	if (result.success && result.data.token) {
		setAuthToken(result.data.token);
	}
	
	return result;
}

/**
 * 发送验证码
 * @param {Object} params - 验证码参数
 */
export async function 发送验证码(params) {
	return await apiRequest('user/send-code', {
		method: 'POST',
		body: params
	});
}

/**
 * 验证验证码
 * @param {Object} params - 验证参数
 */
export async function 验证验证码(params) {
	return await apiRequest('user/verify-code', {
		method: 'POST',
		body: params
	});
}

// ================================
// 👤 用户信息接口
// ================================

/**
 * 获取用户信息
 */
export async function 获取用户信息() {
	return await apiRequest('user/profile');
}

/**
 * 更新用户信息
 * @param {Object} profileData - 用户信息数据
 */
export async function 更新用户信息(profileData) {
	const result = await apiRequest('user/profile', {
		method: 'PUT',
		body: profileData
	});
	
	// 更新本地存储的用户信息
	if (result.success) {
		uni.setStorageSync('userInfo', result.data);
	}
	
	return result;
}

/**
 * 上传头像
 * @param {File} avatarFile - 头像文件
 */
export async function 上传用户头像(avatarFile) {
	const formData = new FormData();
	formData.append('avatar', avatarFile);
	
	return await apiRequest('user/upload-avatar', {
		method: 'POST',
		body: formData
	});
}

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 */
export async function 修改密码(passwordData) {
	return await apiRequest('user/change-password', {
		method: 'POST',
		body: passwordData
	});
}

/**
 * 绑定手机号
 * @param {Object} phoneData - 手机号数据
 */
export async function 绑定手机号(phoneData) {
	return await apiRequest('user/bind-phone', {
		method: 'POST',
		body: phoneData
	});
}

/**
 * 绑定邮箱
 * @param {Object} emailData - 邮箱数据
 */
export async function 绑定邮箱(emailData) {
	return await apiRequest('user/bind-email', {
		method: 'POST',
		body: emailData
	});
}

// ================================
// ⚙️ 用户设置接口
// ================================

/**
 * 获取用户设置
 */
export async function 获取用户设置() {
	return await apiRequest('user/settings');
}

/**
 * 更新用户设置
 * @param {Object} settings - 设置数据
 */
export async function 更新用户设置(settings) {
	return await apiRequest('user/settings', {
		method: 'PUT',
		body: settings
	});
}

/**
 * 获取隐私设置
 */
export async function 获取隐私设置() {
	return await apiRequest('user/privacy-settings');
}

/**
 * 更新隐私设置
 * @param {Object} privacySettings - 隐私设置
 */
export async function 更新隐私设置(privacySettings) {
	return await apiRequest('user/privacy-settings', {
		method: 'PUT',
		body: privacySettings
	});
}

/**
 * 获取通知设置
 */
export async function 获取通知设置() {
	return await apiRequest('user/notification-settings');
}

/**
 * 更新通知设置
 * @param {Object} notificationSettings - 通知设置
 */
export async function 更新通知设置(notificationSettings) {
	return await apiRequest('user/notification-settings', {
		method: 'PUT',
		body: notificationSettings
	});
}

// ================================
// 📊 用户数据接口
// ================================

/**
 * 获取用户统计
 */
export async function 获取用户统计() {
	return await apiRequest('user/stats');
}

/**
 * 获取用户活动记录
 * @param {Object} params - 查询参数
 */
export async function 获取用户活动记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/activity-log?${queryParams}`);
}

/**
 * 获取用户收藏
 * @param {Object} params - 查询参数
 */
export async function 获取用户收藏(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/favorites?${queryParams}`);
}

/**
 * 添加收藏
 * @param {Object} favoriteData - 收藏数据
 */
export async function 添加收藏(favoriteData) {
	return await apiRequest('user/add-favorite', {
		method: 'POST',
		body: favoriteData
	});
}

/**
 * 取消收藏
 * @param {string} favoriteId - 收藏ID
 */
export async function 取消收藏(favoriteId) {
	return await apiRequest('user/remove-favorite', {
		method: 'DELETE',
		body: { favoriteId }
	});
}

// ================================
// 🎯 社交功能接口
// ================================

/**
 * 获取关注列表
 * @param {Object} params - 查询参数
 */
export async function 获取用户关注列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/following?${queryParams}`);
}

/**
 * 获取粉丝列表
 * @param {Object} params - 查询参数
 */
export async function 获取用户粉丝列表(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/followers?${queryParams}`);
}

/**
 * 关注用户
 * @param {string} userId - 用户ID
 */
export async function 关注用户(userId) {
	return await apiRequest('user/follow', {
		method: 'POST',
		body: { userId }
	});
}

/**
 * 取消关注用户
 * @param {string} userId - 用户ID
 */
export async function 取消关注用户(userId) {
	return await apiRequest('user/unfollow', {
		method: 'POST',
		body: { userId }
	});
}

// ================================
// 🏆 积分等级接口
// ================================

/**
 * 获取用户积分信息
 */
export async function 获取用户积分信息() {
	return await apiRequest('user/points');
}

/**
 * 获取积分记录
 * @param {Object} params - 查询参数
 */
export async function 获取积分记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/points-history?${queryParams}`);
}

/**
 * 签到
 */
export async function 用户签到() {
	return await apiRequest('user/check-in', {
		method: 'POST'
	});
}

/**
 * 获取签到记录
 * @param {Object} params - 查询参数
 */
export async function 获取签到记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`user/check-in-history?${queryParams}`);
}

// ================================
// 🔒 安全接口
// ================================

/**
 * 获取登录设备列表
 */
export async function 获取登录设备列表() {
	return await apiRequest('user/login-devices');
}

/**
 * 踢出设备
 * @param {string} deviceId - 设备ID
 */
export async function 踢出设备(deviceId) {
	return await apiRequest('user/kick-device', {
		method: 'POST',
		body: { deviceId }
	});
}

/**
 * 注销账户
 * @param {Object} params - 注销参数
 */
export async function 注销账户(params) {
	return await apiRequest('user/deactivate-account', {
		method: 'POST',
		body: params
	});
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 完整登录流程
 * @param {Object} loginData - 登录数据
 */
export async function 完整登录流程(loginData) {
	try {
		// 1. 执行登录
		const loginResult = await 用户登录(loginData);
		
		if (!loginResult.success) {
			throw new Error(loginResult.message || '登录失败');
		}
		
		// 2. 获取用户完整信息
		const [userInfo, userStats, userSettings] = await Promise.all([
			获取用户信息(),
			获取用户统计(),
			获取用户设置()
		]);
		
		return {
			success: true,
			data: {
				loginResult: loginResult.data,
				userInfo: userInfo.data,
				userStats: userStats.data,
				userSettings: userSettings.data
			}
		};
		
	} catch (error) {
		console.error('完整登录流程失败:', error);
		throw error;
	}
}

/**
 * 获取用户完整信息
 */
export async function 获取用户完整信息() {
	try {
		const [
			userInfo,
			userStats,
			userSettings,
			pointsInfo
		] = await Promise.all([
			获取用户信息(),
			获取用户统计(),
			获取用户设置(),
			获取用户积分信息()
		]);
		
		return {
			success: true,
			data: {
				userInfo: userInfo.data,
				userStats: userStats.data,
				userSettings: userSettings.data,
				pointsInfo: pointsInfo.data
			}
		};
		
	} catch (error) {
		console.error('获取用户完整信息失败:', error);
		throw error;
	}
}

export default {
	用户登录,
	用户注册,
	用户登出,
	刷新token,
	发送验证码,
	验证验证码,
	获取用户信息,
	更新用户信息,
	上传用户头像,
	修改密码,
	绑定手机号,
	绑定邮箱,
	获取用户设置,
	更新用户设置,
	获取隐私设置,
	更新隐私设置,
	获取通知设置,
	更新通知设置,
	获取用户统计,
	获取用户活动记录,
	获取用户收藏,
	添加收藏,
	取消收藏,
	获取用户关注列表,
	获取用户粉丝列表,
	关注用户,
	取消关注用户,
	获取用户积分信息,
	获取积分记录,
	用户签到,
	获取签到记录,
	获取登录设备列表,
	踢出设备,
	注销账户,
	完整登录流程,
	获取用户完整信息
};
