<template>
	<view 
		class="lyrics-popup"
		:class="{ show: visible }"
		@click.self="closePopup"
	>
		<!-- 歌词容器 -->
		<view class="lyrics-container" :class="{ show: visible }">
			<!-- 顶部栏 -->
			<view class="lyrics-header">
				<view class="header-left">
					<text class="header-title">歌词</text>
				</view>
				<view class="header-right" @click="closePopup">
					<text class="close-btn">✕</text>
				</view>
			</view>
			
			<!-- 歌曲信息 -->
			<view class="song-info">
				<text class="song-title">{{ currentMusic.title }}</text>
				<text class="song-artist">{{ currentMusic.artist }}</text>
			</view>
			
			<!-- 歌词滚动区域 -->
			<scroll-view
				class="lyrics-scroll"
				scroll-y
				:scroll-top="lyricsScrollTop"
				scroll-with-animation
			>
				<view class="lyrics-content">
					<!-- 顶部空白 -->
					<view class="lyrics-spacer"></view>
					
					<!-- 歌词列表 -->
					<view
						v-for="(line, index) in lyricsLines"
						:key="index"
						class="lyrics-line"
						:class="{
							active: index === currentLyricsIndex,
							played: index < currentLyricsIndex,
							upcoming: index > currentLyricsIndex
						}"
						@click="seekToLyrics(index)"
					>
						{{ line.text || line }}
					</view>
					
					<!-- 底部空白 -->
					<view class="lyrics-spacer"></view>
				</view>
			</scroll-view>
			
			<!-- 底部进度条 -->
			<view class="lyrics-footer">
				<view class="progress-info">
					<text class="time-text">{{ formatTime(currentTime) }}</text>
					<text class="time-text">{{ formatTime(duration) }}</text>
				</view>
				<view class="progress-bar" @click="seekToProgress">
					<view class="progress-bg"></view>
					<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					<view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LyricsPopup',
	props: {
		// 是否显示
		visible: {
			type: Boolean,
			default: false
		},
		// 当前音乐
		currentMusic: {
			type: Object,
			default: () => ({
				title: '未知歌曲',
				artist: '未知艺术家',
				lyrics: []
			})
		},
		// 当前播放时间(秒)
		currentTime: {
			type: Number,
			default: 0
		},
		// 总时长(秒)
		duration: {
			type: Number,
			default: 0
		},
		// 当前歌词索引
		currentLyricsIndex: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			lyricsScrollTop: 0
		};
	},
	computed: {
		// 播放进度百分比
		progressPercent() {
			if (this.duration === 0) return 0;
			return (this.currentTime / this.duration) * 100;
		},
		
		// 歌词行数组
		lyricsLines() {
			if (!this.currentMusic.lyrics) return [];
			
			// 如果是字符串,按换行符分割
			if (typeof this.currentMusic.lyrics === 'string') {
				return this.currentMusic.lyrics.split('\n').map(text => ({ text }));
			}
			
			// 如果是数组,直接返回
			return this.currentMusic.lyrics;
		}
	},
	watch: {
		// 监听当前歌词索引变化,自动滚动
		currentLyricsIndex(newIndex) {
			this.scrollToCurrentLyrics(newIndex);
		},
		
		// 监听显示状态变化
		visible(newVal) {
			if (newVal) {
				// 显示时滚动到当前歌词
				this.$nextTick(() => {
					this.scrollToCurrentLyrics(this.currentLyricsIndex);
				});
			}
		}
	},
	methods: {
		// 关闭弹窗
		closePopup() {
			this.$emit('close');
		},
		
		// 格式化时间
		formatTime(seconds) {
			if (!seconds || isNaN(seconds)) return '00:00';
			const min = Math.floor(seconds / 60);
			const sec = Math.floor(seconds % 60);
			return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`;
		},
		
		// 滚动到当前歌词
		scrollToCurrentLyrics(index) {
			// 每行歌词高度约80rpx
			const lineHeight = 80;
			// 让当前歌词显示在中间位置
			const containerHeight = 600; // 容器高度
			const targetScrollTop = Math.max(0, (index * lineHeight) - (containerHeight / 2));
			
			this.lyricsScrollTop = targetScrollTop;
		},
		
		// 点击歌词跳转
		seekToLyrics(index) {
			const line = this.lyricsLines[index];
			if (line.time !== undefined) {
				this.$emit('seek', line.time);
			}
		},
		
		// 点击进度条跳转
		seekToProgress(e) {
			const rect = e.currentTarget.getBoundingClientRect();
			const x = e.detail.x - rect.left;
			const percent = x / rect.width;
			const time = percent * this.duration;
			this.$emit('seek', time);
		}
	}
};
</script>

<style lang="scss" scoped>
.lyrics-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(20rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	pointer-events: none;
	transition: opacity 0.3s;
	
	&.show {
		opacity: 1;
		pointer-events: auto;
	}
}

.lyrics-container {
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
	transform: translateY(100%);
	transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
	
	&.show {
		transform: translateY(0);
	}
}

.lyrics-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	padding-top: calc(30rpx + var(--status-bar-height, 0));
	background: rgba(0, 0, 0, 0.2);
}

.header-left {
	flex: 1;
}

.header-title {
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
}

.header-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-btn {
	color: #fff;
	font-size: 40rpx;
	font-weight: bold;
	opacity: 0.8;
	
	&:active {
		opacity: 1;
	}
}

.song-info {
	padding: 40rpx;
	text-align: center;
	background: rgba(0, 0, 0, 0.1);
}

.song-title {
	display: block;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.song-artist {
	display: block;
	color: rgba(255, 255, 255, 0.7);
	font-size: 24rpx;
}

.lyrics-scroll {
	flex: 1;
	overflow: hidden;
}

.lyrics-content {
	padding: 0 40rpx;
}

.lyrics-spacer {
	height: 400rpx;
}

.lyrics-line {
	padding: 20rpx 0;
	color: rgba(255, 255, 255, 0.5);
	font-size: 28rpx;
	line-height: 50rpx;
	text-align: center;
	transition: all 0.3s;
	cursor: pointer;
	
	&.active {
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		transform: scale(1.1);
	}
	
	&.played {
		color: rgba(255, 255, 255, 0.3);
	}
	
	&.upcoming {
		color: rgba(255, 255, 255, 0.5);
	}
	
	&:active {
		transform: scale(0.95);
	}
}

.lyrics-footer {
	padding: 30rpx 40rpx;
	padding-bottom: calc(30rpx + var(--safe-area-inset-bottom, 0));
	background: rgba(0, 0, 0, 0.2);
}

.progress-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.time-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}

.progress-bar {
	position: relative;
	height: 6rpx;
	border-radius: 3rpx;
	overflow: hidden;
	cursor: pointer;
}

.progress-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.2);
}

.progress-fill {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	background: #fff;
	transition: width 0.3s;
}

.progress-thumb {
	position: absolute;
	top: 50%;
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background: #fff;
	transform: translate(-50%, -50%);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	transition: left 0.3s;
}

/* H5端优化 */
/* #ifdef H5 */
.lyrics-popup {
	backdrop-filter: blur(10px);
}
/* #endif */
</style>

