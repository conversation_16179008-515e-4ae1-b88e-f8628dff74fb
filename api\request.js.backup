/**
 * 请求封装模块
 */

// 安全的base64编码函数，支持中文字符
function safeBase64Encode(str) {
  try {
    // 使用TextEncoder和Uint8Array来处理UTF-8字符
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    const binary = Array.from(data, byte => String.fromCharCode(byte)).join('');
    return btoa(binary);
  } catch (error) {
    // 降级到传统方法
    try {
      return btoa(unescape(encodeURIComponent(str)));
    } catch (fallbackError) {
      console.error('Base64编码失败:', fallbackError);
      return '';
    }
  }
}

// 基础请求设置
// 使用模拟数据模式，不发送实际网络请求
const USE_MOCK_DATA = true;
const BASE_URL = ''; // 空字符串，不发送实际请求
const API_VERSION = '';

// 判断环境，根据环境设置不同的BASE_URL
// const BASE_URL = process.env.NODE_ENV === 'development' 
//   ? 'http://localhost:3000'  // 开发环境
//   : 'https://api.example.com'; // 生产环境

/**
 * 通用请求方法
 * @param {Object} options - 请求配置
 * @returns {Promise} 请求结果
 */
export function request(options) {
  return new Promise((resolve, reject) => {
    // 如果使用模拟数据模式，直接返回模拟数据
    if (USE_MOCK_DATA) {
      console.log(`使用模拟数据: ${options.url}`);
      
      // 根据不同的API路径返回不同的模拟数据
      if (options.url.includes('/scenarios')) {
        // 场景列表
        setTimeout(() => {
          resolve({
            code: 200,
            data: []
          });
        }, 300);
        return;
      }
      
      // 数字人相关模拟数据
      if (options.url.includes('/digital-human')) {
        if (options.url.includes('/preset-models')) {
          // 预设数字人模型
          setTimeout(() => {
            resolve({
              code: 200,
              data: [
                { id: 'model1', name: '安娜', type: '女性', previewUrl: '/static/digital-human/avatar1.jpg' },
                { id: 'model2', name: '杰克', type: '男性', previewUrl: '/static/digital-human/avatar2.jpg' },
                { id: 'model3', name: '莉莉', type: '女性', previewUrl: '/static/digital-human/avatar3.jpg' },
                { id: 'model4', name: '马克', type: '男性', previewUrl: '/static/digital-human/avatar4.jpg' }
              ]
            });
          }, 300);
          return;
        }

        if (options.url.includes('/custom-models')) {
          // 用户自定义数字人模型
          setTimeout(() => {
            // 从本地存储获取自定义模型
            let customModels = uni.getStorageSync('customModels') || [];

            // 如果没有数据，生成一些测试数据
            if (customModels.length === 0) {
              customModels = [
                {
                  id: 'custom_1',
                  name: '我的形象1',
                  previewUrl: 'data:image/svg+xml;base64,' + safeBase64Encode(`
                    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                      <rect width="200" height="200" fill="#FF6B6B"/>
                      <text x="100" y="100" text-anchor="middle" dy="0.3em" fill="white" font-size="16" font-family="Arial">形象1</text>
                    </svg>
                  `),
                  fileType: 'image',
                  type: '自定义',
                  hasAudio: false,
                  uploadTime: new Date(Date.now() - 86400000 * 5).toISOString(),
                  isFavorite: false
                },
                {
                  id: 'custom_2',
                  name: '我的形象2',
                  previewUrl: 'data:image/svg+xml;base64,' + safeBase64Encode(`
                    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                      <rect width="200" height="200" fill="#4ECDC4"/>
                      <text x="100" y="100" text-anchor="middle" dy="0.3em" fill="white" font-size="16" font-family="Arial">形象2</text>
                    </svg>
                  `),
                  fileType: 'video',
                  type: '自定义',
                  hasAudio: true,
                  uploadTime: new Date(Date.now() - 86400000 * 4).toISOString(),
                  isFavorite: true
                },
                {
                  id: 'custom_3',
                  name: '我的形象3',
                  previewUrl: 'data:image/svg+xml;base64,' + safeBase64Encode(`
                    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                      <rect width="200" height="200" fill="#45B7D1"/>
                      <text x="100" y="100" text-anchor="middle" dy="0.3em" fill="white" font-size="16" font-family="Arial">形象3</text>
                    </svg>
                  `),
                  fileType: 'image',
                  type: '自定义',
                  hasAudio: false,
                  uploadTime: new Date(Date.now() - 86400000 * 3).toISOString(),
                  isFavorite: false
                },
                {
                  id: 'custom_4',
                  name: '我的形象4',
                  previewUrl: 'data:image/svg+xml;base64,' + safeBase64Encode(`
                    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                      <rect width="200" height="200" fill="#96CEB4"/>
                      <text x="100" y="100" text-anchor="middle" dy="0.3em" fill="white" font-size="16" font-family="Arial">形象4</text>
                    </svg>
                  `),
                  fileType: 'video',
                  type: '自定义',
                  hasAudio: false,
                  uploadTime: new Date(Date.now() - 86400000 * 2).toISOString(),
                  isFavorite: false
                },
                {
                  id: 'custom_5',
                  name: '我的形象5',
                  previewUrl: 'data:image/svg+xml;base64,' + safeBase64Encode(`
                    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                      <rect width="200" height="200" fill="#FECA57"/>
                      <text x="100" y="100" text-anchor="middle" dy="0.3em" fill="white" font-size="16" font-family="Arial">形象5</text>
                    </svg>
                  `),
                  fileType: 'image',
                  type: '自定义',
                  hasAudio: false,
                  uploadTime: new Date(Date.now() - 86400000 * 1).toISOString(),
                  isFavorite: true
                }
              ];
              // 保存测试数据到本地存储
              uni.setStorageSync('customModels', customModels);
            }

            resolve({
              code: 200,
              data: customModels
            });
          }, 300);
          return;
        }

        if (options.url.includes('/preset-voices')) {
          // 预设音色
          setTimeout(() => {
            resolve({
              code: 200,
              data: [
                { id: 'voice1', name: '标准女声', gender: 'female', description: '清晰自然的女性声音', previewUrl: '/static/digital-human/voice1.mp3' },
                { id: 'voice2', name: '标准男声', gender: 'male', description: '浑厚稳重的男性声音', previewUrl: '/static/digital-human/voice2.mp3' },
                { id: 'voice3', name: '温柔女声', gender: 'female', description: '轻柔舒缓的女性声音', previewUrl: '/static/digital-human/voice3.mp3' },
                { id: 'voice4', name: '活力男声', gender: 'male', description: '充满活力的男性声音', previewUrl: '/static/digital-human/voice4.mp3' }
              ]
            });
          }, 300);
          return;
        }
        
        if (options.url.includes('/user-models')) {
          // 用户自定义模型
          setTimeout(() => {
            resolve({
              code: 200,
              data: []
            });
          }, 300);
          return;
        }
        
        if (options.url.includes('/user-voices')) {
          // 用户自定义音色
          setTimeout(() => {
            resolve({
              code: 200,
              data: []
            });
          }, 300);
          return;
        }
        
        if (options.url.includes('/create')) {
          // 创建数字人视频
          setTimeout(() => {
            resolve({
              code: 200,
              data: {
                taskId: 'task_' + Date.now(),
                cost: 10
              }
            });
          }, 300);
          return;
        }
        
        if (options.url.includes('/task/')) {
          // 任务状态
          setTimeout(() => {
            resolve({
              code: 200,
              data: {
                status: 'completed',
                progress: 100,
                resultUrl: 'https://example.com/video.mp4'
              }
            });
          }, 300);
          return;
        }
      }
      
      // 默认返回空数据
      setTimeout(() => {
        resolve({
          code: 200,
          data: null
        });
      }, 300);
      return;
    }
    
    // 以下是实际网络请求的代码
    // 获取令牌
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    };
    
    // 添加授权头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 完整URL
    const url = `${BASE_URL}${API_VERSION}${options.url}`;
    console.log(`请求URL: ${url}`);
    
    // 发送请求
    uni.request({
      url: url,
      method: options.method || 'GET',
      data: options.data,
      header: header,
      timeout: 10000, // 10秒超时
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 处理认证失败
          uni.removeStorageSync('token');
          uni.removeStorageSync('userInfo');
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          
          // 延迟跳转，等待提示完成
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
          
          reject(new Error('登录已过期'));
        } else {
          // 处理其他错误
          uni.showToast({
            title: res.data?.message || '请求失败',
            icon: 'none'
          });
          reject(new Error(res.data?.message || '请求失败'));
        }
      },
      fail: (err) => {
        console.error(`请求失败: ${url}`, err);
        
        // 不显示网络错误提示，避免频繁弹窗
        // uni.showToast({
        //   title: '网络错误，请稍后再试',
        //   icon: 'none'
        // });
        
        // 返回模拟数据以避免页面崩溃
        if (options.url.includes('/scenarios')) {
          console.log('返回场景模拟数据');
          resolve({
            code: 200,
            data: []
          });
        } else {
          reject(err);
        }
      }
    });
  });
}

/**
 * 上传文件请求
 * @param {Object} options - 上传配置
 * @returns {Promise} 上传结果
 */
export function uploadFile(options) {
  return new Promise((resolve, reject) => {
    // 获取令牌
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      ...options.header
    };
    
    // 添加授权头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 上传文件
    const uploadTask = uni.uploadFile({
      url: `${BASE_URL}${API_VERSION}${options.url}`,
      filePath: options.filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header: header,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(JSON.parse(res.data));
        } else if (res.statusCode === 401) {
          // 处理认证失败
          uni.removeStorageSync('token');
          uni.removeStorageSync('userInfo');
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
          
          reject(new Error('登录已过期'));
        } else {
          // 处理其他错误
          let errorMsg = '上传失败';
          try {
            const errorData = JSON.parse(res.data);
            errorMsg = errorData.message || errorMsg;
          } catch (e) {}
          
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
          
          reject(new Error(errorMsg));
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        reject(err);
      }
    });
    
    // 返回上传任务对象，便于外部控制（如监听进度、取消上传等）
    options.onProgressUpdate && uploadTask.onProgressUpdate(options.onProgressUpdate);
  });
}

/**
 * 下载文件请求
 * @param {Object} options - 下载配置
 * @returns {Promise} 下载结果
 */
export function downloadFile(options) {
  return new Promise((resolve, reject) => {
    // 获取令牌
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      ...options.header
    };
    
    // 添加授权头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 下载文件
    const downloadTask = uni.downloadFile({
      url: options.url.startsWith('http') ? options.url : `${BASE_URL}${API_VERSION}${options.url}`,
      header: header,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.tempFilePath);
        } else {
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          });
          reject(new Error('下载失败'));
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        reject(err);
      }
    });
    
    // 返回下载任务对象，便于外部控制（如监听进度、取消下载等）
    options.onProgressUpdate && downloadTask.onProgressUpdate(options.onProgressUpdate);
  });
}

export default {
  request,
  uploadFile,
  downloadFile
}; 