/**
 * 路由工具类
 * 封装uni-app的路由相关方法，确保在不同平台上正常工作
 */

import { getH5ParamQuery, isH5 } from './h5';
import { initHashRouteHandler } from './routeFix';
import { checkRouteState, triggerPageCheck } from './routeFix';

// 路由初始化标志
let routerInitialized = false

// 保存原始导航方法引用，防止被劫持
const originalNavigationMethods = {
  navigateTo: null,
  redirectTo: null,
  switchTab: null,
  navigateBack: null
};

// 全局路由锁，防止短时间内多次触发路由
let navigationLock = false;
let navigationLockTimeout = null;
let lastNavigationTime = 0;
const NAVIGATION_LOCK_INTERVAL = 300; // 毫秒

// 是否已初始化hashchange处理器
let hashChangeHandlerInitialized = false;

/**
 * 重置导航锁
 */
function resetNavigationLock() {
  if (navigationLockTimeout) {
    clearTimeout(navigationLockTimeout);
  }
  navigationLock = false;
  lastNavigationTime = 0;
}

/**
 * 检查导航锁状态
 * @returns {boolean} 是否被锁定
 */
function checkNavigationLock() {
  const now = Date.now();
  if (navigationLock) {
    return true;
  }
  
  if (now - lastNavigationTime < NAVIGATION_LOCK_INTERVAL) {
    navigationLock = true;
    navigationLockTimeout = setTimeout(() => {
      navigationLock = false;
    }, NAVIGATION_LOCK_INTERVAL);
    console.warn('导航请求过于频繁，已锁定', NAVIGATION_LOCK_INTERVAL, 'ms');
    return true;
  }
  
  lastNavigationTime = now;
  return false;
}

/**
 * 检测并防止路由劫持
 */
function detectAndFixRouteHijacking() {
  try {
    // 检查uni对象
    if (typeof uni === 'undefined') return;
    
    // 保存原始导航方法
    if (!window.__originalNavigationMethods) {
      window.__originalNavigationMethods = {
        navigateTo: uni.navigateTo,
        redirectTo: uni.redirectTo,
        switchTab: uni.switchTab,
        reLaunch: uni.reLaunch
      };
    }
    
    // 检查navigateTo方法是否被替换
    if (uni.navigateTo !== window.__originalNavigationMethods.navigateTo) {
      console.warn('检测到navigateTo方法被劫持，恢复原始方法');
      uni.navigateTo = window.__originalNavigationMethods.navigateTo;
    }
    
    // 检查redirectTo方法是否被替换
    if (uni.redirectTo !== window.__originalNavigationMethods.redirectTo) {
      console.warn('检测到redirectTo方法被劫持，恢复原始方法');
      uni.redirectTo = window.__originalNavigationMethods.redirectTo;
    }
    
    // 检查switchTab方法是否被替换
    if (uni.switchTab !== window.__originalNavigationMethods.switchTab) {
      console.warn('检测到switchTab方法被劫持，恢复原始方法');
      uni.switchTab = window.__originalNavigationMethods.switchTab;
    }
    
    // 检查reLaunch方法是否被替换
    if (uni.reLaunch !== window.__originalNavigationMethods.reLaunch) {
      console.warn('检测到reLaunch方法被劫持，恢复原始方法');
      uni.reLaunch = window.__originalNavigationMethods.reLaunch;
    }
  } catch (e) {
    console.error('检测路由劫持失败:', e);
  }
}

/**
 * 紧急恢复系统 - 当检测到页面持续加载超过一定时间时触发
 */
function setupEmergencyRecovery() {
  if (!isH5()) return;
  
  console.log('设置紧急恢复系统');
  
  // 页面加载检测
  let loadingStartTime = Date.now();
  let isPageStuck = false;
  
  // 检测页面是否卡在加载状态
  setInterval(() => {
    // 检查页面是否有加载指示器或遮罩层
    const loadingIndicators = document.querySelectorAll('.loading, .loading-mask, .spinner, .loader');
    
    if (loadingIndicators.length > 0) {
      // 页面正在加载中
      if (!isPageStuck) {
        loadingStartTime = Date.now();
        isPageStuck = true;
      } else {
        // 检查是否加载时间过长
        const loadingDuration = Date.now() - loadingStartTime;
        if (loadingDuration > 15000) { // 15秒
          console.warn('检测到页面加载时间过长，尝试恢复');
          forceRecoverPage();
          isPageStuck = false; // 重置状态
        }
      }
    } else {
      // 页面不在加载状态
      isPageStuck = false;
    }
  }, 5000);
  
  // 添加全局错误处理
  window.addEventListener('error', (event) => {
    console.error('捕获到全局错误:', event.error);
    
    // 检查是否与路由相关的错误
    if (event.error && event.error.stack && 
        (event.error.stack.includes('router') || 
         event.error.stack.includes('hashchange') || 
         event.error.stack.includes('navigate'))) {
      console.warn('检测到路由相关错误，尝试恢复');
      // 取消可能的进行中事件
      event.preventDefault();
      
      // 尝试恢复
      setTimeout(() => {
        forceRecoverPage();
      }, 100);
    }
  });
  
  // 监听未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    
    // 如果是路由相关的拒绝，尝试恢复
    if (event.reason && event.reason.stack && 
        (event.reason.stack.includes('router') || 
         event.reason.stack.includes('navigate'))) {
      console.warn('检测到路由相关Promise拒绝，尝试恢复');
      setTimeout(() => {
        forceRecoverPage();
      }, 100);
    }
  });
  
  // 添加紧急恢复按钮（长按Escape键触发）
  let escKeyPressStart = 0;
  window.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      if (escKeyPressStart === 0) {
        escKeyPressStart = Date.now();
      } else {
        // 检查是否长按超过2秒
        if (Date.now() - escKeyPressStart > 2000) {
          console.log('检测到Escape键长按，执行紧急恢复');
          forceRecoverPage();
          escKeyPressStart = 0;
        }
      }
    }
  });
  
  window.addEventListener('keyup', (event) => {
    if (event.key === 'Escape') {
      escKeyPressStart = 0;
    }
  });
  
  console.log('紧急恢复系统设置完成');
}

/**
 * 强制恢复页面到正常状态
 */
function forceRecoverPage() {
  try {
    console.log('执行强制页面恢复');
    
    // 1. 移除所有加载指示器和遮罩
    const elements = document.querySelectorAll('.loading, .loading-mask, .spinner, .loader, .overlay, .modal, .popup');
    elements.forEach(el => {
      if (el && el.parentNode) {
        try {
          el.style.display = 'none';
          el.style.visibility = 'hidden';
          el.style.opacity = '0';
        } catch (e) {}
      }
    });
    
    // 2. 恢复body样式
    document.body.style.overflow = '';
    document.body.style.position = '';
    document.body.style.height = '';
    document.body.classList.remove('modal-open', 'overflow-hidden', 'fixed');
    
    // 3. 重置所有全局处理状态
    window.__processingHashChange = false;
    window.__manualHashChange = false;
    window.__preventAutoHashChange = false;
    
    // 4. 尝试刷新主视图
    const mainView = document.querySelector('.uni-app') || document.querySelector('#app');
    if (mainView) {
      mainView.style.display = 'none';
      void mainView.offsetHeight; // 强制回流
      setTimeout(() => {
        mainView.style.display = '';
      }, 50);
    }
    
    // 5. 触发自定义恢复事件
    window.dispatchEvent(new CustomEvent('emergency:recover', {
      detail: { timestamp: Date.now() }
    }));
    
    console.log('强制页面恢复完成');
    return true;
  } catch (err) {
    console.error('强制恢复页面失败:', err);
    return false;
  }
}

/**
 * 初始化路由系统
 * 确保路由系统只被初始化一次
 */
export function initRouter() {
  if (routerInitialized) {
    // 即使已初始化，也检查一次劫持
    detectAndFixRouteHijacking();
    return
  }
  
  console.log('初始化路由系统...')
  
  // 确保uni对象存在
  if (typeof uni === 'undefined') {
    console.error('uni对象不存在，路由初始化失败')
    return
  }
  
  // 在H5环境下修复路由
  if (isH5()) {
    // 设置紧急恢复系统
    setupEmergencyRecovery();
    
    // 应用路由修复，使用try-catch避免阻塞
    try {
      fixRouter();
    } catch (err) {
      console.error('应用路由修复失败:', err);
    }
  }
  
  // 保存原始的navigateTo方法
  originalNavigationMethods.navigateTo = uni.navigateTo;
  originalNavigationMethods.redirectTo = uni.redirectTo;
  originalNavigationMethods.switchTab = uni.switchTab;
  originalNavigationMethods.navigateBack = uni.navigateBack;
  
  // 重写navigateTo方法
  uni.navigateTo = function(options) {
    console.log('路由跳转：', options)
    try {
      return originalNavigationMethods.navigateTo.call(this, options)
    } catch (error) {
      console.error('路由跳转失败：', error)
      // 备用方案：使用location跳转（仅在H5环境）
      if (process.env.UNI_PLATFORM === 'h5') {
        const url = options.url
        if (url.startsWith('/pages/')) {
          const path = url.split('?')[0]
          window.location.href = '#' + path
          if (options.success) options.success()
          return
        }
      }
      if (options.fail) options.fail(error)
    } finally {
      if (options.complete) options.complete()
    }
  }
  
  // 同样处理其他导航方法
  uni.switchTab = function(options) {
    console.log('切换标签页：', options)
    try {
      return originalNavigationMethods.switchTab.call(this, options)
    } catch (error) {
      console.error('切换标签页失败：', error)
      if (options.fail) options.fail(error)
    } finally {
      if (options.complete) options.complete()
    }
  }
  
  uni.redirectTo = function(options) {
    console.log('路由重定向：', options)
    try {
      return originalNavigationMethods.redirectTo.call(this, options)
    } catch (error) {
      console.error('路由重定向失败：', error)
      if (options.fail) options.fail(error)
    } finally {
      if (options.complete) options.complete()
    }
  }
  
  // 设置定期检查劫持
  if (typeof window !== 'undefined') {
    // 每3秒检查一次路由是否被劫持
    setInterval(detectAndFixRouteHijacking, 3000);
  }
  
  routerInitialized = true
  console.log('路由系统初始化完成')
}

/**
 * 旧版安全的路由跳转方法
 * 当原生方法失败时提供备选方案
 * @param {Object} options 路由参数
 */
export function legacyNavigateTo(options) {
  if (!routerInitialized) {
    initRouter()
  }
  
  try {
    uni.navigateTo(options)
  } catch (error) {
    console.error('安全路由跳转失败，尝试备选方案：', error)
    // 如果是H5，尝试使用window.location
    if (process.env.UNI_PLATFORM === 'h5') {
      const url = options.url.split('?')[0]
      window.location.href = `#${url}`
    }
  }
}

/**
 * 获取当前路由信息
 * @returns {Object} 当前路由信息
 */
export function getCurrentRoute() {
  let pages = getCurrentPages()
  if (pages.length > 0) {
    let currentPage = pages[pages.length - 1]
    return {
      route: currentPage.route,
      options: currentPage.options || {}
    }
  }
  return null
}

// 加载完毕后初始化路由
setTimeout(() => {
  initRouter()
  
  // 强制处理H5环境下的路由问题
  if (isH5()) {
    console.log('H5环境，强制应用路由修复');
    fixRouter();
    
    // 为window对象添加路由辅助方法，便于全局访问
    window.__routerFixed = true;
    window.__forceSafeNavigate = (url) => {
      try {
        console.log('强制导航被调用:', url);
        const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
        
        // 先执行页面状态整理，确保当前页面处理完毕
        if (typeof window.__VUE_APP__ !== 'undefined') {
          console.log('尝试通知Vue应用准备导航');
          // 通知Vue应用即将导航
        }
        
        // 强制导航
        setTimeout(() => {
          window.location.href = '#/' + cleanUrl;
          console.log('强制导航执行完成');
        }, 50);
        
        return true;
      } catch (e) {
        console.error('强制导航失败:', e);
        return false;
      }
    };
  }
}, 100)

/**
 * 获取当前视图路径
 * 用于检测当前DOM中显示的页面视图
 * @returns {string} 当前视图路径
 */
export function getCurrentView() {
  if (!isH5()) return '';
  
  try {
    // 获取当前页面信息
    const pages = getCurrentPages();
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.route) {
        return currentPage.route;
      }
    }
    
    // DOM检测方法（备用）
    // 检查特定页面的容器是否存在
    const containers = {
      'pages/index': '.home-container, .index-container',
      'pages/create/text': '.text-create-container',
      'pages/create/image': '.image-create-container',
      'pages/create/video': '.video-create-container',
      'pages/create/music': '.music-create-container',
      'pages/user': '.user-container, .profile-container',
      'pages/create/text/result': '.text-result-container',
      'pages/create/image/result': '.image-result-container',
      'pages/create/video/result': '.video-result-container',
      'pages/create/music/result': '.music-result-container'
    };
    
    for (const [path, selector] of Object.entries(containers)) {
      if (document.querySelector(selector)) {
        return path;
      }
    }
    
    // 分析当前URL
    const hash = window.location.hash;
    if (hash) {
      const path = hash.replace(/^#\/?/, '').split('?')[0];
      if (path) return path;
    }
    
    return '';
  } catch (e) {
    console.error('获取当前视图出错:', e);
    return '';
  }
}

// 路由增强设置
export function setupRouter(app) {
  console.log('设置路由增强功能')
  // 可以在这里添加全局路由拦截、权限验证等功能
}

/**
 * 特殊处理H5环境下的路由
 * @param {string} url 路由URL
 * @returns {boolean} 是否成功处理
 */
const handleH5Navigation = (url) => {
  if (!isH5()) return false;
  
  try {
    // 移除开头的/并确保格式正确
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
    
    // 直接使用location.href进行跳转（绝对可靠的方式）
    if (cleanUrl.includes('://')) {
      // 如果是外部链接
      window.location.href = cleanUrl;
    } else {
      // 内部链接使用hash
      window.location.href = '#/' + cleanUrl;
    }
    
    console.log('H5直接导航成功:', cleanUrl);
    return true;
  } catch (e) {
    console.error('H5导航失败:', e);
    return false;
  }
};

/**
 * 安全导航方法(兼容所有环境)
 */
export const safeNavigateTo = (url, options = {}) => {
  if (checkNavigationLock()) {
    console.warn('导航被锁定，忽略请求:', url);
    return false;
  }
  
  try {
    if (isH5()) {
      // 在H5环境中，先检测并修复路由劫持
      detectAndFixRouteHijacking();
      
      // 使用超可靠导航方法
      return ultraReliableNavigate(url, options);
    } else {
      // 非H5环境，使用uni-app原生方法
      return ultraReliableNavigate(url, options);
    }
  } catch (err) {
    console.error('安全导航时出错:', err);
    // 尝试使用基本导航作为回退
    try {
      uni.navigateTo({
        url: url,
        ...options,
        fail: (err) => {
          console.error('基本导航回退也失败:', err);
        }
      });
    } catch (fallbackErr) {
      console.error('安全导航和回退都失败:', fallbackErr);
    }
    return false;
  }
};

/**
 * 标准导航方法，供外部组件和模块使用
 */
export const enhancedNavigateTo = (url, options = {}) => safeNavigateTo(url, options);
export const enhancedRedirectTo = (url, options = {}) => safeNavigateTo(url, { ...options, isRedirect: true });
export const enhancedSwitchTab = (url, options = {}) => safeNavigateTo(url, { ...options, isTab: true });
export const enhancedReLaunch = (url, options = {}) => safeNavigateTo(url, { ...options, isReLaunch: true });

/**
 * 导出标准导航方法的别名，确保与旧代码兼容
 */
export const navigateTo = enhancedNavigateTo;
export const redirectTo = enhancedRedirectTo;
export const switchTab = enhancedSwitchTab;
export const reLaunch = enhancedReLaunch;

/**
 * 返回上一页
 * @param {number} delta 返回的页面数，默认1
 */
export const navigateBack = (delta = 1) => {
  uni.navigateBack({
    delta: delta,
    fail: function(err) {
      console.error('返回失败:', err);
      // 如果返回失败，可能是没有上一页，尝试跳转到首页
      safeNavigateTo('/pages/index/index');
    }
  });
};

/**
 * 重新加载当前页面
 */
export const reloadPage = () => {
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    const currentUrl = `/${currentPage.route}`;
    
    // 使用redirectTo重新加载页面
    uni.redirectTo({
      url: currentUrl,
      fail: (err) => {
        console.error('重新加载页面失败:', err);
      }
    });
  }
};

// 处理hash变化事件
function handleHashChange(event) {
  // 防止重复处理
  if (window.__processingHashChange || window.__forceRefreshing) {
    console.log('已有hash变更处理正在进行，跳过');
    return;
  }
  
  // 设置处理标志
  window.__processingHashChange = true;
  
  try {
    console.log('处理hash变更事件:', event);
    
    // 获取新旧URL
    const oldUrl = event.oldURL || '';
    const newUrl = event.newURL || window.location.href;
    
    // 解析出hash路径
    const oldHash = oldUrl.split('#')[1] || '';
    const newHash = newUrl.split('#')[1] || '';
    
    console.log('hash变更:', oldHash, '->', newHash);
    
    // 检查特殊路径 - 重点关注文本创作页面
    if (newHash && newHash.includes('pages/create/text')) {
      console.log('检测到导航到文本创作页面，添加特殊处理');
      
      // 延迟检查页面是否正确加载
      setTimeout(() => {
        // 检查关键DOM元素是否存在
        const textContainer = document.querySelector('.chat-page, .text-create-container');
        if (!textContainer) {
          console.warn('文本创作页面容器不存在，尝试强制刷新');
          checkAndFixRouteState(0);
        } else if (textContainer.children.length === 0) {
          console.warn('文本创作页面容器为空，尝试强制刷新');
          checkAndFixRouteState(0);
        } else {
          console.log('文本创作页面加载正常');
        }
      }, 800); // 稍微延长检查时间，确保页面有足够时间加载
    }
    
    // 如果hash没有实质变化，可能是重复触发
    if (oldHash === newHash && newHash !== '') {
      console.log('hash没有变化，尝试刷新页面状态');
      
      // 触发页面检查但不直接刷新
      setTimeout(() => {
        if (checkAndFixRouteState(0)) {
          console.log('检测到路由状态不正确，已触发修复');
        }
      }, 300);
      
      // 重置处理标志
      window.__processingHashChange = false;
      return;
    }
    
    // 通知应用哈希已经变更
    try {
      if (typeof window.dispatchEvent === 'function') {
        window.dispatchEvent(new CustomEvent('router:hashChanged', {
          detail: {
            oldHash,
            newHash,
            timestamp: Date.now()
          }
        }));
      }
    } catch (e) {
      console.warn('发送hash变更事件失败:', e);
    }
    
    // 延迟检查页面是否正确加载
    setTimeout(() => {
      const hash = window.location.hash.replace(/^#\/?/, '');
      console.log('检查页面是否正确加载:', hash);
      
      // 尝试查找页面容器
      const pageContainer = document.querySelector('.chat-page, .uni-page-body, .content');
      if (!pageContainer) {
        console.warn('未找到页面容器，可能加载失败，尝试修复');
        checkAndFixRouteState(0);
      } else if (pageContainer.children.length === 0) {
        console.warn('页面容器为空，可能加载失败，尝试修复');
        checkAndFixRouteState(0);
      } else {
        console.log('页面容器检查通过');
      }
      
      // 重置处理标志
      window.__processingHashChange = false;
    }, 500);
  } catch (e) {
    console.error('处理hash变更时出错:', e);
    
    // 出错也要重置标志
    window.__processingHashChange = false;
    
    // 尝试检查并修复路由状态
    setTimeout(() => {
      checkAndFixRouteState(0);
    }, 500);
  }
}

/**
 * 修复页面路由系统，处理hash变更后页面不刷新的问题
 * 特别适用于H5环境下的hash路由
 */
export function fixRouter() {
  // 检查是否是H5环境
  if (!isH5()) return;

  // 首先备份原始的onhashchange方法
  const originalHashChangeHandler = window.onhashchange;
  
  // 包装hashchange处理函数，确保页面更新
  const handleHashChangeWrapper = function(event) {
    // 防止重复处理
    if (window.__currentlyProcessingHash) {
      console.log('已有hash变更正在处理中，跳过');
      return;
    }
    
    // 设置处理中标记
    window.__currentlyProcessingHash = true;
    
    // 调用原始处理方法
    if (typeof handleHashChange === 'function') {
      window.__processingHashChange = true;
      handleHashChange(event);
      
      // 确保页面被正确更新，但控制调用频率
      setTimeout(() => {
        ensurePageUpdate();
        
        // 处理完成后重置标记
        setTimeout(() => {
          window.__processingHashChange = false;
          window.__currentlyProcessingHash = false;
          // 通知辅助路由模块此次hash变更已被主路由处理
          window.__routeFixHashChangeHandled = true;
        }, 300);
      }, 50);
    } else {
      // 即使没有调用handleHashChange，也需要重置处理标记
      setTimeout(() => {
        window.__currentlyProcessingHash = false;
      }, 300);
    }
  };
  
  // 确保页面被正确更新
  function ensurePageUpdate() {
    try {
      // 防止频繁调用
      if (window.__lastPageUpdateTime && Date.now() - window.__lastPageUpdateTime < 500) {
        console.log('页面更新过于频繁，忽略本次更新');
        return;
      }
      
      // 记录本次更新时间
      window.__lastPageUpdateTime = Date.now();
      
      // 获取当前视图和URL
      const currentView = getCurrentView();
      const currentUrl = window.location.href;
      const currentHash = window.location.hash;
      
      // 检查视图是否与URL匹配
      let viewMatchesUrl = false;
      if (currentView && currentHash) {
        const path = currentHash.replace(/^#/, '');
        viewMatchesUrl = currentView.includes(path) || path.includes(currentView);
      }
      
      // 如果视图不匹配URL，刷新页面容器
      if (!viewMatchesUrl) {
        console.log('检测到路由状态不匹配，尝试刷新页面容器');
        
        // 尝试刷新页面容器
        const containers = document.querySelectorAll('.page-container, .uni-page-body, .container, .page');
        
        if (containers && containers.length > 0) {
          console.log('找到页面容器，尝试刷新');
          
          for (let i = 0; i < containers.length; i++) {
            const container = containers[i];
            
            // 触发DOM更新
            if (container.style.display === 'none') {
              container.style.display = 'block';
              setTimeout(() => {
                if (!viewMatchesUrl) {
                  container.style.display = 'none';
                }
              }, 0);
            } else {
              const originalDisplay = container.style.display;
              container.style.display = 'none';
              setTimeout(() => {
                container.style.display = originalDisplay;
              }, 0);
            }
          }
        }
        
        // 派发路由更新事件
        window.dispatchEvent(new CustomEvent('router:updated', {
          detail: {
            url: currentUrl,
            view: currentView,
            timestamp: Date.now()
          }
        }));
      }
      
      // 防止重复触发路由变更
      // 只有在明确需要且时间间隔足够大时再触发
      if (!window.__lastRouteTime || Date.now() - window.__lastRouteTime > 2000) {
        // 控制重新触发路由的次数，避免循环
        if (!window.__routeRetryCount) {
          window.__routeRetryCount = 0;
        }
        
        if (currentHash && !viewMatchesUrl && window.__routeRetryCount < 2) {
          console.log('路由状态不正确，尝试重新触发路由 (尝试次数:', window.__routeRetryCount + 1, ')');
          window.__routeRetryCount++;
          window.__lastRouteTime = Date.now();
          
          // 尝试调用辅助路由的页面检查
          if (typeof triggerPageCheck === 'function') {
            triggerPageCheck();
          }
          
          // 重新触发路由
          setTimeout(() => {
            const event = new HashChangeEvent('hashchange', {
              oldURL: document.location.href,
              newURL: document.location.href
            });
            handleHashChangeWrapper(event);
            
            // 重置重试计数器
            setTimeout(() => {
              window.__routeRetryCount = 0;
            }, 5000);
          }, 100);
        } else if (window.__routeRetryCount >= 2) {
          console.log('已达到最大重试次数，停止路由重试');
        }
      }
    } catch (e) {
      console.error('确保页面更新时出错:', e);
    }
  }
  
  // 移除所有现有的hashchange事件监听器，避免重复
  window.removeEventListener('hashchange', handleHashChange);
  
  // 添加新的事件监听器，使用防抖包装
  const debouncedHashChangeHandler = function(event) {
    // 使用防抖，避免短时间内多次触发
    clearTimeout(window.__hashChangeDebounceTimeout);
    window.__hashChangeDebounceTimeout = setTimeout(() => {
      handleHashChangeWrapper(event);
    }, 100);
  };
  
  window.addEventListener('hashchange', debouncedHashChangeHandler, { capture: true });
  
  // 重写onhashchange属性
  window.onhashchange = debouncedHashChangeHandler;
  
  // 监听辅助路由发出的事件
  window.addEventListener('routeFix:hashChanged', function(event) {
    if (!window.__processingHashChange) {
      console.log('收到辅助路由通知，处理hash变更:', event.detail);
      
      // 创建一个合成事件调用hashchange处理器
      const syntheticEvent = new HashChangeEvent('hashchange', {
        oldURL: document.location.href,
        newURL: document.location.href
      });
      
      handleHashChangeWrapper(syntheticEvent);
    }
  });
  
  window.addEventListener('routeFix:pageNeedsUpdate', function(event) {
    console.log('收到页面需要更新通知:', event.detail);
    ensurePageUpdate();
  });
  
  // 通知辅助路由模块主路由已初始化
  window.dispatchEvent(new CustomEvent('mainRouter:initialized', {
    detail: {
      timestamp: Date.now()
    }
  }));
  
  // 检查当前hash是否存在，如果存在，创建一个合成事件触发hashchange处理
  if (window.location.hash) {
    console.log('初始化时检测到hash存在，触发hashchange处理');
    
    // 创建一个合成事件
    const event = new HashChangeEvent('hashchange', {
      oldURL: document.location.href,
      newURL: document.location.href
    });
    
    // 调用hash变更处理器
    handleHashChangeWrapper(event);
  }
  
  // 设置路由修复完成标记
  window.__routerFixed = true;
  
  console.log('路由修复完成，已启用增强的hash处理');
}

/**
 * 安装自定义路由控制器 - 使用iframe或隐藏DOM元素来辅助路由跳转
 */
function installCustomRouterControl() {
  try {
    if (!isH5()) return;
    
    console.log('安装自定义路由控制器');
    
    // 创建路由辅助容器
    const routerHelper = document.createElement('div');
    routerHelper.id = 'router-helper';
    routerHelper.style.display = 'none';
    document.body.appendChild(routerHelper);
    
    // 创建通用导航函数
    window.__createRouterLink = (path) => {
      try {
        // 清除之前的链接
        routerHelper.innerHTML = '';
        
        // 创建新链接
        const link = document.createElement('a');
        link.href = '#/' + (path.startsWith('/') ? path.substring(1) : path);
        link.textContent = '跳转到' + path;
        link.style.display = 'none';
        
        // 添加到DOM
        routerHelper.appendChild(link);
        
        return link;
      } catch (e) {
        console.error('创建路由链接失败:', e);
        return null;
      }
    };
    
    // 添加DOM辅助导航方法
    window.__navigateByDOM = (path) => {
      const link = window.__createRouterLink(path);
      if (link) {
        try {
          // 模拟点击
          link.click();
          console.log('DOM导航成功执行');
          return true;
        } catch (e) {
          console.error('DOM导航失败:', e);
        }
      }
      return false;
    };
    
    // 添加iframe跳转方法
    window.__navigateByIframe = (path) => {
      try {
        // 使用iframe跳转（解决某些限制）
        let iframe = document.getElementById('router-iframe');
        if (!iframe) {
          iframe = document.createElement('iframe');
          iframe.id = 'router-iframe';
          iframe.style.display = 'none';
          document.body.appendChild(iframe);
        }
        
        // 设置iframe路径
        iframe.src = 'about:blank';
        setTimeout(() => {
          const doc = iframe.contentDocument;
          if (doc) {
            doc.write(`
              <html><body>
              <script>
                try {
                  top.location.href = "#/${path.startsWith('/') ? path.substring(1) : path}";
                } catch(e) {
                  console.error("iframe导航失败:", e);
                }
              </script>
              </body></html>
            `);
            doc.close();
            console.log('iframe导航执行成功');
          }
        }, 10);
        return true;
      } catch (e) {
        console.error('iframe导航失败:', e);
        return false;
      }
    };
    
    // 全局超级导航方法 - 尝试多种方式
    window.__superNavigate = (path) => {
      console.log('超级导航被调用:', path);
      
      // 1. 首先尝试常规方法
      try {
        const cleanPath = path.startsWith('/') ? path.substring(1) : path;
        window.location.href = '#/' + cleanPath;
        return true;
      } catch (e) {
        console.error('常规导航失败:', e);
      }
      
      // 2. 尝试DOM方式
      if (window.__navigateByDOM(path)) return true;
      
      // 3. 尝试iframe方式
      if (window.__navigateByIframe(path)) return true;
      
      // 4. 最后手段：重载页面
      try {
        const cleanPath = path.startsWith('/') ? path.substring(1) : path;
        window.location.href = window.location.origin + window.location.pathname + '#/' + cleanPath;
        return true;
      } catch (finalError) {
        console.error('所有超级导航方法都失败:', finalError);
        return false;
      }
    };
    
    // 全局点击事件代理，拦截所有导航相关点击
    document.addEventListener('click', function(event) {
      // 查找被点击元素或其父元素是否有data-route属性
      let target = event.target;
      let routePath = null;
      
      // 向上遍历DOM树，查找data-route属性或链接
      while (target && target !== document.body) {
        // 检查自定义路由属性
        if (target.getAttribute('data-route')) {
          routePath = target.getAttribute('data-route');
          break;
        }
        
        // 检查是否是标准链接
        if (target.tagName === 'A' && target.getAttribute('href')) {
          const href = target.getAttribute('href');
          // 只处理应用内的hash导航
          if (href && href.startsWith('#/')) {
            routePath = href.substring(1); // 移除#前缀
            break;
          }
        }
        
        // 检查class名称中是否包含card, btn, item等导航相关类
        const classList = target.classList || [];
        for (let i = 0; i < classList.length; i++) {
          const className = classList[i].toLowerCase();
          if (
            className.includes('card') || 
            className.includes('btn') || 
            className.includes('item') || 
            className.includes('nav') || 
            className.includes('link')
          ) {
            const route = target.getAttribute('data-to') || target.getAttribute('data-url') || target.getAttribute('to');
            if (route) {
              routePath = route;
              break;
            }
          }
        }
        
        target = target.parentNode;
      }
      
      // 如果找到了路由路径，使用超级导航
      if (routePath) {
        event.preventDefault();
        event.stopPropagation();
        console.log('全局点击事件拦截到路由:', routePath);
        
        // 点击反馈效果
        if (target) {
          try {
            const originalTransform = target.style.transform;
            const originalOpacity = target.style.opacity;
            
            target.style.transform = 'scale(0.98)';
            target.style.opacity = '0.8';
            
            setTimeout(() => {
              target.style.transform = originalTransform;
              target.style.opacity = originalOpacity;
              
              // 使用超级导航
              setTimeout(() => {
                ultraReliableNavigate(routePath);
              }, 50);
            }, 150);
          } catch (e) {
            console.error('添加点击反馈效果失败:', e);
            // 直接导航
            ultraReliableNavigate(routePath);
          }
        } else {
          // 直接导航
          ultraReliableNavigate(routePath);
        }
        
        return false;
      }
    }, true); // 使用捕获阶段
    
    console.log('自定义路由控制器安装完成');
  } catch (error) {
    console.error('安装自定义路由控制器失败:', error);
  }
}

// 保证路由在H5环境下正确工作
if (isH5()) {
  window.fixRouter = fixRouter;
  // 自动调用修复路由函数
  setTimeout(() => {
    fixRouter();
    console.log('已自动调用修复路由函数');
  }, 100);
}

// 初始化路由修复
if (typeof window !== 'undefined') {
  setTimeout(() => {
    try {
      initHashRouteHandler();
      console.log('路由修复已初始化');
    } catch (e) {
      console.error('路由修复初始化失败:', e);
    }
  }, 0);
}

// 全局应用实例引用
let _app = null;

/**
 * 解析URL路径参数
 */
export function parseUrlParams(url) {
  try {
    const params = {};
    if (!url) return params;
    
    const queryString = url.split('?')[1] || '';
    if (!queryString) return params;
    
    const pairs = queryString.split('&');
    for (const pair of pairs) {
      const [key, value] = pair.split('=');
      if (key) {
        params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
      }
    }
    
    return params;
  } catch (e) {
    console.error('解析URL参数失败:', e);
    return {};
  }
}

/**
 * 合并URL参数
 */
export function mergeUrlParams(url, params) {
  if (!params || typeof params !== 'object') return url;
  
  const [basePath, existingQuery] = url.split('?');
  const existingParams = existingQuery ? parseUrlParams(`?${existingQuery}`) : {};
  
  // 合并参数
  const mergedParams = { ...existingParams, ...params };
  
  // 构建查询字符串
  const queryEntries = [];
  for (const key in mergedParams) {
    if (mergedParams[key] !== undefined && mergedParams[key] !== null) {
      queryEntries.push(`${encodeURIComponent(key)}=${encodeURIComponent(mergedParams[key])}`);
    }
  }
  
  const queryString = queryEntries.length > 0 ? `?${queryEntries.join('&')}` : '';
  return `${basePath}${queryString}`;
}

// 全局导航锁
let isNavigating = false;
const navigationQueue = [];
const MIN_NAVIGATION_INTERVAL = 300; // 最小导航间隔(毫秒)

// 设置全局防抖，防止短时间内多次导航
function setNavigationLock() {
  isNavigating = true;
  lastNavigationTime = Date.now();
  
  // 设置自动解锁
  setTimeout(() => {
    isNavigating = false;
    // 处理队列中的导航请求
    processNavigationQueue();
  }, MIN_NAVIGATION_INTERVAL);
}

// 处理导航队列
function processNavigationQueue() {
  if (isNavigating || navigationQueue.length === 0) return;
  
  const nextNavigation = navigationQueue.shift();
  if (nextNavigation) {
    const { url, options } = nextNavigation;
    // 强制执行，不再进入队列
    options.force = true;
    ultraReliableNavigate(url, options);
  }
}

// 标准化URL
function normalizeUrl(url) {
  if (!url) return '';
  
  // 清理重复斜杠，但保留协议中的双斜杠
  let normalizedUrl = url.replace(/([^:])\/+/g, '$1/');
  
  // 确保内部路径以/开头
  if (!normalizedUrl.startsWith('/') && 
      !normalizedUrl.startsWith('#') && 
      !normalizedUrl.startsWith('http')) {
    normalizedUrl = '/' + normalizedUrl;
  }
  
  // 为hash导航标准化格式
  if (isH5() && !normalizedUrl.startsWith('#') && !normalizedUrl.startsWith('http')) {
    // 添加#前缀，确保格式为 #/path
    normalizedUrl = '#' + (normalizedUrl.startsWith('/') ? normalizedUrl : '/' + normalizedUrl);
  }
  
  return normalizedUrl;
}

// 检测DOM页面是否已加载
function isDOMPageLoaded(url) {
  if (!isH5() || !url) return true;
  
  try {
    // 解析页面路径
    const path = url.replace(/^[#/]+/, '').split('?')[0];
    
    // 页面容器映射
    const containerSelectors = {
      'pages/create/text': '.text-create-container',
      'pages/create/image': '.image-create-container',
      'pages/create/video': '.video-create-container',
      'pages/create/music': '.music-create-container',
      'pages/create/text/result': '.text-result-container',
      'pages/create/image/result': '.image-result-container',
      'pages/create/video/result': '.video-result-container',
      'pages/create/music/result': '.music-result-container',
      'pages/index': '.content',
      'pages/user': '.user-container'
    };
    
    // 查找匹配的容器选择器
    let selector = null;
    for (const key in containerSelectors) {
      if (path.includes(key)) {
        selector = containerSelectors[key];
        break;
      }
    }
    
    // 如果没有特定选择器，使用通用选择器
    if (!selector) {
      selector = '.content, .uni-page-body, .page-container';
    }
    
    // 检查容器是否存在
    const container = document.querySelector(selector);
    return !!container;
  } catch (e) {
    console.error('检测页面加载状态出错:', e);
    return false;
  }
}

/**
 * 超可靠的导航方法，确保在任何情况下都能成功导航
 * @param {string} url 导航URL
 * @param {Object} options 导航选项
 * @returns {Promise} 导航结果的Promise
 */
export function ultraReliableNavigate(url, options = {}) {
  if (!url) {
    console.error('导航URL不能为空');
    return Promise.reject(new Error('导航URL不能为空'));
  }

  // 规范化URL
  let normalizedUrl = url;
  if (!normalizedUrl.startsWith('/')) {
    normalizedUrl = '/' + normalizedUrl;
  }

  // 检查是否是已知的问题页面
  const problemPages = [
    '/pages/create/text',
    '/pages/create/text/index'
  ];
  
  // 如果是已知的问题页面，我们使用特殊处理
  const isProblemPage = problemPages.some(page => normalizedUrl.startsWith(page));
  if (isProblemPage) {
    console.log('检测到已知问题页面，使用特殊处理:', normalizedUrl);
    
    // 标记导航正在进行中
    window.__navigationInProgress = true;
    window.__navigationUrl = normalizedUrl;
    window.__navigationStartTime = Date.now();
    
    // 对于文本创作页面，尝试使用多种方法确保加载
    return new Promise((resolve) => {
      // 确保任何已存在的页面元素被清理
      if (typeof document !== 'undefined') {
        const existingContainer = document.querySelector('.chat-page, .text-create-container');
        if (existingContainer) {
          console.log('找到已存在的文本页面容器，尝试刷新');
          try {
            existingContainer.style.display = 'none';
            void existingContainer.offsetHeight; // 强制回流
            setTimeout(() => {
              existingContainer.style.display = '';
            }, 10);
          } catch (e) {
            console.warn('刷新容器失败:', e);
          }
        }
      }

      // 使用reLaunch确保旧页面被完全卸载
      if (typeof uni !== 'undefined') {
        uni.reLaunch({
          url: normalizedUrl,
          success: () => {
            console.log('使用reLaunch成功导航到:', normalizedUrl);
            
            // 检查页面是否正确加载
            setTimeout(() => {
              if (typeof window !== 'undefined' && window.__checkRouteState) {
                window.__checkRouteState();
              }
              window.__navigationInProgress = false;
            }, 500);
            
            resolve({ success: true, method: 'reLaunch' });
          },
          fail: (err) => {
            console.error('使用reLaunch导航失败:', err);
            
            // 失败时尝试使用H5直接导航
            if (typeof window !== 'undefined') {
              const cleanUrl = normalizedUrl.startsWith('/') ? normalizedUrl.substring(1) : normalizedUrl;
              window.location.href = '#/' + cleanUrl;
              
              // 检查导航效果
              setTimeout(() => {
                window.__navigationInProgress = false;
                if (window.__checkRouteState) {
                  window.__checkRouteState();
                }
              }, 500);
              
              resolve({ success: true, method: 'location.href' });
            } else {
              // 最后可能性 - 尝试重定向
              navigateViaFallbackMethods(normalizedUrl, options);
              window.__navigationInProgress = false;
              resolve({ success: false, method: 'fallback', error: err });
            }
          }
        });
        return;
      }
      
      // 如果没有uni对象，尝试使用基本导航方法
      navigateViaFallbackMethods(normalizedUrl, options);
      window.__navigationInProgress = false;
      resolve({ success: true, method: 'fallback' });
    });
  }

  console.log('尝试导航到:', normalizedUrl);
  
  // 检查是否有正在处理中的导航，避免重复导航
  if (window.__navigationInProgress && !options.force) {
    console.log('已有导航正在处理中，跳过');
    window.__pendingNavigationUrl = normalizedUrl;
    
    // 添加到待处理导航队列
    if (typeof window.__navigationQueue === 'undefined') {
      window.__navigationQueue = [];
    }
    window.__navigationQueue.push({ url: normalizedUrl, options });
    
    return Promise.resolve({ success: false, reason: 'navigation_in_progress' });
  }
  
  // 标记导航正在进行中
  window.__navigationInProgress = true;
  window.__navigationUrl = normalizedUrl;
  window.__navigationStartTime = Date.now();
  
  // 清除可能存在的导航超时
  if (window.__navigationTimeout) {
    clearTimeout(window.__navigationTimeout);
  }
  
  // 设置导航超时（3秒后自动清除导航中状态）
  window.__navigationTimeout = setTimeout(() => {
    if (window.__navigationInProgress) {
      console.log('导航超时，重置状态');
      window.__navigationInProgress = false;
      
      // 如果有待处理的导航，处理队列中的第一个
      if (window.__navigationQueue && window.__navigationQueue.length > 0) {
        const nextNav = window.__navigationQueue.shift();
        console.log('处理下一个待处理导航:', nextNav.url);
        ultraReliableNavigate(nextNav.url, Object.assign({}, nextNav.options, { force: true }));
      }
    }
  }, 3000);
  
  // 首先尝试使用uni框架的导航方法
  if (typeof uni !== 'undefined') {
    return new Promise((resolve) => {
      try {
        uni.navigateTo({
          url: normalizedUrl,
          success: () => {
            console.log('uni.navigateTo 成功');
            window.__navigationInProgress = false;
            resolve({ success: true, method: 'navigateTo' });
          },
          fail: (err) => {
            console.warn('uni.navigateTo 失败，尝试 redirectTo', err);
            
            // 尝试使用redirectTo作为备选
            uni.redirectTo({
              url: normalizedUrl,
              success: () => {
                console.log('uni.redirectTo 成功');
                window.__navigationInProgress = false;
                resolve({ success: true, method: 'redirectTo' });
              },
              fail: (redirectErr) => {
                console.warn('uni.redirectTo 也失败，尝试其他方法', redirectErr);
                
                // 如果uni框架的方法都失败了，尝试使用基本的导航方法
                navigateViaFallbackMethods(normalizedUrl, options);
                window.__navigationInProgress = false;
                resolve({ success: false, method: 'fallback', error: redirectErr });
              }
            });
          }
        });
      } catch (e) {
        console.error('uni导航方法异常，尝试使用基本的导航方法', e);
        navigateViaFallbackMethods(normalizedUrl, options);
        window.__navigationInProgress = false;
        resolve({ success: false, method: 'fallback', error: e });
      }
    });
  }
  
  // 如果没有uni框架，使用基本的导航方法
  return new Promise((resolve) => {
    const result = navigateViaFallbackMethods(normalizedUrl, options);
    window.__navigationInProgress = false;
    resolve({ success: result, method: 'fallback' });
  });
}

/**
 * 检查是否有待处理的导航
 */
function checkPendingNavigation() {
  if (typeof window.__navigationQueue !== 'undefined' && window.__navigationQueue.length > 0) {
    const nextNavigation = window.__navigationQueue.shift();
    if (nextNavigation) {
      console.log('处理待处理导航:', nextNavigation.url);
      // 强制执行下一个导航，不进入队列
      ultraReliableNavigate(nextNavigation.url, { ...nextNavigation.options, force: true });
    }
  }
}

/**
 * 使用备用方法导航（当其他方法失败时）
 * @param {string} cleanUrl 已清理的URL路径
 * @param {Object} options 可选参数
 * @returns {boolean} 是否成功处理
 */
function navigateViaFallbackMethods(cleanUrl, options = {}) {
  // 添加标记避免重复处理
  if (window.__processingFallbackNav) {
    console.log('已有备用导航正在处理中，跳过');
    return false;
  }
  
  window.__processingFallbackNav = true;
  
  // 检查是否是文本创作页面
  const isTextCreationPage = cleanUrl.includes('pages/create/text');
  if (isTextCreationPage) {
    console.log('检测到文本创作页面，使用特殊处理:', cleanUrl);
    
    try {
      // 尝试直接设置location hash并使用随机时间戳避免缓存问题
      const timestamp = Date.now();
      const targetUrl = cleanUrl.includes('?') 
        ? `${cleanUrl}&_t=${timestamp}` 
        : `${cleanUrl}?_t=${timestamp}`;
      
      window.location.href = '#/' + targetUrl;
      console.log('特殊处理完成，已设置location hash为:', targetUrl);
      
      // 确保页面检查
      setTimeout(() => {
        if (window.__checkRouteState) {
          window.__checkRouteState();
        }
        window.__processingFallbackNav = false;
      }, 800);
      
      return true;
    } catch (e) {
      console.error('特殊处理失败:', e);
      window.__processingFallbackNav = false;
      return false;
    }
  }
  
  // 尝试方法1: 使用replace
  try {
    window.location.replace('#/' + cleanUrl);
    
    // 重置处理标记
    setTimeout(() => {
      window.__processingFallbackNav = false;
    }, 200);
    
    return true;
  } catch (e) {
    console.warn('通过replace导航失败:', e);
  }
  
  // 尝试方法2: 创建并点击a标签
  try {
    const a = document.createElement('a');
    a.href = '#/' + cleanUrl;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    // 重置处理标记
    setTimeout(() => {
      window.__processingFallbackNav = false;
    }, 200);
    
    return true;
  } catch (e) {
    console.warn('通过a标签导航失败:', e);
  }
  
  // 尝试方法3: 强制刷新到目标页面（最后手段）
  try {
    const targetUrl = window.location.origin + window.location.pathname + '#/' + cleanUrl + '?_t=' + Date.now();
    window.location.href = targetUrl;
    
    // 重置处理标记
    setTimeout(() => {
      window.__processingFallbackNav = false;
    }, 200);
    
    return true;
  } catch (e) {
    console.error('通过强制刷新导航失败:', e);
    // 重置处理标记
    window.__processingFallbackNav = false;
    return false;
  }
}

/**
 * 非H5环境下的导航实现
 */
function navigateInNonH5Environment(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      // 首先尝试navigateTo
      navigateViaUniAPI(url, 'navigateTo', options)
        .then(resolve)
        .catch(err => {
          console.warn('navigateTo失败，尝试redirectTo:', err);
          
          // 尝试redirectTo
          navigateViaUniAPI(url, 'redirectTo', options)
            .then(resolve)
            .catch(redirectErr => {
              console.warn('redirectTo失败，尝试switchTab:', redirectErr);
              
              // 最后尝试switchTab
              navigateViaUniAPI(url, 'switchTab', options)
                .then(resolve)
                .catch(switchErr => {
                  console.error('所有uni-app导航方法都失败:', switchErr);
                  
                  // 显示提示
                  uni.showToast({
                    title: '页面跳转失败',
                    icon: 'none',
                    duration: 2000
                  });
                  
                  reject(new Error('所有uni-app导航方法都失败'));
                });
            });
        });
    } catch (e) {
      console.error('非H5环境导航异常:', e);
      reject(e);
    }
  });
}

/**
 * 通过uni-app API进行导航
 */
function navigateViaUniAPI(url, method = 'navigateTo', options = {}) {
  return new Promise((resolve, reject) => {
    // 检测并修复可能的路由劫持
    detectAndFixRouteHijacking();
    
    const navigationOptions = {
      url: url,
      ...options,
      success: (res) => {
        console.log(`${method}成功:`, url, res);
        if (options.success) options.success(res);
        resolve({ success: true, method, result: res });
      },
      fail: (err) => {
        console.warn(`${method}失败:`, url, err);
        if (options.fail) options.fail(err);
        reject(err);
      },
      complete: (res) => {
        if (options.complete) options.complete(res);
      }
    };
    
    // 执行导航
    try {
      uni[method](navigationOptions);
    } catch (e) {
      console.error(`执行${method}异常:`, e);
      reject(e);
    }
  });
}

// 注意：这些导航方法已在文件上方(508-511行)被导出，此处无需重复导出
// export const enhancedNavigateTo = safeNavigateTo;
// export const enhancedRedirectTo = (url, options = {}) => safeNavigateTo(url, { ...options, isRedirect: true });
// export const enhancedSwitchTab = (url, options = {}) => safeNavigateTo(url, { ...options, isTab: true });
// export const enhancedReLaunch = (url, options = {}) => safeNavigateTo(url, { ...options, isReLaunch: true }); 

/**
 * 检测并修复路由状态
 * 当发现路由状态不正确时调用此函数进行修复
 * @param {number} attempts 重试次数，默认为0
 * @returns {boolean} 是否成功修复
 */
export function checkAndFixRouteState(attempts = 0) {
  // 最多尝试3次
  if (attempts >= 3) {
    console.error('路由修复尝试次数已达上限，修复失败');
    return false;
  }
  
  console.log(`开始检查并尝试修复路由状态，第${attempts + 1}次尝试`);
  
  try {
    // 获取当前URL
    const currentPath = window.location.hash.replace(/^#\/?/, '');
    
    // 检查页面内容是否正确加载
    const mainContent = document.querySelector('.uni-page-body, .page-container, .content');
    if (!mainContent || mainContent.children.length === 0) {
      console.log('页面内容未正确加载，尝试修复...');
      
      // 触发一次页面刷新
      forcePageRefresh(currentPath);
      
      // 返回true表示已尝试修复
      return true;
    } else {
      // 检查是否有特定页面标识符
      const pageIdentifiers = {
        'pages/create/text': '.chat-page, .text-create-container',
        'pages/create/image': '.image-create-container',
        'pages/index': '.banner, .category-bar',
        'pages/user': '.user-container'
      };
      
      // 检查当前页面是否包含应有的标识
      let pageCorrect = false;
      for (const path in pageIdentifiers) {
        if (currentPath.includes(path)) {
          const selector = pageIdentifiers[path];
          const pageElement = document.querySelector(selector);
          if (pageElement) {
            console.log('页面内容与路由一致，无需修复');
            pageCorrect = true;
            break;
          }
        }
      }
      
      // 如果页面不匹配路由，尝试修复
      if (!pageCorrect) {
        console.log('页面内容与路由不一致，尝试修复...');
        forcePageRefresh(currentPath);
        return true;
      }
      
      return false; // 无需修复
    }
  } catch (e) {
    console.error('检查路由状态时出错:', e);
    
    // 出错时也尝试修复
    setTimeout(() => {
      checkAndFixRouteState(attempts + 1);
    }, 500);
    
    return false;
  }
}

/**
 * 强制页面刷新
 * @param {string} path 当前路径
 */
function forcePageRefresh(path) {
  try {
    // 创建新的URL，添加时间戳参数避免缓存
    const timestamp = Date.now();
    const refreshUrl = path.includes('?') 
      ? `${path}&_refresh=${timestamp}` 
      : `${path}?_refresh=${timestamp}`;
    
    console.log('强制刷新页面:', refreshUrl);
    
    // 标记避免循环
    window.__forceRefreshing = true;
    
    // 使用hash导航的方式刷新
    window.location.hash = '#/' + refreshUrl;
    
    // 也尝试手动触发hashchange事件
    setTimeout(() => {
      // 创建事件
      const hashChangeEvent = new HashChangeEvent('hashchange', {
        oldURL: window.location.href,
        newURL: window.location.href
      });
      
      // 分发事件
      window.dispatchEvent(hashChangeEvent);
      
      // 重置标记
      window.__forceRefreshing = false;
      
      // 通知其他系统
      if (typeof window.dispatchEvent === 'function') {
        window.dispatchEvent(new CustomEvent('routeFix:pageRefreshed', {
          detail: { 
            path: refreshUrl, 
            timestamp: Date.now() 
          }
        }));
      }
    }, 200);
  } catch (e) {
    console.error('强制刷新页面失败:', e);
    
    // 重置标记
    window.__forceRefreshing = false;
  }
}

/**
 * 备用导航方法，当其他方法都失败时使用
 */
function fallbackNavigate(url) {
  try {
    // 尝试直接设置location.href
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
    const timestamp = Date.now();
    const fullUrl = window.location.origin + window.location.pathname + '#/' + cleanUrl + '?_t=' + timestamp;
    console.log('使用终极备用导航:', fullUrl);
    window.location.href = fullUrl;
    return true;
  } catch (e) {
    console.error('备用导航方法也失败:', e);
    return false;
  }
}

/**
 * H5环境下的导航实现
 */
function navigateInH5Environment(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      // 移除开头的/并确保格式正确
      const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
      
      // 尝试通过hash导航
      if (navigateViaHash(cleanUrl)) {
        console.log('通过hash导航成功:', url);
        return resolve({ success: true, method: 'hash' });
      }
      
      // 如果hash导航失败，尝试其他方法
      if (navigateViaHistory(cleanUrl)) {
        console.log('通过history导航成功:', url);
        return resolve({ success: true, method: 'history' });
      }
      
      // 尝试最终备用方法
      if (navigateViaFallbackMethods(cleanUrl)) {
        console.log('通过备用方法导航成功:', url);
        return resolve({ success: true, method: 'fallback' });
      }
      
      // 所有方法都失败
      console.error('所有H5导航方法都失败');
      reject(new Error('所有H5导航方法都失败'));
    } catch (e) {
      console.error('H5导航异常:', e);
      reject(e);
    }
  });
}

/**
 * 通过hash导航（最常用的方法）
 */
function navigateViaHash(cleanUrl) {
  try {
    // 检查当前hash是否已经是目标URL
    const currentHash = window.location.hash.replace('#/', '');
    if (currentHash === cleanUrl) {
      console.log('当前hash已经是目标URL，跳过改变但尝试触发页面更新:', cleanUrl);
      
      // 尝试手动触发页面更新
      if (typeof window.dispatchEvent === 'function') {
        const hashChangeEvent = new HashChangeEvent('hashchange', {
          oldURL: window.location.href,
          newURL: window.location.href
        });
        
        // 添加标记避免重复处理
        window.__processingHashChange = true;
        
        // 触发事件
        window.dispatchEvent(hashChangeEvent);
        
        // 延迟重置处理标记
        setTimeout(() => {
          window.__processingHashChange = false;
        }, 200);
      }
      
      // 尝试通知其他路由修复机制进行页面检查
      if (typeof window.dispatchEvent === 'function') {
        window.dispatchEvent(new CustomEvent('routeFix:pageNeedsUpdate', {
          detail: { url: cleanUrl, timestamp: Date.now() }
        }));
      }
      
      return true;
    }
    
    // 添加标记避免重复处理
    window.__processingHashChange = true;
    
    // 设置新的hash
    window.location.href = '#/' + cleanUrl;
    
    // 检查hash是否成功改变
    setTimeout(() => {
      const updatedHash = window.location.hash.replace('#/', '');
      if (updatedHash !== cleanUrl) {
        console.warn('Hash可能未成功更新，预期:' + cleanUrl + '，实际:' + updatedHash);
      }
      
      // 重置处理标记
      window.__processingHashChange = false;
    }, 200);
    
    return true;
  } catch (e) {
    console.error('Hash导航失败:', e);
    // 重置处理标记
    window.__processingHashChange = false;
    return false;
  }
}

/**
 * 通过History API导航
 */
function navigateViaHistory(cleanUrl) {
  try {
    // 检查当前hash是否已经是目标URL
    const currentHash = window.location.hash.replace('#/', '');
    if (currentHash === cleanUrl) {
      console.log('当前hash已经是目标URL，尝试触发页面更新:', cleanUrl);
      
      // 手动触发事件通知路由更新
      const customEvent = new CustomEvent('router:updated', { 
        detail: { url: cleanUrl, source: 'history' } 
      });
      window.dispatchEvent(customEvent);
      
      return true;
    }
    
    // 添加标记避免重复处理
    window.__processingHistoryNav = true;
    
    const baseUrl = window.location.origin + window.location.pathname;
    const newUrl = baseUrl + '#/' + cleanUrl;
    window.history.pushState(null, '', newUrl);
    
    // 由于pushState不会触发hashchange事件，手动触发一次
    try {
      const hashchangeEvent = new HashChangeEvent('hashchange', {
        oldURL: window.location.href,
        newURL: newUrl
      });
      window.dispatchEvent(hashchangeEvent);
    } catch (e) {
      console.warn('手动触发hashchange事件失败:', e);
      // 尝试通过自定义事件通知
      const customEvent = new CustomEvent('router:updated', { 
        detail: { url: cleanUrl, source: 'history' } 
      });
      window.dispatchEvent(customEvent);
    }
    
    // 重置处理标记
    setTimeout(() => {
      window.__processingHistoryNav = false;
    }, 200);
    
    return true;
  } catch (e) {
    console.error('History导航失败:', e);
    // 重置处理标记
    window.__processingHistoryNav = false;
    return false;
  }
}

 