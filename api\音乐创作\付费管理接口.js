/**
 * 付费管理接口
 * 管理金币扣除、充值支付等付费相关功能
 */

import { apiRequest } from '../common/request.js';
import consumptionRecordService from '@/services/ConsumptionRecordService.js';

// ================================
// 💰 付费扣费接口
// ================================

/**
 * 检查创作费用
 * @param {string} workflowMode - 工作流模式
 * @param {Object} creationParams - 创作参数
 */
export async function 检查创作费用(workflowMode, creationParams) {
	return await apiRequest('music-creation/check-cost', {
		method: 'POST',
		body: {
			workflowMode: workflowMode,
			...creationParams
		}
	});
}

/**
 * 扣除金币
 * @param {Object} params - 扣费参数
 */
export async function 扣除金币(params) {
	return await apiRequest('music-creation/deduct-coins', {
		method: 'POST',
		body: params
	});
}

/**
 * 发起充值支付
 * @param {Object} params - 支付参数
 */
export async function 发起充值支付(params) {
	return await apiRequest('music-creation/initiate-payment', {
		method: 'POST',
		body: params
	});
}

/**
 * 查询支付状态
 * @param {string} paymentId - 支付ID
 */
export async function 查询支付状态(paymentId) {
	return await apiRequest(`music-creation/payment-status?paymentId=${paymentId}`);
}

/**
 * 获取金币套餐列表
 */
export async function 获取金币套餐列表() {
	return await apiRequest('music-creation/coin-packages');
}

/**
 * 获取用户交易记录
 * @param {Object} params - 查询参数
 */
export async function 获取用户交易记录(params = {}) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`music-creation/transaction-history?${queryParams}`);
}

// ================================
// 🎯 业务逻辑封装
// ================================

/**
 * 创作前费用检查和扣除
 * @param {string} workflowMode - 工作流模式
 * @param {Object} creationParams - 创作参数
 */
export async function 创作前费用处理(workflowMode, creationParams) {
	try {
		// 1. 检查费用
		const costResult = await 检查创作费用(workflowMode, creationParams);
		
		if (!costResult.success) {
			throw new Error(costResult.message || '费用检查失败');
		}

		const costInfo = costResult.data;

		// 2. 检查是否有足够金币
		if (!costInfo.canAfford) {
			return {
				success: false,
				errorCode: 'INSUFFICIENT_COINS',
				message: `金币不足，需要 ${costInfo.finalCost} 金币，当前余额 ${costInfo.userBalance} 金币`,
				data: {
					required: costInfo.finalCost,
					current: costInfo.userBalance,
					shortfall: costInfo.finalCost - costInfo.userBalance
				}
			};
		}

		// 3. 扣除金币
		const deductResult = await 扣除金币({
			requestId: `temp_${Date.now()}`,
			costCoins: costInfo.finalCost,
			description: `${workflowMode}模式音乐创作`,
			workflowMode: workflowMode
		});

		// 4. 记录消费
		await recordMusicCreationConsumption(workflowMode, creationParams, costInfo);

		return {
			success: true,
			data: {
				costInfo: costInfo,
				deductResult: deductResult.data,
				message: '费用扣除成功'
			}
		};

	} catch (error) {
		console.error('创作前费用处理失败:', error);
		throw error;
	}
}

/**
 * 充值流程管理
 * @param {string} packageId - 套餐ID
 * @param {string} paymentMethod - 支付方式
 */
export async function 充值流程管理(packageId, paymentMethod) {
	try {
		// 1. 发起支付
		const paymentResult = await 发起充值支付({
			coinPackage: packageId,
			paymentMethod: paymentMethod
		});

		if (!paymentResult.success) {
			throw new Error(paymentResult.message || '发起支付失败');
		}

		const paymentInfo = paymentResult.data;

		// 2. 返回支付信息
		return {
			success: true,
			data: {
				paymentId: paymentInfo.paymentId,
				paymentUrl: paymentInfo.paymentUrl,
				qrCode: paymentInfo.qrCode,
				amount: paymentInfo.amount,
				coins: paymentInfo.coins,
				expireTime: paymentInfo.expireTime
			}
		};

	} catch (error) {
		console.error('充值流程失败:', error);
		throw error;
	}
}

/**
 * 轮询支付状态直到完成
 * @param {string} paymentId - 支付ID
 * @param {Function} onStatusChange - 状态变化回调
 * @param {Object} options - 轮询选项
 */
export async function 轮询支付状态直到完成(paymentId, onStatusChange, options = {}) {
	const {
		maxPolls = 60,         // 最大轮询次数（5分钟）
		pollInterval = 5000,   // 轮询间隔（5秒）
		timeout = 300000       // 总超时时间（5分钟）
	} = options;

	let pollCount = 0;
	const startTime = Date.now();

	return new Promise((resolve, reject) => {
		const poll = async () => {
			try {
				pollCount++;
				const currentTime = Date.now();

				// 检查超时
				if (currentTime - startTime > timeout) {
					reject(new Error('支付超时'));
					return;
				}

				// 检查轮询次数
				if (pollCount > maxPolls) {
					reject(new Error('支付轮询超限'));
					return;
				}

				// 查询支付状态
				const statusResult = await 查询支付状态(paymentId);
				const status = statusResult.data.status;

				// 调用状态变化回调
				if (onStatusChange && typeof onStatusChange === 'function') {
					onStatusChange({
						...statusResult.data,
						pollCount,
						elapsedTime: currentTime - startTime
					});
				}

				// 处理不同状态
				if (status === 'paid') {
					resolve(statusResult);
				} else if (status === 'failed' || status === 'expired') {
					reject(new Error(statusResult.data.message || '支付失败'));
				} else {
					// 继续轮询
					setTimeout(poll, pollInterval);
				}

			} catch (error) {
				reject(error);
			}
		};

		// 开始轮询
		poll();
	});
}

/**
 * 完整充值流程
 * @param {string} packageId - 套餐ID
 * @param {string} paymentMethod - 支付方式
 * @param {Function} onStatusChange - 状态变化回调
 */
export async function 完整充值流程(packageId, paymentMethod, onStatusChange) {
	try {
		// 1. 发起充值
		const paymentInfo = await 充值流程管理(packageId, paymentMethod);
		
		if (!paymentInfo.success) {
			throw new Error('发起充值失败');
		}

		const paymentId = paymentInfo.data.paymentId;

		// 2. 轮询支付状态
		const finalResult = await 轮询支付状态直到完成(
			paymentId,
			onStatusChange
		);

		return {
			success: true,
			data: {
				paymentId: paymentId,
				paymentInfo: paymentInfo.data,
				finalResult: finalResult.data,
				completedAt: new Date().toISOString()
			}
		};

	} catch (error) {
		console.error('完整充值流程失败:', error);
		throw error;
	}
}

/**
 * 记录音乐创作消费
 * @param {string} workflowMode - 工作流模式
 * @param {Object} creationParams - 创作参数
 * @param {Object} costInfo - 费用信息
 */
async function recordMusicCreationConsumption(workflowMode, creationParams, costInfo) {
	try {
		await consumptionRecordService.recordMusicCreationConsumption({
			workflowMode: workflowMode,
			style: creationParams.style || '未指定',
			mood: creationParams.mood || '未指定',
			duration: creationParams.duration || 30,
			cost: costInfo.finalCost,
			originalCost: costInfo.baseCost,
			discount: costInfo.discount || 0
		});
		console.log('✅ 音乐创作消费记录已保存');
	} catch (error) {
		console.error('❌ 记录音乐创作消费失败:', error);
		// 记录失败不影响主流程
	}
}

export default {
	检查创作费用,
	扣除金币,
	发起充值支付,
	查询支付状态,
	获取金币套餐列表,
	获取用户交易记录,
	创作前费用处理,
	充值流程管理,
	轮询支付状态直到完成,
	完整充值流程
};
