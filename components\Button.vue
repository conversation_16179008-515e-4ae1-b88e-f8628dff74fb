<template>
  <button 
    class="custom-btn" 
    :class="[
      type ? `btn-${type}` : '',
      size ? `btn-${size}` : '',
      round ? 'btn-round' : '',
      block ? 'btn-block' : '',
      loading ? 'btn-loading' : '',
      disabled ? 'btn-disabled' : '',
      customClass
    ]"
    :disabled="disabled || loading"
    :loading="loading"
    @tap="handleTap"
  >
    <text v-if="loading" class="btn-loading-icon">●</text>
    <text v-if="icon" class="btn-icon">{{ icon }}</text>
    <slot></slot>
  </button>
</template>

<script>
export default {
  name: 'Button',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'danger', 'info', 'text'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['mini', 'small', 'medium', 'large'].includes(value)
    },
    round: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: ''
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleTap(event) {
      if (!this.disabled && !this.loading) {
        this.$emit('tap', event);
      }
    }
  }
}
</script>

<style lang="scss">
.custom-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 1;
  text-align: center;
  box-sizing: border-box;
  border-radius: 8rpx;
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dcdfe6;
  
  &.btn-primary {
    background-color: #00aaff;
    color: #ffffff;
    border-color: #00aaff;
  }
  
  &.btn-success {
    background-color: #67c23a;
    color: #ffffff;
    border-color: #67c23a;
  }
  
  &.btn-warning {
    background-color: #e6a23c;
    color: #ffffff;
    border-color: #e6a23c;
  }
  
  &.btn-danger {
    background-color: #f56c6c;
    color: #ffffff;
    border-color: #f56c6c;
  }
  
  &.btn-info {
    background-color: #909399;
    color: #ffffff;
    border-color: #909399;
  }
  
  &.btn-text {
    background-color: transparent;
    color: #00aaff;
    border-color: transparent;
    padding: 0;
    height: auto;
  }
  
  &.btn-small {
    height: 64rpx;
    font-size: 24rpx;
    padding: 0 20rpx;
  }
  
  &.btn-mini {
    height: 48rpx;
    font-size: 20rpx;
    padding: 0 16rpx;
  }
  
  &.btn-large {
    height: 96rpx;
    font-size: 32rpx;
    padding: 0 40rpx;
  }
  
  &.btn-round {
    border-radius: 40rpx;
  }
  
  &.btn-block {
    display: flex;
    width: 100%;
  }
  
  &.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-loading {
    opacity: 0.8;
    cursor: wait;
  }
  
  .btn-loading-icon {
    margin-right: 10rpx;
    animation: btn-loading 1s linear infinite;
  }
  
  .btn-icon {
    margin-right: 8rpx;
  }
}

@keyframes btn-loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 