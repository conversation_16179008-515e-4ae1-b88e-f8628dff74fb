<template>
  <view class="base-textarea-container" :class="[platformClass]">
    <!-- 主文本框 -->
    <textarea
      ref="textareaRef"
      class="base-textarea"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :auto-height="autoHeight"
      :fixed="false"
      :cursor-spacing="cursorSpacing"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      :style="textareaStyle"
      @input="handleInput"
      @focus="$emit('focus', $event)"
      @blur="$emit('blur', $event)"
      @confirm="$emit('confirm', $event)"
      @paste="$emit('paste', $event)"
      @keyboardheightchange="$emit('keyboardheightchange', $event)"
      @touchstart="$emit('touchstart', $event)"
      @touchmove="$emit('touchmove', $event)" 
      @touchend="$emit('touchend', $event)"
      @click="$emit('click', $event)"
    ></textarea>
    
    <!-- 发送按钮插槽，放在右下角 -->
    <view class="send-button-slot">
      <slot name="sendButton"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BaseTextArea',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    minHeight: {
      type: Number,
      default: 40
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'confirm', 'paste', 'keyboardheightchange', 'touchstart', 'touchmove', 'touchend', 'click'],
  computed: {
    textareaStyle() {
      return {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`,
        overflowY: 'auto',
        paddingRight: '60px', /* 为发送按钮留出空间 */
      };
    }
  },
  data() {
    return {
      platformClass: '',
      isH5: false,
      isApp: false,
      isMiniProgram: false,
      isIOS: false,
      isAndroid: false,
    };
  },
  created() {
    this.detectPlatform();
  },
  methods: {
    detectPlatform() {
      // 检测当前平台
      // #ifdef H5
      this.isH5 = true;
      this.platformClass = 'h5-platform';
      // #endif
      
      // #ifdef APP-PLUS
      this.isApp = true;
      this.platformClass = 'app-platform';
      // #endif
      
      // #ifdef MP
      this.isMiniProgram = true;
      this.platformClass = 'mp-platform';
      // #endif
      
      // 检测操作系统
      const sys = uni.getSystemInfoSync();
      if (sys) {
        if (sys.platform === 'ios') {
          this.isIOS = true;
          this.platformClass += ' ios-platform';
        } else if (sys.platform === 'android') {
          this.isAndroid = true;
          this.platformClass += ' android-platform';
        }
      }
    },
    handleInput(e) {
      this.$emit('update:modelValue', e.detail.value);
    }
  }
}
</script>

<style scoped>
.base-textarea-container {
  position: relative;
  width: 100%;
}

.base-textarea {
  width: 100%;
  background-color: rgba(40, 40, 60, 0.6);
  color: #e8e8e8;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.base-textarea:focus {
  background-color: rgba(50, 50, 70, 0.8);
  box-shadow: 0 0 3px rgba(120, 100, 220, 0.7);
}

.send-button-slot {
  position: absolute;
  bottom: 5px;
  right: 5px;
}

/* 平台特定样式 */
.h5-platform .base-textarea {
  transition: height 0.2s ease;
}

.ios-platform .base-textarea {
  font-family: -apple-system, SF Pro, sans-serif;
}

.android-platform .base-textarea {
  font-family: Roboto, sans-serif;
}
</style> 