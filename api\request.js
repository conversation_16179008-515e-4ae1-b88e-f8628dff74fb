/**
 * 请求封装模块
 */

// 安全的base64编码函数，支持中文字符
function safeBase64Encode(str) {
  try {
    // 使用TextEncoder和Uint8Array来处理UTF-8字符
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    const binary = Array.from(data, byte => String.fromCharCode(byte)).join('');
    return btoa(binary);
  } catch (error) {
    // 降级到传统方法
    try {
      return btoa(unescape(encodeURIComponent(str)));
    } catch (fallbackError) {
      console.error('Base64编码失败:', fallbackError);
      return '';
    }
  }
}

// 基础请求设置
// 使用模拟数据模式，不发送实际网络请求
const USE_MOCK_DATA = true;
const BASE_URL = ''; // 空字符串，不发送实际请求
const API_VERSION = '';

// 判断环境，根据环境设置不同的BASE_URL
// const BASE_URL = process.env.NODE_ENV === 'development' 
//   ? 'http://localhost:3000'  // 开发环境
//   : 'https://api.example.com'; // 生产环境

/**
 * 通用请求方法
 * @param {Object} options - 请求配置
 * @returns {Promise} 请求结果
 */
export function request(options) {
  return new Promise((resolve, reject) => {
    // 如果使用模拟数据模式，直接返回模拟数据
    if (USE_MOCK_DATA) {
      console.log(`使用模拟数据: ${options.url}`);
      
      // 根据不同的API路径返回不同的模拟数据
      if (options.url.includes('/scenarios')) {
        // 场景列表
        setTimeout(() => {
          resolve({
            code: 200,
            data: []
          });
        }, 300);
        return;
      }

      // 婚姻测试AI分析
      if (options.url.includes('/api/ai/analyze') && options.data?.femaleInfo && options.data?.maleInfo) {
        const { femaleInfo, maleInfo } = options.data;
        setTimeout(() => {
          resolve({
            code: 200,
            data: generateMockAnalysisResult(femaleInfo, maleInfo)
          });
        }, 2000); // 模拟AI分析需要时间
        return;
      }

      // 婚姻测试历史记录
      if (options.url.includes('/api/marriage-test/history')) {
        setTimeout(() => {
          resolve({
            code: 200,
            data: []
          });
        }, 300);
        return;
      }

      // 婚姻测试保存结果
      if (options.url.includes('/api/marriage-test/save')) {
        setTimeout(() => {
          resolve({
            code: 200,
            data: { id: Date.now(), saved: true }
          });
        }, 300);
        return;
      }

      // 默认返回空数据
      setTimeout(() => {
        resolve({
          code: 200,
          data: null
        });
      }, 300);
      return;
    }
    
    // 以下是实际网络请求的代码
    // 获取令牌
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    };
    
    // 添加授权头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 完整URL
    const url = `${BASE_URL}${API_VERSION}${options.url}`;
    console.log(`请求URL: ${url}`);
    
    // 发送请求
    uni.request({
      url: url,
      method: options.method || 'GET',
      data: options.data,
      header: header,
      timeout: 10000, // 10秒超时
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 处理认证失败
          uni.removeStorageSync('token');
          uni.removeStorageSync('userInfo');
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          
          // 延迟跳转，等待提示完成
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
          
          reject(new Error('登录已过期'));
        } else {
          // 处理其他错误
          uni.showToast({
            title: res.data?.message || '请求失败',
            icon: 'none'
          });
          reject(new Error(res.data?.message || '请求失败'));
        }
      },
      fail: (err) => {
        console.error(`请求失败: ${url}`, err);
        
        // 不显示网络错误提示，避免频繁弹窗
        // uni.showToast({
        //   title: '网络错误，请稍后再试',
        //   icon: 'none'
        // });
        
        // 返回模拟数据以避免页面崩溃
        if (options.url.includes('/scenarios')) {
          console.log('返回场景模拟数据');
          resolve({
            code: 200,
            data: []
          });
        } else {
          reject(err);
        }
      }
    });
  });
}

/**
 * 上传文件请求
 * @param {Object} options - 上传配置
 * @returns {Promise} 上传结果
 */
export function uploadFile(options) {
  return new Promise((resolve, reject) => {
    // 获取令牌
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      ...options.header
    };
    
    // 添加授权头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 上传文件
    const uploadTask = uni.uploadFile({
      url: `${BASE_URL}${API_VERSION}${options.url}`,
      filePath: options.filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header: header,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(JSON.parse(res.data));
        } else if (res.statusCode === 401) {
          // 处理认证失败
          uni.removeStorageSync('token');
          uni.removeStorageSync('userInfo');
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
          
          reject(new Error('登录已过期'));
        } else {
          // 处理其他错误
          let errorMsg = '上传失败';
          try {
            const errorData = JSON.parse(res.data);
            errorMsg = errorData.message || errorMsg;
          } catch (e) {}
          
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
          
          reject(new Error(errorMsg));
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        reject(err);
      }
    });
    
    // 返回上传任务对象，便于外部控制（如监听进度、取消上传等）
    options.onProgressUpdate && uploadTask.onProgressUpdate(options.onProgressUpdate);
  });
}

/**
 * 下载文件请求
 * @param {Object} options - 下载配置
 * @returns {Promise} 下载结果
 */
export function downloadFile(options) {
  return new Promise((resolve, reject) => {
    // 获取令牌
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const header = {
      ...options.header
    };
    
    // 添加授权头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 下载文件
    const downloadTask = uni.downloadFile({
      url: options.url.startsWith('http') ? options.url : `${BASE_URL}${API_VERSION}${options.url}`,
      header: header,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.tempFilePath);
        } else {
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          });
          reject(new Error('下载失败'));
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        reject(err);
      }
    });
    
    // 返回下载任务对象，便于外部控制（如监听进度、取消下载等）
    options.onProgressUpdate && downloadTask.onProgressUpdate(options.onProgressUpdate);
  });
}

/**
 * 生成模拟的婚姻配对分析结果
 * @param {Object} femaleInfo - 女方信息
 * @param {Object} maleInfo - 男方信息
 */
function generateMockAnalysisResult(femaleInfo, maleInfo) {
  // 基于姓名生成个性化的配对指数
  const nameHash = (femaleInfo.name + maleInfo.name).split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const baseScore = 75 + (nameHash % 20); // 75-94之间的分数

  // 生成各项指数
  const nameCompatibility = Math.max(80, baseScore + Math.floor(Math.random() * 10) - 5);
  const birthDateCompatibility = Math.max(75, baseScore + Math.floor(Math.random() * 15) - 7);
  const overallScore = Math.round((nameCompatibility + birthDateCompatibility) / 2);

  return {
    compatibilityScore: overallScore,
    overallAssessment: `${femaleInfo.name}和${maleInfo.name}是一对很有缘分的情侣！你们的姓名搭配和生辰八字都显示出良好的匹配度。在感情路上，你们能够相互理解、相互支持，共同创造美好的未来。从传统命理学角度来看，你们的结合将会带来幸福和谐的婚姻生活。`,

    basicAnalysis: {
      nameCompatibility: nameCompatibility,
      birthDateCompatibility: birthDateCompatibility,
      overallMatch: `从姓名学角度分析，${femaleInfo.name}和${maleInfo.name}的名字搭配和谐，寓意美好。${femaleInfo.name}的名字蕴含着温柔善良的品质，而${maleInfo.name}的名字则体现出稳重可靠的特征。生辰八字方面，你们的出生时间显示出良好的互补性，能够在生活中相互扶持，共同面对人生的挑战和机遇。`
    },

    marriageChanges: {
      datingPersonality: `恋爱时，${femaleInfo.name}会展现出细腻温柔的一面，善于关怀和体贴对方的感受。而${maleInfo.name}则会表现得更加成熟稳重，给予${femaleInfo.name}安全感和依靠。你们在恋爱阶段会相互吸引，彼此欣赏对方的优点，共同创造美好的回忆。`,
      loveExpression: `${femaleInfo.name}更喜欢通过细致入微的关怀来表达爱意，比如记住对方的喜好、准备小惊喜等。${maleInfo.name}则倾向于通过实际行动来证明自己的爱，比如为对方解决问题、提供支持等。你们需要学会理解彼此不同的爱的表达方式。`,
      marriageRole: `婚后，${femaleInfo.name}会成为一个贤惠体贴的妻子，善于营造温馨的家庭氛围。${maleInfo.name}则会成为一个负责任的丈夫，承担起家庭的重担，为家人提供稳定的生活保障。你们会形成很好的分工合作关系。`,
      futureChanges: "随着时间的推移，你们的感情会更加深厚稳定。在共同经历人生的起伏后，你们会更加珍惜彼此，理解也会更加深入。婚姻会让你们都变得更加成熟和包容。"
    },

    relationshipAdvice: {
      stability: `你们的关系基础非常稳固，${femaleInfo.name}的温柔善良和${maleInfo.name}的稳重可靠形成了很好的互补。只要保持良好的沟通，相互理解和支持，你们的感情会越来越稳定，婚姻生活也会越来越幸福美满。`,
      communication: `建议你们多进行开放式的交流，${femaleInfo.name}可以更直接地表达自己的想法和需求，而${maleInfo.name}则要学会更加细致地倾听和回应。定期安排深入的谈话时间，分享彼此的感受和想法，这样能够增进相互理解。`,
      interaction: `在日常相处中，${femaleInfo.name}要给${maleInfo.name}更多的个人空间和理解，而${maleInfo.name}则要更加主动地关心和陪伴${femaleInfo.name}。要保持耐心和包容，尊重彼此的个性差异，在冲突时要以解决问题为目标，而不是争输赢。`
    },

    emotionEnhancement: {
      exclusiveSecrets: `你们可以建立一些专属的仪式感，比如每月安排一次特别的约会，去一个有纪念意义的地方。可以一起学习新的技能或爱好，比如烹饪、舞蹈或旅行，这些共同的经历会成为你们独特的回忆。记住重要的纪念日，为彼此准备有意义的礼物。`,
      sweetnessBoost: `${femaleInfo.name}可以通过写情书、准备爱心便当等方式增加甜蜜度。${maleInfo.name}则可以通过送花、安排惊喜约会等方式表达爱意。你们还可以一起看日出日落，一起做饭，一起规划未来，这些简单的事情都能增加生活的甜蜜度。`,
      stabilityMaintenance: `建立共同的目标和兴趣爱好，比如一起健身、一起学习、一起旅行。定期进行感情的"体检"，坦诚地讨论关系中的问题和改进方向。保持对彼此的新鲜感，不断发现对方的新优点。在困难时期要相互支持，共同面对挑战。`
    },

    personalityMatch: {
      femaleTraits: `${femaleInfo.name}性格温柔善良，有着细腻的情感和强烈的责任心。她善于察言观色，能够敏锐地感知他人的情绪变化，并给予适当的关怀和支持。她重视家庭和感情，愿意为所爱的人付出，同时也希望得到对方的理解和珍惜。`,
      maleTraits: `${maleInfo.name}性格稳重可靠，有着包容的心胸和进取的精神。他做事认真负责，有很强的责任感和使命感。他善于思考和规划，能够为家庭和事业做出长远的打算。他虽然不善于表达情感，但内心深处有着深沉的爱意。`,
      complementarity: `你们的性格形成了很好的互补关系。${femaleInfo.name}的细腻温柔能够平衡${maleInfo.name}的理性严肃，而${maleInfo.name}的稳重可靠则能给${femaleInfo.name}带来安全感。你们能够在不同方面相互支持和完善，共同成长。`,
      interactionMode: `建议你们以理解和包容为基础，在沟通中保持开放和诚实。${femaleInfo.name}要学会更直接地表达需求，${maleInfo.name}要学会更温柔地回应。要给彼此足够的个人空间，同时也要保持亲密的连接。在决策时要充分讨论，尊重彼此的意见。`
    },

    futurePrediction: {
      developmentTrend: `你们的感情发展趋势非常良好。在未来的日子里，你们会经历从恋爱到婚姻的美好转变，感情会在磨合中变得更加深厚。你们会一起面对生活的挑战，共同创造属于你们的幸福家庭。随着时间的推移，你们会更加珍惜彼此，成为彼此最重要的支撑。`,
      happinessIndex: Math.min(95, Math.max(85, overallScore + Math.floor(Math.random() * 8))),
      childrenFate: `你们有很好的子女缘分。未来的孩子会继承你们的优秀品质，${femaleInfo.name}的温柔善良和${maleInfo.name}的稳重可靠都会在孩子身上得到体现。你们会成为很好的父母，能够给孩子提供温暖和谐的成长环境。孩子的到来会为你们的生活带来更多欢乐和意义。`
    }
  };
}

export default {
  request,
  uploadFile,
  downloadFile
};