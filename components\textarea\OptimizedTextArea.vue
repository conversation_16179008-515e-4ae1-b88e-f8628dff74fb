<template>
  <view class="optimized-textarea-container" :class="[platformClass, {
    'has-overflow': hasOverflow,
    'selecting': isLongPress || selectionEnabled || isTextSelectionActive()
  }]">
    <TextAreaSelectionHandler 
      ref="selectionHandler"
      :content-element="textareaRef"
      :value="modelValue"
      @long-press="handleLongPress"
      @selection-change="handleSelectionChange"
    >
      <BaseTextArea
        ref="textareaBase"
        :model-value="modelValue"
        :placeholder="showAnimatedPlaceholder ? '' : placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :auto-height="autoHeight"
        :cursor-spacing="cursorSpacing"
        :show-confirm-bar="showConfirmBar"
        :adjust-position="adjustPosition"
        :min-height="minHeight"
        :max-height="maxHeight"
        @update:model-value="$emit('update:modelValue', $event)"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="$emit('confirm', $event)"
        @paste="$emit('paste', $event)"
        @keyboardheightchange="$emit('keyboardheightchange', $event)"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="handleClick"
      >
        <template #sendButton>
          <slot name="sendButton"></slot>
        </template>
      </BaseTextArea>

      <TextAreaScrollHandler
        ref="scrollHandler"
        :content-element="textareaRef"
        :is-h5="isH5"
        @overflow-change="setHasOverflow"
        @touch-start="handleScrollTouchStart"
        @touch-move="handleScrollTouchMove"
        @touch-end="handleScrollTouchEnd"
        @mouse-wheel="handleMouseWheel"
      />

      <!-- 动态提示文字组件 -->
      <AnimatedPlaceholder
        v-if="showAnimatedPlaceholder && !modelValue"
        :placeholder="placeholder"
        :placeholders="placeholders"
        :is-focused="isFocused"
      />
    </TextAreaSelectionHandler>
  </view>
</template>

<script>
import BaseTextArea from './BaseTextArea.vue';
import TextAreaScrollHandler from './TextAreaScrollHandler.vue';
import TextAreaSelectionHandler from './TextAreaSelectionHandler.vue';
import AnimatedPlaceholder from './AnimatedPlaceholder.vue';
import { isTextSelectionActive, countLines, scrollToBottom } from './TextAreaUtils.js';

export default {
  name: 'OptimizedTextArea',
  components: {
    BaseTextArea,
    TextAreaScrollHandler,
    TextAreaSelectionHandler,
    AnimatedPlaceholder
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    minHeight: {
      type: Number,
      default: 40
    },
    isDebug: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'confirm', 'paste', 'keyboardheightchange', 'height-change', 'send'],
  data() {
    return {
      isFocused: false,
      platformClass: '',
      hasOverflow: false,
      textareaHeight: 0,
      contentLines: 1,
      isH5: false,
      isApp: false,
      isMiniProgram: false,
      isIOS: false,
      isAndroid: false,
      debugMode: this.isDebug,
      currentScrollTop: 0,
      userHasScrolled: false,
      isLongPress: false,
      selectionEnabled: false
    };
  },
  computed: {
    textareaRef() {
      if (this.$refs.textareaBase) {
        return this.$refs.textareaBase.$el.querySelector('textarea');
      }
      return null;
    }
  },
  watch: {
    modelValue: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.checkOverflow();
          this.updateContentLines();
          
          // 只在内容首次加载或明显增加时自动滚动
          const isSubstantialAdd = !oldVal || (newVal && oldVal && newVal.length > oldVal.length + 10);
          // 如果用户已经手动滚动，不要自动滚动
          if (isSubstantialAdd && !this.userHasScrolled) {
            this.scrollToBottom();
          }
        });
      }
    }
  },
  created() {
    this.detectPlatform();
  },
  mounted() {
    this.$nextTick(() => {
      this.initTextarea();
      this.updateContentLines();
      this.setupMutationObserver();
      
      // 为H5环境添加鼠标滚轮事件
      if (this.textareaRef && this.isH5) {
        this.textareaRef.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        this.textareaRef.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
      }
    });
  },
  beforeUnmount() {
    // 移除事件监听
    if (this.textareaRef && this.isH5) {
      this.textareaRef.removeEventListener('mousewheel', this.handleMouseWheel);
      this.textareaRef.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
    }
    
    // 移除DOM变化监听
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
  },
  methods: {
    isTextSelectionActive,
    
    detectPlatform() {
      // 检测当前平台
      // #ifdef H5
      this.isH5 = true;
      this.platformClass = 'h5-platform';
      // #endif
      
      // #ifdef APP-PLUS
      this.isApp = true;
      this.platformClass = 'app-platform';
      // #endif
      
      // #ifdef MP
      this.isMiniProgram = true;
      this.platformClass = 'mp-platform';
      // #endif
      
      // 检测操作系统
      const sys = uni.getSystemInfoSync();
      if (sys) {
        if (sys.platform === 'ios') {
          this.isIOS = true;
          this.platformClass += ' ios-platform';
        } else if (sys.platform === 'android') {
          this.isAndroid = true;
          this.platformClass += ' android-platform';
        }
      }
    },
    
    initTextarea() {
      this.checkOverflow();
    },
    
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },
    
    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },
    
    setHasOverflow(value) {
      this.hasOverflow = value;
    },
    
    checkOverflow() {
      if (this.$refs.scrollHandler) {
        this.$refs.scrollHandler.checkOverflow();
      }
    },
    
    handleScrollTouchStart(data) {
      Object.assign(this, data);
    },
    
    handleScrollTouchMove(data) {
      Object.assign(this, data);
    },
    
    handleScrollTouchEnd(data) {
      Object.assign(this, data);
    },
    
    handleTouchStart(e) {
      if (this.$refs.scrollHandler) {
        this.$refs.scrollHandler.handleTouchStart(e);
      }
      
      if (this.$refs.selectionHandler) {
        this.$refs.selectionHandler.handleTouchStart(e);
      }
    },
    
    handleTouchMove(e) {
      if (this.$refs.scrollHandler) {
        this.$refs.scrollHandler.handleTouchMove(e);
      }
    },
    
    handleTouchEnd(e) {
      if (this.$refs.scrollHandler) {
        this.$refs.scrollHandler.handleTouchEnd(e);
      }
      
      if (this.$refs.selectionHandler) {
        this.$refs.selectionHandler.handleTouchEnd(e);
      }
    },
    
    handleClick(e) {
      if (this.$refs.selectionHandler) {
        this.$refs.selectionHandler.handleClick(e);
      }
    },
    
    handleLongPress(e) {
      this.isLongPress = true;
    },
    
    handleSelectionChange(data) {
      if (data && data.enabled !== undefined) {
        this.selectionEnabled = data.enabled;
      }
    },
    
    updateContentLines() {
      this.contentLines = countLines(this.modelValue);
      this.updateHeight();
    },
    
    updateHeight() {
      if (!this.textareaRef) return;
      
      setTimeout(() => {
        const height = this.textareaRef.scrollHeight;
        if (this.textareaHeight !== height) {
          this.textareaHeight = height;
          this.$emit('height-change', height);
        }
      }, 0);
    },
    
    scrollToBottom() {
      if (this.$refs.scrollHandler) {
        this.$refs.scrollHandler.scrollToBottom();
      }
    },
    
    setupMutationObserver() {
      // 监听DOM变化
      if (typeof MutationObserver === 'undefined' || !this.textareaRef) return;
      
      try {
        this.mutationObserver = new MutationObserver((mutations) => {
          this.checkOverflow();
          this.updateHeight();
        });
        
        this.mutationObserver.observe(this.textareaRef, {
          attributes: true,
          childList: true,
          subtree: true
        });
      } catch (e) {
        console.error('设置DOM观察器失败:', e);
      }
    },
    
    handleMouseWheel(e) {
      if (this.$refs.scrollHandler) {
        this.$refs.scrollHandler.handleMouseWheel(e);
      }
    }
  }
}
</script>

<style scoped>
.optimized-textarea-container {
  position: relative;
  width: 100%;
}

.optimized-textarea-container.selecting {
  /* 选择模式下的样式 */
  user-select: text;
  -webkit-user-select: text;
}

/* 平台特定样式 */
.h5-platform {
  /* H5平台特定样式 */
}

.ios-platform {
  /* iOS平台特定样式 */
}

.android-platform {
  /* Android平台特定样式 */
}

.app-platform {
  /* App平台特定样式 */
}

.mp-platform {
  /* 小程序平台特定样式 */
}
</style> 