﻿<template>
  <view class="optimized-textarea-container" :class="[platformClass, {
    'has-overflow': hasOverflow,
    'selecting': isLongPress || selectionEnabled || isTextSelectionActive()
  }]">
    <!-- 主文本框 -->
    <textarea
      ref="textareaRef"
      class="optimized-textarea"
      :value="modelValue"
      :placeholder="showAnimatedPlaceholder ? '' : placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :auto-height="true"
      :fixed="false"
      :cursor-spacing="cursorSpacing"
      :show-confirm-bar="showConfirmBar"
      :adjust-position="adjustPosition"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @paste="handlePaste"
      @keyboardheightchange="handleKeyboardHeightChange"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @click="handleClick"
    ></textarea>
    
    <!-- 发送按钮插槽，放在右下角 -->
    <view class="send-button-slot">
      <slot name="sendButton"></slot>
    </view>
    
    <!-- 动态提示文字组件 -->
    <view 
      v-if="showAnimatedPlaceholder && !modelValue" 
      class="animated-placeholder"
      :class="{ 'is-focused': isFocused }"
    >
      <text class="placeholder-text">{{ currentPlaceholder }}</text>
    </view>

    <!-- H5平台的滚动指示器 -->
    <view 
      v-if="isH5 && hasOverflow" 
      class="scroll-indicator"
      :class="{'show-indicator': hasOverflow && !isTouchingTextarea}"
    ></view>
    
    <!-- 调试用：滚动位置指示器，隐藏 -->
    <view
      v-if="false && debugMode"
      class="debug-scroll-indicator"
      :style="{
        top: `${20 + (currentScrollTop / 10)}px`, 
        height: `${Math.min(50, contentLines * 3)}px`
      }"
    ></view>
    
    <!-- 调试用：显示底部标记，隐藏 -->
    <text
      v-if="false && (isDebug || debugMode)" 
      class="debug-bottom-marker"
    >▼</text>
  </view>
</template>

<script>
export default {
  name: 'OptimizedTextArea',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    placeholders: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number,
      default: -1
    },
    autoHeight: {
      type: Boolean, 
      default: true
    },
    cursorSpacing: {
      type: Number,
      default: 0
    },
    showConfirmBar: {
      type: Boolean,
      default: true
    },
    adjustPosition: {
      type: Boolean,
      default: true
    },
    showAnimatedPlaceholder: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200 // 增大默认最大高度为200px，提供更好的视野
    },
    minHeight: {
      type: Number,
      default: 40 // 默认最小高度40px
    },
    isDebug: {
      type: Boolean,
      default: false
    },
    isSending: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'confirm', 'paste', 'keyboardheightchange', 'height-change', 'send', 'paste-image'],
  computed: {
    textareaStyle() {
      return {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`,
        overflowY: this.hasOverflow ? 'auto' : 'hidden',
        paddingRight: '60px', /* 为发送按钮留出空间 */
      };
    }
  },
  data() {
    return {
      isFocused: false,
      platformClass: '',
      currentPlaceholder: '',
      placeholderIndex: 0,
      placeholderTimer: null,
      hasOverflow: false,
      textareaHeight: 0,
      lastContentHeight: 0,
      isComposing: false,
      isTouchingTextarea: false,
      touchStartY: 0,
      touchStartX: 0,
      touchStartTime: 0,
      isScrolling: false,
      initialScrollTop: 0,
      momentum: 0,
      animationFrameId: null,
      isH5: false,
      isApp: false,
      isMiniProgram: false,
      isIOS: false,
      isAndroid: false,
      contentLines: 1,
      scrollAnimation: null,
      observerAttached: false,
      scrollTimers: [],
      mutationObserver: null,
      lastScrollAttemptTime: 0,
      autoHeightActive: true,
      isUpdatingHeight: false,
      userHasScrolled: false,
      scrollAttempts: 0,
      lastUserInteraction: 0,
      debugInterval: null,
      debugMode: this.isDebug,
      isLongPress: false,
      longPressTimer: null,
      lastClickTime: 0,  // 添加用于检测双击的变量
      selectionEnabled: false, // 标记是否启用了文本选择模式
      selectionLogShown: false, // 添加用于检测文本选择模式的变量
      resetScrollFlagTimer: null,
      initialTextLoaded: false, // 添加标记，表示初始文本是否已加载
      lastDeltaY: 0,     // 用于记录上一次Y轴的变化量
      lastDeltaTime: 0,  // 用于计算速度的时间差
      isHBuilder: false, // 添加HBuilder环境检测标志
      _hasSelectedAll: false, // 添加标记，表示是否已经执行过全选操作
    };
  },
  watch: {
    placeholders: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.currentPlaceholder = newVal[0];
          this.startPlaceholderAnimation();
        } else {
          this.currentPlaceholder = this.placeholder;
        }
      }
    },
    modelValue: {
      immediate: true,
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.checkOverflow();
          
          // 只在内容首次加载或明显增加时自动滚动
          const isSubstantialAdd = !oldVal || (newVal && oldVal && newVal.length > oldVal.length + 10);
          // 如果用户已经手动滚动，不要自动滚动
          if (isSubstantialAdd && !this.userHasScrolled) {
            if (this.scrollAttempts < 3) {
              this.scrollAttempts++;
              this.scrollToBottom();
              
              setTimeout(() => {
                this.scrollAttempts = 0;
              }, 500);
            }
          }
        });
      }
    },
    maxHeight() {
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    }
  },
  created() {
    this.detectPlatform();
    this.setupAppFeatures();
  },
  mounted() {
    this.$nextTick(() => {
      this.initTextarea();
      
      this.setupMutationObserver();
      
      // 重新监听滚动事件
      const textarea = this.getTextareaElement();
      if (textarea && this.isH5) {
        // 添加明确的滚动事件监听
        textarea.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
        
        // 添加键盘事件，支持Ctrl+A全选和Delete/Backspace删除
        textarea.addEventListener('keydown', this.handleKeyDown);
        
        // 设置文本选择属性
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
      }
      
      // 初始化图片粘贴功能
      console.log('初始化图片粘贴功能');
      
      try {
        // 在document级别添加粘贴事件监听
        if (this.isH5 && typeof document !== 'undefined') {
          // 添加全局粘贴事件监听，确保能捕获所有粘贴事件
          document.addEventListener('paste', (e) => {
            console.log('document级别粘贴事件被触发');
            this.handlePaste(e);
          });
          
          // 添加额外的粘贴事件监听在window级别
          if (typeof window !== 'undefined') {
            window.addEventListener('paste', (e) => {
              console.log('window级别粘贴事件被触发');
              this.handlePaste(e);
            });
          }
          
          // 创建隐藏的文件输入元素作为备选方案
          this.createHiddenFileInput();
        }
        
        // 在HBuilder环境中特别处理
        if (this.isHBuilder) {
          console.log('在HBuilder环境中初始化图片粘贴功能');
          
          // 添加全局粘贴事件监听
          if (typeof window !== 'undefined') {
            window.addEventListener('paste', (e) => {
              console.log('window级别粘贴事件被触发');
              this.handlePaste(e);
            });
          }
          
          // 如果有plus环境，添加特殊处理
          if (typeof plus !== 'undefined') {
            // 可以在这里添加plus环境的特殊初始化代码
            console.log('检测到plus环境，启用特殊粘贴处理');
          }
        }
        
        // 在APP环境中特别处理
        if (this.isApp) {
          console.log('在APP环境中初始化图片粘贴功能');
          // APP环境的特殊处理已在enhanceAppTextSelection方法中实现
        }
        
        // 在小程序环境中特别处理
        if (this.isMiniProgram) {
          console.log('在小程序环境中初始化图片粘贴功能');
          // 小程序环境的特殊处理已在setupMiniProgramImagePaste方法中实现
        }
      } catch (err) {
        console.error('初始化图片粘贴功能失败:', err);
      }
      
      // 初始化测试数据
      if (this.isDebug || false) { // 改为false，禁用测试文本
        this.enableDebugging(false); // 禁用调试面板显示
        
        setTimeout(() => {
          const testText = "这是一个测试文本\n用来验证滚动功能是否正常工作\n第三行\n第四行\n第五行\n第六行\n第七行\n第八行\n第九行\n第十行";
          this.setText(testText);
          this.initialTextLoaded = true;
          console.log('添加测试文本以测试滚动');
        }, 1000);
      }
    });
  },
  
  // 初始化图片粘贴功能
  beforeUnmount() {
    this.clearPlaceholderTimer();
    this.cleanupScrollAnimation();
    this.cleanupResizeObserver();
    this.cleanupMutationObserver();
    this.clearAllTimers();
    
    const textarea = this.getTextareaElement();
    if (textarea) {
      // 移除所有事件监听器
      try {
      textarea.removeEventListener('scroll', this.handleScroll);
        textarea.removeEventListener('wheel', this.handleMouseWheel);
      textarea.removeEventListener('mousewheel', this.handleMouseWheel);
      textarea.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
      textarea.removeEventListener('keydown', this.handleKeyDown);
        textarea.removeEventListener('mouseup', this.handleMouseUp);
        textarea.removeEventListener('selectstart', this.handleSelectStart);
        textarea.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('移除事件监听器失败:', e);
      }
    }
    
    // 移除window级别的事件监听器
    if (this.isH5 && window) {
      try {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('移除window事件监听器失败:', e);
      }
    }
    
    // 移除document级别的事件监听器
    if (this.isH5 && document) {
      try {
        document.removeEventListener('paste', this.handlePaste);
      } catch(e) {
        console.error('移除document事件监听器失败:', e);
      }
    }
    
    if (this.debugMode) {
      this.disableDebugging();
    }
  },
  methods: {
    detectPlatform() {
      try {
        // 检测是否为H5环境
        this.isH5 = typeof window !== 'undefined' && typeof document !== 'undefined';
        
        // 检测HBuilder环境
        this.isHBuilder = typeof navigator !== 'undefined' && 
                         (navigator.userAgent.indexOf('HBuilder') > -1 || 
                          navigator.userAgent.indexOf('Html5Plus') > -1 ||
                          (typeof window !== 'undefined' && typeof window.plus !== 'undefined') ||
                          (typeof plus !== 'undefined'));
        
        if (this.isHBuilder) {
          console.log('检测到HBuilder环境，启用特殊粘贴处理和文本选择');
        }
        
        // 检测App环境
        try {
          // HBuilder/uni-app环境检测
          this.isApp = (typeof uni !== 'undefined' && typeof plus !== 'undefined') || 
                       (typeof window !== 'undefined' && window.plus) ||
                       !!navigator?.userAgent?.match(/Html5Plus/i);
        } catch (e) {
          this.isApp = false;
        }
        
        // 小程序环境检测
        try {
          this.isMiniProgram = typeof wx !== 'undefined' && typeof wx.getSystemInfoSync === 'function';
          
          // 额外检查uni-app环境中的小程序
          if (!this.isMiniProgram && typeof uni !== 'undefined' && uni.getSystemInfoSync) {
            const sysInfo = uni.getSystemInfoSync();
            this.isMiniProgram = sysInfo.mp && (sysInfo.mp.weixin || sysInfo.mp.alipay || sysInfo.mp.baidu);
          }
          
          // 如果是小程序环境，启用特殊图片粘贴处理
          if (this.isMiniProgram) {
            console.log('检测到小程序环境，启用特殊图片粘贴处理');
            this.setupMiniProgramImagePaste();
          }
        } catch (e) {
          this.isMiniProgram = false;
        }
        
        // 设备类型检测
        if (this.isH5 && typeof navigator !== 'undefined') {
          this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
          this.isAndroid = /Android/i.test(navigator.userAgent);
        } else if (typeof uni !== 'undefined' && uni.getSystemInfoSync) {
          try {
            const sysInfo = uni.getSystemInfoSync();
            this.isIOS = sysInfo.platform === 'ios';
            this.isAndroid = sysInfo.platform === 'android';
          } catch (e) {
            // 默认检测
            if (typeof navigator !== 'undefined') {
              this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
              this.isAndroid = /Android/i.test(navigator.userAgent);
            }
          }
        }
        
        // 根据不同平台设置类名
        if (this.isH5) {
          this.platformClass = 'h5-platform';
        } else if (this.isApp) {
          this.platformClass = 'app-platform';
        } else if (this.isMiniProgram) {
          this.platformClass = 'mp-platform';
        }
        
        if (this.debugMode) {
          console.log('当前平台:', this.platformClass, 
                      '设备:', this.isIOS ? 'iOS' : this.isAndroid ? 'Android' : '其他');
        }
      } catch (e) {
        console.error('平台检测失败:', e);
        // 默认为H5
        this.platformClass = 'h5-platform';
      }
    },
    
    // 设置小程序环境下的图片粘贴功能
    setupMiniProgramImagePaste() {
      if (!this.isMiniProgram) return;
      
      try {
        // 微信小程序
        if (typeof wx !== 'undefined') {
          // 监听剪贴板变化
          wx.onClipboardData && wx.onClipboardData((res) => {
            if (res && res.data) {
              // 检查是否是图片路径
              if (res.data.indexOf('wxfile://') === 0 || 
                  res.data.indexOf('http') === 0 && 
                  (res.data.indexOf('.png') > -1 || 
                   res.data.indexOf('.jpg') > -1 || 
                   res.data.indexOf('.jpeg') > -1 || 
                   res.data.indexOf('.gif') > -1)) {
                
                // 可能是图片路径，触发图片粘贴事件
                this.$emit('paste-image', {
                  path: res.data,
                  type: 'image/png' // 默认类型
                });
              } else {
                // 普通文本，插入到光标位置
                this.insertTextAtCursor(res.data);
              }
            }
          });
        }
        
        // uni-app环境
        if (typeof uni !== 'undefined') {
          // 添加自定义的图片粘贴按钮（仅在小程序环境）
          this.$nextTick(() => {
            // 可以在这里添加一个选择图片的按钮，但为了保持界面简洁，我们不添加额外UI
            // 而是依赖父组件提供的图片上传功能
          });
        }
      } catch (e) {
        console.error('设置小程序图片粘贴功能失败:', e);
      }
    },

    clearAllTimers() {
      for (const timer of this.scrollTimers) {
        clearTimeout(timer);
      }
      this.scrollTimers = [];
    },

    initTextarea() {
      this.textareaHeight = this.minHeight;
      
      // 确保容器DOM已经加载
      this.$nextTick(() => {
        this.updateHeightAndCheck();
        this.initEvents();
        
        if (this.debugMode) {
          this.enableDebugging();
        }
      });
    },
    
    initEvents() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 清理可能的旧监听器
      try {
        textarea.removeEventListener('scroll', this.handleScroll);
        textarea.removeEventListener('wheel', this.handleMouseWheel);
        textarea.removeEventListener('mousewheel', this.handleMouseWheel);
        textarea.removeEventListener('DOMMouseScroll', this.handleMouseWheel);
        textarea.removeEventListener('keydown', this.handleKeyDown);
        textarea.removeEventListener('mouseup', this.handleMouseUp);
        textarea.removeEventListener('selectstart', this.handleSelectStart);
        textarea.removeEventListener('paste', this.handlePaste);
      } catch(e) {}
      
      // 添加新的监听器
      textarea.addEventListener('scroll', this.handleScroll);
      
      // 特别针对H5环境添加鼠标滚轮事件
      if (this.isH5) {
        textarea.addEventListener('wheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('mousewheel', this.handleMouseWheel, { passive: true });
        textarea.addEventListener('DOMMouseScroll', this.handleMouseWheel, { passive: true });
        
        // 添加键盘事件，支持Ctrl+A全选和其他快捷键
        textarea.addEventListener('keydown', this.handleKeyDown);
        
        // 添加选择事件监听
        textarea.addEventListener('selectstart', this.handleSelectStart);
        textarea.addEventListener('mouseup', this.handleMouseUp);
        
        // 设置基本的文本选择属性
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
        
        // 确保iOS的触摸事件响应更灵敏
        if (this.isIOS) {
          textarea.style.webkitOverflowScrolling = 'touch';
        }
        
        // 禁用自动滚动到底部的行为，让用户控制滚动
        if (typeof this.userHasScrolled === 'undefined') {
          this.userHasScrolled = false;
        }
      }
      
      // 为HBuilder环境添加特殊支持
      if (this.isHBuilder) {
        console.log('HBuilder环境特殊处理');
        
        // 增强选择能力
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.MozUserSelect = 'text';
        textarea.style.msUserSelect = 'text';
        
        // 确保能接收粘贴事件
        textarea.setAttribute('contenteditable', 'true');
        
        // 添加粘贴事件监听
        window.addEventListener('paste', this.handlePaste);
        textarea.addEventListener('paste', this.handlePaste);
        
        // 添加键盘事件支持
        textarea.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keydown', this.handleKeyDown);
        
        // 尝试支持框选
        textarea.addEventListener('selectstart', this.handleSelectStart);
        textarea.addEventListener('mouseup', this.handleMouseUp);
        
        // 添加特殊样式
        textarea.classList.add('hbuilder-textarea');
      }
      
      // 应用滚动优化
      this.enhanceScrolling();
    },
    
    enableDebugging(showPanel = true) {
      console.log('TextArea调试模式已启用');
      
      if (this.isH5 && showPanel) {
        const debugPanel = document.createElement('div');
        debugPanel.style.position = 'fixed';
        debugPanel.style.bottom = '10px';
        debugPanel.style.right = '10px';
        debugPanel.style.backgroundColor = 'rgba(0,0,0,0.7)';
        debugPanel.style.color = '#fff';
        debugPanel.style.padding = '5px 10px';
        debugPanel.style.borderRadius = '5px';
        debugPanel.style.fontSize = '12px';
        debugPanel.style.zIndex = '9999';
        debugPanel.style.maxWidth = '200px';
        debugPanel.id = 'textarea-debug-panel';
        
        const updateDebugInfo = () => {
          const textarea = this.getTextareaElement();
          if (!textarea) return;
          
          debugPanel.innerHTML = `
            <div>平台: ${this.platformClass}</div>
            <div>滚动位置: ${textarea.scrollTop || 0}px</div>
            <div>内容高度: ${textarea.scrollHeight || 0}px</div>
            <div>可视高度: ${textarea.clientHeight || 0}px</div>
            <div>容器高度: ${this.textareaHeight}px</div>
            <div>最大高度: ${this.maxHeight}px</div>
            <div>溢出: ${this.hasOverflow ? '是' : '否'}</div>
            <div>行数: ${this.contentLines}</div>
          `;
        };
        
        document.body.appendChild(debugPanel);
        this.debugInterval = setInterval(updateDebugInfo, 500);
        updateDebugInfo();
      }
    },
    
    disableDebugging() {
      if (this.isH5) {
        clearInterval(this.debugInterval);
        const panel = document.getElementById('textarea-debug-panel');
        if (panel) {
          document.body.removeChild(panel);
        }
      }
    },

    getTextareaElement() {
      if (!this.$refs.textareaRef) return null;
      
      if (this.isH5) {
        if (this.$refs.textareaRef.$el) {
          return this.$refs.textareaRef.$el;
        }
        return this.$refs.textareaRef;
      } 
      
      return this.$refs.textareaRef;
    },
    
    updateHeightAndCheck() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      this.isUpdatingHeight = true;
      
      let contentHeight = this.minHeight;
      
      if (this.isH5) {
        try {
          const originalHeight = textarea.style.height;
          const originalMaxHeight = textarea.style.maxHeight;
          
          textarea.style.height = 'auto';
          textarea.style.maxHeight = 'none';
          
          contentHeight = Math.max(this.minHeight, textarea.scrollHeight);
          
          textarea.style.height = originalHeight;
          textarea.style.maxHeight = originalMaxHeight;
          
          if (this.debugMode) {
            console.log('H5 - 内容高度计算:', contentHeight, 'scrollHeight:', textarea.scrollHeight);
          }
        } catch (e) {
          console.error('计算高度失败:', e);
        }
      } else {
        const lineCount = this.countLines(this.modelValue);
        const lineHeight = 20;
        contentHeight = Math.max(this.minHeight, lineCount * lineHeight);
        
        if (this.debugMode) {
          console.log('非H5 - 行数:', lineCount, '估算高度:', contentHeight);
        }
      }
      
      this.lastContentHeight = contentHeight;
      
      const newHeight = Math.min(Math.max(contentHeight, this.minHeight), this.maxHeight);
      
      if (this.textareaHeight !== newHeight) {
        this.textareaHeight = newHeight;
        this.$emit('height-change', newHeight);
        
        if (this.debugMode) {
          console.log('高度更新为:', newHeight);
        }
      }
      
      const hasOverflow = contentHeight > this.maxHeight;
      if (hasOverflow !== this.hasOverflow) {
        this.hasOverflow = hasOverflow;
        if (this.debugMode) {
          console.log('溢出状态:', hasOverflow ? '溢出' : '未溢出');
        }
      }
      
      this.isUpdatingHeight = false;
      
      this.$forceUpdate();
    },
    
    countLines(text) {
      if (!text) return 1;
      
      const lines = (text.match(/\n/g) || []).length + 1;
      this.contentLines = lines;
      return lines;
    },
    
    cleanupMutationObserver() {
      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
        this.mutationObserver = null;
      }
    },
    
    cleanupResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },

    startPlaceholderAnimation() {
      this.clearPlaceholderTimer();
      
      if (!this.placeholders || this.placeholders.length <= 1) return;
      
      this.placeholderTimer = setInterval(() => {
        this.placeholderIndex = (this.placeholderIndex + 1) % this.placeholders.length;
        this.currentPlaceholder = this.placeholders[this.placeholderIndex];
      }, 3000);
    },

    clearPlaceholderTimer() {
      if (this.placeholderTimer) {
        clearInterval(this.placeholderTimer);
        this.placeholderTimer = null;
      }
    },
    
    cleanupScrollAnimation() {
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
        this.scrollAnimation = null;
      }
    },

    handleClick(e) {
      // 检测是否是双击行为
      const now = Date.now();
      if (now - this.lastClickTime < 300) {
        // 双击，启用文本选择模式
        this.selectionEnabled = true;
        
        // 确保文本可选择
        const textarea = this.getTextareaElement();
        if (textarea) {
          textarea.style.userSelect = 'text';
          textarea.style.webkitUserSelect = 'text';
          textarea.style.MozUserSelect = 'text';
          textarea.style.msUserSelect = 'text';
          
          // 更多平台的属性
          textarea.style.webkitTapHighlightColor = 'rgba(77, 157, 255, 0.3)';
          
          // 尝试手动调整样式，增强选择视觉效果
          if (this.isHBuilder) {
            textarea.classList.add('selecting-text');
          }
        }
        
        // 在APP环境下，尝试使用全选
        if (this.isApp) {
          this.$nextTick(() => {
            try {
              // 尝试原生全选
              if (textarea && typeof textarea.setSelectionRange === 'function') {
                textarea.setSelectionRange(0, textarea.value.length);
              } else if (uni && uni.createSelectorQuery) {
                // 尝试通过uni API操作
                uni.createSelectorQuery()
                  .in(this)
                  .select('.optimized-textarea')
                  .context((res) => {
                    if (res && res.context) {
                      res.context.setSelectionRange(0, this.modelValue.length);
                    }
                  }).exec();
              }
            } catch(e) {
              console.error('全选文本失败:', e);
            }
          });
        }
        
        // 振动反馈
        try {
          if (navigator && navigator.vibrate) {
            navigator.vibrate(50);
          }
        } catch(e) {}
        
        if (this.debugMode) console.log('检测到双击，启用文本选择模式');
      } else {
        // 单击，记录时间
        this.lastClickTime = now;
      }
    },

    handleInput(e) {
      const value = e.detail?.value || e.target?.value || '';
      this.$emit('update:modelValue', value);
      
      // 使用handleContent方法处理内容变化
      this.$nextTick(() => {
        this.handleContent();
      });
    },

    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
      
      // 只有在未检测到用户滚动时才滚动到底部
      if (!this.userHasScrolled) {
        this.$nextTick(() => {
          this.enhanceScrolling();
          // 使用单次滚动，不要强制
          this.scrollToBottom();
        });
      }
    },

    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    handleConfirm(e) {
      // 先触发原有的confirm事件
      this.$emit('confirm', e);
      
      // 如果设置了自动发送，则直接发送消息
      if (this.isSending) {
        this.sendMessage();
      } else {
        // 否则只滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    handlePaste(e) {
      console.log('粘贴事件被触发', e ? '事件对象存在' : '事件对象不存在');
      
      try {
        // 尝试从剪贴板事件获取数据
        if (e && e.clipboardData) {
          console.log('剪贴板数据存在', 
                     'items:', e.clipboardData.items ? e.clipboardData.items.length : '无items', 
                     'files:', e.clipboardData.files ? e.clipboardData.files.length : '无files');
          
          // 优先检查files
          if (e.clipboardData.files && e.clipboardData.files.length > 0) {
            const file = e.clipboardData.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('从clipboardData.files获取到图片:', file.type);
              e.preventDefault();
              
              // 触发图片粘贴事件
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                source: 'clipboard-files'
              });
              
              return false;
            }
          }
          
          // 检查items
          if (e.clipboardData.items) {
            // 遍历所有粘贴项，查找图片
            for (let i = 0; i < e.clipboardData.items.length; i++) {
              const item = e.clipboardData.items[i];
              console.log('检查剪贴板item:', item.kind, item.type);
              
              if (item.kind === 'file' && item.type.indexOf('image') !== -1) {
                try {
                  const blob = item.getAsFile();
                  if (blob) {
                    console.log('从clipboardData.items获取到图片:', item.type);
                    e.preventDefault();
                    
                    // 触发图片粘贴事件
                    this.$emit('paste-image', {
                      blob: blob,
                      type: item.type,
                      source: 'clipboard-items'
                    });
                    
                    return false;
                  }
                } catch (itemErr) {
                  console.error('获取剪贴板项文件失败:', itemErr);
                }
              }
            }
            
            // 如果没有图片，尝试获取文本
            let pastedText = '';
            try {
              pastedText = e.clipboardData.getData('text/plain');
              console.log('从剪贴板获取到文本:', pastedText ? '成功' : '失败');
            } catch (textErr) {
              console.error('获取剪贴板文本失败:', textErr);
            }
            
            if (pastedText) {
              // 检查文本是否可能是图片URL
              if (this.isImageUrl(pastedText)) {
                console.log('检测到可能是图片URL:', pastedText);
                e.preventDefault();
                
                // 触发图片粘贴事件
                this.$emit('paste-image', {
                  path: pastedText,
                  type: 'image/url',
                  source: 'clipboard-text-url'
                });
                
                return false;
              }
              
              // 普通文本，阻止默认行为，手动处理粘贴
              e.preventDefault();
              
              // 获取当前值和光标位置
      const textarea = this.getTextareaElement();
              let currentValue = this.modelValue || '';
              let selectionStart = 0;
              let selectionEnd = 0;
              
              try {
                if (textarea && textarea.selectionStart !== undefined) {
                  selectionStart = textarea.selectionStart;
                  selectionEnd = textarea.selectionEnd;
                }
              } catch (err) {
                console.error('获取选择范围失败:', err);
                // 如果获取失败，默认追加到末尾
                selectionStart = currentValue.length;
                selectionEnd = currentValue.length;
              }
              
              // 插入文本
              const newValue = currentValue.substring(0, selectionStart) + 
                              pastedText + 
                              currentValue.substring(selectionEnd);
              
              // 更新值
              this.$emit('update:modelValue', newValue);
              
              // 更新光标位置
        this.$nextTick(() => {
                try {
                  if (textarea && typeof textarea.setSelectionRange === 'function') {
                    const newPosition = selectionStart + pastedText.length;
          textarea.focus();
          textarea.setSelectionRange(newPosition, newPosition);
                  }
                } catch (err) {
                  console.error('设置光标位置失败:', err);
                }
          
                // 更新高度和滚动
          this.updateHeightAndCheck();
                this.scrollToBottom();
              });
              
              return false;
            }
          }
        }
        
        // H5环境下，如果无法通过剪贴板获取图片，尝试使用其他方法
        if (this.isH5 && (!e || !e.clipboardData || (!e.clipboardData.files?.length && !e.clipboardData.items?.length))) {
          console.log('H5环境下无法通过剪贴板事件获取图片，尝试其他方法');
          
          // 尝试使用navigator.clipboard API但不显示任何提示
          this.tryPasteImageWithNavigatorClipboard(false);
        }
        
        // 特殊处理HBuilder环境
        if (this.isHBuilder) {
          console.log('HBuilder环境特殊处理粘贴');
          
          // 在HBuilder环境中，不要尝试使用navigator.clipboard API
          // 因为它会导致"Read permission denied"错误
          
          // 也不要尝试使用document.execCommand('paste')
          // 因为它会导致"callback is not a function"错误
          
          // 直接让系统处理粘贴事件，不阻止默认行为
          return true;
        }
      } catch (err) {
        console.error('处理粘贴事件失败:', err);
        
        // 如果在处理过程中出错，尝试使用文件选择器作为备选方案
        if (this.isH5) {
          console.log('粘贴处理出错，尝试使用文件选择器');
          setTimeout(() => {
            this.showFileSelector();
          }, 300);
        }
      }
      
      // 如果上述方法失败，不阻止默认行为，让系统处理粘贴
      return true;
    },
    
    // 检查文本是否是图片URL
    isImageUrl(text) {
      if (!text) return false;
      
      // 简单检查是否是图片URL
      const trimmed = text.trim().toLowerCase();
      return (trimmed.startsWith('http') || trimmed.startsWith('https') || 
              trimmed.startsWith('data:image') || trimmed.startsWith('file:') || 
              trimmed.startsWith('wxfile:')) && 
             (trimmed.endsWith('.png') || trimmed.endsWith('.jpg') || 
              trimmed.endsWith('.jpeg') || trimmed.endsWith('.gif') || 
              trimmed.endsWith('.webp') || trimmed.endsWith('.bmp') ||
              trimmed.indexOf('data:image/') !== -1);
    },
    
    // 尝试在HBuilder环境下粘贴
    tryHBuilderPaste() {
      try {
        // 不尝试使用plus.pasteboard API，因为它可能不可用或导致错误
        console.log('HBuilder环境下简化粘贴处理');
        
        // 直接尝试获取剪贴板文本
        if (typeof uni !== 'undefined' && uni.getClipboardData) {
          uni.getClipboardData({
            success: (res) => {
              if (res && res.data) {
                console.log('成功通过uni.getClipboardData获取内容');
                
                // 检查是否是图片URL
                if (this.isImageUrl(res.data)) {
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/url',
                    source: 'hbuilder-clipboard'
                  });
                } else {
                  // 普通文本，插入到当前位置
                  this.insertTextAtCursor(res.data);
                }
              } else {
                console.log('剪贴板内容为空');
              }
            },
            fail: (err) => {
              console.error('uni.getClipboardData失败:', err);
              
              // 给用户提示
              if (typeof uni !== 'undefined' && uni.showToast) {
                uni.showToast({
                  title: '无法访问剪贴板，请手动粘贴',
                  icon: 'none',
                  duration: 2000
                });
              }
            }
          });
        } else {
          console.log('uni.getClipboardData不可用');
        }
      } catch (err) {
        console.error('HBuilder粘贴处理失败:', err);
      }
    },

    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e);
      
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    
    handleTouchStart(e) {
      const touch = e.touches[0];
      this.touchStartY = touch.clientY;
      this.touchStartX = touch.clientX;
      this.touchStartTime = Date.now();
      this.isTouchingTextarea = true;
      
      // 如果长按启动文本选择
      if (this.longPressTimer) clearTimeout(this.longPressTimer);
      this.longPressTimer = setTimeout(() => {
        this.isLongPress = true;
        this.selectionEnabled = true;
        
        // 在App环境下触发全选
        if (this.isApp) {
          this.selectAllText();
        }
        
        // 振动反馈
        try {
          if (navigator && navigator.vibrate) {
            navigator.vibrate(50);
          }
        } catch(e) {}
      }, 300);
    },
    
    handleTouchMove(e) {
      // 确保是单点触摸
      if (e.touches.length !== 1) return;
      
      // 文本选择模式不进行滚动处理
      if (this.isLongPress || this.selectionEnabled) return;
      
      const touch = e.touches[0];
      const currentY = touch.clientY;
      const currentX = touch.clientX;
      const deltaY = currentY - this.touchStartY;
      const deltaX = currentX - this.touchStartX;
      
      // 记录时间和位移，用于计算惯性
      const now = Date.now();
      const deltaTime = now - this.lastUserInteraction;
      this.lastDeltaY = deltaY;
      this.lastDeltaTime = deltaTime;
      
      // 检测是否是明确的垂直滑动，降低判断门槛
      if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 3) {
        // 取消长按定时器
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
        
        // 标记用户滚动
        this.userHasScrolled = true;
        this.lastUserInteraction = now;
        
        // 直接执行滚动，最大化灵敏度
        const textarea = this.getTextareaElement();
        if (textarea) {
          // 大幅提高灵敏度，特别是在HBuilder环境
          let sensitivity = this.isH5 ? 2.2 : 3.0;
          // APP环境特别优化
          if (this.isApp) sensitivity = 3.5;
          
          // 直接根据手指位置移动，使用更高的灵敏度
          textarea.scrollTop -= deltaY * sensitivity;
          
          // 更新起始点，实现连续滚动效果
          this.touchStartY = currentY;
          this.touchStartX = currentX;
        }
      } else if (Math.abs(deltaX) > 10) {
        // 水平滑动，可能是文本选择，取消长按
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      }
    },
    
    handleTouchEnd(e) {
      // 取消长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }
      
      // 计算惯性滚动
      const now = Date.now();
      const timeElapsed = now - this.lastUserInteraction;
      
      // 只有在短时间内结束触摸，且有明显滑动才应用惯性
      if (timeElapsed < 100 && Math.abs(this.lastDeltaY) > 5) {
        // 计算滑动的速度
        const velocity = this.lastDeltaY / Math.max(10, this.lastDeltaTime);
        if (Math.abs(velocity) > 0.1) {
          const textarea = this.getTextareaElement();
          if (textarea) {
            this.applyInertialScroll(textarea, velocity);
          }
        }
      }
      
      // 重置状态
      this.isTouchingTextarea = false;
      
      // 延迟重置文本选择状态
      if (!this.isTextSelectionActive()) {
        setTimeout(() => {
          this.isLongPress = false;
          this.selectionEnabled = false;
        }, 1000);
      }
    },
    
    // 检测是否正在进行文本选择
    isTextSelectionActive() {
      try {
        // 仅在H5环境中检查
        if (this.isH5 && window) {
          // 检查window.getSelection
          if (window.getSelection) {
            const selection = window.getSelection();
            if (selection && selection.type === 'Range' && selection.toString().length > 0) {
              return true;
            }
          }
          
          // 检查文本框自身的选择状态
          const textarea = this.getTextareaElement();
          if (textarea && 
              typeof textarea.selectionStart !== 'undefined' && 
              textarea.selectionStart !== textarea.selectionEnd) {
            return true;
          }
        }
        
        // 非H5环境，依靠selectionEnabled标志
        return this.selectionEnabled;
      } catch (e) {
        console.error('检查文本选择状态时出错:', e);
      }
      
      // 默认返回false
      return false;
    },

    applyScroll(element, scrollPosition) {
      if (!element) return;
      
      try {
        // 直接高效设置滚动位置，无需额外逻辑
        const maxScroll = Math.max(0, element.scrollHeight - element.clientHeight);
        const limitedScroll = Math.max(0, Math.min(maxScroll, scrollPosition));
        
        // 优先使用直接赋值，速度更快
        element.scrollTop = limitedScroll;
        
        // 对特定平台的额外优化
        if (this.isApp) {
          // APP环境尝试修复可能的滚动问题
          setTimeout(() => {
            if (Math.abs(element.scrollTop - limitedScroll) > 2) {
              element.scrollTop = limitedScroll;
            }
          }, 0);
        }
      } catch (err) {
        console.error('滚动应用失败:', err);
      }
    },
    
    applyInertialScroll(element, velocity) {
      if (!element || Math.abs(velocity) < 0.05) return;
      
      // 计算初始速度，APP环境使用更大的值
      const initialSpeed = velocity * (this.isApp ? 100 : 80);
      // 减缓减速率，延长滑动距离
      const deceleration = this.isApp ? 0.95 : 0.92;
      
      let currentSpeed = initialSpeed;
      let currentPosition = element.scrollTop;
      
      // 清理旧的动画
      if (this.scrollAnimation) {
        cancelAnimationFrame(this.scrollAnimation);
      }
      
      // 创建惯性动画
      const animate = () => {
        // 应用当前速度
        currentPosition -= currentSpeed;
        
        // 边界检查
        if (currentPosition < 0) {
          currentPosition = 0;
          currentSpeed = 0;
        } else if (currentPosition > element.scrollHeight - element.clientHeight) {
          currentPosition = element.scrollHeight - element.clientHeight;
          currentSpeed = 0;
        }
        
        // 应用位置
        element.scrollTop = currentPosition;
        
        // 减速
        currentSpeed *= deceleration;
        
        // 继续动画或停止
        if (Math.abs(currentSpeed) > 0.5) {
          this.scrollAnimation = requestAnimationFrame(animate);
        } else {
          this.scrollAnimation = null;
        }
      };
      
      // 开始动画
      this.scrollAnimation = requestAnimationFrame(animate);
    },

    setCursorToEnd() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;

      try {
        const len = this.modelValue.length;
        
        if (this.isH5) {
          textarea.focus();
          
          if (typeof textarea.selectionStart !== 'undefined') {
            textarea.selectionStart = len;
            textarea.selectionEnd = len;
          }
        } else {
          if (uni && uni.createSelectorQuery) {
            const query = uni.createSelectorQuery().in(this);
            query.select('.optimized-textarea').context((res) => {
              if (res && res.context) {
                res.context.setSelectionRange(len, len);
              }
            }).exec();
          }
        }
        
        this.forceScrollToBottom();
      } catch (error) {
        console.error('设置光标位置失败:', error);
      }
    },

    setupMutationObserver() {
      if (this.isH5 && window.MutationObserver) {
        const textarea = this.getTextareaElement();
        if (!textarea) return;
        
        this.mutationObserver = new MutationObserver(mutations => {
          if (this.debugMode) {
            console.log('检测到DOM变化:', mutations.length);
          }
          
          setTimeout(() => {
            if (!this.userHasScrolled) {
              this.forceScrollToBottom();
            }
          }, 50);
        });
        
        const config = {
          childList: true,
          subtree: true,
          characterData: true,
          attributes: true
        };
        
        this.mutationObserver.observe(textarea, config);
      }
    },
    
    handleScroll(e) {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 记录用户主动滚动行为
      if (Date.now() - this.lastUserInteraction < 1000) {
        this.userHasScrolled = true;
        
        // 一段时间后重置用户滚动标志，但延长时间
        clearTimeout(this.resetScrollFlagTimer);
        this.resetScrollFlagTimer = setTimeout(() => {
          this.userHasScrolled = false;
        }, 5000); // 增加到5秒，给用户更多的阅读和编辑时间
      }
      
      // 检查是否滚动到顶部或底部
      const isAtTop = textarea.scrollTop <= 0;
      const isAtBottom = Math.abs(textarea.scrollTop + textarea.clientHeight - textarea.scrollHeight) <= 2;
      
      if (isAtTop) {
        textarea.classList.add('at-top');
        textarea.classList.remove('at-bottom');
      } else if (isAtBottom) {
        textarea.classList.remove('at-top');
        textarea.classList.add('at-bottom');
      } else {
        textarea.classList.remove('at-top');
        textarea.classList.remove('at-bottom');
      }
    },
    
    updateScrollIndicator(textarea, isAtBottom) {
      if (!this.hasOverflow) return;
      
      const container = this.$el;
      if (!container) return;
      
      if (isAtBottom) {
        container.classList.remove('show-bottom-indicator');
      } else {
        container.classList.add('show-bottom-indicator');
      }
    },
    
    setText(text) {
      // 设置文本时不应该重置用户滚动状态
      const wasInitialText = !this.initialTextLoaded;
      
      this.$emit('update:modelValue', text);
      
      this.$nextTick(() => {
        this.updateHeightAndCheck();
        
        // 只有首次加载文本时才自动滚动到底部
        if (wasInitialText) {
          setTimeout(() => {
            this.userHasScrolled = false; // 临时重置状态
            this.forceScrollToBottom();
            this.initialTextLoaded = true;
          }, 50);
          
          setTimeout(() => {
            this.userHasScrolled = false;
            this.forceScrollToBottom();
            this.setCursorToEnd();
          }, 200);
        }
      });
    },

    clearText() {
      this.$emit('update:modelValue', '');
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    },

    focus() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.focus();
        this.forceScrollToBottom();
      }
    },

    blur() {
      const textarea = this.getTextareaElement();
      if (textarea) {
        textarea.blur();
      }
    },
    
    updateHeightAndScroll() {
      this.updateHeightAndCheck();
      this.forceScrollToBottom();
    },

    forceScrollToBottom() {
      // 强制滚动（忽略userHasScrolled状态）
      this.scrollToBottom(true);
    },
    
    scrollToBottom(force = false) {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 如果用户已经手动滚动，且不是强制滚动，则不执行
      if (this.userHasScrolled && !force) {
        return;
      }
      
      // 简单直接地设置滚动位置
      try {
        textarea.scrollTop = textarea.scrollHeight;
      } catch (e) {
        console.error('滚动失败:', e);
      }
    },

    checkOverflow() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      // 判断内容是否超出最大高度
      const contentHeight = this.isH5 ? 
        textarea.scrollHeight : 
        this.countLines(this.modelValue) * 20;
      
      const hasOverflow = contentHeight > this.maxHeight;
      
      if (hasOverflow !== this.hasOverflow) {
        this.hasOverflow = hasOverflow;
        if (this.debugMode) {
          console.log('溢出状态变化:', hasOverflow ? '有溢出' : '无溢出');
        }
      }
    },

    // 更新检查处理内容变化
    handleContent() {
      this.updateHeightAndCheck();
      
      // 如果不是文本选择操作且用户没有手动滚动，滚动到底部
      if (!this.isTextSelectionActive() && !this.selectionEnabled && !this.userHasScrolled) {
        this.scrollToBottom();
      } else if (this.isTextSelectionActive() || this.selectionEnabled) {
        // 如果正在选择文本，确保不干扰选择
        this.$nextTick(() => {
          if (!document.activeElement || document.activeElement !== this.getTextareaElement()) {
            // 如果文本框不是活跃元素，尝试聚焦
            const textarea = this.getTextareaElement();
            if (textarea) textarea.focus();
          }
        });
      }
    },

    // 重写增强滚动方法，移除一些可能干扰的优化
    enhanceScrolling() {
      const textarea = this.getTextareaElement();
      if (!textarea || !this.isH5) return;
      
      // 确保滚动行为简单直接
      textarea.style.overflowY = 'auto';
      
      if (this.isIOS) {
        // iOS特殊处理
        textarea.style.webkitOverflowScrolling = 'touch';
      } else {
        // 其他平台，重置一些样式
        textarea.style.scrollBehavior = 'auto';
        textarea.style.willChange = 'auto';
      }
    },

    // 显式处理鼠标滚轮事件
    handleMouseWheel(e) {
      // 一旦检测到鼠标滚轮，立即标记用户已滚动
      this.userHasScrolled = true;
      
      if (this.debugMode) {
        console.log('检测到鼠标滚轮事件，禁用自动滚动');
      }
      
      // 延长用户滚动标记的持续时间
      clearTimeout(this.resetScrollFlagTimer);
      this.resetScrollFlagTimer = setTimeout(() => {
        this.userHasScrolled = false;
        if (this.debugMode) console.log('重置用户滚动标记');
      }, 10000); // 10秒内不自动滚动
    },

    // 增强处理键盘事件
    handleKeyDown(e) {
      // 处理Ctrl+A全选
      if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault(); // 阻止默认行为确保我们的全选生效
        this.selectionEnabled = true;
        
        // 立即执行全选
        try {
          this.selectAllText();
          
          // 特别处理HBuilder环境
          if (this.isHBuilder) {
            // 额外的全选处理
            const textarea = this.getTextareaElement();
            if (textarea) {
              // 尝试所有可能的全选方法
              if (typeof textarea.select === 'function') {
                textarea.select();
              }
              
              // 尝试设置选择范围到全文
              if (typeof textarea.setSelectionRange === 'function') {
                textarea.setSelectionRange(0, this.modelValue.length || 0);
              }
              
              // 使用document.execCommand
              try {
                document.execCommand('selectAll');
              } catch(e) {}
            }
          }
        } catch(err) {
          console.error('全选失败:', err);
        }
        
        return false;
      }
      
      // 处理Ctrl+V粘贴
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        console.log('检测到Ctrl+V粘贴快捷键');
        
        // 特别处理HBuilder环境
        if (this.isHBuilder) {
          try {
            // 检查剪贴板是否有图片（HBuilder环境）
            if (typeof plus !== 'undefined' && plus.pasteboard) {
              plus.pasteboard.getImageItems(items => {
                if (items && items.length > 0) {
                  console.log('HBuilder环境检测到粘贴的图片');
                  
                  // 获取第一个图片
                  const imageItem = items[0];
                  
                  // 触发图片粘贴事件
                  this.$emit('paste-image', {
                    path: imageItem.path,
                    type: imageItem.type || 'image/png'
                  });
                  
                  return;
                } else {
                  // 如果没有图片，尝试获取文本
                  this.tryGetClipboardText();
                }
              }, err => {
                console.error('获取剪贴板图片失败:', err);
                // 尝试获取文本
                this.tryGetClipboardText();
              });
              
              return false;
            } else {
              // 尝试使用navigator.clipboard API获取剪贴板内容
              this.tryGetClipboardText();
            }
          } catch(err) {
            console.error('尝试使用HBuilder剪贴板API失败:', err);
          }
        }
        
        // 不阻止默认行为，让系统处理粘贴
        return true;
      }
      
      // 处理Ctrl+Delete或Ctrl+Backspace清空文本框
      if ((e.ctrlKey || e.metaKey) && (e.key === 'Delete' || e.key === 'Backspace')) {
        // 如果有内容，清空文本框
        if (this.modelValue) {
          e.preventDefault(); // 阻止默认行为
          
          // 直接清空文本，不依赖全选
          this.$emit('update:modelValue', '');
          
          return false;
        }
      }
      
      // 处理Delete或Backspace删除选中内容
      if (e.key === 'Delete' || e.key === 'Backspace') {
        console.log('检测到Delete或Backspace键');
        
        try {
          // 检查是否有选中文本
          const textarea = this.getTextareaElement();
          let hasSelection = false;
          
          // 方法1：检查textarea的选择范围
          if (textarea && 
              typeof textarea.selectionStart !== 'undefined' && 
              textarea.selectionStart !== textarea.selectionEnd) {
            hasSelection = true;
          }
          
          // 方法2：检查document.getSelection
          if (!hasSelection && document.getSelection) {
            const selection = document.getSelection();
            if (selection && selection.type === 'Range' && selection.toString().length > 0) {
              hasSelection = true;
            }
          }
          
          // 方法3：检查是否可能是全选状态
          if (!hasSelection && document.activeElement === textarea && 
              this.selectionEnabled && this.modelValue) {
            console.log('可能是全选状态，尝试删除全部内容');
            e.preventDefault(); // 阻止默认行为
            this.$emit('update:modelValue', '');
            return false;
          }
          
          // 如果有选中文本，手动处理删除
          if (hasSelection) {
            console.log('检测到有选中文本，执行删除');
            e.preventDefault(); // 阻止默认行为
            this.deleteSelectedText();
            return false;
          }
        } catch(err) {
          console.error('处理Delete/Backspace键失败:', err);
        }
      }
      
      // 处理Ctrl+X剪切
      if ((e.ctrlKey || e.metaKey) && e.key === 'x') {
        // 确保在HBuilder环境下剪切功能正常工作
        if (this.isHBuilder) {
          try {
            // 先复制选中文本
            this.copySelectedText();
            // 然后删除选中文本
            setTimeout(() => {
              this.deleteSelectedText();
            }, 100);
          } catch(err) {
            console.error('剪切操作失败:', err);
          }
        }
      }
      
      // 处理Ctrl+C复制
      if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        // 确保在HBuilder环境下复制功能正常工作
        if (this.isHBuilder) {
          try {
            this.copySelectedText();
          } catch(err) {
            console.error('复制操作失败:', err);
          }
        }
      }
      
      // 处理Escape键，取消选择
      if (e.key === 'Escape') {
        this.selectionEnabled = false;
        
        // 取消选择
        try {
          const textarea = this.getTextareaElement();
          if (textarea && typeof textarea.selectionStart !== 'undefined') {
            const cursorPos = textarea.selectionEnd || 0;
            textarea.setSelectionRange(cursorPos, cursorPos);
          }
        } catch(err) {
          console.error('取消选择失败:', err);
        }
      }
    },
    
    // 尝试获取剪贴板文本内容
    tryGetClipboardText() {
      // 在HBuilder环境中，避免使用navigator.clipboard API
      if (this.isHBuilder) {
        console.log('HBuilder环境下避免使用navigator.clipboard API');
        
        // 尝试使用uni API
        this.tryUniClipboardAPI();
        return;
      }
      
      // 尝试使用navigator.clipboard API
      if (navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText()
          .then(text => {
            if (text) {
              console.log('从navigator.clipboard获取到文本');
              this.insertTextAtCursor(text);
            }
          })
          .catch(err => {
            console.error('navigator.clipboard.readText失败:', err);
            
            // 尝试使用uni API
            this.tryUniClipboardAPI();
          });
      } else {
        // 尝试使用uni API
        this.tryUniClipboardAPI();
      }
    },
    
    // 尝试使用uni API获取剪贴板内容
    tryUniClipboardAPI() {
      if (typeof uni !== 'undefined' && uni.getClipboardData) {
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              console.log('从uni.getClipboardData获取到文本');
              this.insertTextAtCursor(res.data);
            }
          },
          fail: (err) => {
            console.error('uni.getClipboardData失败:', err);
          }
        });
      }
    },
    
    // 增强删除选中文本方法
    deleteSelectedText() {
      console.log('执行删除选中文本方法');
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 获取当前文本和选择范围
        let text = this.modelValue || '';
        let start = 0;
        let end = text.length;
        
        // 尝试获取选择范围
        try {
          if (textarea.selectionStart !== undefined && textarea.selectionEnd !== undefined) {
            start = textarea.selectionStart;
            end = textarea.selectionEnd;
            console.log('获取到选择范围:', start, end);
          }
        } catch (e) {
          console.error('获取选择范围失败，将删除全部文本:', e);
        }
        
        // 如果没有选择范围或选择范围无效，检查是否为全选状态
        if (start === end) {
          // 检查是否可能是全选状态但未能正确获取范围
          if (document.activeElement === textarea && 
              (document.getSelection().toString() === text || 
               document.getSelection().type === 'Range')) {
            console.log('检测到可能是全选状态，执行全部清空');
            start = 0;
            end = text.length;
          } else {
            console.log('没有选择范围，不执行删除');
            return;
          }
        }
        
        console.log('删除选中部分，从', start, '到', end);
        
        // 创建新文本（删除选中部分）
        const newText = text.substring(0, start) + text.substring(end);
        
        // 更新文本
        this.$emit('update:modelValue', newText);
        
        // 重新设置光标位置
        this.$nextTick(() => {
          try {
            textarea.focus();
            if (typeof textarea.setSelectionRange === 'function') {
              textarea.setSelectionRange(start, start);
              }
            } catch (e) {
            console.error('设置光标位置失败:', e);
            }
          });
      } catch (e) {
        console.error('删除文本失败:', e);
        
        // 如果删除失败，尝试直接清空
        this.$emit('update:modelValue', '');
      }
    },

    // 在组件创建时添加平台特定的初始化
    setupAppFeatures() {
      if (!this.isApp) return;
      
      // 尝试增强APP环境的文本编辑功能
      try {
        // 监听uni的ready事件
        if (typeof uni !== 'undefined') {
          // 必要时设置全局APP样式
          if (typeof plus !== 'undefined' && plus.webview) {
            const currentWebview = plus.webview.currentWebview();
            if (currentWebview) {
              // 可能的APP特定优化
              currentWebview.setStyle({
                softinputMode: 'adjustResize' // 软键盘弹出时自动调整页面大小
              });
            }
          }
          
          // 在APP环境中启用更多选择功能
          this.$nextTick(() => {
            setTimeout(() => {
              this.enhanceAppTextSelection();
            }, 500);
          });
        }
      } catch (e) {
        console.error('APP功能增强失败:', e);
      }
    },

    // APP环境中的选择和编辑增强
    enhanceAppTextSelection() {
      if (!this.isApp) return;
      
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 1. 确保样式正确
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        textarea.style.webkitTouchCallout = 'default';
        
        // 2. 添加APP环境下的图片粘贴支持
        if (typeof plus !== 'undefined') {
          // 监听plus环境下的粘贴事件
          document.addEventListener('paste', (e) => {
            this.handlePaste(e);
          });
          
          // 添加图片粘贴支持
          this.setupAppImagePaste();
        }
        
        // 3. 尝试添加自定义菜单
        if (typeof plus !== 'undefined' && plus.nativeUI) {
          // 添加自定义长按操作
          textarea.addEventListener('longpress', (e) => {
            // 阻止默认行为
            e.preventDefault();
            
            // 设置选择模式
            this.isLongPress = true;
            this.selectionEnabled = true;
            
            // 触发振动
            if (plus.device && plus.device.vibrate) {
              plus.device.vibrate(50);
            }
            
            // 全选文本
            this.selectAllText();
            
            // 可以考虑显示自定义菜单
            plus.nativeUI.actionSheet({
              title: '文本操作',
              cancel: '取消',
              buttons: [
                {title: '全选'},
                {title: '复制'},
                {title: '剪切'},
                {title: '粘贴'},
                {title: '粘贴图片'},
                {title: '删除'}
              ]
            }, (e) => {
              // 处理菜单选择
              switch(e.index) {
                case 1: // 全选
                  this.selectAllText();
                  break;
                case 2: // 复制
                  this.copySelectedText();
                  break;
                case 3: // 剪切
                  this.cutSelectedText();
                  break;
                case 4: // 粘贴
                  // 尝试获取剪贴板内容并粘贴
                  this.tryGetClipboardText();
                  break;
                case 5: // 粘贴图片
                  this.tryPasteImage();
                  break;
                case 6: // 删除
                  this.deleteSelectedText();
                  break;
              }
            });
          });
        }
      } catch (e) {
        console.error('enhanceAppTextSelection失败:', e);
      }
    },
    
    // 设置APP环境下的图片粘贴功能
    setupAppImagePaste() {
      if (!this.isApp) return;
      
      try {
        // 针对plus环境
        if (typeof plus !== 'undefined' && plus.pasteboard) {
          // 可以在这里添加特定的初始化代码
          console.log('APP环境下启用图片粘贴支持');
        }
      } catch (e) {
        console.error('设置APP图片粘贴功能失败:', e);
      }
    },
    
    // 尝试粘贴图片
    tryPasteImage() {
      console.log('尝试粘贴图片，当前环境:', 
                 this.isH5 ? 'H5' : 
                 this.isApp ? 'APP' : 
                 this.isMiniProgram ? '小程序' : 
                 this.isHBuilder ? 'HBuilder' : '未知');
      
      try {
        // 针对不同平台实现图片粘贴
        if (this.isHBuilder || (this.isApp && typeof plus !== 'undefined' && plus.pasteboard)) {
          // HBuilder/APP环境
          console.log('使用plus.pasteboard API获取图片');
          
          plus.pasteboard.getImageItems(items => {
            if (items && items.length > 0) {
              console.log('获取到图片项:', items.length);
              
              // 获取第一个图片
              const imageItem = items[0];
              console.log('图片路径:', imageItem.path ? '有效' : '无效');
              
              // 触发图片粘贴事件
              this.$emit('paste-image', {
                path: imageItem.path,
                type: imageItem.type || 'image/png',
                source: 'tryPasteImage-plus'
              });
            } else {
              console.log('剪贴板中没有图片，尝试获取文本');
              
              // 尝试获取文本并检查是否是图片URL
              plus.pasteboard.getString(text => {
                if (text && this.isImageUrl(text)) {
                  console.log('剪贴板文本可能是图片URL:', text);
                  
                  // 触发图片粘贴事件
                  this.$emit('paste-image', {
                    path: text,
                    type: 'image/url',
                    source: 'tryPasteImage-plus-text'
                  });
                } else {
                  console.log('剪贴板文本不是图片URL');
                  
                  // 如果在H5环境中，打开文件选择器
                  if (this.isH5) {
                    this.showFileSelector();
                  }
                }
              });
            }
          }, err => {
            console.error('获取剪贴板图片失败:', err);
            
            // 如果在H5环境中，打开文件选择器
            if (this.isH5) {
              this.showFileSelector();
            }
          });
        } else if (this.isH5) {
          // H5环境
          console.log('H5环境尝试获取剪贴板图片');
          
          // 方法1: 使用navigator.clipboard API (现代浏览器)
          if (navigator.clipboard && navigator.clipboard.read) {
            console.log('尝试使用navigator.clipboard.read');
            
            navigator.clipboard.read()
              .then(clipboardItems => {
                console.log('读取到剪贴板项:', clipboardItems.length);
                
                let foundImage = false;
                
                for (const clipboardItem of clipboardItems) {
                  console.log('剪贴板项类型:', clipboardItem.types);
                  
                  for (const type of clipboardItem.types) {
                    if (type.startsWith('image/')) {
                      console.log('检测到图片类型:', type);
                      foundImage = true;
                      
                      clipboardItem.getType(type)
                        .then(blob => {
                          console.log('获取到图片blob:', blob.size);
                          
                          // 触发图片粘贴事件
                          this.$emit('paste-image', {
                            blob: blob,
                            type: type,
                            source: 'navigator-clipboard-read'
                          });
                        })
                        .catch(err => {
                          console.error('获取图片blob失败:', err);
                          // 如果获取blob失败，直接打开文件选择器
                          this.showFileSelector();
                        });
                      
                      break;
                    }
                  }
                  
                  if (foundImage) break;
                }
                
                if (!foundImage) {
                  console.log('剪贴板中没有图片，直接打开文件选择器');
                  this.showFileSelector();
                }
              })
              .catch(err => {
                console.error('读取剪贴板内容失败:', err);
                
                // 权限错误或其他错误，直接打开文件选择器
                console.log('无法访问剪贴板，打开文件选择器');
                this.showFileSelector();
              });
          } else {
            // 如果navigator.clipboard.read不可用，直接打开文件选择器
            console.log('navigator.clipboard.read不可用，打开文件选择器');
            this.showFileSelector();
          }
        } else if (this.isMiniProgram) {
          // 小程序环境
          console.log('小程序环境尝试获取剪贴板图片');
          
          if (typeof wx !== 'undefined' && wx.getClipboardData) {
            wx.getClipboardData({
              success: (res) => {
                console.log('获取到剪贴板数据:', res.data ? '有内容' : '无内容');
                
                if (res.data && this.isImageUrl(res.data)) {
                  console.log('剪贴板内容可能是图片URL:', res.data);
                  
                  // 可能是图片路径
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/png', // 默认类型
                    source: 'wx-getClipboardData'
                  });
                } else {
                  console.log('剪贴板中没有图片URL');
                }
              },
              fail: (err) => {
                console.error('获取剪贴板数据失败:', err);
              }
            });
          } else if (typeof uni !== 'undefined' && uni.getClipboardData) {
            // 尝试使用uni API
            uni.getClipboardData({
              success: (res) => {
                console.log('获取到uni剪贴板数据:', res.data ? '有内容' : '无内容');
                
                if (res.data && this.isImageUrl(res.data)) {
                  console.log('uni剪贴板内容可能是图片URL:', res.data);
                  
                  // 可能是图片路径
                  this.$emit('paste-image', {
                    path: res.data,
                    type: 'image/png', // 默认类型
                    source: 'uni-getClipboardData'
                  });
                } else {
                  console.log('uni剪贴板中没有图片URL');
                }
              }
            });
          } else {
            console.log('小程序环境无法直接访问剪贴板');
          }
        } else {
          console.log('当前环境不支持图片粘贴');
        }
      } catch (e) {
        console.error('尝试粘贴图片失败:', e);
        
        // 如果在H5环境中出错，打开文件选择器
        if (this.isH5) {
          console.log('粘贴图片出错，打开文件选择器');
          this.showFileSelector();
        }
      }
    },
    
    // 通过创建临时事件尝试粘贴图片
    tryPasteImageViaEvent() {
      console.log('尝试通过创建临时事件粘贴图片');
      
      // 在HBuilder环境中避免使用document.execCommand
      if (this.isHBuilder) {
        console.log('HBuilder环境不使用document.execCommand，避免callback错误');
        return;
      }
      
      try {
        // 创建一个临时的可编辑元素
        const tempDiv = document.createElement('div');
        tempDiv.contentEditable = true;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '0';
        tempDiv.style.width = '1px';
        tempDiv.style.height = '1px';
        tempDiv.style.opacity = '0';
        tempDiv.style.pointerEvents = 'none';
        
        // 确保元素可以接收粘贴事件
        tempDiv.setAttribute('tabindex', '-1');
        
        // 添加到文档
        document.body.appendChild(tempDiv);
        
        // 聚焦元素
        tempDiv.focus();
        
        // 添加粘贴事件监听
        let pasteDetected = false;
        const pasteHandler = (e) => {
          pasteDetected = true;
          console.log('临时元素捕获到粘贴事件');
          
          // 检查是否有图片
          if (e.clipboardData && e.clipboardData.files && e.clipboardData.files.length > 0) {
            const file = e.clipboardData.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('临时元素从clipboardData.files获取到图片:', file.type);
              e.preventDefault();
              e.stopPropagation();
              
              // 触发图片粘贴事件
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                source: 'temp-div-paste-files'
              });
            }
          }
        };
        
        tempDiv.addEventListener('paste', pasteHandler);
        
        // 尝试触发粘贴事件
        let pasteSuccess = false;
        try {
          pasteSuccess = document.execCommand('paste');
          console.log('document.execCommand("paste")结果:', pasteSuccess);
        } catch (execErr) {
          console.error('document.execCommand("paste")失败:', execErr);
        }
        
        // 检查是否粘贴了图片
        setTimeout(() => {
          // 移除粘贴事件监听器
          tempDiv.removeEventListener('paste', pasteHandler);
          
          // 检查是否有图片元素
          const images = tempDiv.querySelectorAll('img');
          
          if (images.length > 0) {
            console.log('检测到粘贴的图片元素:', images.length);
            
            // 处理第一个图片
            this.processImageElement(images[0]);
          } else {
            // 检查是否有背景图片
            const computedStyle = window.getComputedStyle(tempDiv);
            const backgroundImage = computedStyle.backgroundImage;
            
            if (backgroundImage && backgroundImage !== 'none' && backgroundImage.indexOf('url(') !== -1) {
              console.log('检测到背景图片:', backgroundImage);
              
              // 提取URL
              const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
              if (urlMatch && urlMatch[1]) {
                const imageUrl = urlMatch[1];
                
                // 触发图片粘贴事件
                this.$emit('paste-image', {
                  path: imageUrl,
                  type: 'image/url',
                  source: 'temp-div-background'
                });
              }
            } else if (!pasteDetected && !pasteSuccess) {
              console.log('未检测到粘贴的图片，尝试使用文件选择器');
              
              // 如果没有检测到粘贴事件和图片，提供文件选择器作为备选
              if (this.isH5) {
                setTimeout(() => {
                  // 不显示任何提示，直接返回
                  return;
                }, 300);
              }
            }
          }
          
          // 清理临时元素
          try {
            document.body.removeChild(tempDiv);
          } catch (cleanupErr) {
            console.error('清理临时元素失败:', cleanupErr);
          }
        }, 200);
      } catch (err) {
        console.error('通过事件粘贴图片失败:', err);
        
        // 如果失败，尝试使用文件选择器
        if (this.isH5) {
          setTimeout(() => {
            this.showFileSelector();
          }, 300);
        }
      }
    },
    
    // 处理从粘贴事件中获取的图片元素
    processImageElement(imgElement) {
      if (!imgElement) return;
      
      try {
        // 获取图片src
        const imgSrc = imgElement.src;
        
        if (imgSrc) {
          console.log('获取到图片src:', imgSrc.substring(0, 30) + (imgSrc.length > 30 ? '...' : ''));
          
          // 如果是data URL，转换为blob
          if (imgSrc.startsWith('data:')) {
            this.dataURLToBlob(imgSrc, (blob) => {
              if (blob) {
                // 触发图片粘贴事件
                this.$emit('paste-image', {
                  blob: blob,
                  type: blob.type || 'image/png',
                  source: 'paste-event-data-url'
                });
              } else {
                // 如果转换失败，直接使用data URL
                this.$emit('paste-image', {
                  path: imgSrc,
                  type: imgSrc.split(';')[0].replace('data:', ''),
                  source: 'paste-event-data-url-fallback'
                });
              }
            });
          } else {
            // 普通URL
            this.$emit('paste-image', {
              path: imgSrc,
              type: 'image/url',
              source: 'paste-event-url'
            });
          }
        } else {
          console.log('图片元素没有src属性');
          
          // 尝试从style中提取背景图片
          const style = imgElement.getAttribute('style');
          if (style && style.includes('background-image')) {
            const match = style.match(/background-image:\s*url\(['"]?(.*?)['"]?\)/i);
            if (match && match[1]) {
              this.$emit('paste-image', {
                path: match[1],
                type: 'image/url',
                source: 'paste-event-background'
              });
            }
          }
        }
      } catch (err) {
        console.error('处理图片元素失败:', err);
      }
    },

    // 将Data URL转换为Blob
    dataURLToBlob(dataURL, callback) {
      try {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        
        const blob = new Blob([u8arr], { type: mime });
        callback(blob);
      } catch (err) {
        console.error('转换Data URL失败:', err);
        callback(null);
      }
    },

    // 复制选中文本
    copySelectedText() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 获取选择的文本
        let selectedText = '';
        
        if (this.isH5) {
          // H5环境
          if (window.getSelection) {
            selectedText = window.getSelection().toString();
          } else if (document.selection) {
            selectedText = document.selection.createRange().text;
          }
        }
        
        // 如果没有通过window选择获取到文本，就从textarea本身获取
        if (!selectedText && textarea.selectionStart !== undefined) {
          const start = textarea.selectionStart;
          const end = textarea.selectionEnd;
          selectedText = this.modelValue.substring(start, end);
        }
        
        // 如果还是没有选中的文本，则尝试全选
        if (!selectedText) {
          selectedText = this.modelValue;
        }
        
        // 复制到剪贴板
        if (selectedText) {
          if (uni && uni.setClipboardData) {
            uni.setClipboardData({
              data: selectedText,
              success: () => {
                uni.showToast({
                  title: '已复制',
                  icon: 'none',
                  duration: 1500
                });
              }
            });
          } else if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(selectedText)
              .then(() => {
                if (uni) {
                  uni.showToast({
                    title: '已复制',
                    icon: 'none',
                    duration: 1500
                  });
                }
              });
          }
        }
      } catch (e) {
        console.error('复制文本失败:', e);
      }
    },

    // 剪切选中文本
    cutSelectedText() {
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 先复制文本
        this.copySelectedText();
        
        // 然后删除选中的文本
        this.deleteSelectedText();
      } catch (e) {
        console.error('剪切文本失败:', e);
      }
    },

    // 添加发送方法，外部可调用
    sendMessage() {
      // 如果没有内容，不处理
      if (!this.modelValue || this.modelValue.trim() === '') {
        return false;
      }
      
      // 发送前的内容
      const content = this.modelValue;
      
      // 清空输入框
      this.$emit('update:modelValue', '');
      
      // 重置用户滚动标志
      this.userHasScrolled = false;
      
      // 更新高度
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
      
      // 触发发送事件，传递发送的内容
      this.$emit('send', content);
      
      return true;
    },

    // 增强插入文本到光标位置的方法
    insertTextAtCursor(text) {
      if (!text) return;
      console.log('执行插入文本到光标位置:', text.substring(0, 20) + (text.length > 20 ? '...' : ''));
      
      const textarea = this.getTextareaElement();
      let newText = this.modelValue || '';
      
      try {
        // 尝试获取当前选择位置
        let start = 0;
        let end = 0;
        
        if (textarea && typeof textarea.selectionStart !== 'undefined') {
          start = textarea.selectionStart || 0;
          end = textarea.selectionEnd || 0;
          } else {
          // 如果无法获取选择位置，默认追加到末尾
          start = end = newText.length;
        }
        
        // 插入文本
        newText = newText.substring(0, start) + text + newText.substring(end);
        
        // 更新文本
        this.$emit('update:modelValue', newText);
        
        // 更新UI和光标位置
        this.$nextTick(() => {
          try {
            // 尝试聚焦和设置光标位置
            if (textarea) {
              textarea.focus();
              
              if (typeof textarea.setSelectionRange === 'function') {
                const newPosition = start + text.length;
                textarea.setSelectionRange(newPosition, newPosition);
              }
            }
          } catch (err) {
            console.error('设置光标位置失败:', err);
          }
          
          // 更新高度和滚动
          this.updateHeightAndCheck();
          this.forceScrollToBottom();
          
          // 200ms后再次滚动到底部，确保内容完全加载
          setTimeout(() => {
            this.forceScrollToBottom();
          }, 200);
        });
      } catch (err) {
        console.error('插入文本失败，尝试直接追加:', err);
        
        // 出错时的备用方案：直接追加到末尾
        try {
          newText = newText + text;
        this.$emit('update:modelValue', newText);
        
        this.$nextTick(() => {
            this.updateHeightAndCheck();
            this.forceScrollToBottom();
          });
        } catch (backupErr) {
          console.error('备用插入方法也失败:', backupErr);
        }
      }
      
      // 触发粘贴事件
      this.$emit('paste', { detail: { value: text } });
    },

    // 添加全选按钮点击事件
    handleSelectAllButtonClick() {
      if (this.disabled) return;
      
      console.log('全选按钮被点击');
      this.selectAllText();
      
      // 选择后显示提示
      uni.showToast({
        title: '已全选',
        icon: 'none',
        duration: 1000
      });
    },

    // 添加全选并复制按钮点击事件
    handleSelectAndCopyButtonClick() {
      if (this.disabled || !this.modelValue) return;
      
      console.log('全选并复制按钮被点击');
      
      // 直接将文本复制到剪贴板
      if (uni && uni.setClipboardData) {
        uni.setClipboardData({
          data: this.modelValue,
          success: () => {
            // 显示视觉反馈
            this.selectionEnabled = true;
            
            // 尝试执行全选以提供视觉反馈
            this.selectAllText();
            
            // 提示用户
            uni.showToast({
              title: '内容已复制到剪贴板',
              icon: 'none',
              duration: 1500
            });
            
            // 1.5秒后重置选择状态
            setTimeout(() => {
              this.selectionEnabled = false;
            }, 1500);
          },
          fail: () => {
            uni.showToast({
              title: '复制失败，请手动操作',
              icon: 'none',
              duration: 1500
            });
          }
        });
      } else {
        // 尝试常规全选方法
        this.selectAllText();
        
        // 尝试复制
        this.copySelectedText();
      }
    },
    
    // 处理选择开始事件
    handleSelectStart(e) {
      // 设置选择模式
      this.selectionEnabled = true;
      
      if (this.debugMode) {
        console.log('选择文本开始');
      }
    },
    
    // 处理鼠标抬起事件，用于检测文本选择
    handleMouseUp(e) {
      // 短暂延迟检查是否有文本被选中
      setTimeout(() => {
        if (this.isTextSelectionActive()) {
          this.selectionEnabled = true;
          
          if (this.debugMode) {
            console.log('检测到文本选择');
          }
        } else {
          // 如果没有文本被选中，可以考虑重置选择状态
          // 但为了不干扰用户操作，这里不立即重置
        }
      }, 100);
    },
    
    // 添加全选并清空按钮点击事件
    handleSelectAndClearButtonClick() {
      if (this.disabled) return;
      
      console.log('全选并清空按钮被点击');
      
      // 直接清空文本，不依赖全选
      this.$emit('update:modelValue', '');
      
      // 提示用户
      uni.showToast({
        title: '内容已清空',
        icon: 'none',
        duration: 1500
      });
      
      // 重置选择状态
      this.selectionEnabled = false;
      
      // 更新高度
      this.$nextTick(() => {
        this.updateHeightAndCheck();
      });
    },

    // 增强全选方法 - 修复"this.selectAllText is not a function"错误
    selectAllText() {
      console.log('执行全选文本方法');
      const textarea = this.getTextareaElement();
      if (!textarea) return;
      
      try {
        // 设置选择状态
        this.selectionEnabled = true;
        
        // 使用多种方法尝试全选，提高成功率
        // 方法1: 原生select方法
        if (typeof textarea.select === 'function') {
          textarea.focus();
          textarea.select();
          console.log('使用textarea.select()方法全选');
        }
        
        // 方法2: 设置选择范围
        if (typeof textarea.setSelectionRange === 'function') {
          textarea.focus();
          textarea.setSelectionRange(0, this.modelValue.length || 0);
          console.log('使用setSelectionRange方法全选');
        }
        
        // 方法3: 使用document.execCommand
        try {
          document.execCommand('selectAll');
          console.log('使用document.execCommand("selectAll")方法全选');
        } catch(e) {
          console.log('document.execCommand("selectAll")失败');
        }
        
        // 方法4: 对于特定平台的API
        if (this.isApp && uni && uni.createSelectorQuery) {
          // 通过uni API尝试全选
          uni.createSelectorQuery()
            .in(this)
            .select('.optimized-textarea')
            .context((res) => {
              if (res && res.context) {
                res.context.focus();
                res.context.setSelectionRange(0, this.modelValue.length || 0);
                console.log('使用uni API全选');
              }
            }).exec();
        }
        
        // 手动设置文本选择样式
        textarea.classList.add('selecting-text');
        
        // 创建一个标记，表示已经执行过全选操作
        this._hasSelectedAll = true;
        
        // 设置一个延时，确保选择状态被正确标记
        setTimeout(() => {
          // 再次确认选择状态
          if (document.activeElement === textarea) {
            this.selectionEnabled = true;
            console.log('确认全选状态已设置');
          }
        }, 100);
        
      } catch (e) {
        console.error('全选文本失败:', e);
        
        // 如果全选失败，至少标记选择状态
        this.selectionEnabled = true;
      }
    },

    // 添加粘贴按钮点击事件处理
    handlePasteButtonClick() {
      console.log('粘贴按钮被点击');
      
      // 显示加载提示
      uni.showLoading({
        title: '获取剪贴板内容...',
        mask: true
      });
      
      // 尝试多种方法获取剪贴板内容
      this.getClipboardContentMultiMethod((text) => {
        // 隐藏加载提示
        uni.hideLoading();
        
        if (text) {
          // 直接追加文本到末尾，不考虑光标位置，确保内容能被添加
          const newText = (this.modelValue || '') + text;
          this.$emit('update:modelValue', newText);
          
          // 更新UI
          this.$nextTick(() => {
            this.updateHeightAndCheck();
            this.forceScrollToBottom();
            
            // 提供成功反馈
            uni.showToast({
              title: '已粘贴内容',
              icon: 'success',
              duration: 1500
            });
          });
        } else {
          // 提供失败反馈
          uni.showToast({
            title: '剪贴板为空或无法访问',
            icon: 'none',
            duration: 1500
          });
        }
      });
    },
    
    // 使用多种方法尝试获取剪贴板内容
    getClipboardContentMultiMethod(callback) {
      let contentObtained = false;
      let timeoutId = setTimeout(() => {
        if (!contentObtained) {
          console.log('获取剪贴板内容超时');
          callback(''); // 超时返回空字符串
        }
      }, 3000); // 3秒超时
      
      // 方法1: 使用plus.clipboard API
      if (typeof plus !== 'undefined' && plus.clipboard) {
        try {
          plus.clipboard.get((text) => {
            clearTimeout(timeoutId);
            contentObtained = true;
            
            if (text) {
              console.log('成功通过plus.clipboard获取内容');
              callback(text);
            } else {
              // 尝试下一个方法
              this.tryUniClipboardAPI(callback);
            }
          }, (err) => {
            console.error('plus.clipboard.get失败:', err);
            // 尝试下一个方法
            this.tryUniClipboardAPI(callback);
          });
        } catch (err) {
          console.error('使用plus.clipboard出错:', err);
          // 尝试下一个方法
          this.tryUniClipboardAPI(callback);
        }
      } else {
        // 尝试下一个方法
        this.tryUniClipboardAPI(callback);
      }
    },
    
    // 尝试使用uni API获取剪贴板内容
    tryUniClipboardAPI(callback) {
      if (typeof uni !== 'undefined' && uni.getClipboardData) {
        uni.getClipboardData({
          success: (res) => {
            if (res.data) {
              console.log('成功通过uni.getClipboardData获取内容');
              callback(res.data);
            } else {
              // 尝试其他方法
              this.tryNavigatorClipboard(callback);
            }
          },
          fail: (err) => {
            console.error('uni.getClipboardData失败:', err);
            // 尝试其他方法
            this.tryNavigatorClipboard(callback);
          }
        });
      } else {
        // 尝试其他方法
        this.tryNavigatorClipboard(callback);
      }
    },
    
    // 尝试使用navigator.clipboard API
    tryNavigatorClipboard(callback) {
      if (navigator && navigator.clipboard && navigator.clipboard.readText) {
        navigator.clipboard.readText()
          .then((text) => {
            if (text) {
              console.log('成功通过navigator.clipboard获取内容');
              callback(text);
            } else {
              // 尝试其他方法
              this.tryDocumentExecCommand(callback);
            }
          })
          .catch((err) => {
            console.error('navigator.clipboard.readText失败:', err);
            // 尝试其他方法
            this.tryDocumentExecCommand(callback);
          });
      } else {
        // 尝试其他方法
        this.tryDocumentExecCommand(callback);
      }
    },
    
    // 尝试使用document.execCommand
    tryDocumentExecCommand(callback) {
      try {
        if (document && document.execCommand) {
          const textarea = this.getTextareaElement();
          if (textarea) {
            textarea.focus();
            const result = document.execCommand('paste');
            if (result) {
              console.log('成功通过document.execCommand获取内容');
              // 这种方法直接粘贴到了文本框，不需要额外处理
              callback(''); // 返回空字符串，因为内容已经被插入
            } else {
              console.log('document.execCommand("paste")失败');
              callback(''); // 所有方法都失败
            }
          } else {
            callback(''); // 所有方法都失败
          }
        } else {
          callback(''); // 所有方法都失败
        }
      } catch (err) {
        console.error('document.execCommand("paste")出错:', err);
        callback(''); // 所有方法都失败
      }
    },

    // 处理系统粘贴按钮点击
    handleSystemPaste() {
      console.log('系统粘贴按钮被点击');
      
      // 获取文本框元素
      const textarea = this.getTextareaElement();
      if (!textarea) {
        console.error('找不到文本框元素');
        return;
      }
      
      // 聚焦文本框
      textarea.focus();
      
      try {
        // 尝试使用document.execCommand触发粘贴
        if (document && document.execCommand) {
          const result = document.execCommand('paste');
          console.log('document.execCommand("paste")结果:', result);
          
          // 不显示任何提示，无论成功与否
        } else {
          // 不显示任何提示
          console.log('document.execCommand不可用');
        }
      } catch (err) {
        console.error('触发系统粘贴失败:', err);
        // 不显示任何提示
      }
    },

    // 创建隐藏的文件输入元素作为备选方案
    createHiddenFileInput() {
      if (!this.isH5 || typeof document === 'undefined') return;
      
      try {
        // 检查是否已经存在
        let fileInput = document.getElementById('hidden-image-input');
        if (fileInput) {
          document.body.removeChild(fileInput);
        }
        
        // 创建新的文件输入元素
        fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'hidden-image-input';
        fileInput.accept = 'image/*';
        fileInput.style.position = 'absolute';
        fileInput.style.top = '-9999px';
        fileInput.style.left = '-9999px';
        fileInput.style.opacity = '0';
        fileInput.style.pointerEvents = 'none';
        fileInput.style.visibility = 'hidden';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
        
        // 添加变更事件监听器
        fileInput.addEventListener('change', (e) => {
          console.log('文件选择器变更事件触发');
          if (fileInput.files && fileInput.files.length > 0) {
            const file = fileInput.files[0];
            if (file && file.type.indexOf('image') !== -1) {
              console.log('从文件输入获取到图片:', file.type, file.name, file.size);
              
              // 触发图片粘贴事件
              this.$emit('paste-image', {
                blob: file,
                type: file.type,
                name: file.name,
                size: file.size,
                source: 'file-input'
              });
              
              // 重置文件输入，以便可以再次选择相同的文件
              fileInput.value = '';
            }
          }
        });
        
        // 添加到文档
        document.body.appendChild(fileInput);
        
        console.log('创建了隐藏的文件输入元素作为备选方案');
      } catch (err) {
        console.error('创建隐藏文件输入元素失败:', err);
      }
    },
    
    // 显示文件选择器作为粘贴备选方案
    showFileSelector() {
      if (!this.isH5 || typeof document === 'undefined') return;
      
      try {
        let fileInput = document.getElementById('hidden-image-input');
        if (!fileInput) {
          // 如果不存在，先创建
          this.createHiddenFileInput();
          fileInput = document.getElementById('hidden-image-input');
        }
        
        if (fileInput) {
          // 重置value确保能够选择相同的文件
          fileInput.value = '';
          
          // 确保文件输入元素可见且可交互
          fileInput.style.position = 'fixed';
          fileInput.style.top = '50%';
          fileInput.style.left = '50%';
          fileInput.style.transform = 'translate(-50%, -50%)';
          fileInput.style.opacity = '0';
          fileInput.style.pointerEvents = 'auto';
          fileInput.style.visibility = 'visible';
          fileInput.style.width = '100%';
          fileInput.style.height = '100%';
          fileInput.style.zIndex = '9999';
          
          // 点击文件输入元素
          console.log('触发文件选择器点击');
          fileInput.click();
          
          // 点击后恢复隐藏状态
          setTimeout(() => {
            fileInput.style.position = 'absolute';
            fileInput.style.top = '-9999px';
            fileInput.style.left = '-9999px';
            fileInput.style.transform = 'none';
            fileInput.style.pointerEvents = 'none';
            fileInput.style.visibility = 'hidden';
            fileInput.style.width = '1px';
            fileInput.style.height = '1px';
            fileInput.style.zIndex = '-1';
          }, 500);
        } else {
          console.error('找不到文件输入元素');
          
          // 尝试直接创建并点击一个临时的文件输入
          const tempInput = document.createElement('input');
          tempInput.type = 'file';
          tempInput.accept = 'image/*';
          tempInput.style.position = 'fixed';
          tempInput.style.top = '50%';
          tempInput.style.left = '50%';
          tempInput.style.transform = 'translate(-50%, -50%)';
          tempInput.style.opacity = '0';
          tempInput.style.zIndex = '9999';
          
          tempInput.addEventListener('change', (e) => {
            if (tempInput.files && tempInput.files.length > 0) {
              const file = tempInput.files[0];
              if (file && file.type.indexOf('image') !== -1) {
                console.log('从临时文件输入获取到图片:', file.type);
                
                // 触发图片粘贴事件
                this.$emit('paste-image', {
                  blob: file,
                  type: file.type,
                  source: 'temp-file-input'
                });
              }
              
              // 清理临时元素
              document.body.removeChild(tempInput);
            }
          });
          
          document.body.appendChild(tempInput);
          tempInput.click();
        }
      } catch (err) {
        console.error('显示文件选择器失败:', err);
        
        // 最后的备选方案：使用uni API
        if (typeof uni !== 'undefined' && uni.chooseImage) {
          uni.chooseImage({
            count: 1,
            success: (res) => {
              if (res.tempFilePaths && res.tempFilePaths.length > 0) {
                this.$emit('paste-image', {
                  path: res.tempFilePaths[0],
                  type: 'image/png',
                  source: 'uni-choose-image'
                });
              }
            }
          });
        }
      }
    },

    // 尝试使用navigator.clipboard API粘贴图片
    tryPasteImageWithNavigatorClipboard(showPrompt = true) {
      if (!this.isH5 || typeof navigator === 'undefined' || !navigator.clipboard) {
        return;
      }
      
      console.log('尝试使用navigator.clipboard API粘贴图片');
      
      try {
        // 检查是否支持read方法
        if (navigator.clipboard.read) {
          navigator.clipboard.read()
            .then(clipboardItems => {
              console.log('成功读取剪贴板项:', clipboardItems.length);
              
              let imagePromises = [];
              
              // 处理所有剪贴板项
              for (const clipboardItem of clipboardItems) {
                // 检查可用的类型
                for (const type of clipboardItem.types) {
                  if (type.startsWith('image/')) {
                    console.log('检测到图片类型:', type);
                    
                    // 获取图片blob
                    const imagePromise = clipboardItem.getType(type)
                      .then(blob => {
                        console.log('获取到图片blob:', blob.size);
                        
                        // 触发图片粘贴事件
                        this.$emit('paste-image', {
                          blob: blob,
                          type: type,
                          source: 'navigator-clipboard-read'
                        });
      
      return true;
                      })
                      .catch(err => {
                        console.error('获取图片blob失败:', err);
                        return false;
                      });
                    
                    imagePromises.push(imagePromise);
                    break;
                  }
                }
              }
              
              // 如果没有找到图片，检查是否有其他备选方案
              if (imagePromises.length === 0) {
                console.log('剪贴板中没有检测到图片，尝试其他方法');
                this.tryPasteImageViaEvent();
              }
              
              return Promise.all(imagePromises);
            })
            .catch(err => {
              console.error('读取剪贴板失败:', err);
              
              // 如果是权限错误，提示用户
              if (err.name === 'NotAllowedError' || err.message.includes('permission')) {
                console.log('剪贴板访问被拒绝，可能需要权限');

                // 不显示任何提示对话框
                if (showPrompt && this.isH5) {
                  this.showFileSelector();
                }
              } else {
                // 其他错误，尝试备选方案
                this.tryPasteImageViaEvent();
              }
            });
        } else {
          // 不支持read方法，尝试备选方案
          console.log('navigator.clipboard.read不可用，尝试其他方法');
          this.tryPasteImageViaEvent();
        }
      } catch (err) {
        console.error('使用navigator.clipboard API失败:', err);
        this.tryPasteImageViaEvent();
      }
    },

    
  }
};
</script>

<style>
.optimized-textarea-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: visible;
  box-sizing: border-box;
}

.optimized-textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 12px 16px;
  padding-right: 60px; /* 仅为发送按钮留出空间 */
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid rgba(77, 157, 255, 0.3);
  border-radius: 30px;
  background-color: rgba(45, 45, 55, 0.6);
  color: rgba(255, 255, 255, 0.9);
  outline: none;
  resize: none;
  transition: border-color 0.3s, background-color 0.3s;
  overflow-y: auto !important; 
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: auto; /* 允许正常滚动行为 */
  touch-action: auto; /* 保持原生触摸行为 */
  position: relative;
  /* 确保文本可选择 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  /* 允许iOS长按显示菜单 */
  -webkit-touch-callout: default !important;
  /* 设置文本光标 */
  cursor: text;
  /* 移除可能干扰滚动的属性 */
  transform: none;
  -webkit-transform: none;
  will-change: auto;
  scroll-behavior: auto;
}

.optimized-textarea:focus {
  border-color: rgba(77, 157, 255, 0.8);
  box-shadow: 0 0 0 2px rgba(77, 157, 255, 0.3), 0 2px 12px rgba(77, 157, 255, 0.4);
  background-color: rgba(40, 40, 60, 0.6);
}

.animated-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  pointer-events: none;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  transition: opacity 0.3s;
}

.is-focused .placeholder-text {
  opacity: 0.6;
}

.h5-platform .optimized-textarea::-webkit-scrollbar {
  width: 3px;
  background-color: transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  transition: background-color 0.3s;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

.h5-platform .optimized-textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 优化滚动提示指示器 */
.optimized-textarea-container.has-overflow::after {
  content: '';
  position: absolute;
  bottom: 10px;
  right: 55px;
  width: 4px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(77, 157, 255, 0.7), transparent);
  border-radius: 2px;
  pointer-events: none;
  opacity: 0.8;
  animation: scrollIndicator 1.5s infinite;
}

@keyframes scrollIndicator {
  0% { opacity: 0.4; height: 15px; }
  50% { opacity: 0.8; height: 20px; }
  100% { opacity: 0.4; height: 15px; }
}

/* iOS设备优化 */
@supports (-webkit-touch-callout: none) {
  .optimized-textarea {
    -webkit-overflow-scrolling: touch !important;
    padding-top: 13px; /* 微调iOS上的文字位置 */
    -webkit-tap-highlight-color: rgba(77, 157, 255, 0.2);
    /* 允许iOS文本选择菜单 */
    -webkit-touch-callout: default !important;
    -webkit-user-select: text !important;
    /* 移除干扰滚动的样式 */
    -webkit-transform: none;
    transform: none;
    backface-visibility: visible;
  }
  
  /* iOS设备上加强文本选择样式 */
  .optimized-textarea::selection {
    background-color: rgba(77, 157, 255, 0.4);
  }
}

.debug-bottom-marker {
  position: absolute;
  right: 8px;
  bottom: 8px;
  color: rgba(0, 255, 255, 0.7);
  font-size: 16px;
  pointer-events: none;
  z-index: 100;
}

.h5-platform .optimized-textarea {
  overflow-y: auto !important;
}

.mp-platform .optimized-textarea {
  position: relative;
  z-index: 1;
}

.app-platform .optimized-textarea {
  position: relative;
  z-index: 1;
}

.has-overflow .optimized-textarea {
  box-shadow: inset 0 -8px 8px -8px rgba(77, 157, 255, 0.3);
}

@media (pointer: coarse) {
  .optimized-textarea {
    font-size: 16px;
  }
  
  .h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
    width: 6px;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s;
}

.show-indicator {
  opacity: 1;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.3; width: 30px; }
  100% { opacity: 0.7; width: 40px; }
}

.debug-scroll-indicator {
  position: absolute;
  right: 5px;
  width: 3px;
  background: rgba(100, 200, 255, 0.5);
  border-radius: 3px;
  pointer-events: none;
  z-index: 100;
}

@keyframes pulse-border {
  0% { border-color: rgba(77, 157, 255, 0.3); }
  50% { border-color: rgba(77, 157, 255, 0.6); }
  100% { border-color: rgba(77, 157, 255, 0.3); }
}

.optimized-textarea.at-top,
.optimized-textarea.at-bottom {
  animation: pulse-border 1s ease infinite;
}

.optimized-textarea.at-top {
  box-shadow: inset 0 4px 8px -4px rgba(77, 157, 255, 0.3);
}

.optimized-textarea.at-bottom {
  box-shadow: inset 0 -4px 8px -4px rgba(77, 157, 255, 0.3);
}

.send-button-slot {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optimized-textarea-container.show-bottom-indicator::after {
  content: '↓';
  position: absolute;
  bottom: 8px;
  right: 45px;
  width: 20px;
  height: 20px;
  background-color: rgba(77, 157, 255, 0.7);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  animation: bounce 1s infinite alternate;
  pointer-events: none;
  z-index: 10;
}

@keyframes bounce {
  0% { transform: translateY(0); }
  100% { transform: translateY(-3px); }
}

.h5-platform .optimized-textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.4) transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar {
  width: 5px;
  background-color: transparent;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 10px;
}

.h5-platform .optimized-textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 文本选择样式 */
.optimized-textarea::selection {
  background: rgba(77, 157, 255, 0.4);
  color: #ffffff;
}

/* Android设备优化 */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  .optimized-textarea {
    overflow-y: auto;
    scrollbar-width: thin;
    /* 确保Android上的滚动行为正常 */
    touch-action: auto;
    -webkit-transform: none;
    transform: none;
  }
}

/* 触摸状态优化 */
.optimized-textarea-container.selecting .optimized-textarea {
  /* 文本选择模式下优化样式 */
  touch-action: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -webkit-touch-callout: default !important;
  /* 添加视觉反馈 */
  background-color: rgba(50, 50, 70, 0.7) !important;
  border-color: rgba(77, 157, 255, 0.7) !important;
  box-shadow: inset 0 0 0 1px rgba(77, 157, 255, 0.8) !important;
}

/* APP环境下文本选择强化 */
.app-platform .optimized-textarea {
  caret-color: rgba(77, 157, 255, 0.9);
  -webkit-user-select: auto;
  user-select: auto;
  -webkit-touch-callout: default;
  touch-callout: default;
  transition-property: border-color, background-color;
  transition-duration: 0.3s;
}

/* 加强文本选择视觉样式 */
.optimized-textarea::selection {
  background-color: rgba(77, 157, 255, 0.5) !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* HBuilder环境特殊样式 */
.optimized-textarea.selecting-text {
  background-color: rgba(50, 50, 70, 0.7) !important;
  border-color: rgba(77, 157, 255, 0.7) !important;
  box-shadow: inset 0 0 0 1px rgba(77, 157, 255, 0.8) !important;
}

/* 粘贴按钮样式 */
.paste-button {
  display: none;
}

.paste-button:active {
  display: none;
}

.paste-icon {
  display: none;
}

.paste-text {
  display: none;
}

/* 粘贴按钮样式 - 大号 */
.paste-button-large {
  display: none;
}

.paste-button-large:active {
  display: none;
}

@keyframes pulse-button {
  0% { box-shadow: 0 0 0 0 rgba(77, 157, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(77, 157, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(77, 157, 255, 0); }
}

/* 粘贴按钮样式 - 小号 */
.paste-button-small {
  display: none;
}

.paste-button-small:active {
  display: none;
}

.paste-button-hbuilder {
  display: none;
}

.paste-button-hbuilder:active {
  display: none;
}

.paste-icon-text {
  display: none;
}

/* HBuilder环境特殊样式 */
.hbuilder-textarea {
  -webkit-user-select: text !important;
  user-select: text !important;
  -webkit-touch-callout: default !important;
  cursor: text !important;
  -webkit-tap-highlight-color: rgba(77, 157, 255, 0.3) !important;
}


</style> 




