---
type: "always_apply"
---

# AI行为规则 - 统一规范

## 🚨 **核心原则：极度保守修改**
**只修改用户明确要求的部分，绝对不要动其他任何代码！**

### **环境配置**
**项目运行的端口端口：5173，这是Vite开发服务器的端口**

## 🚫 **AI绝对禁止的破坏性习惯**

### **1. 禁止"优化"心理**
- 看到代码不要想着改进，除非用户明确要求
- 不要为了"完美"而重写现有代码
- 不要添加用户没有要求的功能

### **2. 禁止"统一"冲动**
- 看到相似代码不要想着统一，除非用户明确要求
- 不要为了"一致性"而修改其他接口
- 不要批量修改相似的代码结构

### **3. 禁止"完整性"错觉**
- 修改一个地方不要认为需要修改相关的所有地方
- 不要因为修改一个函数就修改所有调用的地方
- 不要扩大修改范围

### **4. 禁止"顺便修改"**
- 绝对不要在修改目标代码时"顺便"修改其他代码
- 优先修改单个相关文件，多文件修改需要说明原因
- 遵循分层修改范围控制原则

## ❌ **AI绝对禁止使用save-file覆盖文件**
**AI习惯使用save-file覆盖整个文件，这会删除其他接口和功能！**

### **禁止的危险操作：**
```javascript
// ❌ 绝对禁止：使用save-file覆盖整个文件
save-file({
  path: "services/config/basic-config-service.js",
  file_content: "整个文件的新内容..."  // 这会删除所有其他接口！
});
```

## 🛡️ **强制备份机制**
**修改任何文件前必须先备份，只保留一个最新的正确备份**

### **备份操作规范：**
```bash
# ✅ 修改前必须执行的备份命令
cp services/config/basic-config-service.js services/config/basic-config-service.js.backup
```

### **备份文件管理：**
1. **只保留一个备份** - 每次修改前覆盖之前的备份
2. **备份最新的正确版本** - 确保备份文件是可用的
3. **修改完成后提醒用户验证** - 确认功能正常后才算完成

## ✅ **唯一允许的修改方式**
**只能使用str-replace-editor进行精确修改，绝不覆盖整个文件**

### **正确的修改流程：**
```javascript
// 1. 修改前备份
launch-process({
  command: "cp services/config/basic-config-service.js services/config/basic-config-service.js.backup",
  wait: true
});

// 2. 使用str-replace-editor精确修改
str-replace-editor({
  command: "str_replace",
  path: "services/config/basic-config-service.js",
  old_str: "只修改需要修改的具体代码段",
  new_str: "修改后的代码段",
  old_str_start_line_number: 45,
  old_str_end_line_number: 47
});
```

## ✅ **AI必须养成的保守习惯**

### **1. 精确定位修改范围**
- 明确修改的开始行和结束行
- 精确指定old_str_start_line_number和old_str_end_line_number
- 最大修改范围：单个容器内的代码

### **2. 分层修改范围控制**
- 优先最小化修改，能少改就少改
- 根据修改复杂度采用不同策略
- 功能完整性优先，不能为了限制而破坏功能

### **3. 强烈的边界意识**
- 时刻提醒自己不要越界修改
- 严格遵守接口容器边界
- 不得跨容器修改

### **4. 修改前自我检查**
每次修改前必须自问：
- [ ] 我是否只修改了用户明确要求的部分？
- [ ] 我是否动了其他不相关的代码？
- [ ] 我的修改是否是最小化的？
- [ ] 我是否有"优化"或"统一"的冲动？
- [ ] 我是否确认了修改的具体行数？

## 🎯 **分层修改范围控制策略**

### **🟢 简单修改（优先执行）**
- **修改范围：** 不超过20行
- **适用场景：** bug修复、参数调整、简单逻辑修改
- **执行方式：** 直接执行，无需特别说明
- **文件限制：** 单文件修改

### **🟡 中等修改（需要说明）**
- **修改范围：** 20-50行
- **适用场景：** 功能增强、错误处理完善、接口优化
- **执行方式：** 修改前说明修改范围和原因
- **文件限制：** 优先单文件，相关文件需说明

### **🟠 复杂修改（需要用户确认）**
- **修改范围：** 50-100行
- **适用场景：** 新功能实现、复杂逻辑重构
- **执行方式：** 修改前征得用户同意，详细说明修改计划
- **文件限制：** 可修改多个相关文件，需列出清单

### **🔴 大型修改（分步执行）**
- **修改范围：** 超过100行
- **适用场景：** 大型功能开发、架构调整
- **执行方式：** 必须分步进行，每步不超过50行
- **文件限制：** 分步修改多个文件，每步验证功能

### **📋 修改前评估清单**
每次修改前必须评估：
1. **修改复杂度：** 简单/中等/复杂/大型？
2. **影响范围：** 单文件/相关文件/批量文件？
3. **是否可分步：** 能否拆分成多个小修改？
4. **用户期望：** 用户是否期望一次性完成？
5. **功能完整性：** 修改后功能是否完整可用？

## 🚀 **高效问题解决原则**

### **⚡ 深度分析优先**
- **直击根本** - 遇到问题时，立即进行深度分析，不被表面现象误导
- **多层验证** - 同时检查前端显示、API响应、数据库数据，快速定位问题根源
- **主动排查** - 不等待用户提供更多信息，主动使用工具深入调查
- **一次性解决** - 找到根本原因后，制定完整解决方案，避免反复修复

### **🎯 智能规则遵守**
- **精准修改** - 只修改需要修改的具体代码段，不进行覆盖式修改
- **保护核心** - 不修改系统主文件和关键配置，但不影响问题诊断
- **架构兼容** - 遵循微服务架构，但不让架构规则阻碍问题解决效率
- **验证完整** - 修复后进行完整验证，确保问题彻底解决且不影响其他功能
- **灵活执行** - 根据实际需求灵活应用修改范围限制，功能完整性优先

## 🔧 **工具使用规范**

### **调试工具**
- **Playwright** - 用于前端问题分析、定位和验证
- **Sequential thinking** - 用于深度思考和问题分析
- **codebase-retrieval** - 用于获取代码上下文信息

### **修改工具**
- **str-replace-editor** - 唯一允许的文件修改工具
- **launch-process** - 用于备份文件和执行命令
- **view** - 用于查看文件内容

### **禁用工具**
- **save-file** - 禁止用于覆盖现有文件（只能创建新文件）

## 📋 **AI修改文件前的强制检查清单**

每次修改文件前必须确认：
- [ ] 我是否需要修改现有文件？
- [ ] 我是否已经备份了原文件？
- [ ] 我是否只使用str-replace-editor而不是save-file？
- [ ] 我的修改复杂度属于哪个层级（简单/中等/复杂/大型）？
- [ ] 我是否按照对应层级的要求执行（说明/确认/分步）？
- [ ] 我是否确认不会影响其他接口或功能？
- [ ] 我是否准备好提醒用户验证功能？

**只有通过所有检查，才能进行修改！**

## ⚠️ **违反后果**

违反这些AI行为规则将导致：
- 其他接口功能丢失或破坏
- 系统不稳定和用户体验极差
- 需要花费大量时间修复被破坏的功能
- 用户对AI的信任度严重下降

## 💡 **记住：用户的核心要求**

**用户最关心的是：AI能够精准地只修改需要修改的部分，而不破坏其他任何功能！**

**AI必须时刻记住：保守修改比完美修改更重要！**

## 🎯 **AI行为自我约束**

每次修改文件前必须自问：
1. **我是否在使用save-file覆盖现有文件？** - 如果是，立即停止！
2. **我是否已经备份了原文件？** - 如果没有，立即备份！
3. **我的修改是否会删除其他接口？** - 如果会，立即停止！
4. **我是否在进行"优化"而不是解决具体问题？** - 如果是，立即停止！

🚨 AI数据库表管理严格规则
禁止新建数据库表 - 除非确认原始表有问题
必须先检查现有表 - 使用SHOW TABLES和DESCRIBE命令
必须使用原始表结构 - 查找并使用系统原有的表
必须备份重要数据 - 修改前备份原始数据
必须清理多余表 - 删除不必要的重复表

**AI必须严格遵守这些规则，没有任何例外！**

## 🧠 **深度分析和一次性解决问题原则**

### **⚡ 深度思考优先**
- **禁止反复修修补补** - 遇到问题时，必须先进行深度分析，找到根本原因
- **一次性彻底解决** - 不允许"先处理再验证再处理再验证"的循环模式
- **全面问题诊断** - 使用所有可用工具深入调查问题的各个层面
- **根本原因定位** - 不被表面现象误导，直击问题核心

### **🔍 问题分析流程**
1. **深度分析阶段** - 使用多种工具全面分析问题
2. **根本原因识别** - 确定问题的真正根源
3. **完整解决方案制定** - 制定彻底的解决方案
4. **一次性实施** - 完整执行解决方案
5. **验证确认** - 确保问题彻底解决

### **🚫 严禁的问题处理方式**
- **反复试错** - 禁止不断尝试小修改
- **表面修复** - 禁止只解决表面症状
- **分散处理** - 禁止把一个问题拆成多次处理
- **盲目修改** - 禁止没有深度分析就开始修改

### **✅ 正确的问题处理方式**
- **Sequential thinking深度思考** - 每次遇到问题都要深度思考
- **多工具综合分析** - 使用diagnostics、view、codebase-retrieval等工具
- **完整解决方案** - 制定涵盖所有相关方面的解决方案
- **一次性完成** - 确保问题一次性彻底解决

### **📋 问题处理检查清单**
每次遇到问题时必须确认：
- [ ] 我是否进行了深度分析？
- [ ] 我是否找到了根本原因？
- [ ] 我的解决方案是否完整？
- [ ] 我是否能一次性解决问题？
- [ ] 我是否避免了反复修补？

**记住：深度分析一次，彻底解决问题，比反复修补一百次更有效！**

## 如果所有规则你的都明白了，请在第一句话回复我：“规则已知晓”，不然我以为你没有明白规则
