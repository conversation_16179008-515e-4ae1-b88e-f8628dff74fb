<template>
  <view class="message-item" :class="message.type === 'ai' ? 'ai-message-item' : 'user-message-item'">
    <view class="message" :class="message.type === 'ai' ? 'ai-message' : 'user-message'">
      <!-- 头像 - 仅AI消息显示 -->
      <view class="avatar ai-avatar" v-if="message.type === 'ai'">
        <text>AI</text>
      </view>
      
      <view class="message-content">
        <view class="message-bubble">
          <text class="message-text">{{ message.content }}</text>
          
          <!-- 媒体内容 - 仅用户消息显示 -->
          <view class="message-media-content" v-if="message.type === 'user' && message.media && message.media.length > 0">
            <view class="message-media-list">
              <view 
                class="message-media-item" 
                v-for="(media, mediaIndex) in message.media" 
                :key="mediaIndex"
                :class="media.type"
              >
                <!-- 图片预览 -->
                <image 
                  v-if="media.type === 'image'" 
                  class="message-media-image" 
                  :src="media.src" 
                  mode="aspectFill"
                  @tap="previewImage(media.src)"
                />
                
                <!-- 文档预览 -->
                <view v-if="media.type === 'document'" class="message-media-doc" @tap="openDocument(media)">
                  <text class="message-doc-icon">📄</text>
                  <text class="message-doc-name">{{media.name}}</text>
                  <text v-if="media.size" class="message-doc-size">{{media.size}}</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 消息操作按钮 - 仅AI消息显示 -->
          <view class="message-actions" v-if="message.type === 'ai' && message.content">
            <view class="action-btn copy-btn" @tap="copyMessage(message.content)">
              <text class="action-icon">📋</text>
            </view>
            <view 
              class="action-btn speak-btn" 
              @tap="speakMessage(message.content)" 
              :class="{'speaking': isSpeaking}"
            >
              <text class="action-icon">{{ isSpeaking ? '🔈' : '🔊' }}</text>
            </view>
            <view 
              class="action-btn regenerate-btn" 
              @tap="regenerateMessage" 
              v-if="isLatestMessage && !isGenerating"
            >
              <text class="action-icon">🔄</text>
              <text class="regenerate-cost">-{{ coinCost }}</text>
            </view>
          </view>
        </view>
        
        <!-- 消息时间 -->
        <view class="message-time">{{ formattedTime }}</view>
      </view>
      
      <!-- 头像 - 仅用户消息显示 -->
      <view class="avatar user-avatar" v-if="message.type === 'user'">
        <text>您</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MessageItem',
  props: {
    message: {
      type: Object,
      required: true
    },
    isLatestMessage: {
      type: Boolean,
      default: false
    },
    isGenerating: {
      type: Boolean,
      default: false
    },
    coinCost: {
      type: Number,
      default: 10
    },
    isSpeakingMessage: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isSpeaking() {
      return this.isSpeakingMessage;
    },
    formattedTime() {
      return this.formatTime(this.message.timestamp);
    }
  },
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${hours}:${minutes}`;
    },
    
    previewImage(src) {
      if (!src) return;
      
      try {
        uni.previewImage({
          urls: [src],
          current: src
        });
      } catch (e) {
        console.error('预览图片失败:', e);
      }
      
      this.$emit('preview-image', src);
    },
    
    openDocument(media) {
      if (!media || !media.src) return;
      
      this.$emit('open-document', media);
    },
    
    copyMessage(content) {
      if (!content) return;
      
      try {
        uni.setClipboardData({
          data: content,
          success: () => {
            uni.showToast({
              title: '已复制到剪贴板',
              icon: 'none'
            });
          }
        });
      } catch (e) {
        console.error('复制消息失败:', e);
      }
      
      this.$emit('copy-message', content);
    },
    
    speakMessage(content) {
      if (!content) return;
      
      this.$emit('speak-message', {
        content: content,
        messageId: this.message.id || null
      });
    },
    
    regenerateMessage() {
      this.$emit('regenerate-message');
    }
  }
}
</script>

<style scoped>
.message-item {
  padding: 10rpx 20rpx;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.message {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}

.ai-message {
  justify-content: flex-start;
}

.user-message {
  justify-content: flex-end;
}

.avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  flex-shrink: 0;
}

.ai-avatar {
  background-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.5);
}

.ai-avatar text {
  color: #f1c40f;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-avatar {
  background-color: rgba(50, 180, 130, 0.8);
  color: #fff;
  margin-left: 10rpx;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: calc(100% - 170rpx);
}

.message-bubble {
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  margin-bottom: 5rpx;
  position: relative;
}

.ai-message .message-bubble {
  background-color: rgba(80, 80, 100, 0.3);
  color: #e8e8e8;
}

.user-message .message-bubble {
  background-color: rgba(110, 86, 207, 0.8);
  color: #fff;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 22rpx;
  color: rgba(200, 200, 210, 0.5);
  margin-top: 2rpx;
  align-self: flex-start;
}

.ai-message .message-time {
  text-align: left;
}

.user-message .message-time {
  text-align: right;
  align-self: flex-end;
}

.message-media-content {
  margin-top: 10rpx;
}

.message-media-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.message-media-item {
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.message-media-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 8rpx;
}

.message-media-doc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(60, 60, 80, 0.3);
  padding: 16rpx;
  border-radius: 8rpx;
  width: 150rpx;
  height: 150rpx;
}

.message-doc-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.message-doc-name {
  font-size: 22rpx;
  color: #e8e8e8;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-doc-size {
  font-size: 18rpx;
  color: rgba(200, 200, 210, 0.6);
  margin-top: 4rpx;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  background-color: rgba(80, 80, 100, 0.3);
  transition: all 0.2s ease;
}

.action-btn:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
}

.speak-btn.speaking {
  background-color: rgba(110, 86, 207, 0.8);
}

.regenerate-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.regenerate-cost {
  font-size: 20rpx;
  color: #e8e8e8;
}
</style> 