/**
 * 系统主页接口
 * 管理首页展示、搜索、分类导航等功能
 */

import { apiRequest } from '../common/request.js';

// ================================
// 🏠 首页数据接口
// ================================

/**
 * 获取首页数据
 */
export async function 获取首页数据() {
	return await apiRequest('home/index-data');
}

/**
 * 获取轮播图数据
 */
export async function 获取轮播图数据() {
	return await apiRequest('home/banners');
}

/**
 * 获取分类数据
 */
export async function 获取分类数据() {
	return await apiRequest('home/categories');
}

/**
 * 获取热门工具
 */
export async function 获取热门工具() {
	return await apiRequest('home/hot-tools');
}

/**
 * 获取AI创作场景
 */
export async function 获取AI创作场景() {
	return await apiRequest('home/scenarios');
}

// ================================
// 🔍 搜索接口
// ================================

/**
 * 搜索内容
 * @param {Object} params - 搜索参数
 */
export async function 搜索内容(params) {
	const queryParams = new URLSearchParams(params).toString();
	return await apiRequest(`home/search?${queryParams}`);
}

/**
 * 获取搜索建议
 * @param {string} keyword - 关键词
 */
export async function 获取搜索建议(keyword) {
	return await apiRequest(`home/search-suggestions?keyword=${encodeURIComponent(keyword)}`);
}

/**
 * 获取热门搜索
 */
export async function 获取热门搜索() {
	return await apiRequest('home/hot-searches');
}

/**
 * 记录搜索历史
 * @param {string} keyword - 搜索关键词
 */
export async function 记录搜索历史(keyword) {
	return await apiRequest('home/search-history', {
		method: 'POST',
		body: { keyword }
	});
}

// ================================
// 📊 统计接口
// ================================

/**
 * 记录页面访问
 * @param {Object} params - 访问参数
 */
export async function 记录页面访问(params) {
	return await apiRequest('home/page-visit', {
		method: 'POST',
		body: params
	});
}

/**
 * 记录功能点击
 * @param {Object} params - 点击参数
 */
export async function 记录功能点击(params) {
	return await apiRequest('home/feature-click', {
		method: 'POST',
		body: params
	});
}

export default {
	获取首页数据,
	获取轮播图数据,
	获取分类数据,
	获取热门工具,
	获取AI创作场景,
	搜索内容,
	获取搜索建议,
	获取热门搜索,
	记录搜索历史,
	记录页面访问,
	记录功能点击
};
