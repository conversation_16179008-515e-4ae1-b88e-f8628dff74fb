<template>
	<view class="date-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content" @click.stop>
			<view class="picker-header">
				<text class="header-title">选择出生日期</text>
			</view>

			<!-- 新的下拉输入框组合 -->
			<view class="date-selector-container">
				<!-- 年份选择 -->
				<view class="date-input-group">
					<view class="input-with-dropdown">
						<input 
							class="date-input" 
							type="number" 
							:value="selectedYear" 
							@input="onYearInput"
							placeholder="年份"
						/>
						<text class="unit-label">年</text>
						<view class="dropdown-arrow" @click="toggleYearDropdown">▼</view>
					</view>
					<scroll-view
						v-if="showYearDropdown"
						class="dropdown-list"
						scroll-y
						@scroll="onYearScroll"
						@click.stop
						@touchmove.stop
					>
						<view
							v-for="year in years"
							:key="year"
							class="dropdown-item"
							:class="{ active: year === selectedYear }"
							@click.stop="selectYear(year)"
							@tap.stop="selectYear(year)"
						>
							{{ year }}年
						</view>
					</scroll-view>
				</view>

				<!-- 月份选择 -->
				<view class="date-input-group">
					<view class="input-with-dropdown">
						<input 
							class="date-input" 
							type="number" 
							:value="selectedMonth" 
							@input="onMonthInput"
							placeholder="月份"
							min="1" 
							max="12"
						/>
						<text class="unit-label">月</text>
						<view class="dropdown-arrow" @click="toggleMonthDropdown">▼</view>
					</view>
					<scroll-view
						v-if="showMonthDropdown"
						class="dropdown-list"
						scroll-y
						@scroll="onMonthScroll"
						@click.stop
						@touchmove.stop
					>
						<view
							v-for="month in months"
							:key="month"
							class="dropdown-item"
							:class="{ active: month === selectedMonth }"
							@click.stop="selectMonth(month)"
							@tap.stop="selectMonth(month)"
						>
							{{ month.toString().padStart(2, '0') }}月
						</view>
					</scroll-view>
				</view>

				<!-- 日期选择 -->
				<view class="date-input-group">
					<view class="input-with-dropdown">
						<input 
							class="date-input" 
							type="number" 
							:value="selectedDay" 
							@input="onDayInput"
							placeholder="日期"
							:min="1" 
							:max="daysInMonth"
						/>
						<text class="unit-label">日</text>
						<view class="dropdown-arrow" @click="toggleDayDropdown">▼</view>
					</view>
					<scroll-view
						v-if="showDayDropdown"
						class="dropdown-list"
						scroll-y
						@scroll="onDayScroll"
						@click.stop
						@touchmove.stop
					>
						<view
							v-for="day in days"
							:key="day"
							class="dropdown-item"
							:class="{ active: day === selectedDay }"
							@click.stop="selectDay(day)"
							@tap.stop="selectDay(day)"
						>
							{{ day.toString().padStart(2, '0') }}日
						</view>
					</scroll-view>
				</view>
			</view>
			
			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const currentYear = new Date().getFullYear();
		const currentMonth = new Date().getMonth() + 1;
		const currentDay = new Date().getDate();

		// 生成年份选项（1900-2045）
		const years = [];
		for (let i = 1900; i <= currentYear + 25; i++) {
			years.push(i);
		}

		// 生成月份选项（1-12）
		const months = [];
		for (let i = 1; i <= 12; i++) {
			months.push(i);
		}

		return {
			selectedYear: currentYear,
			selectedMonth: currentMonth,
			selectedDay: currentDay,
			years,
			months,
			showYearDropdown: false,
			showMonthDropdown: false,
			showDayDropdown: false
		}
	},
	computed: {
		// 根据年月计算当月天数
		daysInMonth() {
			if (this.selectedYear && this.selectedMonth) {
				return new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			}
			return 31;
		},
		// 生成日期选项
		days() {
			const days = [];
			for (let i = 1; i <= this.daysInMonth; i++) {
				days.push(i);
			}
			return days;
		}
	},
	watch: {
		// 监听月份变化，调整日期
		selectedMonth() {
			if (this.selectedDay > this.daysInMonth) {
				this.selectedDay = this.daysInMonth;
			}
		},
		// 监听年份变化，调整日期（闰年处理）
		selectedYear() {
			if (this.selectedDay > this.daysInMonth) {
				this.selectedDay = this.daysInMonth;
			}
		}
	},
	methods: {
		// 年份输入
		onYearInput(e) {
			const value = parseInt(e.detail.value);
			if (value >= 1900 && value <= 2045) {
				this.selectedYear = value;
			}
		},
		// 月份输入
		onMonthInput(e) {
			const value = parseInt(e.detail.value);
			if (value >= 1 && value <= 12) {
				this.selectedMonth = value;
			}
		},
		// 日期输入
		onDayInput(e) {
			const value = parseInt(e.detail.value);
			if (value >= 1 && value <= this.daysInMonth) {
				this.selectedDay = value;
			}
		},
		// 切换年份下拉框
		toggleYearDropdown() {
			this.showYearDropdown = !this.showYearDropdown;
			this.showMonthDropdown = false;
			this.showDayDropdown = false;
		},
		// 切换月份下拉框
		toggleMonthDropdown() {
			this.showMonthDropdown = !this.showMonthDropdown;
			this.showYearDropdown = false;
			this.showDayDropdown = false;
		},
		// 切换日期下拉框
		toggleDayDropdown() {
			this.showDayDropdown = !this.showDayDropdown;
			this.showYearDropdown = false;
			this.showMonthDropdown = false;
		},
		// 选择年份
		selectYear(year) {
			console.log('选择年份:', year);
			this.selectedYear = year;
			this.showYearDropdown = false;
		},
		// 选择月份
		selectMonth(month) {
			console.log('选择月份:', month);
			this.selectedMonth = month;
			this.showMonthDropdown = false;
		},
		// 选择日期
		selectDay(day) {
			this.selectedDay = day;
			this.showDayDropdown = false;
		},
		// 滚动事件（防止内存泄漏）
		onYearScroll() {
			// 使用官方滚动，无需自定义处理
		},
		onMonthScroll() {
			// 使用官方滚动，无需自定义处理
		},
		onDayScroll() {
			// 使用官方滚动，无需自定义处理
		},
		// 关闭选择器
		close() {
			this.showYearDropdown = false;
			this.showMonthDropdown = false;
			this.showDayDropdown = false;
			this.$emit('close');
		},
		// 确认选择
		confirm() {
			const dateStr = `${this.selectedYear}-${this.selectedMonth.toString().padStart(2, '0')}-${this.selectedDay.toString().padStart(2, '0')}`;
			this.$emit('confirm', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				dateString: dateStr
			});
			this.close();
		}
	}
}
</script>

<style scoped>
.date-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	background: #F5E6D3;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 40rpx;
	max-width: 600rpx;
	width: 90%;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.picker-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #8B4513;
}

.date-selector-container {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.date-input-group {
	flex: 1;
	position: relative;
}

.input-with-dropdown {
	position: relative;
	display: flex;
	align-items: center;
	background: white;
	border: 2rpx solid #8B4513;
	border-radius: 12rpx;
	padding: 0 10rpx;
	height: 80rpx;
}

.date-input {
	flex: 1;
	border: none;
	outline: none;
	font-size: 28rpx;
	color: #8B4513;
	text-align: center;
	background: transparent;
}

.unit-label {
	font-size: 24rpx;
	color: #8B4513;
	margin-left: 5rpx;
}

.dropdown-arrow {
	font-size: 20rpx;
	color: #8B4513;
	margin-left: 10rpx;
	cursor: pointer;
	transition: transform 0.3s ease;
}

.dropdown-list {
	position: absolute;
	top: 85rpx;
	left: 0;
	right: 0;
	background: white;
	border: 2rpx solid #8B4513;
	border-radius: 12rpx;
	max-height: 300rpx;
	z-index: 99999;
	box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.2);
}

.dropdown-item {
	padding: 20rpx;
	text-align: center;
	font-size: 28rpx;
	color: #8B4513;
	border-bottom: 1rpx solid #E8E8E8;
	cursor: pointer;
	transition: background-color 0.2s ease;
	user-select: none;
	-webkit-user-select: none;
	position: relative;
}

.dropdown-item:last-child {
	border-bottom: none;
}

.dropdown-item:hover {
	background: #F5E6D3;
}

.dropdown-item.active {
	background: #8B4513;
	color: white;
	font-weight: bold;
}

.picker-footer {
	display: flex;
	justify-content: space-between;
	gap: 30rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: #F5E6D3;
	color: #8B4513;
	border: 2rpx solid #8B4513;
}

.btn-cancel:hover {
	background: #E8D5C4;
}

.btn-confirm {
	background: #8B4513;
	color: white;
}

.btn-confirm:hover {
	background: #654321;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
	.date-selector-container {
		flex-direction: column;
		gap: 15rpx;
	}

	.date-input-group {
		width: 100%;
	}
}
</style>
