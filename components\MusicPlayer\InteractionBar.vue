<template>
	<view class="interaction-bar" :style="themeStyles">
		<!-- 下载按钮 -->
		<view class="action-btn" @click="handleDownload">
			<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
				<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
				<polyline points="7 10 12 15 17 10"></polyline>
				<line x1="12" y1="15" x2="12" y2="3"></line>
			</svg>
			<text class="action-text">下载</text>
		</view>
		
		<!-- 分享按钮 -->
		<view class="action-btn" @click="handleShare">
			<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
				<circle cx="18" cy="5" r="3"></circle>
				<circle cx="6" cy="12" r="3"></circle>
				<circle cx="18" cy="19" r="3"></circle>
				<line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
				<line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
			</svg>
			<text class="action-text">分享</text>
		</view>
		
		<!-- 喜欢按钮 -->
		<view class="action-btn" :class="{ active: isLiked }" @click="handleLike">
			<svg v-if="!isLiked" class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
				<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
			</svg>
			<svg v-else class="action-icon" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2">
				<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
			</svg>
			<text class="action-text">{{ formatCount(likeCount) }}</text>
		</view>
		
		<!-- 评论按钮 -->
		<view class="action-btn" @click="handleComment">
			<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
				<path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
			</svg>
			<text class="action-text">{{ formatCount(commentCount) }}</text>
		</view>
		
		<!-- 播放列表按钮 -->
		<view class="action-btn playlist-btn" @click="handlePlaylist">
			<svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
				<line x1="8" y1="6" x2="21" y2="6"></line>
				<line x1="8" y1="12" x2="21" y2="12"></line>
				<line x1="8" y1="18" x2="21" y2="18"></line>
				<line x1="3" y1="6" x2="3.01" y2="6"></line>
				<line x1="3" y1="12" x2="3.01" y2="12"></line>
				<line x1="3" y1="18" x2="3.01" y2="18"></line>
			</svg>
		</view>
	</view>
</template>

<script>
import { getTheme } from '@/utils/playerThemes.js';

export default {
	name: 'InteractionBar',
	props: {
		// 音乐信息
		music: {
			type: Object,
			required: true,
			default: () => ({
				likes: 0,
				comments: 0,
				isLiked: false
			})
		},
		// 当前主题
		currentTheme: {
			type: String,
			default: 'tech_blue'
		}
	},
	data() {
		return {
			isLiked: false,
			likeCount: 0,
			commentCount: 0
		};
	},
	computed: {
		// 主题样式
		themeStyles() {
			const theme = getTheme(this.currentTheme);
			return {
				'--icon-color': theme.colors.iconColor,
				'--icon-color-active': theme.colors.iconColorActive,
				'--text-primary': theme.colors.textPrimary,
				'--button-bg': theme.colors.buttonBg,
				'--button-bg-active': theme.colors.buttonBgActive
			};
		}
	},
	mounted() {
		this.initData();
	},
	watch: {
		music: {
			handler() {
				this.initData();
			},
			deep: true
		}
	},
	methods: {
		// 初始化数据
		initData() {
			this.isLiked = this.music.isLiked || false;
			this.likeCount = this.music.likes || 0;
			this.commentCount = this.music.comments || 0;
		},
		
		// 下载
		handleDownload() {
			this.$emit('download', this.music);
		},
		
		// 分享
		handleShare() {
			this.$emit('share', this.music);
		},
		
		// 喜欢/取消喜欢
		handleLike() {
			this.isLiked = !this.isLiked;
			this.likeCount += this.isLiked ? 1 : -1;
			
			this.$emit('like', {
				music: this.music,
				isLiked: this.isLiked,
				likeCount: this.likeCount
			});
		},
		
		// 评论
		handleComment() {
			this.$emit('comment', this.music);
		},
		
		// 播放列表
		handlePlaylist() {
			this.$emit('playlist');
		},
		
		// 格式化数字
		formatCount(count) {
			if (!count || count === 0) return '';
			if (count >= 10000) {
				return (count / 10000).toFixed(1) + 'w';
			}
			if (count >= 1000) {
				return (count / 1000).toFixed(1) + 'k';
			}
			return count.toString();
		}
	}
};
</script>

<style lang="scss" scoped>
.interaction-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 40rpx;
	background: rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 15rpx 20rpx;
	background: var(--button-bg, rgba(255, 255, 255, 0.1));
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	transition: all 0.3s;
	min-width: 100rpx;
	
	&:active {
		transform: scale(0.95);
		background: var(--button-bg-active, rgba(255, 255, 255, 0.2));
	}
	
	&.active {
		.action-icon {
			color: var(--icon-color-active, #50E3C2);
			animation: heartBeat 0.3s ease;
		}
		
		.action-text {
			color: var(--icon-color-active, #50E3C2);
		}
	}
}

.playlist-btn {
	min-width: 80rpx;
	padding: 15rpx;
}

.action-icon {
	width: 44rpx;
	height: 44rpx;
	color: var(--icon-color, #FFFFFF);
	transition: all 0.3s;
}

.action-text {
	color: var(--text-primary, #FFFFFF);
	font-size: 22rpx;
	white-space: nowrap;
}

@keyframes heartBeat {
	0%, 100% {
		transform: scale(1);
	}
	25% {
		transform: scale(1.3);
	}
	50% {
		transform: scale(1.1);
	}
	75% {
		transform: scale(1.2);
	}
}

/* H5端优化 */
/* #ifdef H5 */
.action-btn {
	cursor: pointer;
	
	&:hover {
		background: var(--button-bg-active, rgba(255, 255, 255, 0.2));
		
		.action-icon {
			transform: scale(1.1);
		}
	}
}
/* #endif */
</style>

