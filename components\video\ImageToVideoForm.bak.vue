<style lang="scss" scoped>
.image-to-video-form {
  padding-bottom: 30rpx;
  
  .form-section {
    margin-bottom: 30rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;
    }
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 15rpx;
      display: block;
    }
    
    .upload-limit {
      font-size: 24rpx;
      color: #ffffff;
    }
  }
  
  .mode-tabs {
    display: flex;
    border-radius: 12rpx;
    overflow: hidden;
    margin-bottom: 15rpx;
    border: 1px solid #2c2c44;
    
    .mode-tab {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0;
      background-color: #0f3460;
      
      &.active {
        background-color: #0abde3;
        
        image {
          filter: brightness(1.2);
        }
        
        text {
          color: #ffffff;
          font-weight: 500;
        }
      }
      
      image {
        width: 50rpx;
        height: 50rpx;
        margin-bottom: 10rpx;
        opacity: 0.9;
      }
      
      text {
        font-size: 26rpx;
        color: #ffffff;
      }
    }
  }
  
  .mode-description {
    font-size: 24rpx;
    color: #ffffff;
    line-height: 1.4;
    padding: 0 10rpx;
  }
  
  .image-upload-area {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;
    
    .image-preview-item, .upload-btn {
      width: calc(33.33% - 20rpx);
      margin: 10rpx;
      height: 180rpx;
      position: relative;
      border-radius: 12rpx;
      overflow: hidden;
    }
    
    .image-preview-item {
      border: 1px solid #2c2c44;
      background-color: #0f3460;
      
      &.start-frame, &.end-frame {
        width: calc(50% - 20rpx);
        height: 240rpx;
        
        &::after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 60rpx;
          background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
        }
      }
      
      .preview-image {
        width: 100%;
        height: 100%;
      }
      
      .image-tools {
        position: absolute;
        top: 5rpx;
        right: 5rpx;
        display: flex;
        
        .tool-btn {
          width: 50rpx;
          height: 50rpx;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 25rpx;
          margin-left: 5rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          
          image {
            width: 30rpx;
            height: 30rpx;
          }
        }
      }
      
      .frame-label {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 10rpx 0;
        text-align: center;
        color: #fff;
        font-size: 24rpx;
        z-index: 1;
      }
    }
    
    .upload-btn {
      border: 2rpx dashed #48dbfb;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(15, 52, 96, 0.5);
      
      .upload-icon {
        font-size: 60rpx;
        color: #48dbfb;
        line-height: 1;
        margin-bottom: 10rpx;
      }
      
      .upload-text {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
  }
  
  .upload-tips {
    margin-top: 15rpx;
    font-size: 24rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
  }
  
  .enhance-input {
    width: 100%;
    height: 120rpx;
    background-color: #0f3460;
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    border: 1px solid #2c2c44;
    margin-bottom: 10rpx;
    color: #ffffff;
  }
  
  .text-counter {
    text-align: right;
    font-size: 24rpx;
    color: #ffffff;
  }
  
  .parameters-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;
    
    .parameter-item {
      width: 50%;
      padding: 15rpx 15rpx 15rpx 0;
      
      .parameter-label {
        font-size: 28rpx;
        color: #ffffff;
        margin-bottom: 15rpx;
        display: block;
        font-weight: 500;
      }
      
      .parameter-slider-container {
        position: relative;
        padding-right: 60rpx;
        margin-top: 20rpx;
        
        .parameter-value {
          position: absolute;
          right: 0;
          top: 0;
          font-size: 26rpx;
          color: #ffda79;
          font-weight: 500;
        }
      }
      
      .quality-options, .ratio-options {
        display: flex;
        
        .quality-btn, .ratio-btn {
          flex: 1;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          font-weight: 500;
          background-color: #0f3460;
          color: #ffffff;
          margin-right: 10rpx;
          border-radius: 8rpx;
          border: 2rpx solid #3a6299;
          transition: all 0.2s ease;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
          
          &:last-child {
            margin-right: 0;
          }
          
          &.active {
            background-color: #1e3799;
            color: #ffffff;
            border-color: #4a69bd;
            box-shadow: 0 0 10rpx rgba(74, 105, 189, 0.5);
            transform: translateY(-2rpx);
          }
          
          image {
            width: 30rpx;
            height: 30rpx;
            margin-right: 8rpx;
          }
        }
      }
      
      .music-options {
        .music-selection {
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #0f3460;
          border-radius: 8rpx;
          padding: 0 20rpx;
          border: 2rpx solid #3a6299;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
          
          text {
            font-size: 28rpx;
            color: #ffffff;
            font-weight: 500;
            
            &.arrow-icon {
              font-size: 36rpx;
              color: #ffffff;
              transform: rotate(90deg);
            }
          }
        }
      }
    }
  }
  
  .music-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .music-container {
      width: 90%;
      max-height: 70%;
      background-color: #16213e;
      border-radius: 20rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .popup-header {
        padding: 30rpx;
        border-bottom: 1px solid #2c2c44;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .popup-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #ffffff;
        }
        
        .close-btn {
          font-size: 40rpx;
          color: #ffffff;
          padding: 0 10rpx;
        }
      }
      
      .music-list {
        flex: 1;
        overflow-y: auto;
        
        .music-item {
          padding: 30rpx;
          border-bottom: 1px solid #2c2c44;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .music-content {
            flex: 1;
            
            .music-title {
              font-size: 30rpx;
              font-weight: 500;
              color: #ffffff;
              margin-bottom: 10rpx;
              display: block;
            }
            
            .music-info {
              display: flex;
              align-items: center;
              
              .music-duration, .music-author {
                font-size: 24rpx;
                color: #ffffff;
              }
              
              .music-duration {
                margin-right: 20rpx;
                
                &:before {
                  content: '⏱';
                  margin-right: 5rpx;
                }
              }
              
              .music-author {
                &:before {
                  content: '👤';
                  margin-right: 5rpx;
                }
              }
            }
          }
          
          .music-action {
            display: flex;
            align-items: center;
            
            .play-btn {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
              background-color: #0f3460;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 20rpx;
              
              image {
                width: 28rpx;
                height: 28rpx;
              }
              
              &.playing {
                background-color: #0abde3;
              }
            }
            
            .use-btn {
              padding: 10rpx 25rpx;
              background-color: #0abde3;
              color: #ffffff;
              font-size: 26rpx;
              border-radius: 30rpx;
            }
          }
        }
      }
    }
  }
  
  .crop-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .crop-container {
    width: 90%;
    height: 80%;
    background-color: #333;
    border-radius: 20rpx;
    overflow: hidden;
  }
}

.upload-area {
  background-color: #0f3460;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .upload-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
    opacity: 0.8;
  }
  
  .upload-text {
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    margin-bottom: 10rpx;
  }
  
  .upload-subtext {
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
  }
  
  .upload-btn {
    margin-top: 30rpx;
    width: 240rpx;
    height: 70rpx;
    line-height: 70rpx;
    background-color: #0abde3;
    color: #ffffff;
    font-size: 28rpx;
    text-align: center;
    border-radius: 35rpx;
  }
}

.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  
  .image-preview-item {
    width: calc(33.33% - 20rpx);
    margin: 10rpx;
    position: relative;
    
    .preview-container {
      position: relative;
      width: 100%;
      padding-top: 100%; /* 1:1 Aspect Ratio */
      border-radius: 12rpx;
      overflow: hidden;
      background-color: #0f3460;
      
      .preview-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .remove-btn {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .remove-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }
    
    .image-label {
      position: absolute;
      bottom: 10rpx;
      left: 10rpx;
      background-color: rgba(0, 0, 0, 0.5);
      color: #ffffff;
      font-size: 24rpx;
      padding: 4rpx 10rpx;
      border-radius: 6rpx;
    }
    
    .key-frame-tag {
      position: absolute;
      top: 10rpx;
      left: 10rpx;
      background-color: rgba(10, 189, 227, 0.8);
      color: #ffffff;
      font-size: 22rpx;
      padding: 6rpx 12rpx;
      border-radius: 6rpx;
    }
    
    &.add-more {
      .preview-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2rpx dashed #48dbfb;
        
        .add-icon {
          width: 50rpx;
          height: 50rpx;
          margin-bottom: 10rpx;
        }
        
        .add-text {
          font-size: 24rpx;
          color: #ffffff;
        }
      }
    }
  }
}

.mode-selector {
  display: flex;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  
  .mode-option {
    flex: 1;
    padding: 20rpx 0;
    text-align: center;
    font-size: 28rpx;
    background-color: #0f3460;
    color: #ffffff;
    
    &:first-child {
      border-right: 1px solid #2c2c44;
    }
    
    &.active {
      background-color: #0abde3;
      color: #ffffff;
    }
  }
}

.image-description {
  background-color: #0f3460;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  
  .description-label {
    font-size: 28rpx;
    color: #ffffff;
    margin-bottom: 15rpx;
  }
  
  textarea {
    width: 100%;
    height: 150rpx;
    font-size: 28rpx;
    color: #ffffff;
  }
  
  .text-counter {
    text-align: right;
    font-size: 24rpx;
    color: #ffffff;
    margin-top: 10rpx;
  }
}

.ratio-options {
  display: flex;
  
  .ratio-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    background-color: #0f3460;
    color: #ffffff;
    margin-right: 10rpx;
    border-radius: 8rpx;
    border: 2rpx solid #3a6299;
    transition: all 0.2s ease;
    padding: 8rpx 0;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
    
    &:last-child {
      margin-right: 0;
    }
    
    &.active {
      background-color: #1e3799;
      color: #ffffff;
      border-color: #4a69bd;
      box-shadow: 0 0 10rpx rgba(74, 105, 189, 0.5);
      transform: translateY(-2rpx);
    }
    
    image {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 6rpx;
    }
    
    text {
      font-size: 24rpx;
      font-weight: 500;
    }
  }
}
</style> 