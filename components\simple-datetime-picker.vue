<template>
	<view class="datetime-picker" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<!-- 头部 -->
			<view class="picker-header">
				<text class="header-title">选择出生日期时间</text>
			</view>

			<!-- 列标签 - 移到上面 -->
			<view class="column-labels-top">
				<text class="column-label">年</text>
				<text class="column-label">月</text>
				<text class="column-label">日</text>
				<text class="column-label">时</text>
				<text class="column-label">分</text>
			</view>

			<!-- 日期时间选择器 -->
			<view class="picker-container">
				<!-- 选择框指示器 -->
				<view class="picker-indicator"></view>

				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
					:indicator-style="indicatorStyle"
					:mask-style="maskStyle"
				>
					<!-- 年份列 -->
					<picker-view-column>
						<view
							v-for="(year, index) in years"
							:key="index"
							class="picker-item"
						>
							{{ year }}
						</view>
					</picker-view-column>

					<!-- 月份列 -->
					<picker-view-column>
						<view
							v-for="(month, index) in months"
							:key="index"
							class="picker-item"
						>
							{{ String(month).padStart(2, '0') }}
						</view>
					</picker-view-column>

					<!-- 日期列 -->
					<picker-view-column>
						<view
							v-for="(day, index) in days"
							:key="index"
							class="picker-item"
						>
							{{ String(day).padStart(2, '0') }}
						</view>
					</picker-view-column>

					<!-- 小时列 -->
					<picker-view-column>
						<view
							v-for="(hour, index) in hours"
							:key="index"
							class="picker-item"
						>
							{{ String(hour).padStart(2, '0') }}
						</view>
					</picker-view-column>

					<!-- 分钟列 -->
					<picker-view-column>
						<view
							v-for="(minute, index) in minutes"
							:key="index"
							class="picker-item"
						>
							{{ String(minute).padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>

			<!-- 操作按钮 -->
			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'SimpleDatetimePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		const now = new Date();
		return {
			selectedYear: now.getFullYear(),
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate(),
			selectedHour: now.getHours(),
			selectedMinute: now.getMinutes(),
			pickerValue: [0, 0, 0, 0, 0],
			years: this.generateYears(),
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
			hours: this.generateHours(),
			minutes: this.generateMinutes(),
			// 选择器指示器样式
			indicatorStyle: `
				height: 80rpx;
				background: rgba(0, 122, 255, 0.1);
				border: 2rpx solid #007aff;
				border-radius: 8rpx;
			`,
			// 遮罩样式
			maskStyle: `
				background: linear-gradient(180deg,
					rgba(255, 255, 255, 0.9) 0%,
					rgba(255, 255, 255, 0.6) 30%,
					transparent 50%,
					rgba(255, 255, 255, 0.6) 70%,
					rgba(255, 255, 255, 0.9) 100%
				);
			`
		};
	},
	computed: {
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initPicker();
			}
		}
	},
	methods: {
		generateYears() {
			const years = [];
			const currentYear = new Date().getFullYear();
			for (let i = 1900; i <= currentYear + 20; i++) {
				years.push(i);
			}
			return years;
		},
		generateHours() {
			const hours = [];
			for (let i = 0; i < 24; i++) {
				hours.push(i);
			}
			return hours;
		},
		generateMinutes() {
			const minutes = [];
			for (let i = 0; i < 60; i++) {
				minutes.push(i);
			}
			return minutes;
		},
		initPicker() {
			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.selectedMonth - 1;
			const dayIndex = this.selectedDay - 1;
			const hourIndex = this.selectedHour;
			const minuteIndex = this.selectedMinute;

			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0,
				hourIndex >= 0 ? hourIndex : 0,
				minuteIndex >= 0 ? minuteIndex : 0
			];
		},
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value;
			
			this.selectedYear = this.years[yearIndex];
			this.selectedMonth = monthIndex + 1;
			this.selectedDay = this.days[dayIndex];
			this.selectedHour = this.hours[hourIndex];
			this.selectedMinute = this.minutes[minuteIndex];
			
			this.pickerValue = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex];
		},
		close() {
			this.$emit('close');
		},
		confirm() {
			const year = String(this.selectedYear);
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');

			const result = {
				date: `${year}-${month}-${day}`,
				time: `${hour}:${minute}`,
				datetime: `${year}-${month}-${day} ${hour}:${minute}`,
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				hour: this.selectedHour,
				minute: this.selectedMinute
			};

			this.$emit('confirm', result);
		}
	}
};
</script>

<style scoped>
.datetime-picker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

.picker-content {
	position: relative;
	width: 100%;
	max-width: 750rpx;
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
	padding: 40rpx 30rpx 30rpx;
}

.picker-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

/* 顶部标签 */
.column-labels-top {
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
	padding: 0 20rpx;
}

.column-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 600;
}

.picker-container {
	position: relative;
	height: 400rpx;
	margin-bottom: 40rpx;
}

/* 选择框指示器 */
.picker-indicator {
	position: absolute;
	top: 50%;
	left: 20rpx;
	right: 20rpx;
	height: 80rpx;
	transform: translateY(-50%);
	background: rgba(0, 122, 255, 0.08);
	border: 2rpx solid #007aff;
	border-radius: 12rpx;
	pointer-events: none;
	z-index: 1;
}

.picker-view {
	width: 100%;
	height: 100%;
}

.picker-item {
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.picker-footer {
	display: flex;
	gap: 20rpx;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 600;
}

.btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.btn-confirm {
	background: #007aff;
	color: white;
}
</style>
