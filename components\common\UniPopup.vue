<template>
  <view class="uni-popup" v-if="showPopup" @click.stop="handleMaskClick">
    <view class="uni-popup-mask"></view>
    <view class="uni-popup-content" :class="[`uni-popup-${type}`]" @click.stop>
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'uni-popup',
  props: {
    // 开启动画
    animation: {
      type: Boolean,
      default: true
    },
    // 弹出层类型，可选值：top/bottom/center
    type: {
      type: String,
      default: 'center'
    },
    // 是否开启点击遮罩关闭
    maskClick: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      showPopup: false
    }
  },
  methods: {
    /**
     * 打开弹窗
     */
    open() {
      this.showPopup = true
      this.$emit('change', {
        show: true
      })
    },
    /**
     * 关闭弹窗
     */
    close() {
      this.showPopup = false
      this.$emit('change', {
        show: false
      })
    },
    /**
     * 点击遮罩关闭弹窗
     */
    handleMaskClick() {
      if (!this.maskClick) return
      this.close()
    }
  }
}
</script>

<style lang="scss">
.uni-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  overflow: hidden;
  
  .uni-popup-mask {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 1;
  }
  
  .uni-popup-content {
    position: absolute;
    box-sizing: border-box;
    
    &.uni-popup-center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
    }
    
    &.uni-popup-top {
      top: 0;
      left: 0;
      width: 100%;
    }
    
    &.uni-popup-bottom {
      bottom: 0;
      left: 0;
      width: 100%;
    }
  }
}
</style> 