<template>
	<view class="datetime-picker-modal" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期和时间</text>
			</view>

			<!-- 日期选择区域 -->
			<view class="date-section">
				<view class="section-title">📅 出生日期</view>
				<view class="date-grid">
					<!-- 年份选择 -->
					<view class="date-item">
						<label class="date-label">年份</label>
						<select class="date-select" v-model="selectedYear" @change="validateYear">
							<option value="">选择年份</option>
							<option v-for="year in yearOptions" :key="year" :value="year">
								{{ year }}年
							</option>
						</select>
					</view>

					<!-- 月份选择 -->
					<view class="date-item">
						<label class="date-label">月份</label>
						<select class="date-select" v-model="selectedMonth" @change="validateMonth">
							<option value="">选择月份</option>
							<option v-for="month in monthOptions" :key="month" :value="month">
								{{ month }}月
							</option>
						</select>
					</view>

					<!-- 日期选择 -->
					<view class="date-item">
						<label class="date-label">日期</label>
						<select class="date-select" v-model="selectedDay" @change="validateDay">
							<option value="">选择日期</option>
							<option v-for="day in dayOptions" :key="day" :value="day">
								{{ day }}日
							</option>
						</select>
					</view>
				</view>
			</view>

			<!-- 时间选择区域 -->
			<view class="time-section">
				<view class="section-title">🕐 出生时间</view>
				<view class="time-grid">
					<!-- 小时选择 -->
					<view class="time-item">
						<label class="time-label">小时</label>
						<select class="time-select" v-model="selectedHour">
							<option value="">选择小时</option>
							<option v-for="hour in hourOptions" :key="hour" :value="hour">
								{{ String(hour).padStart(2, '0') }}时
							</option>
						</select>
					</view>

					<!-- 分钟选择 -->
					<view class="time-item">
						<label class="time-label">分钟</label>
						<select class="time-select" v-model="selectedMinute">
							<option value="">选择分钟</option>
							<option v-for="minute in minuteOptions" :key="minute" :value="minute">
								{{ String(minute).padStart(2, '0') }}分
							</option>
						</select>
					</view>
				</view>
			</view>

			<!-- 当前选择预览 -->
			<view class="selection-preview">
				<view class="preview-title">当前选择</view>
				<view class="preview-content">
					<text class="preview-date">{{ formatSelectedDateTime }}</text>
				</view>
			</view>

			<view class="modal-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirmSelection" :disabled="!isValidDateTime">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PerfectDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			// 日期选择
			selectedYear: '',
			selectedMonth: '',
			selectedDay: '',

			// 时间选择
			selectedHour: '',
			selectedMinute: ''
		}
	},
	computed: {
		// 年份选项 (1900-2030)
		yearOptions() {
			const years = [];
			for (let i = 1900; i <= 2030; i++) {
				years.push(i);
			}
			return years;
		},

		// 月份选项 (1-12)
		monthOptions() {
			return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
		},

		// 日期选项 (根据年月动态计算)
		dayOptions() {
			if (!this.selectedYear || !this.selectedMonth) return [];
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		},

		// 小时选项 (0-23)
		hourOptions() {
			const hours = [];
			for (let i = 0; i <= 23; i++) {
				hours.push(i);
			}
			return hours;
		},

		// 分钟选项 (0-59，每5分钟一个选项)
		minuteOptions() {
			const minutes = [];
			for (let i = 0; i <= 59; i += 5) {
				minutes.push(i);
			}
			return minutes;
		},

		// 格式化显示的日期时间
		formatSelectedDateTime() {
			const datePart = this.formatDatePart();
			const timePart = this.formatTimePart();

			if (!datePart && !timePart) {
				return '请选择出生日期和时间';
			}

			return `${datePart} ${timePart}`.trim();
		},

		// 验证日期时间是否完整
		isValidDateTime() {
			return this.selectedYear && this.selectedMonth && this.selectedDay &&
				   this.selectedHour !== '' && this.selectedMinute !== '';
		}
	},
	watch: {
		value: {
			handler(newValue) {
				if (newValue) {
					this.parseValue(newValue);
				}
			},
			immediate: true
		},
		
		// 当月份或年份改变时，调整日期
		selectedMonth() {
			this.adjustDay();
		},
		selectedYear() {
			this.adjustDay();
		}
	},
	methods: {
		// 解析传入的日期时间值
		parseValue(value) {
			if (!value) return;

			// 支持日期格式：2025-01-19 或 2025-01-19 14:30
			const parts = value.split(' ');
			const datePart = parts[0];
			const timePart = parts[1];

			// 解析日期部分
			if (datePart) {
				const dateComponents = datePart.split('-');
				if (dateComponents.length === 3) {
					this.selectedYear = parseInt(dateComponents[0]);
					this.selectedMonth = parseInt(dateComponents[1]);
					this.selectedDay = parseInt(dateComponents[2]);
				}
			}

			// 解析时间部分
			if (timePart) {
				const timeComponents = timePart.split(':');
				if (timeComponents.length >= 2) {
					this.selectedHour = parseInt(timeComponents[0]);
					this.selectedMinute = parseInt(timeComponents[1]);
				}
			}
		},

		// 格式化日期部分
		formatDatePart() {
			if (!this.selectedYear || !this.selectedMonth || !this.selectedDay) {
				const year = this.selectedYear || '____';
				const month = this.selectedMonth || '__';
				const day = this.selectedDay || '__';
				return `${year}年${month}月${day}日`;
			}
			return `${this.selectedYear}年${this.selectedMonth}月${this.selectedDay}日`;
		},

		// 格式化时间部分
		formatTimePart() {
			if (this.selectedHour === '' || this.selectedMinute === '') {
				const hour = this.selectedHour !== '' ? String(this.selectedHour).padStart(2, '0') : '__';
				const minute = this.selectedMinute !== '' ? String(this.selectedMinute).padStart(2, '0') : '__';
				return `${hour}:${minute}`;
			}
			return `${String(this.selectedHour).padStart(2, '0')}:${String(this.selectedMinute).padStart(2, '0')}`;
		},

		// 验证年份（下拉选择，无需特殊验证）
		validateYear() {
			// 当年份改变时，重新计算日期选项
			if (this.selectedDay && this.dayOptions.length > 0 && !this.dayOptions.includes(parseInt(this.selectedDay))) {
				this.selectedDay = '';
			}
		},

		// 验证月份
		validateMonth() {
			// 当月份改变时，重新计算日期选项
			if (this.selectedDay && this.dayOptions.length > 0 && !this.dayOptions.includes(parseInt(this.selectedDay))) {
				this.selectedDay = '';
			}
		},

		// 验证日期（下拉选择，无需特殊验证）
		validateDay() {
			// 日期选择无需额外验证
		},

		// 关闭选择器
		close() {
			this.$emit('close');
		},

		// 确认选择
		confirmSelection() {
			if (!this.isValidDateTime) {
				uni.showToast({
					title: '请选择完整的日期和时间',
					icon: 'none'
				});
				return;
			}

			const year = String(this.selectedYear).padStart(4, '0');
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');

			const dateTimeString = `${year}-${month}-${day} ${hour}:${minute}`;

			console.log('选择的日期时间:', dateTimeString);
			this.$emit('confirm', {
				date: `${year}-${month}-${day}`,
				time: `${hour}:${minute}`,
				datetime: dateTimeString
			});
			this.close();
		}
	}
}
</script>

<style scoped>
/* 模态框主体 - 最高层级 */
.datetime-picker-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
}

/* 模态框遮罩 */
.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1;
}

.picker-content {
	background: white;
	border-radius: 20px;
	width: 100%;
	max-width: 480px;
	max-height: 90vh;
	overflow-y: auto;
	padding: 0;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	animation: slideUp 0.3s ease-out;
	position: relative;
	z-index: 10;
}

@keyframes slideUp {
	from {
		transform: translateY(100px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 头部 */
.picker-header {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	padding: 20px;
	border-radius: 20px 20px 0 0;
	text-align: center;
}

.header-title {
	font-size: 18px;
	font-weight: 600;
}

/* 日期选择区域 */
.date-section {
	padding: 25px 20px 20px;
	border-bottom: 1px solid #f0f0f0;
}

.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.date-grid {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 15px;
}

.date-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.date-label {
	font-size: 14px;
	font-weight: 500;
	color: #666;
	text-align: center;
}

.date-select {
	padding: 12px 8px;
	border: 2px solid #e9ecef;
	border-radius: 10px;
	background: white;
	font-size: 14px;
	font-weight: 500;
	color: #333;
	text-align: center;
	outline: none;
	transition: all 0.3s ease;
	cursor: pointer;
}

.date-select:focus {
	border-color: #dc143c;
	box-shadow: 0 0 0 3px rgba(220, 20, 60, 0.1);
}

/* 时间选择区域 */
.time-section {
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.time-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15px;
}

.time-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.time-label {
	font-size: 14px;
	font-weight: 500;
	color: #666;
	text-align: center;
}

.time-select {
	padding: 12px 8px;
	border: 2px solid #e9ecef;
	border-radius: 10px;
	background: white;
	font-size: 14px;
	font-weight: 500;
	color: #333;
	text-align: center;
	outline: none;
	transition: all 0.3s ease;
	cursor: pointer;
}

.time-select:focus {
	border-color: #dc143c;
	box-shadow: 0 0 0 3px rgba(220, 20, 60, 0.1);
}

/* 选择预览 */
.selection-preview {
	padding: 20px;
	text-align: center;
}

.preview-title {
	font-size: 14px;
	font-weight: 500;
	color: #666;
	margin-bottom: 10px;
}

.preview-content {
	background: rgba(220, 20, 60, 0.1);
	border-radius: 12px;
	padding: 15px;
}

.preview-date {
	font-size: 16px;
	font-weight: 600;
	color: #dc143c;
}

/* 底部按钮 */
.modal-footer {
	display: flex;
	border-top: 1px solid #e9ecef;
}

.btn-cancel,
.btn-confirm {
	flex: 1;
	padding: 18px;
	border: none;
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-cancel {
	background: #f8f9fa;
	color: #666;
	border-radius: 0 0 0 20px;
}

.btn-cancel:hover {
	background: #e9ecef;
}

.btn-confirm {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	border-radius: 0 0 20px 0;
}

.btn-confirm:hover {
	background: linear-gradient(135deg, #b91c3c 0%, #a0182f 100%);
}

.btn-confirm:disabled {
	background: #ccc;
	cursor: not-allowed;
}

.btn-confirm:disabled:hover {
	background: #ccc;
}
</style>
