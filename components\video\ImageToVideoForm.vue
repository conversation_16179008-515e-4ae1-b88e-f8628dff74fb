<template>
  <view class="image-to-video-form">
    <!-- 图片上传与模式选择区域 -->
    <view class="form-section image-upload-section">
      <text class="section-title">参考图片</text>
      
      <!-- 模式选择标签页 -->
      <view class="mode-tabs">
        <view 
          class="mode-tab" 
          :class="{ active: mode === 'multi-reference' }"
          @tap="switchMode('multi-reference')"
        >
          <image src="/static/icons/multi_ref.png" mode="aspectFit"></image>
          <text>多图参考</text>
        </view>
        <view 
          class="mode-tab" 
          :class="{ active: mode === 'keyframes' }"
          @tap="switchMode('keyframes')"
        >
          <image src="/static/icons/keyframes.png" mode="aspectFit"></image>
          <text>首尾帧过渡</text>
        </view>
      </view>
      
      <text class="mode-description">{{ modeDescriptions[mode] }}</text>
      <text class="upload-limit">{{ uploadLimitText }}</text>
      
      <!-- 多图参考模式图片上传区域 -->
      <view v-if="mode === 'multi-reference'">
        <!-- 多图参考说明 -->
        <view class="sequence-tip">请上传2-4张风格一致的清晰参考图片，AI将综合这些图片的风格生成视频</view>
        
        <!-- 图片预览区域 - 网格布局 -->
        <view 
          class="multi-images-grid" 
          v-if="imagePreview.length > 0"
        >
          <view 
            v-for="(item, index) in imagePreview" 
            :key="index"
            class="image-preview-item"
          >
            <image class="preview-image" :src="item" mode="aspectFill"></image>
            <view class="image-label">图 {{index + 1}}</view>
            <view class="image-tools">
              <view class="tool-btn preview-btn" @tap="previewImage(index)">
                <text>👁</text>
              </view>
              <view class="tool-btn delete-btn" @tap="deleteImage(index)">
                <text>✕</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 上传按钮 -->
        <view class="upload-btn-container" v-if="canAddMoreImages">
          <view class="upload-btn" @tap="chooseImage()">
            <text class="upload-icon">+</text>
            <text class="upload-text">添加图片 (最多{{maxImages[mode]}}张，{{imagePreview.length}}/{{maxImages[mode]}})</text>
          </view>
        </view>
      </view>
      
      <!-- 首尾帧过渡模式图片上传区域 -->
      <view class="keyframes-area" v-else>
        <view class="keyframe-hint">请上传清晰、风格一致的图片，AI将自动生成平滑过渡</view>
        <view class="keyframes-container">
          <!-- 起始帧 -->
          <view class="keyframe-item">
            <view class="keyframe-label start">起始帧</view>
            <view 
              class="image-preview-item" 
              v-if="imagePreview.length > 0"
            >
              <image class="preview-image" :src="imagePreview[0]" mode="aspectFill"></image>
              <view class="image-label start-label">起始帧</view>
              <view class="image-tools">
                <view class="tool-btn preview-btn" @tap="previewImage(0)">
                  <text>👁</text>
                </view>
                <view class="tool-btn delete-btn" @tap="deleteImage(0)">
                  <text>✕</text>
                </view>
              </view>
            </view>
            <view 
              class="upload-btn empty-frame" 
              v-else 
              @tap="chooseImage(0)"
            >
              <text class="upload-icon">+</text>
              <text class="upload-text">上传起始帧</text>
            </view>
          </view>
          
          <!-- 过渡箭头 -->
          <view class="transition-arrow">
            <image src="/static/icons/arrow_right.png" mode="aspectFit"></image>
          </view>
          
          <!-- 结束帧 -->
          <view class="keyframe-item">
            <view class="keyframe-label end">结束帧</view>
            <view 
              class="image-preview-item" 
              v-if="imagePreview.length > 1"
            >
              <image class="preview-image" :src="imagePreview[1]" mode="aspectFill"></image>
              <view class="image-label end-label">结束帧</view>
              <view class="image-tools">
                <view class="tool-btn preview-btn" @tap="previewImage(1)">
                  <text>👁</text>
                </view>
                <view class="tool-btn delete-btn" @tap="deleteImage(1)">
                  <text>✕</text>
                </view>
              </view>
            </view>
            <view 
              class="upload-btn empty-frame" 
              v-else 
              @tap="chooseImage(1)"
            >
              <text class="upload-icon">+</text>
              <text class="upload-text">上传结束帧</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 图像增强提示词 -->
      <view class="prompt-container">
        <text class="prompt-label">增强提示词</text>
        <textarea 
          class="enhance-input"
          v-model="enhancePrompt"
          placeholder="描述您希望视频呈现的风格、场景或细节，AI将参考这些提示词增强效果"
          maxlength="200"
        ></textarea>
        <view class="text-counter">{{ enhancePrompt.length }}/200</view>
      </view>
      
      <!-- 模式特有参数 -->
      <view v-if="mode === 'keyframes'" class="parameter-slider">
        <text class="parameter-label">过渡平滑度</text>
        <view class="parameter-slider-container">
          <slider 
            :value="smoothness" 
            :min="0" 
            :max="100" 
            :step="5" 
            @change="onSmoothnessChange"
            activeColor="#0abde3"
            backgroundColor="#0f3460"
            blockColor="#ffda79"
            block-size="24"
          ></slider>
          <text class="parameter-value">{{ smoothness }}%</text>
        </view>
      </view>
      

    </view>

    <!-- 视频参数设置区域 -->
    <view class="form-section">
      <text class="section-title">视频参数</text>
      <view class="parameters-grid">
        <!-- 视频时长 -->
        <view class="parameter-item">
          <text class="parameter-label">时长</text>
          <view class="duration-options">
            <view
              class="duration-btn"
              :class="{ active: duration === 5 }"
              @tap="duration = 5"
            >5秒</view>
            <view
              class="duration-btn"
              :class="{ active: duration === 10 }"
              @tap="duration = 10"
            >10秒</view>
          </view>
        </view>
        
        <!-- 视频质量 -->
        <view class="parameter-item full-width">
          <text class="parameter-label">质量</text>
          <view class="quality-options">
            <view
              class="quality-btn"
              :class="{ active: quality === '480p' }"
              @tap="quality = '480p'"
            >480p</view>
            <view
              class="quality-btn"
              :class="{ active: quality === '720p' }"
              @tap="quality = '720p'"
            >720p</view>
            <view
              class="quality-btn"
              :class="{ active: quality === '1080p' }"
              @tap="quality = '1080p'"
            >1080p</view>
          </view>
        </view>
        
        <!-- 视频比例 -->
        <view class="parameter-item full-width">
          <text class="parameter-label">比例</text>
          <view class="ratio-options">
            <view
              v-for="(ratio, index) in videoRatios"
              :key="index"
              class="ratio-btn"
              :class="{ active: selectedRatio === ratio.value }"
              @tap="selectRatio(ratio.value)"
            >
              <view class="ratio-visual" :class="'ratio-' + ratio.value.replace(':', '-')"></view>
              <text>{{ ratio.label }}</text>
            </view>
          </view>
        </view>
        

      </view>
    </view>
    

    
    <!-- 裁剪弹窗 -->
    <view class="crop-popup" v-if="showCropPopup">
      <!-- 裁剪界面内容 -->
    </view>
  </view>
</template>

<script>
export default {
  name: 'ImageToVideoForm',
  data() {
    return {
      mode: 'multi-reference', // 'multi-reference' 或 'keyframes'
      modeDescriptions: {
        'multi-reference': '上传2-4张风格一致的参考图片，AI将综合风格生成连续视频',
        'keyframes': '上传起始帧和结束帧，AI将自动生成两者之间的平滑过渡'
      },
      // 为每种模式分别存储图片数据
      modeData: {
        'multi-reference': {
          imageList: [], // 存储实际文件
          imagePreview: [], // 预览图片URL
        },
        'keyframes': {
          imageList: [], // 存储实际文件
          imagePreview: [], // 预览图片URL
        }
      },
      imageList: [], // 当前模式的文件引用
      imagePreview: [], // 当前模式的预览引用
      enhancePrompt: '',
      duration: 5,
      smoothness: 70, // 过渡平滑度(仅关键帧模式)
      quality: '720p', // 480p, 720p, 1080p
      selectedRatio: '16:9',
      showCropPopup: false,
      estimatedCost: 300,
      maxImages: {
        'multi-reference': 4, // 降低到4张，更符合模型实际能力
        'keyframes': 2
      },
      
      videoRatios: [
        { label: '横屏16:9', value: '16:9' },
        { label: '竖屏9:16', value: '9:16' },
        { label: '正方形1:1', value: '1:1' },
        { label: '传统4:3', value: '4:3' }
      ],
      

      
      shouldSaveState: true,
      storageKey: 'image-to-video-form-state'
    }
  },
  mounted() {
    // 初始化当前模式的图片数据引用
    this.imageList = this.modeData[this.mode].imageList;
    this.imagePreview = this.modeData[this.mode].imagePreview;
    
    // 在组件挂载后尝试恢复状态
    this.loadFormState();
  },
  computed: {
    uploadLimitText() {
      return `最多${this.maxImages[this.mode]}张图片`;
    },
    
    canAddMoreImages() {
      return this.imagePreview.length < this.maxImages[this.mode];
    },
    
    canGenerate() {
      if (this.mode === 'multi-reference') {
        return this.imagePreview.length >= 1;
      } else {
        return this.imagePreview.length === 2;
      }
    }
  },
  watch: {
    // 监听模式变化
    mode() {
      // 切换模式时清空图片
      this.imageList = [];
      this.imagePreview = [];
      this.updateEstimatedCost();
      // 保存状态
      this.saveFormState();
    },
    
    // 监听图片列表变化
    imagePreview: {
      handler() {
        this.saveFormState();
      },
      deep: true
    },
    
    // 监听重要参数变化
    enhancePrompt() {
      this.saveFormState();
    },
    
    duration() {
      this.updateEstimatedCost();
      this.saveFormState();
    },
    
    smoothness() {
      this.saveFormState();
    },
    

    
    quality() {
      this.updateEstimatedCost();
      this.saveFormState();
    },
    
    selectedRatio() {
      this.saveFormState();
    },
    

  },
  methods: {
    // 方法实现部分将在后续添加
    switchMode(mode) {
      if (mode !== this.mode) {
        // 保存当前模式的图片数据
        this.modeData[this.mode].imageList = [...this.imageList];
        this.modeData[this.mode].imagePreview = [...this.imagePreview];
        
        // 切换模式
        this.mode = mode;
        
        // 加载新模式的图片数据
        this.imageList = [...this.modeData[mode].imageList];
        this.imagePreview = [...this.modeData[mode].imagePreview];
        
        this.updateEstimatedCost();
        
        uni.showToast({
          title: mode === 'multi-reference' ? '已切换到多图参考模式' : '已切换到首尾帧过渡模式',
          icon: 'none'
        });
        
        // 保存状态
        this.saveFormState();
      }
    },
    chooseImage(frameIndex) {
      // 判断是否可以继续添加图片
      if (!this.canAddMoreImages) {
        uni.showToast({
          title: `最多只能上传${this.maxImages[this.mode]}张图片`,
          icon: 'none'
        });
        return;
      }
      
      // 计算还可以选择的图片数量
      const remainingCount = this.maxImages[this.mode] - this.imagePreview.length;
      
      // 弹出选择器
      uni.chooseImage({
        count: this.mode === 'keyframes' ? 1 : remainingCount, // 首尾帧模式下只能选1张，多图参考模式可多选
        sizeType: ['compressed'], // 压缩图片
        sourceType: ['album', 'camera'], // 相册和相机
        success: (res) => {
          const tempFiles = res.tempFiles;
          const tempFilePaths = res.tempFilePaths;
          
          // 添加图片
          if (this.mode === 'keyframes') {
            // 首尾帧模式，添加到指定位置
            if (frameIndex === 0 || this.imagePreview.length === 0) {
              this.imageList[0] = tempFiles[0];
              this.imagePreview[0] = tempFilePaths[0];
            } else {
              this.imageList[1] = tempFiles[0];
              this.imagePreview[1] = tempFilePaths[0];
            }
          } else {
            // 多图参考模式，可能有多张图片
            for (let i = 0; i < tempFilePaths.length; i++) {
              // 检查是否已达到最大图片数量
              if (this.imagePreview.length >= this.maxImages[this.mode]) {
                break;
              }
              
              // 添加到末尾
              this.imageList.push(tempFiles[i]);
              this.imagePreview.push(tempFilePaths[i]);
            }
            
            // 如果选择了多于允许的图片数量，提示用户
            if (tempFilePaths.length > remainingCount) {
              uni.showToast({
                title: `已选择前${remainingCount}张图片，达到最大限制`,
                icon: 'none',
                duration: 2000
              });
            }
          }
          
          // 同步当前模式的图片数据到modeData中
          this.modeData[this.mode].imageList = [...this.imageList];
          this.modeData[this.mode].imagePreview = [...this.imagePreview];
          
          this.updateEstimatedCost();
          // 保存当前状态到本地存储
          this.saveFormState();
        }
      });
    },
    previewImage(index) {
      uni.previewImage({
        urls: this.imagePreview,
        current: this.imagePreview[index]
      });
    },
    deleteImage(index) {
      // 删除指定索引的图片
      this.imageList.splice(index, 1);
      this.imagePreview.splice(index, 1);
      
      // 同步当前模式的图片数据到modeData中
      this.modeData[this.mode].imageList = [...this.imageList];
      this.modeData[this.mode].imagePreview = [...this.imagePreview];
      
      this.updateEstimatedCost();
      // 保存当前状态到本地存储
      this.saveFormState();
    },
    onDurationChange(e) {
      this.duration = e.detail.value;
    },
    onSmoothnessChange(e) {
      this.smoothness = e.detail.value;
    },

    selectRatio(ratio) {
      this.selectedRatio = ratio;
    },

    updateEstimatedCost() {
      // 根据视频参数计算预估成本
      let baseCost = 300; // 图生视频基础成本更高

      // 根据时长增加成本
      if (this.duration === 10) {
        baseCost += 100; // 10秒比5秒多100金币
      }

      // 根据质量增加成本
      if (this.quality === '720p') {
        baseCost *= 1.5;
      } else if (this.quality === '1080p') {
        baseCost *= 2;
      }

      // 根据模式和图片数量增加成本
      if (this.mode === 'keyframes') {
        baseCost *= 1.2; // 首尾帧模式更复杂
      }
      baseCost += this.imagePreview.length * 15;

      this.estimatedCost = Math.round(baseCost);
    },
    saveDraft() {
      const draft = {
        type: 'image-to-video',
        mode: this.mode,
        imageList: this.imageList,
        enhancePrompt: this.enhancePrompt,
        duration: this.duration,
        smoothness: this.smoothness,
        quality: this.quality,
        ratio: this.selectedRatio,
        timestamp: new Date().getTime()
      };
      
      // 保存草稿，实际项目中应保存到本地或服务器
      console.log('保存草稿:', draft);
      uni.showToast({
        title: '草稿已保存',
        icon: 'success'
      });
    },
    getDraftData() {
      // 返回当前表单的草稿数据
      return {
        type: 'image-to-video',
        mode: this.mode,
        imageList: this.imageList,
        enhancePrompt: this.enhancePrompt,
        duration: this.duration,
        smoothness: this.smoothness,
        quality: this.quality,
        ratio: this.selectedRatio,
        timestamp: new Date().getTime()
      };
    },
    generateVideo() {
      if (this.mode === 'multi-reference' && this.imagePreview.length === 0) {
        uni.showToast({
          title: '请至少上传一张参考图片',
          icon: 'none'
        });
        return;
      }
      
      if (this.mode === 'keyframes' && this.imagePreview.length !== 2) {
        uni.showToast({
          title: '请上传起始帧和结束帧',
          icon: 'none'
        });
        return;
      }
      
      const params = {
        mode: this.mode,
        images: this.imageList,
        enhancePrompt: this.enhancePrompt,
        duration: this.duration,
        smoothness: this.smoothness,
        quality: this.quality,
        ratio: this.selectedRatio,
        cost: this.estimatedCost
      };
      
      this.$emit('generate', params);
    },
    // 保存表单状态到本地存储
    saveFormState() {
      if (!this.shouldSaveState) return;
      
      try {
        const stateToSave = {
          mode: this.mode,
          modeData: {
            'multi-reference': {
              imagePreview: this.modeData['multi-reference'].imagePreview
            },
            'keyframes': {
              imagePreview: this.modeData['keyframes'].imagePreview
            }
          },
          enhancePrompt: this.enhancePrompt,
          duration: this.duration,
          smoothness: this.smoothness,
          quality: this.quality,
          selectedRatio: this.selectedRatio,
          timestamp: new Date().getTime()
        };
        
        // 使用uni-app的存储API保存状态
        uni.setStorageSync(this.storageKey, JSON.stringify(stateToSave));
        console.log('表单状态已保存');
      } catch (error) {
        console.error('保存表单状态失败:', error);
      }
    },
    
    // 从本地存储加载表单状态
    loadFormState() {
      try {
        const savedState = uni.getStorageSync(this.storageKey);
        
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          
          // 检查存储的时间戳，如果超过24小时则不恢复（可选）
          const now = new Date().getTime();
          const savedTime = parsedState.timestamp || 0;
          const oneDay = 24 * 60 * 60 * 1000;
          
          if (now - savedTime > oneDay) {
            console.log('保存的状态已过期，不再恢复');
            uni.removeStorageSync(this.storageKey);
            return;
          }
          
          // 暂时禁用状态保存以避免循环调用
          this.shouldSaveState = false;
          
          // 恢复基本表单值
          this.mode = parsedState.mode || 'multi-reference';
          this.enhancePrompt = parsedState.enhancePrompt || '';
          this.duration = parsedState.duration || 5;
          this.smoothness = parsedState.smoothness || 70;
          this.quality = parsedState.quality || '720p';
          this.selectedRatio = parsedState.selectedRatio || '16:9';
          
          // 恢复各模式的图片数据
          if (parsedState.modeData) {
            // 多图参考模式
            if (parsedState.modeData['multi-reference'] && Array.isArray(parsedState.modeData['multi-reference'].imagePreview)) {
              this.modeData['multi-reference'].imagePreview = parsedState.modeData['multi-reference'].imagePreview;
              // 生成对应的imageList占位符
              this.modeData['multi-reference'].imageList = parsedState.modeData['multi-reference'].imagePreview.map(url => ({
                path: url,
                size: 0,
                type: 'image'
              }));
              console.log(`已恢复多图参考模式的${this.modeData['multi-reference'].imagePreview.length}张图片`);
            }
            
            // 首尾帧过渡模式
            if (parsedState.modeData['keyframes'] && Array.isArray(parsedState.modeData['keyframes'].imagePreview)) {
              this.modeData['keyframes'].imagePreview = parsedState.modeData['keyframes'].imagePreview;
              // 生成对应的imageList占位符
              this.modeData['keyframes'].imageList = parsedState.modeData['keyframes'].imagePreview.map(url => ({
                path: url,
                size: 0,
                type: 'image'
              }));
              console.log(`已恢复首尾帧过渡模式的${this.modeData['keyframes'].imagePreview.length}张图片`);
            }
            
            // 加载当前模式的图片数据
            this.imagePreview = [...this.modeData[this.mode].imagePreview];
            this.imageList = [...this.modeData[this.mode].imageList];
          }
          

          
          // 更新预估成本
          this.updateEstimatedCost();
          
          // 重新启用状态保存
          setTimeout(() => {
            this.shouldSaveState = true;
          }, 500);
          
          console.log('表单状态已恢复');
        } else {
          console.log('没有找到保存的表单状态');
        }
      } catch (error) {
        console.error('加载表单状态失败:', error);
      }
    },
    
    // 清除保存的表单状态
    clearFormState() {
      try {
        uni.removeStorageSync(this.storageKey);
        console.log('表单状态已清除');
      } catch (error) {
        console.error('清除表单状态失败:', error);
      }
    },
    // 修改setImageFromTextToImage方法支持多图数据结构
    setImageFromTextToImage(imageResult) {
      // 根据不同的数据结构处理
      if (imageResult.allImages && imageResult.allImages.length > 0) {
        // 多图情况
        const images = imageResult.allImages;
        
        // 根据当前模式决定如何处理多张图片
        if (this.mode === 'keyframes' && images.length >= 2) {
          // 首尾帧模式 - 使用第一张和最后一张作为首尾帧
          this.switchMode('keyframes');
          this.imagePreview = [images[0].url, images[images.length - 1].url];
          this.imageFiles = [
            { path: images[0].url },
            { path: images[images.length - 1].url }
          ];
        } else {
          // 多图参考模式 - 使用所有图片
          this.switchMode('multi-reference');
          this.imagePreview = images.map(img => img.url);
          this.imageFiles = images.map(img => ({ path: img.url }));
        }
        
        // 设置默认增强提示词
        if (!this.enhancePrompt) {
          this.enhancePrompt = '高清细节，流畅过渡';
        }
        
        // 更新成本估算
        this.updateEstimatedCost();
      } else if (imageResult && imageResult.url) {
        // 单张图片的情况
        this.imagePreview = [imageResult.url];
        this.imageFiles = [{ path: imageResult.url }];
        
        // 如果是首尾帧模式但只有一张图片，则切换到多图参考模式
        if (this.mode === 'keyframes' && this.imagePreview.length < 2) {
          this.switchMode('multi-reference');
        }
        
        // 可以设置一些默认的增强提示词
        if (!this.enhancePrompt) {
          this.enhancePrompt = '高清细节，流畅过渡';
        }
        
        // 更新成本估算
        this.updateEstimatedCost();
      } else if (Array.isArray(imageResult)) {
        // 数组形式的多张图片
        this.imagePreview = [...imageResult];
        this.imageFiles = imageResult.map(url => ({ path: url }));
        
        // 更新成本估算
        this.updateEstimatedCost();
      }
    },
    // 从历史记录恢复图片
    setImages(images) {
      if (!images) return;
      
      if (Array.isArray(images)) {
        // 处理多张图片
        this.images = images.map(img => {
          return {
            id: img.id || `img_${Date.now()}_${Math.random().toString(36).substring(7)}`,
            url: img.url || img,
            isLoading: false
          };
        });
      } else if (typeof images === 'object') {
        // 处理单张图片
        this.images = [{
          id: images.id || `img_${Date.now()}_${Math.random().toString(36).substring(7)}`,
          url: images.url || images,
          isLoading: false
        }];
      }
      
      // 更新表单状态
      this.updateFormState();
    },
  }
}
</script>

<style lang="scss" scoped>
.image-to-video-form {
  padding-bottom: 30rpx;
  
  .form-section {
    margin-bottom: 30rpx;
    background-color: rgba(15, 52, 96, 0.5);
    border-radius: 16rpx;
    padding: 20rpx;
    border: 1px solid rgba(74, 105, 189, 0.3);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
    
    &.image-upload-section {
      padding-bottom: 30rpx;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;
    }
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 15rpx;
      display: block;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 8rpx;
        height: 30rpx;
        width: 6rpx;
        background: linear-gradient(to bottom, #4a69bd, #0abde3);
        border-radius: 3rpx;
      }
    }
    
    .upload-limit {
      font-size: 26rpx;
      color: #ffffff;
      padding: 6rpx 16rpx;
      background: linear-gradient(to right, rgba(10, 189, 227, 0.3), rgba(10, 189, 227, 0.1));
      border-radius: 20rpx;
      display: inline-block;
      margin: 8rpx 0 15rpx;
      font-weight: 500;
      border: 1px solid rgba(10, 189, 227, 0.3);
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
    }
  }
  
  .mode-tabs {
    display: flex;
    border-radius: 12rpx;
    overflow: hidden;
    margin-bottom: 15rpx;
    border: 1px solid #2c2c44;
    
    .mode-tab {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding: 14rpx 0;
      background-color: #0f3460;
      position: relative;
      transition: all 0.3s ease;
      
      &.active {
        background: linear-gradient(to bottom, rgba(10, 189, 227, 0.4), rgba(74, 105, 189, 0.7));
        box-shadow: inset 0 0 12rpx rgba(74, 105, 189, 0.4);
        border-color: #0abde3;
        
        image {
          filter: brightness(1.3);
          transform: scale(1.1);
        }
        
        text {
          color: #ffffff;
          font-weight: 700;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
        }
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 30%;
          right: 30%;
          height: 4rpx;
          background: linear-gradient(to right, rgba(10, 189, 227, 0.4), #ffffff, rgba(10, 189, 227, 0.4));
          border-radius: 2rpx;
          box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.6);
        }
      }
      
      image {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
        opacity: 0.9;
        transition: all 0.3s ease;
      }
      
      text {
        font-size: 26rpx;
        color: #ffffff;
        transition: all 0.3s ease;
      }
      
      &:active {
        background-color: rgba(10, 189, 227, 0.2);
        transform: translateY(1rpx);
      }
    }
  }
  
  .mode-description {
    font-size: 26rpx;
    color: #ffffff;
    line-height: 1.5;
    padding: 14rpx 20rpx;
    margin-bottom: 15rpx;
    background: linear-gradient(to right, rgba(10, 189, 227, 0.3), rgba(9, 30, 66, 0.4));
    border-radius: 10rpx;
    border-left: 4rpx solid #0abde3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  }
  
  .sequence-tip {
    font-size: 26rpx;
    color: #ffda79;
    background: linear-gradient(to right, rgba(255, 218, 121, 0.2), rgba(255, 218, 121, 0.1));
    border-radius: 10rpx;
    padding: 14rpx 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-weight: 500;
    border: 1px solid rgba(255, 218, 121, 0.3);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &::before {
      content: "⚠️";
      margin-right: 10rpx;
      font-size: 28rpx;
    }
  }
  
  .multi-images-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 8rpx;
    padding: 5rpx 0;
    margin-bottom: 15rpx;
    
    .image-preview-item {
      height: 150rpx;
      position: relative;
      border-radius: 8rpx;
      overflow: hidden;
      border: 1px solid #3a6299;
      background-color: #0f3460;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
      
      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .image-label {
        position: absolute;
        left: 5rpx;
        bottom: 5rpx;
        background: linear-gradient(to right, rgba(10, 189, 227, 0.85), rgba(0, 0, 0, 0.85));
        color: #ffffff;
        font-size: 22rpx;
        padding: 4rpx 10rpx;
        border-radius: 6rpx;
        font-weight: bold;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.3);
        letter-spacing: 1rpx;
        z-index: 5;
        min-width: 30rpx;
        text-align: center;
        
        &.start-label {
          background: linear-gradient(to right, rgba(10, 189, 227, 0.9), rgba(0, 0, 0, 0.8));
          border-color: rgba(10, 189, 227, 0.5);
        }
        
        &.end-label {
          background: linear-gradient(to right, rgba(74, 105, 189, 0.9), rgba(0, 0, 0, 0.8));
          border-color: rgba(74, 105, 189, 0.5);
        }
      }
      
      .image-tools {
        position: absolute;
        top: 5rpx;
        right: 5rpx;
        display: flex;
        z-index: 10;
        
        .tool-btn {
          width: 40rpx;
          height: 40rpx;
          border-radius: 20rpx;
          margin-left: 5rpx;
          font-size: 22rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: rgba(0, 0, 0, 0.7);
          color: #ffffff;
          font-weight: bold;
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.4);
          
          &.preview-btn {
            background-color: rgba(74, 105, 189, 0.8);
            border-color: rgba(72, 219, 251, 0.6);
          }
          
          &.delete-btn {
            background-color: rgba(214, 48, 49, 0.8);
            border-color: rgba(255, 121, 121, 0.6);
          }
          
          &:active {
            transform: scale(0.9);
            opacity: 0.8;
          }
          
          text {
            line-height: 1;
          }
        }
      }
    }
  }
  
  .upload-btn-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10rpx;
    margin-bottom: 10rpx;
    
    .upload-btn {
      width: 60%;
      height: 80rpx;
      border: 2rpx dashed #48dbfb;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background-color: rgba(15, 52, 96, 0.5);
      border-radius: 12rpx;
      
      .upload-icon {
        font-size: 40rpx;
        color: #48dbfb;
        line-height: 1;
        margin-right: 10rpx;
      }
      
      .upload-text {
        font-size: 26rpx;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }
  
  .keyframes-area {
    margin: 15rpx 0;
    
    .keyframe-hint {
      font-size: 26rpx;
      color: #ffda79;
      background: linear-gradient(to right, rgba(255, 218, 121, 0.2), rgba(255, 218, 121, 0.1));
      border-radius: 10rpx;
      padding: 14rpx 20rpx;
      margin-bottom: 20rpx;
      text-align: center;
      font-weight: 500;
      border: 1px solid rgba(255, 218, 121, 0.3);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      
      &::before {
        content: "⚠️";
        margin-right: 10rpx;
        font-size: 28rpx;
      }
    }
    
    .keyframes-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .keyframe-item {
        width: 42%;
        position: relative;
        
        .keyframe-label {
          font-size: 26rpx;
          color: #ffffff;
          margin-bottom: 10rpx;
          text-align: center;
          background-color: rgba(0, 0, 0, 0.3);
          padding: 6rpx 0;
          border-radius: 6rpx;
          font-weight: 500;
          
          &.start {
            background-color: rgba(10, 189, 227, 0.3);
          }
          
          &.end {
            background-color: rgba(74, 105, 189, 0.3);
          }
        }
        
        .image-preview-item {
          height: 200rpx;
          position: relative;
          border-radius: 12rpx;
          overflow: hidden;
          border: 1px solid #3a6299;
          background-color: #0f3460;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
          
          .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .image-label {
            position: absolute;
            left: 8rpx;
            bottom: 8rpx;
            background: linear-gradient(to right, rgba(10, 189, 227, 0.85), rgba(0, 0, 0, 0.85));
            color: #ffffff;
            font-size: 26rpx;
            padding: 6rpx 14rpx;
            border-radius: 10rpx;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.3);
            letter-spacing: 1rpx;
            z-index: 5;
            min-width: 40rpx;
            text-align: center;
            
            &.start-label {
              background: linear-gradient(to right, rgba(10, 189, 227, 0.9), rgba(0, 0, 0, 0.8));
              border-color: rgba(10, 189, 227, 0.5);
            }
            
            &.end-label {
              background: linear-gradient(to right, rgba(74, 105, 189, 0.9), rgba(0, 0, 0, 0.8));
              border-color: rgba(74, 105, 189, 0.5);
            }
          }
          
          .image-tools {
            position: absolute;
            top: 10rpx;
            right: 10rpx;
            display: flex;
            z-index: 10;
            
            .tool-btn {
              width: 50rpx;
              height: 50rpx;
              background-color: rgba(0, 0, 0, 0.7);
              border-radius: 25rpx;
              margin-left: 8rpx;
              display: flex;
              justify-content: center;
              align-items: center;
              box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
              font-size: 30rpx;
              color: #ffffff;
              font-weight: bold;
              transition: all 0.2s ease;
              border: 1px solid rgba(255, 255, 255, 0.3);
              
              &.preview-btn {
                background-color: rgba(74, 105, 189, 0.8);
                border-color: rgba(72, 219, 251, 0.6);
                text {
                  font-size: 28rpx;
                }
              }
              
              &.delete-btn {
                background-color: rgba(214, 48, 49, 0.8);
                border-color: rgba(255, 121, 121, 0.6);
                text {
                  font-size: 24rpx;
                }
              }
              
              &:active {
                transform: scale(0.9);
                opacity: 0.8;
              }
              
              text {
                line-height: 1;
              }
            }
          }
        }
        
        .upload-btn.empty-frame {
          height: 200rpx;
          border: 2rpx dashed #48dbfb;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background-color: rgba(15, 52, 96, 0.5);
          border-radius: 12rpx;
          transition: all 0.2s ease;
          
          &:active {
            background-color: rgba(15, 52, 96, 0.8);
            transform: translateY(2rpx);
          }
          
          .upload-icon {
            font-size: 50rpx;
            color: #48dbfb;
            line-height: 1;
            margin-bottom: 15rpx;
            text-shadow: 0 0 8rpx rgba(72, 219, 251, 0.7);
          }
          
          .upload-text {
            font-size: 26rpx;
            color: #ffffff;
            font-weight: 500;
            background-color: rgba(74, 105, 189, 0.4);
            padding: 6rpx 16rpx;
            border-radius: 8rpx;
            box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
          }
        }
      }
      
      .transition-arrow {
        width: 10%;
        display: flex;
        justify-content: center;
        align-items: center;
        
        image {
          width: 100%;
          height: 40rpx;
          opacity: 0.8;
        }
      }
    }
  }
  
  .prompt-container {
    margin-top: 25rpx;
    padding-top: 20rpx;
    border-top: 1px dashed rgba(74, 105, 189, 0.5);
    
    .prompt-label {
      font-size: 28rpx;
      color: #ffffff;
      margin-bottom: 15rpx;
      display: block;
      font-weight: 500;
    }
  }
  
  .enhance-input {
    width: 100%;
    height: 120rpx;
    background-color: #0f3460;
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    border: 1px solid #3a6299;
    margin-bottom: 10rpx;
    color: #ffffff;
    box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  }
  
  .text-counter {
    text-align: right;
    font-size: 24rpx;
    color: #ffffff;
  }
  
  .parameter-slider {
    margin-top: 25rpx;
    padding-top: 20rpx;
    border-top: 1px dashed rgba(74, 105, 189, 0.5);
    
    .parameter-label {
      font-size: 28rpx;
      color: #ffffff;
      margin-bottom: 15rpx;
      display: block;
      font-weight: 500;
    }
    
    .parameter-slider-container {
      position: relative;
      padding-right: 60rpx;
      margin-top: 20rpx;
      
      .parameter-value {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 26rpx;
        color: #ffda79;
        font-weight: 500;
      }
    }
  }
  
  .parameters-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;
    
    .parameter-item {
      width: 100%;
      
      .parameter-label {
        font-size: 28rpx;
        color: #ffffff;
        margin-bottom: 15rpx;
        display: block;
        font-weight: 500;
      }
      
      .parameter-slider-container {
        position: relative;
        padding-right: 60rpx;
        margin-top: 20rpx;
        
        .parameter-value {
          position: absolute;
          right: 0;
          top: 0;
          font-size: 26rpx;
          color: #ffda79;
          font-weight: 500;
        }
      }

      .duration-options {
        display: flex;

        .duration-btn {
          flex: 1;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          font-weight: 600;
          background-color: #0f3460;
          color: #ffffff;
          margin-right: 10rpx;
          border-radius: 8rpx;
          border: 2rpx solid #3a6299;
          transition: all 0.2s ease;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
          letter-spacing: 1rpx;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);

          &:last-child {
            margin-right: 0;
          }

          &.active {
            background: linear-gradient(to bottom, #4a69bd, #1e3799);
            color: #ffffff;
            border-color: #48dbfb;
            box-shadow: 0 0 12rpx rgba(74, 105, 189, 0.7);
            transform: translateY(-2rpx);
            position: relative;
            overflow: hidden;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3rpx;
              background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
              box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.6);
            }

            &::before {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 2rpx;
              background: rgba(255, 255, 255, 0.3);
            }
          }

          &:active {
            transform: translateY(1rpx);
            box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
          }
        }
      }

      .quality-options {
        display: flex;
        
        .quality-btn {
          flex: 1;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          font-weight: 600;
          background-color: #0f3460;
          color: #ffffff;
          margin-right: 10rpx;
          border-radius: 8rpx;
          border: 2rpx solid #3a6299;
          transition: all 0.2s ease;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
          letter-spacing: 1rpx;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
          
          &:last-child {
            margin-right: 0;
          }
          
          &.active {
            background: linear-gradient(to bottom, #4a69bd, #1e3799);
            color: #ffffff;
            border-color: #48dbfb;
            box-shadow: 0 0 12rpx rgba(74, 105, 189, 0.7);
            transform: translateY(-2rpx);
            position: relative;
            overflow: hidden;
            
            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3rpx;
              background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
              box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.6);
            }
            
            &::before {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 2rpx;
              background: rgba(255, 255, 255, 0.3);
            }
          }
          
          &:active {
            transform: translateY(1rpx);
            box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
          }
        }
      }
      
      .ratio-options {
        display: flex;
        gap: 15rpx;

        .ratio-btn {
          flex: 1;
          height: 120rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          background-color: #0f3460;
          color: #ffffff;
          border-radius: 12rpx;
          border: 2rpx solid #3a6299;
          transition: all 0.2s ease;
          padding: 12rpx 8rpx;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);

          &.active {
            background: linear-gradient(to bottom, #4a69bd, #1e3799);
            color: #ffffff;
            border-color: #48dbfb;
            box-shadow: 0 0 12rpx rgba(74, 105, 189, 0.7);
            transform: translateY(-2rpx);
          }

          .ratio-visual {
            margin-bottom: 12rpx;
            border: 2rpx solid #ffffff;
            background-color: rgba(255, 255, 255, 0.1);

            &.ratio-16-9 {
              width: 48rpx;
              height: 27rpx;
            }

            &.ratio-9-16 {
              width: 27rpx;
              height: 48rpx;
            }

            &.ratio-1-1 {
              width: 36rpx;
              height: 36rpx;
            }

            &.ratio-4-3 {
              width: 48rpx;
              height: 36rpx;
            }
            
            text {
              font-size: 28rpx;
              font-weight: 800;
              color: #ffffff;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
              background: linear-gradient(to bottom, #ffffff, #48dbfb);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              transform: scale(1.05);
              letter-spacing: 2rpx;
            }
          }
          
          image {
            width: 40rpx;
            height: 40rpx;
            margin-bottom: 8rpx;
            transition: all 0.2s ease;
          }
          
          text {
            font-size: 28rpx;
            font-weight: 800;
            text-shadow: 0 2px 3px rgba(0, 0, 0, 0.9);
            white-space: nowrap;
            color: #ffffff;
            background-color: rgba(9, 30, 66, 0.7);
            padding: 4rpx 10rpx;
            border-radius: 6rpx;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            letter-spacing: 1rpx;
            transition: all 0.2s ease;
          }
          
          &:active {
            transform: translateY(1rpx);
            box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
          }
        }
      }
      
      .music-options {
        .music-selection {
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #0f3460;
          border-radius: 8rpx;
          padding: 0 20rpx;
          border: 2rpx solid #3a6299;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.4);
          transition: all 0.2s ease;
          
          &:active {
            background-color: #0c2c50;
            transform: translateY(2rpx);
            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
          }
          
          text {
            font-size: 28rpx;
            color: #ffffff;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            
            &.arrow-icon {
              font-size: 36rpx;
              color: #48dbfb;
              transform: rotate(90deg);
            }
          }
        }
      }
    }
  }
  
  .music-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .music-container {
      width: 90%;
      max-height: 70%;
      background-color: #16213e;
      border-radius: 20rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .popup-header {
        padding: 30rpx;
        border-bottom: 1px solid #2c2c44;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .popup-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #ffffff;
        }
        
        .close-btn {
          font-size: 40rpx;
          color: #ffffff;
          padding: 0 10rpx;
        }
      }
      
      .music-list {
        flex: 1;
        overflow-y: auto;
        
        .music-item {
          padding: 30rpx;
          border-bottom: 1px solid #2c2c44;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .music-info {
            flex: 1;
            
            .music-name {
              font-size: 30rpx;
              font-weight: 500;
              color: #ffffff;
              margin-bottom: 10rpx;
              display: block;
            }
            
            .music-duration {
              font-size: 24rpx;
              color: #ffffff;
            }
          }
          
          .music-controls {
            display: flex;
            align-items: center;
            
            .play-btn {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
              background-color: #0f3460;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 20rpx;
              
              image {
                width: 30rpx;
                height: 30rpx;
              }
            }
            
            .select-btn {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              border: 2rpx solid #3a6299;
              display: flex;
              align-items: center;
              justify-content: center;
              
              &.selected {
                background-color: #0abde3;
              }
              
              image {
                width: 24rpx;
                height: 24rpx;
              }
            }
          }
        }
      }
    }
  }
  
  .crop-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .crop-container {
    width: 90%;
    height: 80%;
    background-color: #333;
    border-radius: 20rpx;
    overflow: hidden;
  }
}
</style> 