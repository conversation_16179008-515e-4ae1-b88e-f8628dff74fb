# 图生视频功能 API 文档

## 📋 概述

基于图片生成视频的AI工作流

## 🚀 快速开始

```javascript
import { 图生视频 } from '@/api/图生视频/index.js';

const result = await 图生视频({
    imageInput: '示例值',
    duration: '示例值',
    motionType: '示例值',
    resolution: '示例值'
});
```

## 📝 API 接口

### 主要接口

#### `图生视频(formData, options)`
执行图生视频功能的主接口。

**参数：**
- `imageInput` (string): imageInput *必需*
- `duration` (string): duration *必需*
- `motionType` (string): motionType *必需*
- `resolution` (string): resolution *必需*

**返回：**
```javascript
{
    success: true,
    data: {
        // 处理结果
    }
}
```

## ⚙️ 配置说明

### 工作流配置
- 工作流ID: `image_to_video_workflow_001`
- 工作流类型: `image_to_video`
- 基础费用: 40金币

## 🔧 工作流对接

### 结构化参数格式
```javascript
{
    requestId: "req_id",
    workflowId: "image_to_video_workflow_001",
    workflowType: "image_to_video",
    structuredParams: {
        imageInput: { type: "text", value: "值", placeholder: "{{imageInput}}" },
        duration: { type: "text", value: "值", placeholder: "{{duration}}" },
        motionType: { type: "text", value: "值", placeholder: "{{motionType}}" },
        resolution: { type: "text", value: "值", placeholder: "{{resolution}}" }
    }
}
```

## 🚨 错误处理

常见错误码：
- `IMAGE_TO_VIDEO_001`: 参数格式不正确
- `IMAGE_TO_VIDEO_007`: 金币余额不足
- `IMAGE_TO_VIDEO_008`: 工作流执行超时

## 📞 技术支持

如有问题，请联系开发团队。