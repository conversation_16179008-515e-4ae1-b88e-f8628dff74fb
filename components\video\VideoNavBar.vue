<template>
  <view class="video-navbar">
    <view class="nav-content">
      <view class="back-btn" @tap="goBack">
        <text class="back-icon"><<</text>
        <text class="back-text">返回</text>
      </view>
      <text class="navbar-title">{{ title }}</text>
      <view class="action-btns">
        <view class="history-btn" @tap="goToHistory">
          <svg class="history-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- 文件夹外框 -->
            <rect x="3" y="5" width="18" height="15" rx="2" stroke="white" stroke-width="2"/>
            <!-- 文件夹顶部 -->
            <path d="M3 8H21" stroke="white" stroke-width="2"/>
            <!-- 时钟指针 -->
            <circle cx="12" cy="14" r="4" stroke="white" stroke-width="2"/>
            <path d="M12 12V14H14" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <text class="btn-text">历史记录</text>
        </view>
        <view class="help-btn" @tap="showHelp">
          <svg class="help-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="9" stroke="white" stroke-width="2"/>
            <path d="M12 16V16.5" stroke="white" stroke-width="2" stroke-linecap="round"/>
            <path d="M12 14C12 11 15 11.5 15 9C15 7.5 13.6667 6 12 6C10.3333 6 9 7 9 9" stroke="white" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 帮助弹窗 -->
    <view class="help-popup" v-if="showHelpPopup" @tap.stop="closeHelp">
      <view class="help-content" @tap.stop>
        <view class="help-header">
          <text class="help-title">功能帮助</text>
          <text class="close-btn" @tap="closeHelp">×</text>
        </view>
        <view class="help-body">
          <view class="help-item">
            <text class="help-item-title">文生视频</text>
            <text class="help-item-desc">通过文本描述生成视频内容，适合创意表达和概念可视化</text>
          </view>
          <view class="help-item">
            <text class="help-item-title">图生视频</text>
            <text class="help-item-desc">使用图片作为参考生成视频，支持多图参考和首尾帧过渡两种模式</text>
          </view>
          <view class="help-item">
            <text class="help-item-title">视频参数</text>
            <text class="help-item-desc">调整视频风格、时长、比例等参数，实现个性化创作</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 更多选项弹窗 -->
    <view class="more-options" v-if="showOptions" @tap.stop="closeMoreOptions">
      <view class="options-panel" @tap.stop>
        <view class="option-item" @tap="goToTemplates">
          <image src="/static/icons/template.png" mode="aspectFit"></image>
          <text>模板库</text>
        </view>
        <view class="option-item" @tap="goToDrafts">
          <image src="/static/icons/draft.png" mode="aspectFit"></image>
          <text>草稿箱</text>
        </view>
        <view class="option-item" @tap="goToHistory">
          <image src="/static/icons/history.png" mode="aspectFit"></image>
          <text>历史记录</text>
        </view>
        <view class="option-item" @tap="goToSettings">
          <image src="/static/icons/setting.png" mode="aspectFit"></image>
          <text>设置</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '视频生成'
    }
  },
  data() {
    return {
      showHelpPopup: false,
      showOptions: false
    }
  },
  methods: {
    goBack() {
      try {
        uni.navigateBack({
          fail: () => {
            // 如果返回失败，则跳转到首页
            uni.switchTab({
              url: '/pages/index/index'
            });
          }
        });
      } catch (err) {
        console.error('返回上一页失败:', err);
        // 跳转到首页
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    },
    
    showHelp() {
      this.$emit('show-help');
      this.showOptions = false;
    },
    
    closeHelp() {
      this.showHelpPopup = false;
    },
    
    openMoreOptions() {
      this.$emit('show-options');
      this.showHelpPopup = false;
    },
    
    closeMoreOptions() {
      this.showOptions = false;
    },
    
    goToTemplates() {
      uni.navigateTo({
        url: '/pages/create/video/templates'
      });
      this.showOptions = false;
    },
    
    goToDrafts() {
      uni.navigateTo({
        url: '/pages/create/video/drafts'
      });
      this.showOptions = false;
    },
    
    goToHistory() {
      uni.navigateTo({
        url: '/pages/history/detail?type=video'
      });
      this.showOptions = false;
    },
    
    goToSettings() {
      uni.navigateTo({
        url: '/pages/create/video/settings'
      });
      this.showOptions = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.video-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #16213e;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
  
  .nav-content {
    height: 90rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx;
  }
  
  .back-btn {
    display: flex;
    align-items: center;
    padding: 15rpx 0;
    
    .back-icon {
      font-size: 32rpx;
      font-weight: bold;
      margin-right: 8rpx;
      color: #e6e6e6;
    }
    
    .back-text {
      font-size: 28rpx;
      color: #e6e6e6;
    }
  }
  
  .navbar-title {
    font-size: 34rpx;
    font-weight: 500;
    color: #ffffff;
    flex: 1;
    text-align: center;
    margin: 0 20rpx;
  }
  
  .action-btns {
    display: flex;
    align-items: center;
    
    .history-btn {
      height: 60rpx;
      display: flex;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 30rpx;
      padding: 0 20rpx;
      margin-right: 15rpx;
      
      .history-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }
      
      .btn-text {
        font-size: 24rpx;
        color: #ffffff;
        display: block;
      }
    }
    
    .help-btn {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:active {
        transform: scale(0.95);
        background-color: rgba(255, 255, 255, 0.3);
      }
      
      .help-icon {
        width: 28rpx;
        height: 28rpx;
      }
    }
  }
  
  .help-popup, .more-options {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .help-content {
    width: 80%;
    background-color: #16213e;
    border-radius: 20rpx;
    overflow: hidden;
    
    .help-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1px solid #2c2c44;
      
      .help-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
      }
      
      .close-btn {
        font-size: 40rpx;
        color: #e6e6e6;
        padding: 0 10rpx;
      }
    }
    
    .help-body {
      padding: 30rpx;
      max-height: 70vh;
      overflow-y: auto;
      
      .help-item {
        margin-bottom: 30rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .help-item-title {
          font-size: 30rpx;
          font-weight: 600;
          color: #0abde3;
          margin-bottom: 10rpx;
          display: block;
        }
        
        .help-item-desc {
          font-size: 28rpx;
          color: #e6e6e6;
          line-height: 1.5;
        }
      }
    }
  }
  
  .options-panel {
    position: absolute;
    top: 90rpx;
    right: 30rpx;
    background-color: #16213e;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
    width: 240rpx;
    
    .option-item {
      display: flex;
      align-items: center;
      padding: 20rpx 30rpx;
      
      &:not(:last-child) {
        border-bottom: 1px solid #2c2c44;
      }
      
      image {
        width: 36rpx;
        height: 36rpx;
        margin-right: 15rpx;
        opacity: 0.9;
      }
      
      text {
        font-size: 28rpx;
        color: #e6e6e6;
      }
    }
  }
}
</style> 