/**
 * routeFix.js - 路由修复辅助模块
 * 作为router.js的辅助模块，协同处理路由问题
 * 使用低优先级监听机制，确保与主路由系统不冲突
 */

import { isH5 } from './h5';

// 模块状态变量
const state = {
  initialized: false,
  mainSystemRunning: false,
  pendingNavigations: [],
  lastHashChange: 0,
  navigationInProgress: false
};

/**
 * 初始化hash路由处理器
 * 作为主路由系统的后备保障，仅在主系统可能失效时介入
 * 
 * @returns {boolean} 是否成功初始化
 */
export function initHashRouteHandler() {
  // 仅在H5环境下执行
  if (!isH5()) return false;
  
  // 避免重复初始化
  if (state.initialized) {
    console.log('路由修复辅助模块已初始化');
    return true;
  }
  
  console.log('开始初始化路由修复辅助模块');
  
  // 检查主路由系统是否已初始化
  if (window.__mainRouterFixed) {
    console.log('检测到主路由系统已运行，切换到辅助模式');
    state.mainSystemRunning = true;
  }
  
  // 监听主路由系统的初始化事件
  window.addEventListener('mainRouter:initialized', function() {
    console.log('收到主路由系统初始化通知');
    state.mainSystemRunning = true;
  });
  
  // 拦截history.pushState方法
  const originalPushState = window.history.pushState;
  window.history.pushState = function(...args) {
    // 调用原始方法
    originalPushState.apply(this, args);
    
    // 如果是hash变更，通知主路由系统
    const url = args[2] || '';
    if (url.includes('#')) {
      console.log('检测到pushState变更hash:', url);
      
      // 发送事件通知主路由系统
      window.dispatchEvent(new CustomEvent('routeFix:hashChanged', {
        detail: {
          url,
          timestamp: Date.now(),
          source: 'pushState'
        }
      }));
      
      // 如果主路由系统未处理，则手动触发hashchange事件
      if (!state.mainSystemRunning) {
        console.log('主路由系统未运行，手动触发hashchange');
        
        // 创建合成事件
        const syntheticEvent = new HashChangeEvent('hashchange', {
          oldURL: window.location.href,
          newURL: url
        });
        
        // 标记为辅助模块触发的变化
        window.__preventRecursiveHashChange = true;
        
        // 发送事件
        window.dispatchEvent(syntheticEvent);
        
        // 取消标记
        setTimeout(() => {
          window.__preventRecursiveHashChange = false;
        }, 100);
      }
    }
  };
  
  // 拦截history.replaceState方法
  const originalReplaceState = window.history.replaceState;
  window.history.replaceState = function(...args) {
    // 调用原始方法
    originalReplaceState.apply(this, args);
    
    // 如果是hash变更，通知主路由系统
    const url = args[2] || '';
    if (url.includes('#')) {
      console.log('检测到replaceState变更hash:', url);
      
      // 发送事件通知主路由系统
      window.dispatchEvent(new CustomEvent('routeFix:hashChanged', {
        detail: {
          url,
          timestamp: Date.now(),
          source: 'replaceState'
        }
      }));
      
      // 如果主路由系统未处理，则手动触发hashchange事件
      if (!state.mainSystemRunning) {
        console.log('主路由系统未运行，手动触发hashchange');
        
        // 创建合成事件
        const syntheticEvent = new HashChangeEvent('hashchange', {
          oldURL: window.location.href,
          newURL: url
        });
        
        // 标记为辅助模块触发的变化
        window.__preventRecursiveHashChange = true;
        
        // 发送事件
        window.dispatchEvent(syntheticEvent);
        
        // 取消标记
        setTimeout(() => {
          window.__preventRecursiveHashChange = false;
        }, 100);
      }
    }
  };
  
  // 低优先级监听hashchange事件，仅在主系统未处理时介入
  window.addEventListener('hashchange', function(event) {
    // 检查当前时间戳，避免短时间内重复处理
    const now = Date.now();
    if (now - state.lastHashChange < 200) {
      console.log('忽略过于频繁的hashchange事件');
      return;
    }
    
    // 更新最后变更时间
    state.lastHashChange = now;
    
    // 检查是否由主路由系统处理中
    if (window.__processingHashChange) {
      console.log('主路由系统正在处理hashchange，无需辅助介入');
      return;
    }
    
    // 检查是否由辅助模块自身触发
    if (window.__preventRecursiveHashChange) {
      console.log('辅助模块触发的hashchange，无需重复处理');
      return;
    }
    
    console.log('辅助模块处理hashchange:', 
      event?.newURL || window.location.href
    );
    
    // 标记为辅助模块处理的变化
    window.__routeFixProcessing = true;
    
    // 检查页面是否需要更新
    setTimeout(checkPageNeedsUpdate, 300);
    
    // 重置标记
    setTimeout(() => {
      window.__routeFixProcessing = false;
    }, 500);
  }, { capture: false });  // 使用非捕获模式，让主系统先处理
  
  // 后备导航方法，仅在主系统不存在时使用
  window.routeFixNavigate = function(url) {
    if (state.mainSystemRunning) {
      console.log('主路由系统运行中，不使用后备导航');
      return false;
    }
    
    if (!url) return false;
    
    console.log('使用后备导航方法:', url);
    
    // 规范化URL
    let normalizedUrl = url;
    if (!url.startsWith('#') && !url.startsWith('/')) {
      normalizedUrl = '/' + url;
    }
    
    if (!url.startsWith('#')) {
      normalizedUrl = '#' + normalizedUrl;
    }
    
    // 使用replaceState更新URL
    window.history.replaceState(null, '', normalizedUrl);
    
    // 手动触发hashchange事件
    const syntheticEvent = new HashChangeEvent('hashchange', {
      oldURL: window.location.href,
      newURL: window.location.href
    });
    
    // 标记为辅助模块触发的变化
    window.__preventRecursiveHashChange = true;
    
    // 发送事件
    window.dispatchEvent(syntheticEvent);
    
    // 取消标记
    setTimeout(() => {
      window.__preventRecursiveHashChange = false;
    }, 100);
    
    return true;
  };
  
  // 标记为已初始化
  state.initialized = true;
  console.log('路由修复辅助模块初始化完成');
  return true;
}

// 检查页面是否需要更新
function checkPageNeedsUpdate() {
  // 防止频繁调用
  if (window.__lastRouteFix_updateTime && Date.now() - window.__lastRouteFix_updateTime < 800) {
    console.log('辅助模块：页面更新请求过于频繁，忽略本次请求');
    return;
  }
  
  // 记录本次更新时间
  window.__lastRouteFix_updateTime = Date.now();
  
  // 只在主系统不处理时检查
  if (window.__processingHashChange || window.__routeFixHashChangeHandled) {
    // 重置标记
    window.__routeFixHashChangeHandled = false;
    return;
  }
  
  console.log('检查页面是否需要更新');
  
  // 发送页面需要更新的通知
  window.dispatchEvent(new CustomEvent('routeFix:pageNeedsUpdate', {
    detail: {
      url: window.location.href,
      hash: window.location.hash,
      timestamp: Date.now()
    }
  }));
}

// 如果是H5环境，自动初始化
if (isH5()) {
  // 延迟初始化，确保主路由系统有机会先初始化
  setTimeout(() => {
    if (!state.initialized) {
      initHashRouteHandler();
    }
  }, 1000);
}

/**
 * 检查路由状态
 * @returns {Object} 路由状态信息
 */
export function checkRouteState() {
  if (!isH5()) return { status: 'non-h5' };
  
  return {
    status: state.initialized ? 'initialized' : 'not-initialized',
    mainSystemRunning: state.mainSystemRunning,
    lastHashChange: state.lastHashChange,
    navigationInProgress: state.navigationInProgress
  };
}

/**
 * 手动触发页面更新检查
 * @returns {boolean} 是否成功触发检查
 */
export function triggerPageCheck() {
  if (!isH5() || !state.initialized) return false;
  
  checkPageNeedsUpdate();
  return true;
}

// 导出辅助函数
export const routeFixHelpers = {
  checkRouteState,
  triggerPageCheck
};

// 默认导出初始化函数
export default initHashRouteHandler; 