<template>
  <view class="input-area">
    <!-- 预设提示词切换按钮 -->
    <view class="toggle-button" @tap="toggleExpand" style="margin-top: 0; margin-bottom: 0;">
      <text class="toggle-text">{{ isCollapsed ? '预设提示词' : '收起提示词' }}</text>
      <text class="toggle-icon" :style="{ transform: isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)' }">▼</text>
    </view>
    
    <!-- 文本框区域 -->
    <view class="input-container" style="padding-top: 5px;">
      <view class="input-wrapper">
        <!-- 文本框组件 -->
        <view class="textarea-container">
          <StableTextArea
            v-model="inputText"
            :placeholder="currentPlaceholder"
            :placeholders="placeholderTexts"
            :show-animated-placeholder="!inputText"
            :min-height="40"
            :max-height="200"
            auto-height
            show-confirm-bar
            adjust-position
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            @paste="handlePaste"
            @height-change="handleTextareaHeightChange"
            ref="messageInput"
          >
            <!-- 发送按钮插槽内容 -->
            <template #sendButton>
              <!-- +号按钮 -->
              <button
                class="feature-btn feature-btn-horizontal"
                @tap="toggleFeatureMenu"
                :class="{'active': showFeatureMenu}"
              >
                <view class="plus-icon"></view>
              </button>

              <!-- 发送按钮和金币容器 -->
              <view class="send-btn-wrapper">
                <!-- 金币消耗显示 -->
                <view class="coin-number-top" v-if="inputText && !isGenerating" :class="{'coin-animate': isAnimatingCoin}">-{{ coinCost }}</view>

                <!-- 发送按钮 -->
                <button
                  class="send-btn-large"
                  @tap="handleSendMessage"
                  :disabled="!canSend && !isGenerating"
                  :class="{ 'is-generating': isGenerating }"
                >
                  <view class="arrow-container-large" v-if="!isGenerating">
                    <view class="arrow-icon-large"></view>
                  </view>
                  <view class="stop-icon" v-else @tap.stop="stopGenerating">✕</view>
                </button>
              </view>
            </template>
          </StableTextArea>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import StableTextArea from '../../textarea/StableTextArea.vue';

export default {
  name: 'InputArea',
  components: {
    StableTextArea
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    isCollapsed: {
      type: Boolean,
      default: true
    },
    placeholderTexts: {
      type: Array,
      default: () => []
    },
    currentPlaceholder: {
      type: String,
      default: '请输入内容...'
    },
    isGenerating: {
      type: Boolean,
      default: false
    },
    coinCost: {
      type: Number,
      default: 10
    },
    showFeatureMenu: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputText: this.value,
      isAnimatingCoin: false
    };
  },
  computed: {
    canSend() {
      return this.inputText.trim().length > 0;
    }
  },
  watch: {
    value(newVal) {
      this.inputText = newVal;
    },
    inputText(newVal) {
      this.$emit('update:value', newVal);
    }
  },
  methods: {
    toggleExpand() {
      this.$emit('toggle-expand');
    },
    
    handleInputFocus(e) {
      this.$emit('focus', e);
    },
    
    handleInputBlur(e) {
      this.$emit('blur', e);
    },
    
    handleTextareaHeightChange(height) {
      this.$emit('height-change', height);
    },
    
    handlePaste(e) {
      this.$emit('paste', e);
    },
    
    handleSendMessage() {
      if (!this.canSend && !this.isGenerating) return;
      
      if (this.isGenerating) {
        this.stopGenerating();
      } else {
        // 显示金币动画
        this.isAnimatingCoin = true;
        setTimeout(() => {
          this.isAnimatingCoin = false;
        }, 300);
        
        this.$emit('send');
      }
    },
    
    toggleFeatureMenu() {
      this.$emit('toggle-feature-menu');
    },
    
    stopGenerating() {
      this.$emit('stop-generating');
    },
    
    focus() {
      if (this.$refs.messageInput) {
        this.$refs.messageInput.focus();
      }
    },
    
    blur() {
      if (this.$refs.messageInput) {
        this.$refs.messageInput.blur();
      }
    },
    
    clear() {
      this.inputText = '';
    }
  }
}
</script>

<style scoped>
.input-area {
  width: 100%;
  padding: 10rpx;
  box-sizing: border-box;
}

.toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  font-size: 24rpx;
  color: rgba(200, 200, 210, 0.7);
}

.toggle-text {
  margin-right: 4rpx;
}

.toggle-icon {
  transition: transform 0.3s ease;
}

.input-container {
  width: 100%;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.textarea-container {
  position: relative;
  width: 100%;
}

.send-button-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: 5px;
  right: 5px;
}

.feature-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(80, 80, 100, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

/* 水平排列的功能按钮 */
.feature-btn-horizontal {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(80, 80, 100, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  flex-shrink: 0;
  border: none;
}

.plus-icon {
  position: relative;
  width: 24rpx;
  height: 24rpx;
}

.plus-icon::before,
.plus-icon::after {
  content: '';
  position: absolute;
  background-color: rgba(220, 220, 230, 0.9);
}

.plus-icon::before {
  width: 24rpx;
  height: 2rpx;
  top: 11rpx;
  left: 0;
}

.plus-icon::after {
  width: 2rpx;
  height: 24rpx;
  left: 11rpx;
  top: 0;
}

.feature-btn.active {
  background-color: rgba(110, 86, 207, 0.8);
  transform: rotate(45deg);
}

.send-btn-wrapper {
  position: relative;
}

.coin-number-top {
  position: absolute;
  top: -24rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20rpx;
  color: rgba(255, 80, 80, 0.9);
  transition: all 0.2s ease;
}

.coin-animate {
  transform: translate(-50%, -5rpx);
  opacity: 0.7;
}

.send-btn-large {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(110, 86, 207, 0.9), rgba(130, 90, 230, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(110, 86, 207, 0.4);
  transition: all 0.2s ease;
}

.send-btn-large:active {
  transform: scale(0.92);
  background: linear-gradient(135deg, rgba(100, 76, 197, 0.9), rgba(120, 80, 220, 0.9));
}

.send-btn-large[disabled] {
  background: rgba(80, 80, 100, 0.4);
  box-shadow: none;
}

.arrow-container-large {
  width: 40%;
  height: 40%;
  position: relative;
}

.arrow-icon-large {
  border-style: solid;
  border-width: 0 2rpx 2rpx 0;
  border-color: rgba(240, 240, 245, 0.9);
  display: inline-block;
  padding: 6rpx;
  transform: rotate(-45deg);
  position: absolute;
  top: 50%;
  left: 40%;
  margin-top: -6rpx;
  margin-left: -6rpx;
}

.send-btn-large.is-generating {
  background: rgba(255, 100, 100, 0.8);
}

.stop-icon {
  font-size: 24rpx;
  color: #fff;
}
</style> 