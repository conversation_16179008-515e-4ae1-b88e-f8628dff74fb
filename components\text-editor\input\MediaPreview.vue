<template>
  <view class="media-preview-container" :class="{'preview-with-panel': !isCollapsed}">
    <view class="media-items-container">
      <view
        class="media-item-wrapper"
        v-for="(item, index) in mediaItems"
        :key="index"
      >
        <view class="media-item-card">
          <!-- 序号标签 - 放在框的左上角边缘，一半在框内，一半在框外 -->
          <view class="media-item-index">{{item.index}}</view>

          <!-- 删除按钮 - 放在框的右上角边缘，一半在框内，一半在框外 -->
          <view
            class="media-item-delete-btn"
            @tap.stop="removeMediaItem(index)"
          >
            <text class="delete-icon">×</text>
          </view>

          <!-- 图片预览 -->
          <image
            v-if="item.type === 'image'"
            class="media-preview-image"
            :src="item.src"
            mode="aspectFit"
            @tap="previewImage(item.src)"
          />

          <!-- 文档预览 -->
          <view v-if="item.type === 'document'" class="media-preview-doc" @tap="openDocument(item)">
            <text class="doc-preview-icon">📄</text>
            <text class="doc-preview-name">{{item.name}}</text>
          </view>
        </view>

        <!-- 插入引用按钮 - 紧贴卡片框底部 -->
        <view class="media-item-controls">
          <view class="media-item-insert-btn" @tap.stop="insertImageReference(item)">
            <text class="insert-icon">↩</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MediaPreview',
  props: {
    mediaItems: {
      type: Array,
      default: () => []
    },
    isCollapsed: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    removeMediaItem(index) {
      this.$emit('remove-media', index);
    },

    insertImageReference(item) {
      this.$emit('insert-reference', item);
    },

    previewImage(src) {
      this.$emit('preview-image', src);
    },

    openDocument(media) {
      this.$emit('open-document', media);
    }
  }
}
</script>

<style scoped>
/* 媒体预览区样式 */
.media-preview-container {
  width: 100%;
  background-color: rgba(30, 30, 50, 0.4);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2px; /* 保持内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  height: auto; /* 自适应高度 */
  min-height: 24px; /* 最小高度 */
  overflow-y: auto;
  z-index: 95;
  transition: none; /* 移除过渡效果 */
}

.media-items-container {
  display: flex !important;
  flex-wrap: wrap !important; /* 允许项目换行 */
  gap: 2px !important; /* 减小间隙 */
  padding: 2px !important;
  max-height: 160px !important; /* 限制最大高度 */
  overflow-y: auto !important; /* 添加垂直滚动 */
  align-content: flex-start !important; /* 从顶部开始排列 */
}

.media-item-wrapper {
  display: flex !important;
  flex-direction: column !important;
  margin: 8px !important; /* 减小外边距 */
  width: 70px !important; /* 减小宽度 */
}

.media-item-card {
  position: relative !important;
  width: 70px !important; /* 减小宽度 */
  height: 70px !important; /* 减小高度 */
  border-radius: 6px !important; /* 减小圆角 */
  overflow: visible !important;
  background-color: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.media-preview-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: contain;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px; /* 添加内边距，让图片不要太靠边 */
}

.media-item-index {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 20px !important; /* 减小大小 */
  height: 20px !important; /* 减小大小 */
  background-color: #3366FF !important;
  color: #fff !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 11px !important; /* 减小字体 */
  font-weight: bold !important;
  line-height: 1 !important;
  z-index: 10 !important; /* 提高z-index确保显示在最上层 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  transform: translate(-50%, -50%) !important;
}

.media-item-delete-btn {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  width: 24px !important; /* 增大宽度 */
  height: 24px !important; /* 增大高度 */
  background-color: #FF3333 !important;
  color: #fff !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important; /* 增大字体 */
  font-weight: bold !important;
  line-height: 1 !important;
  z-index: 20 !important; /* 进一步提高z-index */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  transform: translate(50%, -50%) !important;
  cursor: pointer !important; /* 添加指针样式 */
  -webkit-tap-highlight-color: transparent !important; /* 移除点击高亮 */
  touch-action: manipulation !important; /* 优化触摸操作 */
}

.delete-icon {
  font-size: 16px !important; /* 增大删除图标 */
  font-weight: bold !important;
  color: #ffffff !important;
  pointer-events: none !important; /* 确保点击穿透到父元素 */
}

.media-item-controls {
  display: flex !important;
  justify-content: center !important;
  margin-top: 0 !important;
  width: 100% !important;
  height: 24px !important; /* 固定高度 */
}

.media-item-insert-btn {
  background-color: rgba(51, 102, 255, 0.2) !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
}

.insert-icon {
  font-size: 26px !important; /* 稍微减小字体大小 */
  color: #3366FF !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* 调整文档图标尺寸 */
.media-preview-doc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.doc-preview-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.doc-preview-name {
  font-size: 10px;
  color: #ffffff;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.preview-with-panel {
  margin-bottom: 320px; /* 参数面板展开时的底部间距 */
}
</style>
