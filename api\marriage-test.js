/**
 * 婚姻测试API接口
 */

import { request } from './request.js'

/**
 * 婚姻配对分析API
 */
export const marriageTestAPI = {

  /**
   * 进行婚姻配对分析
   * @param {Object} data - 分析数据
   * @param {Object} data.femaleInfo - 女方信息 {name, birthDate, birthTime}
   * @param {Object} data.maleInfo - 男方信息 {name, birthDate, birthTime}
   */
  async analyze(data) {
    try {
      // 直接传递参数到后端，让后端构建提示词
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/marriage-comprehensive-analysis',
        method: 'POST',
        data: {
          // 女方信息
          femaleName: data.femaleInfo.name,
          femaleBirthDate: data.femaleInfo.birthDate,
          femaleBirthTime: data.femaleInfo.birthTime || '12:00',

          // 男方信息
          maleName: data.maleInfo.name,
          maleBirthDate: data.maleInfo.birthDate,
          maleBirthTime: data.maleInfo.birthTime || '12:00',

          // 分析类型
          analysisType: 'comprehensive',
          useBazi: true
        }
      })

      return response
    } catch (error) {
      console.error('婚姻配对分析失败:', error)
      // 返回默认数据
      return this.getDefaultAnalysisData(data.femaleInfo.name, data.maleInfo.name, data.femaleInfo.birthDate, data.maleInfo.birthDate)
    }
  },

  /**
   * 简化的姓名配对分析（用于结果页面）
   * @param {string} maleName - 男方姓名
   * @param {string} femaleName - 女方姓名
   * @param {string} maleBirthDate - 男方生日
   * @param {string} femaleBirthDate - 女方生日
   * @param {string} maleBirthTime - 男方出生时间
   * @param {string} femaleBirthTime - 女方出生时间
   */
  async simpleAnalyze(maleName, femaleName, maleBirthDate, femaleBirthDate, maleBirthTime, femaleBirthTime) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/marriage-analysis',
        method: 'POST',
        data: {
          maleName: maleName,
          femaleName: femaleName,
          maleBirthDate: maleBirthDate,
          femaleBirthDate: femaleBirthDate,
          maleBirthTime: maleBirthTime,
          femaleBirthTime: femaleBirthTime,
          useBazi: true // 标记使用八字功能
        }
      })

      return response
    } catch (error) {
      console.error('婚姻配对分析失败:', error)
      throw error
    }
  },

  /**
   * 仅姓名配对分析（不包含生辰八字）
   * @param {string} maleName - 男方姓名
   * @param {string} femaleName - 女方姓名
   */
  async nameOnlyAnalyze(maleName, femaleName) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/marriage-analysis',
        method: 'POST',
        data: {
          maleName: maleName,
          femaleName: femaleName,
          useBazi: false // 标记不使用八字功能
        }
      })

      return response
    } catch (error) {
      console.error('简化姓名配对分析失败:', error)
      // 返回默认数据
      return this.getDefaultAnalysisData(maleName, femaleName, maleBirthDate, femaleBirthDate)
    }
  },

  /**
   * 个人姻缘分析
   * @param {Object} data - 个人信息
   * @param {string} data.personalName - 个人姓名
   * @param {string} data.personalGender - 个人性别 ('male' | 'female')
   * @param {boolean} data.useBazi - 是否使用生辰八字
   * @param {string} data.personalBirthDate - 出生日期 (可选)
   * @param {string} data.personalBirthTime - 出生时间 (可选)
   */
  async personalMarriageAnalyze(data) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/personal-marriage-analysis',
        method: 'POST',
        data: {
          personalName: data.personalName,
          personalGender: data.personalGender,
          useBazi: data.useBazi ? 'true' : 'false',
          personalBirthDate: data.personalBirthDate || null,
          personalBirthTime: data.personalBirthTime || null
        }
      })

      return response
    } catch (error) {
      console.error('个人姻缘分析失败:', error)
      // 返回默认数据
      return this.getDefaultPersonalAnalysisData(data.personalName, data.personalGender)
    }
  },

  /**
   * 获取默认分析数据
   */
  getDefaultAnalysisData(maleName, femaleName, maleBirthDate, femaleBirthDate) {
    const score = Math.floor(Math.random() * 20) + 75; // 75-95分随机

    // 获取星座信息
    const maleZodiac = this.getZodiacSign(maleBirthDate);
    const femaleZodiac = this.getZodiacSign(femaleBirthDate);

    return {
      success: true,
      data: {
        compatibilityScore: score.toString(),
        compatibility: [
          `你们的姓名五行配对指数为${score}分，属于高度匹配。`,
          `${maleName}(${maleZodiac})与${femaleName}(${femaleZodiac})的星座配对非常和谐。`,
          '从生辰八字分析，你们的五行相配，天作之合。',
          '在感情发展方面，你们有着天然的默契和理解。'
        ],
        personality: [
          `${maleName}性格温和稳重，具有很强的责任感和包容心。`,
          `${femaleName}性格活泼开朗，善于沟通，具有很强的亲和力。`,
          '你们的性格互补性很强，能够在生活中相互支持。',
          '追求对方成功率很高，因为你们的性格特质相互吸引。'
        ],
        marriageChanges: [
          '恋爱时期，你们会表现出最真实的自己，充满激情和浪漫。',
          '对方会感受到被深深爱着的幸福感，这种感觉会让关系更加稳固。',
          '结婚后，你们会成为相互扶持的伴侣，共同面对生活的挑战。',
          '婚后你们会变得更加成熟稳重，学会在平淡中寻找幸福。'
        ],
        loveAdvice: [
          '你们的恋爱婚姻稳定性很高，基础牢固，发展前景良好。',
          '建议多进行深度沟通，分享彼此的内心想法和感受。',
          '婚前要学会包容理解，婚后要保持新鲜感和仪式感。',
          '遇到分歧时，要以理解和妥协为主，避免激烈争吵。'
        ],
        relationshipEnhancement: [
          '定期安排约会时间，保持恋爱时的浪漫和激情。',
          '学会表达爱意，通过小惊喜和贴心举动提升感情温度。',
          '建立共同的兴趣爱好，增加相处时间和话题。',
          '保持良好的沟通习惯，及时化解矛盾和误解。'
        ]
      }
    }
  },

  /**
   * 获取星座
   */
  getZodiacSign(birthDate) {
    if (!birthDate) return '未知';

    const date = new Date(birthDate);
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const zodiacSigns = [
      { name: '摩羯座', start: [12, 22], end: [1, 19] },
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] }
    ];

    for (let sign of zodiacSigns) {
      if (this.isDateInRange(month, day, sign.start, sign.end)) {
        return sign.name;
      }
    }

    return '未知';
  },

  /**
   * 判断日期是否在范围内
   */
  isDateInRange(month, day, start, end) {
    if (start[0] === end[0]) {
      // 同一个月
      return month === start[0] && day >= start[1] && day <= end[1];
    } else {
      // 跨月
      return (month === start[0] && day >= start[1]) ||
             (month === end[0] && day <= end[1]);
    }
  },

  /**
   * 获取分析历史记录
   * @param {string} userId - 用户ID
   */
  async getHistory(userId) {
    try {
      const response = await request({
        url: '/api/marriage-test/history',
        method: 'GET',
        data: { userId }
      })
      
      return response
    } catch (error) {
      console.error('获取历史记录失败:', error)
      throw error
    }
  },

  /**
   * 保存分析结果
   * @param {Object} data - 分析结果数据
   */
  async saveResult(data) {
    try {
      const response = await request({
        url: '/api/marriage-test/save',
        method: 'POST',
        data: {
          ...data,
          createdAt: new Date().toISOString()
        }
      })
      
      return response
    } catch (error) {
      console.error('保存分析结果失败:', error)
      throw error
    }
  },

  /**
   * 获取配对统计数据
   */
  async getStatistics() {
    try {
      const response = await request({
        url: '/api/marriage-test/statistics',
        method: 'GET'
      })

      return response
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  },

  /**
   * 个人姻缘分析（仅姓名）
   * @param {string} personalName - 个人姓名
   * @param {string} personalGender - 个人性别 ('male' 或 'female')
   */
  async personalAnalyze(personalName, personalGender) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/personal-marriage-analysis',
        method: 'POST',
        data: {
          personalName: personalName,
          personalGender: personalGender,
          useBazi: false
        }
      });

      return response;
    } catch (error) {
      console.error('个人姻缘分析失败:', error);
      return this.getDefaultPersonalAnalysisData(personalName, personalGender);
    }
  },

  /**
   * 个人姻缘分析（姓名+生辰八字）
   * @param {string} personalName - 个人姓名
   * @param {string} personalGender - 个人性别 ('male' 或 'female')
   * @param {string} personalBirthDate - 出生日期
   * @param {string} personalBirthTime - 出生时间
   */
  async personalAnalyzeWithBazi(personalName, personalGender, personalBirthDate, personalBirthTime) {
    try {
      const response = await request({
        url: 'http://aaa.fanshengyun.com/api/personal-marriage-analysis',
        method: 'POST',
        data: {
          personalName: personalName,
          personalGender: personalGender,
          useBazi: true,
          personalBirthDate: personalBirthDate,
          personalBirthTime: personalBirthTime
        }
      });

      return response;
    } catch (error) {
      console.error('个人姻缘分析（含八字）失败:', error);
      return this.getDefaultPersonalAnalysisData(personalName, personalGender);
    }
  },

  /**
   * 获取默认个人姻缘分析数据
   * @param {string} personalName - 个人姓名
   * @param {string} personalGender - 个人性别
   */
  getDefaultPersonalAnalysisData(personalName, personalGender) {
    const score = Math.floor(Math.random() * 25) + 70; // 70-95分随机
    const genderText = personalGender === 'male' ? '他' : '她';
    const oppositeGender = personalGender === 'male' ? '女性' : '男性';

    return {
      success: true,
      data: {
        personalFortuneScore: score.toString(),
        personalMarriage: [
          `${personalName}的婚姻运势指数为${score}分，整体运势良好。`,
          `从姓名学角度分析，${genderText}具有很强的婚姻缘分，容易遇到合适的伴侣。`,
          `${genderText}的性格特质有利于建立稳定的婚姻关系，能够给伴侣带来安全感。`,
          `在婚姻中，${personalName}会是一个负责任、有担当的伴侣。`
        ],
        partnerTraits: [
          `${personalName}的理想伴侣应该是性格温和、善解人意的${oppositeGender}。`,
          `对方可能具有较强的沟通能力和包容心，能够理解${genderText}的想法。`,
          `理想伴侣在外貌上可能偏向清秀型，气质优雅，有一定的文化修养。`,
          `你们很可能通过朋友介绍或工作场合相识，缘分来得自然而然。`
        ],
        marriageCount: [
          `从姓名学分析，${personalName}的婚姻运势稳定，大概率会有一段长久的婚姻。`,
          `${genderText}对感情比较专一，不轻易开始也不轻易结束一段关系。`,
          `如果能够在合适的时机遇到对的人，婚姻会非常幸福美满。`,
          `建议在25-30岁之间认真考虑婚姻大事，这个时期的感情运势最佳。`
        ],
        relationshipManagement: [
          `在夫妻关系中，${personalName}应该多表达自己的想法和感受，增进彼此了解。`,
          `保持适度的独立空间，既要亲密无间，也要给对方一定的自由度。`,
          `定期安排浪漫的约会时光，保持婚姻的新鲜感和激情。`,
          `遇到矛盾时要冷静沟通，以理解和包容为主，避免情绪化的争吵。`
        ]
      }
    };
  }
}



/**
 * 姓名学分析工具函数
 */
export const nameAnalysisUtils = {
  
  /**
   * 计算姓名笔画数
   * @param {string} name - 姓名
   */
  calculateStrokes(name) {
    // 简化的笔画计算，实际应用中需要更完整的字典
    const strokeMap = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
      '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
      // 这里应该包含更多汉字的笔画数据
    }
    
    let totalStrokes = 0
    for (let char of name) {
      totalStrokes += strokeMap[char] || Math.floor(Math.random() * 20) + 1
    }
    
    return totalStrokes
  },

  /**
   * 分析姓名五行属性
   * @param {string} name - 姓名
   */
  analyzeFiveElements(name) {
    const elements = ['金', '木', '水', '火', '土']
    // 简化的五行分析，实际应用中需要更复杂的算法
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    return elements[hash % elements.length]
  },

  /**
   * 计算姓名配对指数
   * @param {string} name1 - 第一个姓名
   * @param {string} name2 - 第二个姓名
   */
  calculateCompatibility(name1, name2) {
    const strokes1 = this.calculateStrokes(name1)
    const strokes2 = this.calculateStrokes(name2)
    const element1 = this.analyzeFiveElements(name1)
    const element2 = this.analyzeFiveElements(name2)
    
    // 简化的配对算法
    let score = 50 // 基础分
    
    // 笔画数配对
    const strokeDiff = Math.abs(strokes1 - strokes2)
    if (strokeDiff <= 3) score += 20
    else if (strokeDiff <= 6) score += 10
    
    // 五行配对
    const elementCompatibility = {
      '金': ['土', '金'],
      '木': ['水', '木'],
      '水': ['金', '水'],
      '火': ['木', '火'],
      '土': ['火', '土']
    }
    
    if (elementCompatibility[element1]?.includes(element2)) {
      score += 20
    }
    
    // 姓名长度配对
    if (name1.length === name2.length) {
      score += 10
    }
    
    return Math.min(100, Math.max(60, score))
  }
}

/**
 * 生辰八字分析工具函数
 */
export const birthDateAnalysisUtils = {
  
  /**
   * 分析生辰八字配对
   * @param {string} birthDate1 - 第一个生日
   * @param {string} birthDate2 - 第二个生日
   */
  analyzeBirthDateCompatibility(birthDate1, birthDate2) {
    const date1 = new Date(birthDate1)
    const date2 = new Date(birthDate2)
    
    let score = 50 // 基础分
    
    // 年份配对
    const yearDiff = Math.abs(date1.getFullYear() - date2.getFullYear())
    if (yearDiff <= 2) score += 15
    else if (yearDiff <= 5) score += 10
    else if (yearDiff <= 10) score += 5
    
    // 月份配对
    const monthDiff = Math.abs(date1.getMonth() - date2.getMonth())
    if (monthDiff <= 1 || monthDiff >= 11) score += 15
    else if (monthDiff <= 3 || monthDiff >= 9) score += 10
    
    // 星座配对（简化）
    const zodiacScore = this.calculateZodiacCompatibility(date1, date2)
    score += zodiacScore
    
    return Math.min(100, Math.max(60, score))
  },

  /**
   * 计算星座配对指数
   * @param {Date} date1 - 第一个日期
   * @param {Date} date2 - 第二个日期
   */
  calculateZodiacCompatibility(date1, date2) {
    // 简化的星座配对算法
    const zodiac1 = this.getZodiacSign(date1)
    const zodiac2 = this.getZodiacSign(date2)
    
    // 星座配对表（简化）
    const compatibilityMap = {
      '白羊座': ['狮子座', '射手座', '双子座'],
      '金牛座': ['处女座', '摩羯座', '巨蟹座'],
      '双子座': ['天秤座', '水瓶座', '白羊座'],
      // ... 其他星座配对
    }
    
    if (compatibilityMap[zodiac1]?.includes(zodiac2)) {
      return 20
    } else if (zodiac1 === zodiac2) {
      return 15
    } else {
      return 5
    }
  },

  /**
   * 获取星座
   * @param {Date} date - 日期
   */
  getZodiacSign(date) {
    const month = date.getMonth() + 1
    const day = date.getDate()
    
    const zodiacSigns = [
      { name: '摩羯座', start: [12, 22], end: [1, 19] },
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] }
    ]
    
    for (let sign of zodiacSigns) {
      if (this.isDateInRange(month, day, sign.start, sign.end)) {
        return sign.name
      }
    }
    
    return '未知'
  },

  /**
   * 判断日期是否在范围内
   */
  isDateInRange(month, day, start, end) {
    if (start[0] === end[0]) {
      // 同一个月
      return month === start[0] && day >= start[1] && day <= end[1]
    } else {
      // 跨月
      return (month === start[0] && day >= start[1]) ||
             (month === end[0] && day <= end[1])
    }
  },


