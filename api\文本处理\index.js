/**
 * 文本处理功能统一入口文件
 * 整合文本处理功能的所有接口和业务逻辑
 * 创建时间：2025-01-11
 */

import { 执行文本处理工作流, 查询文本处理状态, 取消文本处理工作流 } from './工作流执行接口.js';
import { 文本处理工作流配置, 文本处理参数验证规则, 文本处理错误码, 文本处理状态 } from './工作流配置.js';

// 文本处理功能统一API
export const 文本处理功能API = {
    执行文本处理工作流,
    查询文本处理状态,
    取消文本处理工作流,
    
    配置: 文本处理工作流配置,
    验证规则: 文本处理参数验证规则,
    错误码: 文本处理错误码,
    状态码: 文本处理状态
};

// 快捷方法
export async function 文本处理(formData, options = {}) {
    return await 执行文本处理工作流(formData, options);
}

export default 文本处理功能API;