<template>
  <view class="text-to-video-form">
    <view class="form-section">
      <text class="section-title">提示词</text>
      <view class="prompt-wrapper">
        <textarea
          class="prompt-input"
          v-model="prompt"
          placeholder="请描述您想要生成的视频内容，例如：一只金色的猫咪在海边奔跑，阳光照耀着海面"
          :maxlength="1000"
          @input="handlePromptChange"
        ></textarea>
        <view class="text-counter">{{ prompt.length }}/1000</view>
        
        <!-- AI智能提示 -->
        <view class="ai-suggestions" v-if="showSuggestions && suggestions.length > 0">
          <view class="suggestion-header">
            <text>智能提示</text>
            <text class="close-suggestions" @tap="closeSuggestions">×</text>
          </view>
          <scroll-view scroll-y class="suggestions-list">
            <view 
              v-for="(item, index) in suggestions" 
              :key="index" 
              class="suggestion-item"
              @tap="applySuggestion(item)"
            >
              {{ item }}
            </view>
          </scroll-view>
        </view>
      </view>
      

    </view>

    <view class="form-section">
      <view class="section-header">
        <text class="section-title">反向提示词</text>
        <text class="section-subtitle">描述您不希望在视频中出现的内容</text>
      </view>
      <textarea
        class="negative-prompt-input"
        v-model="negativePrompt"
        placeholder="例如：模糊、低质量、扭曲的脸、不自然的姿势"
        :maxlength="200"
      ></textarea>
      <view class="text-counter">{{ negativePrompt.length }}/200</view>
    </view>

    <view class="form-section">
      <view class="section-header">
        <text class="section-title">视频风格提示词</text>
        <text class="section-subtitle">描述您希望视频呈现的风格特征</text>
      </view>
      <textarea
        class="style-prompt-input"
        v-model="stylePrompt"
        placeholder="例如：写实风格、卡通风格、3D渲染、动漫风格、水墨画风格、油画风格、素描风格、科幻风格等"
        :maxlength="200"
      ></textarea>
      <view class="text-counter">{{ stylePrompt.length }}/200</view>
    </view>

    <view class="form-section">
      <text class="section-title">视频参数</text>
      <view class="parameters-grid">
        <!-- 视频时长 -->
        <view class="parameter-item">
          <text class="parameter-label">时长</text>
          <view class="duration-options">
            <view
              class="duration-btn"
              :class="{ active: duration === 5 }"
              @tap="duration = 5"
            >5秒</view>
            <view
              class="duration-btn"
              :class="{ active: duration === 10 }"
              @tap="duration = 10"
            >10秒</view>
          </view>
        </view>

        <!-- 视频质量 -->
        <view class="parameter-item full-width">
          <text class="parameter-label">质量</text>
          <view class="quality-options">
            <view
              class="quality-btn"
              :class="{ active: quality === '480p' }"
              @tap="quality = '480p'"
            >480p</view>
            <view
              class="quality-btn"
              :class="{ active: quality === '720p' }"
              @tap="quality = '720p'"
            >720p</view>
            <view
              class="quality-btn"
              :class="{ active: quality === '1080p' }"
              @tap="quality = '1080p'"
            >1080p</view>
          </view>
        </view>
        
        <!-- 视频比例 -->
        <view class="parameter-item full-width">
          <text class="parameter-label">比例</text>
          <view class="ratio-options">
            <view
              v-for="(ratio, index) in videoRatios"
              :key="index"
              class="ratio-btn"
              :class="{ active: selectedRatio === ratio.value }"
              @tap="selectRatio(ratio.value)"
            >
              <view class="ratio-visual" :class="'ratio-' + ratio.value.replace(':', '-')"></view>
              <text>{{ ratio.label }}</text>
            </view>
          </view>
        </view>
        

      </view>
    </view>
    

  </view>
</template>

<script>
export default {
  name: 'TextToVideoForm',
  data() {
    return {
      prompt: '',
      negativePrompt: '',
      stylePrompt: '',
      duration: 5,
      quality: '720p',
      selectedRatio: '16:9',
      suggestions: [],
      showSuggestions: false,
      estimatedCost: 200,

      
      videoRatios: [
        { label: '横屏16:9', value: '16:9', icon: '/static/icons/ratio_16_9.png' },
        { label: '竖屏9:16', value: '9:16', icon: '/static/icons/ratio_9_16.png' },
        { label: '正方形1:1', value: '1:1', icon: '/static/icons/ratio_1_1.png' },
        { label: '传统4:3', value: '4:3', icon: '/static/icons/ratio_4_3.png' }
      ]
    }
  },
  watch: {
    duration(val) {
      this.updateEstimatedCost();
    },
    quality(val) {
      this.updateEstimatedCost();
    }
  },
  methods: {
    handlePromptChange(e) {
      // 实时智能提示
      if (this.prompt && this.prompt.length > 5) {
        // 模拟获取智能提示，实际项目中应通过API调用获取
        setTimeout(() => {
          this.generateSuggestions();
        }, 500);
      } else {
        this.showSuggestions = false;
        this.suggestions = [];
      }
    },
    
    generateSuggestions() {
      // 这里应该是API调用，现在用模拟数据
      const currentPrompt = this.prompt.toLowerCase();
      
      // 根据输入的不同关键词生成不同的建议
      if (currentPrompt.includes('猫')) {
        this.suggestions = [
          '一只优雅的橘猫在阳光下漫步，背景是模糊的花园',
          '可爱的猫咪在玩毛线球，周围散落着五彩的纱线',
          '猫咪在窗台上打盹，阳光透过窗帘在它身上形成条纹'
        ];
      } else if (currentPrompt.includes('城市')) {
        this.suggestions = [
          '繁华的现代城市夜景，高楼大厦霓虹灯闪烁',
          '雨后的城市街道，湿润的路面反射着霓虹灯光',
          '鸟瞰大都市的日落景色，城市轮廓与天空的金色交融'
        ];
      } else if (currentPrompt.includes('海')) {
        this.suggestions = [
          '平静的海面上日落的景色，金色的阳光洒在波浪上',
          '壮观的海浪拍打在岩石上，水花四溅形成壮丽景象',
          '蓝绿色透明海水中的珊瑚礁和五彩斑斓的热带鱼'
        ];
      } else {
        // 默认建议
        this.suggestions = [
          '为您的提示词增加更多细节描述',
          '考虑添加场景的光线、颜色或氛围',
          '描述主体的动作或情绪会让视频更生动'
        ];
      }
      
      this.showSuggestions = true;
    },
    
    closeSuggestions() {
      this.showSuggestions = false;
    },
    
    applySuggestion(suggestion) {
      this.prompt = suggestion;
      this.showSuggestions = false;
    },
    

    
    selectRatio(ratio) {
      this.selectedRatio = ratio;
    },
    

    

    
    updateEstimatedCost() {
      // 根据视频参数计算预估成本
      let baseCost = 200; // 基础成本

      // 根据时长增加成本
      if (this.duration === 10) {
        baseCost += 100; // 10秒比5秒多100金币
      }

      // 根据质量增加成本
      if (this.quality === '720p') {
        baseCost *= 1.5;
      } else if (this.quality === '1080p') {
        baseCost *= 2;
      }

      this.estimatedCost = Math.round(baseCost);
    },
    
    saveDraft() {
      const draft = {
        type: 'text-to-video',
        prompt: this.prompt,
        negativePrompt: this.negativePrompt,
        stylePrompt: this.stylePrompt,
        duration: this.duration,
        quality: this.quality,
        ratio: this.selectedRatio,
        timestamp: new Date().getTime()
      };

      // 保存草稿，实际项目中应保存到本地或服务器
      console.log('保存草稿:', draft);
      uni.showToast({
        title: '草稿已保存',
        icon: 'success'
      });
    },

    getDraftData() {
      // 返回当前表单的草稿数据
      return {
        type: 'text-to-video',
        prompt: this.prompt,
        negativePrompt: this.negativePrompt,
        stylePrompt: this.stylePrompt,
        duration: this.duration,
        quality: this.quality,
        ratio: this.selectedRatio,
        timestamp: new Date().getTime()
      };
    },
    
    generateVideo() {
      if (!this.prompt) {
        uni.showToast({
          title: '请输入提示词',
          icon: 'none'
        });
        return;
      }
      
      const params = {
        prompt: this.prompt,
        negativePrompt: this.negativePrompt,
        stylePrompt: this.stylePrompt,
        duration: this.duration,
        quality: this.quality,
        ratio: this.selectedRatio,
        cost: this.estimatedCost
      };
      
      this.$emit('generate', params);
    },
    
    // 从历史记录恢复提示词
    setPrompt(prompt) {
      this.prompt = prompt;
      this.handlePromptChange();
    }
  }
}
</script>

<style lang="scss" scoped>
.text-to-video-form {
  padding-bottom: 30rpx;
  
  .form-section {
    margin-bottom: 30rpx;
    background-color: rgba(15, 52, 96, 0.5);
    border-radius: 16rpx;
    padding: 20rpx;
    border: 1px solid rgba(74, 105, 189, 0.3);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 15rpx;
      display: block;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 8rpx;
        height: 30rpx;
        width: 6rpx;
        background: linear-gradient(to bottom, #4a69bd, #0abde3);
        border-radius: 3rpx;
      }
    }
    
    .section-header {
      margin-bottom: 15rpx;
      
      .section-title {
        margin-bottom: 5rpx;
      }
      
      .section-subtitle {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
    
    .prompt-wrapper {
      position: relative;
      
      .prompt-input {
        width: 100%;
        height: 200rpx;
        background-color: #0f3460;
        border-radius: 12rpx;
        padding: 20rpx;
        font-size: 28rpx;
        color: #ffffff;
      }
      
      .text-counter {
        position: absolute;
        right: 20rpx;
        bottom: 10rpx;
        font-size: 24rpx;
        color: #ffffff;
      }
      
      .ai-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #16213e;
        border-radius: 0 0 12rpx 12rpx;
        box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.3);
        z-index: 10;
        max-height: 300rpx;
        
        .suggestion-header {
          display: flex;
          justify-content: space-between;
          padding: 15rpx 20rpx;
          border-bottom: 1px solid #2c2c44;
          
          text {
            font-size: 26rpx;
            color: #ffffff;
          }
          
          .close-suggestions {
            color: #ffffff;
          }
        }
        
        .suggestions-list {
          max-height: 250rpx;
          
          .suggestion-item {
            padding: 15rpx 20rpx;
            font-size: 26rpx;
            color: #ffffff;
            line-height: 1.4;
            border-bottom: 1px solid #2c2c44;
            
            &:active {
              background-color: #0f3460;
            }
          }
        }
      }
    }
    

    
    .negative-prompt-input, .style-prompt-input {
      width: 100%;
      height: 120rpx;
      background-color: #0f3460;
      border-radius: 12rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #ffffff;
    }
    
    .text-counter {
      text-align: right;
      font-size: 24rpx;
      color: #ffffff;
      margin-top: 10rpx;
    }
    

    

    .parameters-grid {
      display: flex;
      flex-wrap: wrap;
      
      .parameter-item {
        width: 50%;
        padding: 8rpx 15rpx 8rpx 0;

        &.full-width {
          width: 100%;
          padding-right: 0;
        }

        .parameter-label {
          font-size: 28rpx;
          color: #ffffff;
          margin-bottom: 15rpx;
          display: block;
          font-weight: 500;
        }
        
        .duration-options {
          display: flex;

          .duration-btn {
            flex: 1;
            height: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            font-weight: 500;
            background-color: #0f3460;
            color: #ffffff;
            margin-right: 10rpx;
            border-radius: 8rpx;
            border: 2rpx solid #3a6299;
            transition: all 0.2s ease;
            box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);

            &:last-child {
              margin-right: 0;
            }

            &.active {
              background-color: #1e3799;
              color: #ffffff;
              border-color: #4a69bd;
              box-shadow: 0 0 10rpx rgba(74, 105, 189, 0.5);
              transform: translateY(-2rpx);
            }
          }
        }
        
        .quality-options {
          display: flex;
          
          .quality-btn {
            flex: 1;
            height: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            font-weight: 500;
            background-color: #0f3460;
            color: #ffffff;
            margin-right: 10rpx;
            border-radius: 8rpx;
            border: 2rpx solid #3a6299;
            transition: all 0.2s ease;
            box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
            
            &:last-child {
              margin-right: 0;
            }
            
            &.active {
              background-color: #1e3799;
              color: #ffffff;
              border-color: #4a69bd;
              box-shadow: 0 0 10rpx rgba(74, 105, 189, 0.5);
              transform: translateY(-2rpx);
            }
          }
        }
        
        .ratio-options {
          display: flex;
          gap: 15rpx;

          .ratio-btn {
            flex: 1;
            height: 120rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #0f3460;
            color: #ffffff;
            border-radius: 12rpx;
            border: 2rpx solid #3a6299;
            transition: all 0.2s ease;
            padding: 15rpx 10rpx;
            box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);

            &.active {
              background-color: #1e3799;
              color: #ffffff;
              border-color: #4a69bd;
              box-shadow: 0 0 15rpx rgba(74, 105, 189, 0.5);
              transform: translateY(-2rpx);
            }

            .ratio-visual {
              margin-bottom: 12rpx;
              border: 2rpx solid #ffffff;
              background-color: rgba(255, 255, 255, 0.1);

              &.ratio-16-9 {
                width: 48rpx;
                height: 27rpx;
              }

              &.ratio-9-16 {
                width: 27rpx;
                height: 48rpx;
              }

              &.ratio-1-1 {
                width: 36rpx;
                height: 36rpx;
              }

              &.ratio-4-3 {
                width: 48rpx;
                height: 36rpx;
              }
            }

            text {
              font-size: 22rpx;
              font-weight: 500;
              color: #ffffff;
              text-align: center;
              line-height: 1.2;
            }
          }
        }
        

      }
    }
  }
  

}
</style> 