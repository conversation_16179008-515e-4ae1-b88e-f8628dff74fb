<template>
  <view class="presets-container">
    <!-- 提示词分类标签 -->
    <scroll-view 
      class="category-scroll-view" 
      scroll-x 
      :show-scrollbar="false" 
      :enhanced="true" 
      :bounces="true"
    >
      <view class="category-tabs">
        <view 
          v-for="(category, index) in categories" 
          :key="index"
          class="category-tab"
          :class="{ active: activeCategory === index }"
          @tap="setActiveCategory(index)"
        >
          {{ category.name }}
        </view>
      </view>
    </scroll-view>
    
    <!-- 提示词标签区域 - 三行水平布局 -->
    <view class="tags-container">
      <!-- 常用提示词 -->
      <view v-show="activeCategory === 0 || activeCategory === -1" class="tags-section">
        <view class="tags-rows">
          <!-- 第一行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(presetPromptTags, 0)"
                :key="'prompt-row1-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
          
          <!-- 第二行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(presetPromptTags, 1)"
                :key="'prompt-row2-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 100) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
          
          <!-- 第三行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(presetPromptTags, 2)"
                :key="'prompt-row3-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 200) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 类型标签 -->
      <view v-show="activeCategory === 1 || activeCategory === -1" class="tags-section">
        <view class="tags-rows">
          <!-- 第一行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(typeTags, 0)"
                :key="'type-row1-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 300) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
          
          <!-- 第二行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(typeTags, 1)"
                :key="'type-row2-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 400) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
          
          <!-- 第三行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(typeTags, 2)"
                :key="'type-row3-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 500) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 风格标签 -->
      <view v-show="activeCategory === 2 || activeCategory === -1" class="tags-section">
        <view class="tags-rows">
          <!-- 第一行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(styleTags, 0)"
                :key="'style-row1-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 600) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
          
          <!-- 第二行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(styleTags, 1)"
                :key="'style-row2-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 700) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
          
          <!-- 第三行 -->
          <scroll-view 
            class="tags-row-scroll" 
            scroll-x 
            :show-scrollbar="isH5"
            :enhanced="true"
            :bounces="true"
          >
            <view class="tags-row">
              <view 
                class="preset-tag" 
                v-for="(tag, index) in getTagsForRow(styleTags, 2)"
                :key="'style-row3-'+index"
                @tap="selectTag(tag)"
                :style="{ backgroundColor: getRandomColor(index + 800) }"
              >
                {{ tag.title }}
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 收起提示区域 -->
      <view class="collapse-hint">
        <text>收起提示词 ▲</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PromptTags',
  props: {
    presetPromptTags: {
      type: Array,
      default: () => []
    },
    typeTags: {
      type: Array,
      default: () => []
    },
    styleTags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeCategory: -1, // -1表示显示所有分类
      isH5: false,
      isIOS: false,
      isAndroid: false,
      categories: [
        { name: '全部', id: 'all' },
        { name: '常用', id: 'common' },
        { name: '类型', id: 'type' },
        { name: '风格', id: 'style' }
      ],
      colorCache: {}, // 缓存颜色，确保同一个标签颜色一致
      colorPalette: [
        // 明亮的绿色系
        'rgba(76, 175, 80, 0.85)',  // 绿色
        'rgba(139, 195, 74, 0.85)', // 浅绿色
        'rgba(104, 159, 56, 0.85)', // 深绿色
        'rgba(85, 185, 120, 0.85)', // 青绿色
        
        // 蓝色系
        'rgba(33, 150, 243, 0.85)', // 蓝色
        'rgba(3, 169, 244, 0.85)',  // 浅蓝色
        'rgba(0, 188, 212, 0.85)',  // 青色
        'rgba(0, 150, 136, 0.85)',  // 青绿色
        
        // 紫色系
        'rgba(156, 39, 176, 0.85)', // 紫色
        'rgba(103, 58, 183, 0.85)', // 深紫色
        'rgba(170, 82, 204, 0.85)', // 亮紫色
        'rgba(126, 87, 194, 0.85)', // 淡紫色
        
        // 红色系
        'rgba(244, 67, 54, 0.85)',  // 红色
        'rgba(233, 30, 99, 0.85)',  // 粉红色
        'rgba(216, 27, 96, 0.85)',  // 深粉色
        'rgba(229, 57, 53, 0.85)',  // 亮红色
        
        // 橙黄色系
        'rgba(255, 152, 0, 0.85)',  // 橙色
        'rgba(255, 193, 7, 0.85)',  // 琥珀色
        'rgba(255, 235, 59, 0.85)', // 黄色
        'rgba(255, 87, 34, 0.85)',  // 深橙色
        
        // 其他颜色
        'rgba(121, 85, 72, 0.85)',  // 棕色
        'rgba(158, 158, 158, 0.85)', // 灰色
        'rgba(96, 125, 139, 0.85)', // 蓝灰色
        'rgba(255, 112, 67, 0.85)', // 珊瑚色
      ]
    };
  },
  created() {
    this.detectPlatform();
  },
  methods: {
    detectPlatform() {
      // 检测当前平台
      // #ifdef H5
      this.isH5 = true;
      // #endif
      
      // 检测操作系统
      const sys = uni.getSystemInfoSync();
      if (sys) {
        if (sys.platform === 'ios') {
          this.isIOS = true;
        } else if (sys.platform === 'android') {
          this.isAndroid = true;
        }
      }
    },
    
    // 获取指定行的标签
    getTagsForRow(tags, rowIndex) {
      if (!tags || tags.length === 0) return [];
      
      // 计算每行应该显示的标签数量（总数除以3）
      const tagsPerRow = Math.ceil(tags.length / 3);
      
      // 计算当前行的起始和结束索引
      const startIndex = rowIndex * tagsPerRow;
      const endIndex = Math.min(startIndex + tagsPerRow, tags.length);
      
      // 返回当前行的标签
      return tags.slice(startIndex, endIndex);
    },
    
    selectTag(tag) {
      if (!tag) return;
      
      this.$emit('select-tag', tag);
    },
    
    setActiveCategory(index) {
      this.activeCategory = index;
    },
    
    getRandomColor(index) {
      // 使用缓存确保同一个索引总是返回相同的颜色
      if (this.colorCache[index] !== undefined) {
        return this.colorCache[index];
      }
      
      // 生成随机颜色
      const colorIndex = Math.floor(Math.random() * this.colorPalette.length);
      const color = this.colorPalette[colorIndex];
      
      // 缓存颜色
      this.colorCache[index] = color;
      
      return color;
    }
  }
}
</script>

<style scoped>
.presets-container {
  margin-bottom: 20rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 分类标签样式 */
.category-scroll-view {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 16rpx;
}

.category-tabs {
  display: flex;
  padding: 0 8rpx;
}

.category-tab {
  padding: 8rpx 24rpx;
  margin: 0 8rpx;
  font-size: 24rpx;
  color: rgba(200, 200, 210, 0.8);
  background-color: rgba(60, 60, 80, 0.3);
  border-radius: 30rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.category-tab.active {
  color: #ffffff;
  background-color: rgba(110, 86, 207, 0.8);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 标签容器 */
.tags-container {
  width: 100%;
}

.tags-section {
  margin-bottom: 16rpx;
}

/* 三行布局 */
.tags-rows {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 标签行滚动区域 */
.tags-row-scroll {
  width: 100%;
  white-space: nowrap;
}

.tags-row {
  display: inline-flex;
  padding: 4rpx;
}

.preset-tag {
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #ffffff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  margin-right: 16rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.preset-tag:active {
  transform: scale(0.95);
  opacity: 0.7;
}

/* 收起提示区域 */
.collapse-hint {
  text-align: center;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: rgba(200, 200, 210, 0.7);
  background-color: rgba(40, 40, 60, 0.3);
  border-radius: 0 0 16rpx 16rpx;
  margin-top: 10rpx;
}

/* H5平台特定样式 */
/* #ifdef H5 */
.tags-row-scroll::-webkit-scrollbar {
  height: 4px;
}

.tags-row-scroll::-webkit-scrollbar-track {
  background: rgba(30, 30, 50, 0.1);
  border-radius: 2px;
}

.tags-row-scroll::-webkit-scrollbar-thumb {
  background: rgba(110, 86, 207, 0.3);
  border-radius: 2px;
}

.tags-row-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(110, 86, 207, 0.5);
}

.category-scroll-view::-webkit-scrollbar {
  display: none;
}
/* #endif */

/* 移动端平台特定样式 */
/* #ifdef APP-PLUS || MP */
.tags-row-scroll {
  -webkit-overflow-scrolling: touch;
}
/* #endif */
</style> 