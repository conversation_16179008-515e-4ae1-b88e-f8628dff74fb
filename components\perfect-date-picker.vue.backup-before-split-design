<template>
	<view class="date-picker-modal" v-if="visible">
		<view class="picker-mask" @click="close"></view>
		<view class="picker-content">
			<view class="picker-header">
				<text class="header-title">选择出生日期和时间</text>
			</view>

			<view class="picker-container">
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
					@pickend="onPickerEnd"
					@wheel.prevent="onWheel"
				>
					<!-- 年份列 -->
					<picker-view-column>
						<view
							v-for="(year, index) in years"
							:key="index"
							class="picker-item"
						>
							{{ year }}年
						</view>
					</picker-view-column>

					<!-- 月份列 -->
					<picker-view-column>
						<view
							v-for="(month, index) in months"
							:key="index"
							class="picker-item"
						>
							{{ month }}月
						</view>
					</picker-view-column>

					<!-- 日期列 -->
					<picker-view-column>
						<view
							v-for="(day, index) in days"
							:key="index"
							class="picker-item"
						>
							{{ day }}日
						</view>
					</picker-view-column>

					<!-- 小时列 -->
					<picker-view-column>
						<view
							v-for="(hour, index) in hours"
							:key="index"
							class="picker-item"
						>
							{{ String(hour).padStart(2, '0') }}时
						</view>
					</picker-view-column>

					<!-- 分钟列 -->
					<picker-view-column>
						<view
							v-for="(minute, index) in minutes"
							:key="index"
							class="picker-item"
						>
							{{ String(minute).padStart(2, '0') }}分
						</view>
					</picker-view-column>
				</picker-view>

				<!-- 选择框指示器 -->
				<view class="selection-indicator"></view>
			</view>

			<!-- 当前选择预览 -->
			<view class="selection-preview">
				<view class="preview-label">当前选择</view>
				<view class="preview-content">
					<text class="preview-text">{{ formatDateTime }}</text>
				</view>
			</view>

			<view class="picker-footer">
				<button class="btn-cancel" @click="close">取消</button>
				<button class="btn-confirm" @click="confirm">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PerfectDatePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: String,
			default: ''
		}
	},
	data() {
		const now = new Date();
		const currentYear = now.getFullYear();
		
		return {
			// 当前选中的年月日时分
			selectedYear: currentYear,
			selectedMonth: now.getMonth() + 1,
			selectedDay: now.getDate(),
			selectedHour: now.getHours(),
			selectedMinute: Math.floor(now.getMinutes() / 5) * 5, // 取最近的5分钟倍数

			// picker-view的索引值 [年, 月, 日, 时, 分]
			pickerValue: [0, 0, 0, 0, 0],

			// 年份数组（1900-2035）
			years: this.generateYears(),

			// 月份数组（1-12）
			months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],

			// 小时数组（0-23）
			hours: this.generateHours(),

			// 分钟数组（每5分钟一个选项）
			minutes: this.generateMinutes(),

			// 滚动状态
			isScrolling: false,
			scrollTimer: null,
			wheelTimer: null // 鼠标滚轮定时器
		}
	},
	computed: {
		// 动态计算当月天数
		days() {
			const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			const days = [];
			for (let i = 1; i <= daysInMonth; i++) {
				days.push(i);
			}
			return days;
		},

		// 格式化显示的日期时间
		formatDateTime() {
			const year = this.selectedYear;
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');

			return `${year}年${this.selectedMonth}月${this.selectedDay}日 ${hour}:${minute}`;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initializePicker();
			}
		},
		
		// 监听月份变化，调整日期
		selectedMonth() {
			this.adjustDayIfNeeded();
		},
		
		selectedYear() {
			this.adjustDayIfNeeded();
		}
	},
	created() {
		this.initializePicker();
	},
	methods: {
		// 生成年份数组
		generateYears() {
			const currentYear = new Date().getFullYear();
			const years = [];
			// 扩展年份范围从1800年到当前年份+10年，满足更广泛的出生年份需求
			for (let i = 1800; i <= currentYear + 10; i++) {
				years.push(i);
			}
			return years;
		},

		// 生成小时数组
		generateHours() {
			const hours = [];
			for (let i = 0; i <= 23; i++) {
				hours.push(i);
			}
			return hours;
		},

		// 生成分钟数组（每5分钟一个选项）
		generateMinutes() {
			const minutes = [];
			for (let i = 0; i <= 59; i += 5) {
				minutes.push(i);
			}
			return minutes;
		},
		
		// 初始化picker位置
		initializePicker() {
			const yearIndex = this.years.findIndex(year => year === this.selectedYear);
			const monthIndex = this.selectedMonth - 1;
			const dayIndex = this.selectedDay - 1;
			const hourIndex = this.hours.findIndex(hour => hour === this.selectedHour);
			const minuteIndex = this.minutes.findIndex(minute => minute === this.selectedMinute);

			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0,
				hourIndex >= 0 ? hourIndex : 0,
				minuteIndex >= 0 ? minuteIndex : 0
			];

			console.log('初始化picker:', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				hour: this.selectedHour,
				minute: this.selectedMinute,
				pickerValue: this.pickerValue
			});
		},
		
		// picker值变化处理
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value;

			// 更新选中值
			this.selectedYear = this.years[yearIndex] || this.selectedYear;
			// 修复月份计算：monthIndex是从0开始的索引，实际月份应该是monthIndex + 1
			this.selectedMonth = monthIndex + 1;
			this.selectedDay = this.days[dayIndex] || this.selectedDay;
			this.selectedHour = this.hours[hourIndex] || this.selectedHour;
			this.selectedMinute = this.minutes[minuteIndex] || this.selectedMinute;

			// 更新picker值
			this.pickerValue = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex];

			// 标记正在滚动
			this.isScrolling = true;

			console.log('picker变化:', {
				indexes: [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex],
				values: [this.selectedYear, this.selectedMonth, this.selectedDay, this.selectedHour, this.selectedMinute]
			});
		},
		
		// 滚动结束处理
		onPickerEnd(e) {
			console.log('滚动结束，开始吸附处理');
			
			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}
			
			// 延迟执行吸附，确保滚动完全停止
			this.scrollTimer = setTimeout(() => {
				this.isScrolling = false;
				this.performSnap();
			}, 100);
		},
		
		// 执行智能吸附
		performSnap() {
			const currentValue = [...this.pickerValue];
			console.log('当前picker值:', currentValue);
			
			// 计算目标吸附位置
			const targetValue = currentValue.map((value, index) => {
				return this.calculateSnapPosition(value, index);
			});
			
			console.log('目标吸附位置:', targetValue);
			
			// 检查是否需要吸附
			const needSnap = currentValue.some((val, index) => 
				Math.abs(val - targetValue[index]) > 0.05
			);
			
			if (needSnap) {
				console.log('执行吸附动画');
				this.animateToTarget(targetValue);
			} else {
				console.log('位置已正确，无需吸附');
			}
		},
		
		// 计算吸附位置
		calculateSnapPosition(value, columnIndex) {
			// 获取当前列的最大索引
			let maxIndex;
			switch(columnIndex) {
				case 0: maxIndex = this.years.length - 1; break;
				case 1: maxIndex = this.months.length - 1; break;
				case 2: maxIndex = this.days.length - 1; break;
				default: maxIndex = 0;
			}
			
			// 计算整数部分和小数部分
			const intPart = Math.floor(value);
			const decPart = value - intPart;
			
			let targetIndex;
			
			// 优化的吸附算法，提供更好的居中效果
			if (decPart < 0.2) {
				// 小数部分很小，向下吸附
				targetIndex = intPart;
			} else if (decPart > 0.8) {
				// 小数部分很大，向上吸附
				targetIndex = intPart + 1;
			} else {
				// 中间区域，选择最近的整数，确保居中
				targetIndex = Math.round(value);
			}
			
			// 确保在有效范围内
			return Math.max(0, Math.min(targetIndex, maxIndex));
		},
		
		// 动画到目标位置
		animateToTarget(targetValue) {
			const startValue = [...this.pickerValue];
			const startTime = Date.now();
			const duration = 200; // 优化动画时长，更快更流畅
			
			const animate = () => {
				const elapsed = Date.now() - startTime;
				const progress = Math.min(elapsed / duration, 1);
				
				// 缓动函数：先快后慢
				const easeProgress = 1 - Math.pow(1 - progress, 3);
				
				// 计算当前位置
				const currentPos = startValue.map((start, index) => {
					const target = targetValue[index];
					return start + (target - start) * easeProgress;
				});
				
				this.pickerValue = currentPos;
				
				if (progress < 1) {
					requestAnimationFrame(animate);
				} else {
					// 动画结束，设置精确值
					this.pickerValue = targetValue;
					this.updateSelectedValues(targetValue);
				}
			};
			
			requestAnimationFrame(animate);
		},
		
		// 更新选中的年月日
		updateSelectedValues(pickerValue) {
			this.selectedYear = this.years[pickerValue[0]] || this.selectedYear;
			// 修复月份计算：pickerValue[1]是从0开始的索引，实际月份应该是索引 + 1
			this.selectedMonth = pickerValue[1] + 1;
			this.selectedDay = this.days[pickerValue[2]] || this.selectedDay;

			console.log('最终选中值:', {
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay
			});
		},
		
		// 调整日期（处理无效日期）
		adjustDayIfNeeded() {
			const maxDay = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
			if (this.selectedDay > maxDay) {
				this.selectedDay = maxDay;
				this.initializePicker(); // 重新初始化picker位置
			}
		},
		
		// 处理鼠标滚轮事件
		onWheel(e) {
			try {
				e.preventDefault();
				e.stopPropagation();

				// 获取滚动方向
				const delta = e.deltaY > 0 ? 1 : -1;

				// 简化的列选择逻辑，避免使用getBoundingClientRect
				// 默认滚动年份列，可以通过键盘修饰键切换
				let columnIndex = 0; // 默认年份列

				// 使用键盘修饰键来选择不同的列
				if (e.shiftKey) {
					columnIndex = 1; // 月份列
				} else if (e.ctrlKey || e.metaKey) {
					columnIndex = 2; // 日期列
				}

				let newValue = [...this.pickerValue];

				switch(columnIndex) {
					case 0:
						// 滚动年份列
						const maxYearIndex = this.years.length - 1;
						newValue[0] = Math.max(0, Math.min(maxYearIndex, newValue[0] + delta));
						break;
					case 1:
						// 滚动月份列
						newValue[1] = Math.max(0, Math.min(11, newValue[1] + delta));
						break;
					case 2:
						// 滚动日期列
						const maxDayIndex = this.days.length - 1;
						newValue[2] = Math.max(0, Math.min(maxDayIndex, newValue[2] + delta));
						break;
				}

				// 更新picker值
				this.pickerValue = newValue;
				this.updateSelectedValues(newValue);

				// 延迟执行吸附
				clearTimeout(this.wheelTimer);
				this.wheelTimer = setTimeout(() => {
					this.performSnap();
				}, 150);
			} catch (error) {
				console.warn('滚轮事件处理出错:', error);
			}
		},

		// 关闭选择器
		close() {
			this.$emit('close');
		},

		// 确认选择
		confirm() {
			const year = String(this.selectedYear);
			const month = String(this.selectedMonth).padStart(2, '0');
			const day = String(this.selectedDay).padStart(2, '0');
			const hour = String(this.selectedHour).padStart(2, '0');
			const minute = String(this.selectedMinute).padStart(2, '0');

			const dateStr = `${year}-${month}-${day}`;
			const timeStr = `${hour}:${minute}`;
			const dateTimeStr = `${dateStr} ${timeStr}`;

			console.log('确认选择日期时间:', {
				date: dateStr,
				time: timeStr,
				datetime: dateTimeStr
			});

			this.$emit('confirm', {
				date: dateStr,
				time: timeStr,
				datetime: dateTimeStr,
				year: this.selectedYear,
				month: this.selectedMonth,
				day: this.selectedDay,
				hour: this.selectedHour,
				minute: this.selectedMinute
			});
		}
	}
}
</script>

<style scoped>
/* 模态框容器 */
.date-picker-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: flex-end;
}

/* 遮罩层 */
.picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
}

/* 选择器内容 */
.picker-content {
	position: relative;
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	width: 100%;
	max-width: 400rpx;
	margin: 0 auto;
	padding: 0;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 头部 */
.picker-header {
	text-align: center;
	padding: 24rpx 30rpx;
	background: #f8f9fa;
	border-bottom: 1rpx solid #e0e0e0;
}

.header-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* picker容器 */
.picker-container {
	position: relative;
	height: 400rpx;
	overflow: hidden;
}

/* picker-view样式 */
.picker-view {
	height: 100%;
	width: 100%;
}

/* picker项目样式 */
.picker-item {
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	font-size: 34rpx;
	font-weight: 500;
	color: #333;
	transition: all 0.2s ease;
}

/* 选择框指示器 */
.selection-indicator {
	position: absolute;
	top: 50%;
	left: 20rpx;
	right: 20rpx;
	height: 80rpx;
	transform: translateY(-50%);

	/* 背景渐变 */
	background: linear-gradient(135deg,
		rgba(220, 20, 60, 0.06) 0%,
		rgba(255, 182, 193, 0.1) 50%,
		rgba(220, 20, 60, 0.06) 100%
	);

	/* 边框 */
	border: 3rpx solid rgba(220, 20, 60, 0.5);
	border-radius: 16rpx;

	/* 阴影效果 */
	box-shadow:
		0 4rpx 16rpx rgba(220, 20, 60, 0.15),
		inset 0 2rpx 8rpx rgba(255, 255, 255, 0.4);

	/* 禁用点击事件 */
	pointer-events: none;
	z-index: 10;
}

/* 渐变遮罩效果 */
.picker-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(180deg,
		rgba(255, 255, 255, 0.9) 0%,
		rgba(255, 255, 255, 0.6) 20%,
		rgba(255, 255, 255, 0.2) 35%,
		transparent 45%,
		transparent 55%,
		rgba(255, 255, 255, 0.2) 65%,
		rgba(255, 255, 255, 0.6) 80%,
		rgba(255, 255, 255, 0.9) 100%
	);
	pointer-events: none;
	z-index: 5;
}

/* 底部按钮 */
.picker-footer {
	display: flex;
	justify-content: space-between;
	margin-top: 50rpx;
	gap: 30rpx;
}

.btn-cancel, .btn-confirm {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	text-align: center;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	transition: all 0.2s ease;
}

.btn-cancel {
	background: #f8f8f8;
	color: #666;
}

.btn-cancel:active {
	background: #e8e8e8;
}

.btn-confirm {
	background: linear-gradient(135deg, #dc143c 0%, #b91c3c 100%);
	color: white;
	box-shadow: 0 4rpx 16rpx rgba(220, 20, 60, 0.3);
}

.btn-confirm:active {
	background: linear-gradient(135deg, #b91c3c 0%, #991b3c 100%);
	transform: translateY(1rpx);
}

/* 响应式优化 */
@media (max-width: 750rpx) {
	.picker-content {
		padding: 30rpx;
	}

	.picker-item {
		font-size: 32rpx;
	}

	.selection-indicator {
		left: 15rpx;
		right: 15rpx;
	}
}

/* 选择预览区域 */
.selection-preview {
	padding: 20px;
	background: #f8f9fa;
	border-top: 1px solid #e9ecef;
	text-align: center;
}

.preview-label {
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
}

.preview-content {
	background: rgba(220, 20, 60, 0.1);
	border-radius: 8px;
	padding: 12px 16px;
	display: inline-block;
	min-width: 200px;
}

.preview-text {
	font-size: 16px;
	font-weight: 600;
	color: #dc143c;
}
</style>
