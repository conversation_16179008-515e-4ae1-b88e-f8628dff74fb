<template>
  <view 
    class="selection-menu" 
    v-if="visible && selectedText"
    :style="menuStyle"
    @click.stop
  >
    <view class="menu-arrow"></view>
    <view class="menu-content">
      <view class="selected-text-preview">
        "{{ selectedText.length > 20 ? selectedText.substring(0, 20) + '...' : selectedText }}"
      </view>
      <view class="menu-actions">
        <button class="menu-btn copy-btn" @click="copyText">
          <text class="btn-icon">📋</text>
          <text class="btn-text">复制</text>
        </button>
        <button class="menu-btn delete-btn" @click="deleteText">
          <text class="btn-icon">🗑️</text>
          <text class="btn-text">删除</text>
        </button>
        <button class="menu-btn replace-btn" @click="replaceText">
          <text class="btn-icon">✏️</text>
          <text class="btn-text">替换</text>
        </button>
        <button class="menu-btn enhance-btn" @click="enhanceText">
          <text class="btn-icon">✨</text>
          <text class="btn-text">润色</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TextSelectionMenu',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedText: {
      type: String,
      default: ''
    },
    selectionStart: {
      type: Number,
      default: 0
    },
    selectionEnd: {
      type: Number,
      default: 0
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    }
  },
  computed: {
    menuStyle() {
      return {
        left: this.position.x + 'px',
        top: (this.position.y - 80) + 'px'
      };
    }
  },
  methods: {
    // 复制文本
    copyText() {
      if (!this.selectedText) return;
      
      try {
        uni.setClipboardData({
          data: this.selectedText,
          success: () => {
            uni.showToast({
              title: '已复制到剪贴板',
              icon: 'none',
              duration: 1500
            });
            this.$emit('action-complete', 'copy');
          },
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('复制文本失败:', error);
      }
    },

    // 删除文本
    deleteText() {
      this.$emit('delete-text', {
        start: this.selectionStart,
        end: this.selectionEnd
      });
      this.$emit('action-complete', 'delete');
    },

    // 替换文本
    replaceText() {
      uni.showModal({
        title: '替换文本',
        content: '请输入新的文本内容',
        editable: true,
        placeholderText: this.selectedText,
        success: (res) => {
          if (res.confirm && res.content) {
            this.$emit('replace-text', {
              start: this.selectionStart,
              end: this.selectionEnd,
              newText: res.content
            });
            this.$emit('action-complete', 'replace');
          }
        }
      });
    },

    // 润色文本
    enhanceText() {
      this.$emit('enhance-text', {
        text: this.selectedText,
        start: this.selectionStart,
        end: this.selectionEnd
      });
      this.$emit('action-complete', 'enhance');
    }
  }
}
</script>

<style scoped>
.selection-menu {
  position: fixed;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-width: 280px;
  max-width: 320px;
}

.menu-arrow {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(0, 0, 0, 0.9);
}

.menu-content {
  padding: 12px;
}

.selected-text-preview {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-align: center;
  margin-bottom: 12px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  word-break: break-all;
}

.menu-actions {
  display: flex;
  justify-content: space-around;
  gap: 8px;
}

.menu-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.menu-btn:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.2);
}

.btn-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.btn-text {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.9);
}

.copy-btn:hover {
  border-color: rgba(76, 175, 80, 0.6);
  background: rgba(76, 175, 80, 0.1);
}

.delete-btn:hover {
  border-color: rgba(244, 67, 54, 0.6);
  background: rgba(244, 67, 54, 0.1);
}

.replace-btn:hover {
  border-color: rgba(255, 193, 7, 0.6);
  background: rgba(255, 193, 7, 0.1);
}

.enhance-btn:hover {
  border-color: rgba(156, 39, 176, 0.6);
  background: rgba(156, 39, 176, 0.1);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .selection-menu {
    min-width: 280px;
    max-width: 320px;
    padding: 12px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .menu-btn {
    padding: 12px 8px;
    min-width: 64px;
    height: 56px;
    border-radius: 12px;
  }

  .btn-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .btn-text {
    font-size: 12px;
    font-weight: 500;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .selection-menu {
    min-width: 300px;
    max-width: 340px;
    padding: 16px;
    border-radius: 20px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .menu-btn {
    padding: 14px 10px;
    min-width: 68px;
    height: 60px;
    border-radius: 14px;
    transition: all 0.2s ease;
  }

  .menu-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
  }

  .btn-icon {
    font-size: 22px;
    margin-bottom: 3px;
  }

  .btn-text {
    font-size: 13px;
    font-weight: 600;
  }
}
</style>
