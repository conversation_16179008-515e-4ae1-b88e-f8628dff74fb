/**
 * 文本处理功能工作流执行接口
 * 基于通用工作流基础类的文本处理功能实现
 * 创建时间：2025-01-11
 */

import { WorkflowBase, StructuredParamsBuilder } from '../common/workflow-base.js';
import { 文本处理工作流配置, 文本处理参数验证规则, 文本处理错误码, 文本处理状态 } from './工作流配置.js';

/**
 * 文本处理工作流执行类
 */
class Workflow extends WorkflowBase {
    constructor() {
        super('文本处理', 文本处理工作流配置);
        this.validationRules = 文本处理参数验证规则;
        this.errorCodes = 文本处理错误码;
        this.statusCodes = 文本处理状态;
    }

    /**
     * 执行文本处理工作流
     * @param {Object} formData - 表单数据
     * @param {Object} options - 执行选项
     */
    async execute(formData, options = {}) {
        try {
            // 1. 验证输入参数
            this.validateParams(formData);

            // 2. 构建结构化参数
            const structuredParams = this.buildParams(formData);

            // 3. 执行工作流
            const result = await this.executeWorkflow(structuredParams, {
                ...options,
                onProgress: (progress) => {
                    console.log(`文本处理进度: ${progress.status} - ${progress.message || ''}`);
                    if (options.onProgress) {
                        options.onProgress(progress);
                    }
                }
            });

            return {
                success: true,
                data: {
                    ...result.data,
                    module: '文本处理',
                    formData: formData,
                    executedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('文本处理工作流执行失败:', error);
            return this.formatError(error);
        }
    }

    /**
     * 验证文本处理参数
     * @param {Object} formData - 表单数据
     */
    validateParams(formData) {
        this.validateStructuredParams(formData, this.validationRules.required);
        return true;
    }

    /**
     * 构建文本处理结构化参数
     * @param {Object} formData - 表单数据
     */
    buildParams(formData) {
        const builder = new StructuredParamsBuilder();

        
        if (formData.textInput) {
            builder.addTextParam('textInput', formData.textInput);
        }
        if (formData.processType) {
            builder.addTextParam('processType', formData.processType);
        }
        if (formData.outputFormat) {
            builder.addTextParam('outputFormat', formData.outputFormat);
        }

        return builder.build();
    }
}

// 创建文本处理工作流实例
const Workflow = new Workflow();

// 导出接口方法
export async function 执行文本处理工作流(formData, options = {}) {
    return await Workflow.execute(formData, options);
}

export async function 查询文本处理状态(requestId) {
    return await Workflow.queryWorkflowStatus(requestId);
}

export async function 取消文本处理工作流(requestId) {
    return await Workflow.cancelWorkflow(requestId);
}

export default {
    执行文本处理工作流,
    查询文本处理状态,
    取消文本处理工作流
};