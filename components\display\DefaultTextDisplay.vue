<template>
  <view class="default-text-display">
    <view class="content-container">
      <rich-text :nodes="formattedContent" class="content-text"></rich-text>
    </view>
    <view class="action-bar">
      <view class="action-button copy-button" @click="copyContent">
        <text class="action-icon">📋</text>
        <text class="action-label">复制</text>
      </view>
      <view class="action-button save-button" @click="saveContent">
        <text class="action-icon">💾</text>
        <text class="action-label">保存</text>
      </view>
      <view class="action-button share-button" @click="shareContent">
        <text class="action-icon">📤</text>
        <text class="action-label">分享</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DefaultTextDisplay',
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  computed: {
    formattedContent() {
      // 将内容转换为rich-text可以显示的格式
      if (!this.content || !this.content.content) return '';
      
      return this.content.content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.+?)\*/g, '<em>$1</em>');
    }
  },
  methods: {
    copyContent() {
      // 复制内容到剪贴板
      uni.setClipboardData({
        data: this.content.content,
        success: () => {
          uni.showToast({
            title: '内容已复制到剪贴板',
            icon: 'none'
          });
        }
      });
      
      // 触发事件
      this.$emit('action', 'copy', this.content);
    },
    saveContent() {
      // 保存内容
      uni.showToast({
        title: '内容已保存',
        icon: 'success'
      });
      
      // 触发事件
      this.$emit('action', 'save', this.content);
    },
    shareContent() {
      // 分享内容
      uni.showShareMenu({
        withShareTicket: true,
        success: () => {
          uni.showToast({
            title: '请选择分享方式',
            icon: 'none'
          });
        }
      });
      
      // 触发事件
      this.$emit('action', 'share', this.content);
    }
  }
}
</script>

<style scoped>
.default-text-display {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.content-container {
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  border: 1px solid #eaeaea;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 0;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.action-button:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.action-label {
  font-size: 24rpx;
  font-weight: 500;
}

.copy-button {
  background-color: #e3f2fd;
  color: #1976d2;
}

.copy-button .action-label {
  color: #1976d2;
}

.save-button {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.save-button .action-label {
  color: #2e7d32;
}

.share-button {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.share-button .action-label {
  color: #7b1fa2;
}
</style> 