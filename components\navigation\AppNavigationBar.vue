<template>
  <view class="custom-navigation-bar" 
        style="position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; height: 60px !important; background-color: #FFFFFF !important; box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.06) !important; border-top: 1px solid rgba(0, 0, 0, 0.05) !important; z-index: 999999 !important; display: flex !important; align-items: center !important; visibility: visible !important; opacity: 1 !important; pointer-events: auto !important;"
        :style="{ paddingBottom: safeAreaInsetBottom + 'px' }">
    <!-- 导航项容器 -->
    <view class="navigation-container">
      <!-- 首页 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 0 }"
        @click="navigate(0, '/pages/index/index')">
        <text class="nav-text">首页</text>
        <view v-if="currentTab === 0" class="active-indicator"></view>
      </view>

      <!-- 热门 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 1 }"
        @click="navigate(1, '/pages/hot-tools/index')"
        @tap="navigate(1, '/pages/hot-tools/index')">
        <text class="nav-text hot">热门</text>
        <view v-if="currentTab === 1" class="active-indicator hot-indicator"></view>
      </view>

      <!-- 中间按钮 - 消耗件 -->
      <view class="nav-item center-button-container">
        <view class="center-button" @click="openCreateMenu">
          <text class="plus-icon">+</text>
        </view>
      </view>

      <!-- 邀请 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 3 }"
        @click="navigate(3, '/pages/commission/index')">
        <text class="nav-text">邀请</text>
        <view v-if="currentTab === 3" class="active-indicator"></view>
      </view>

      <!-- 我的 -->
      <view
        class="nav-item"
        :class="{ active: currentTab === 4 }"
        @click="navigate(4, '/pages/user/index')">
        <text class="nav-text">我的</text>
        <view v-if="currentTab === 4" class="active-indicator"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AppNavigationBar',
  data() {
    return {
      currentTab: 0,
      safeAreaInsetBottom: 0
    }
  },
  created() {
    // 监听页面路径变化更新当前激活的标签
    uni.$on('page-show', this.handlePageShow);
    console.log('AppNavigationBar: 已创建并监听page-show事件');
  },
  mounted() {
    // 获取底部安全区高度
    this.getSafeAreaInset();
    
    // 初始化当前页面对应的标签
    this.updateCurrentTab();
    console.log('AppNavigationBar: 已挂载并初始化当前标签');
    
    // 确保组件在下一帧完成渲染后强制更新当前标签
    setTimeout(() => {
      // 获取当前页面路由
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      if (currentPage) {
        const currentPath = '/' + currentPage.route;
        console.log('初始化当前页面路径:', currentPath);
        this.updateCurrentTab(currentPath);
      } else {
        console.log('无法获取当前页面路径');
      }
    }, 100);
  },
  beforeDestroy() {
    uni.$off('page-show', this.handlePageShow);
    console.log('AppNavigationBar: 已移除page-show事件监听');
  },
  methods: {
    // 获取底部安全区高度
    getSafeAreaInset() {
      // #ifdef APP-PLUS || H5
      try {
        const safeArea = uni.getSystemInfoSync().safeAreaInsets;
        if (safeArea && safeArea.bottom > 0) {
          this.safeAreaInsetBottom = safeArea.bottom;
        }
      } catch (e) {
        console.error('获取安全区域失败', e);
        this.safeAreaInsetBottom = 0;
      }
      // #endif
    },
    
    // 导航到指定页面
    navigate(index, url) {
      console.log('🚀 navigate方法被调用:', index, url);

      // 检查当前页面路径，而不仅仅是tab索引
      const currentPath = window.location.hash || '';
      const normalizedUrl = url.startsWith('/') ? url : '/' + url;

      console.log('导航检查 - 当前路径:', currentPath, '目标路径:', normalizedUrl);

      // 如果当前已经在目标页面，则不需要导航
      if (currentPath.includes(normalizedUrl.substring(1))) {
        console.log('已在目标页面，无需导航:', url);
        return;
      }
      
      console.log('AppNavigationBar: 导航到', index, url);
      this.currentTab = index;
      
      // 改进导航逻辑，确保可靠导航到tabBar页面
      try {
        // 对于tabBar页面，应该使用switchTab方法
        if (url === '/pages/index/index' ||
            url === '/pages/hot-tools/index' ||
            url === '/pages/commission/index' ||
            url === '/pages/user/index') {
          console.log('使用switchTab导航到tabBar页面:', url);
          uni.switchTab({
            url: url,
            success: () => {
              console.log('导航成功:', url);
            },
            fail: (err) => {
              console.error('switchTab失败:', err);
              this.fallbackNavigation(url);
            }
          });
        } else {
          // 非tabBar页面使用navigateTo
          uni.navigateTo({
            url: url,
            fail: (err) => {
              console.error('navigateTo失败:', err);
              this.fallbackNavigation(url);
            }
          });
        }
      } catch (e) {
        console.error('导航异常:', e);
        this.fallbackNavigation(url);
      }
    },
    
    // 导航失败时的备用方案
    fallbackNavigation(url) {
      console.log('尝试备用导航方案:', url);
      
      // 先尝试redirectTo
      uni.redirectTo({
        url: url,
        fail: (err) => {
          console.error('redirectTo失败:', err);
          
          // 最后尝试reLaunch，这是最可靠但开销最大的方法
          uni.reLaunch({
            url: url,
            fail: (err2) => {
              console.error('所有导航方法都失败:', err2);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    
    // 打开创建菜单
    openCreateMenu() {
      console.log('AppNavigationBar: 打开创建菜单');
      
      // 触发全局事件显示创建菜单
      uni.$emit('show-create-menu');
    },
    
    // 处理页面显示事件
    handlePageShow({path}) {
      if (!path) return;
      this.updateCurrentTab(path);
    },
    
    // 根据当前页面路径更新标签状态
    updateCurrentTab(path) {
      if (!path && typeof path !== 'string') {
        path = '';
      }
      
      const currentPath = path || (typeof window !== 'undefined' ? window.location.hash : '');
      console.log('当前路径:', currentPath);
      
      // 更精确的路径匹配
      if (currentPath.includes('/pages/index/index') || currentPath === '/' || currentPath === '' || currentPath === '#/') {
        this.currentTab = 0;
        console.log('当前标签: 首页');
      } else if (currentPath.includes('/pages/hot-tools/index')) {
        this.currentTab = 1;
        console.log('当前标签: 热门');
      } else if (currentPath.includes('/pages/commission/index')) {
        this.currentTab = 3;
        console.log('当前标签: 邀请');
      } else if (currentPath.includes('/pages/user/index')) {
        this.currentTab = 4;
        console.log('当前标签: 我的');
      }
    }
  }
}
</script>

<style scoped>
/* 注意：主要导航样式已移至navigation.css，这里只保留特定的组件内部样式 */

.navigation-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 100%;
  padding: 8px 4px;
  min-width: 0;
}

.nav-text {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  transition: all 0.3s;
  letter-spacing: 0.8px;
  text-align: center;
}

.nav-item.active .nav-text {
  color: #3498db;
  font-weight: 700;
  transform: scale(1.1);
}

.active-indicator {
  position: absolute;
  bottom: 8px;
  width: 20px;
  height: 3px;
  background-color: #3498db;
  border-radius: 3px;
}

.hot-indicator {
  background-color: #FF4500;
}

.nav-text.hot {
  color: #FF4500;
}

.center-button-container {
  position: relative;
  overflow: visible;
}

.center-button {
  position: absolute;
  bottom: 18px;
  left: 50%;
  transform: translateX(-50%);
  width: 58px;
  height: 58px;
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6px 15px rgba(255, 107, 53, 0.4);
  border: 4px solid #FFFFFF;
  z-index: 1000;
}

.plus-icon {
  color: #FFFFFF;
  font-size: 38px;
  font-weight: bold;
  line-height: 1;
  margin-top: -2px;
}

.center-button:active {
  transform: translateX(-50%) scale(0.94);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
</style> 