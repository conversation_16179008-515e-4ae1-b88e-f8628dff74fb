<template>
  <view class="smart-popup-container">
    <!-- 触发按钮 -->
    <view 
      :id="triggerId"
      class="trigger-btn" 
      @click="showPopup"
    >
      <slot name="trigger">
        <text class="default-trigger">⋮</text>
      </slot>
    </view>

    <!-- 智能弹窗遮罩 -->
    <view
      v-if="visible"
      class="popup-overlay"
      @click.stop="hidePopup"
      @touchstart.stop="hidePopup"
    >
      <!-- 指示线 - 连接触发按钮和弹窗 -->
      <view 
        v-if="showIndicator"
        class="indicator-line"
        :style="indicatorStyle"
      ></view>
      
      <!-- 智能弹窗 -->
      <view
        class="smart-popup"
        :style="popupStyle"
        @click.stop
        @touchstart.stop
      >
        <!-- 弹窗箭头指示器 -->
        <view 
          class="popup-arrow"
          :style="arrowStyle"
          :class="arrowClass"
        ></view>
        
        <!-- 弹窗内容 -->
        <view class="popup-content">
          <!-- 标题区域 - 显示是哪个卡片的操作 -->
          <view v-if="title" class="popup-title">
            {{ title }}
          </view>
          
          <!-- 菜单项 -->
          <view 
            v-for="(item, index) in menuItems" 
            :key="index"
            class="popup-item"
            :class="{ disabled: item.disabled }"
            @click="handleItemClick(item)"
          >
            <text v-if="item.icon" class="popup-icon">{{ item.icon }}</text>
            <text class="popup-text">{{ item.text }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SmartPopup',
  props: {
    // 菜单项
    menuItems: {
      type: Array,
      default: () => []
    },
    // 弹窗标题
    title: {
      type: String,
      default: ''
    },
    // 是否显示指示线
    showIndicator: {
      type: Boolean,
      default: true
    },
    // 弹窗最小边距
    margin: {
      type: Number,
      default: 20
    }
  },
  
  data() {
    return {
      visible: false,
      triggerId: `smart_trigger_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      triggerRect: null,
      popupStyle: {},
      arrowStyle: {},
      arrowClass: '',
      indicatorStyle: {},
      systemInfo: null
    };
  },
  
  methods: {
    // 显示弹窗
    async showPopup() {
      try {
        // 获取系统信息
        this.systemInfo = uni.getSystemInfoSync();
        
        // 获取触发按钮位置
        const rect = await this.getTriggerRect();
        if (!rect) {
          console.warn('无法获取触发按钮位置');
          return;
        }
        
        this.triggerRect = rect;
        this.visible = true;
        
        // 下一帧计算弹窗位置
        this.$nextTick(() => {
          this.calculateOptimalPosition();
        });
        
      } catch (error) {
        console.error('显示弹窗时出错:', error);
      }
    },
    
    // 隐藏弹窗
    hidePopup() {
      this.visible = false;
      this.popupStyle = {};
      this.arrowStyle = {};
      this.indicatorStyle = {};
    },
    
    // 获取触发按钮位置
    getTriggerRect() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this);
        query.select(`#${this.triggerId}`).boundingClientRect((rect) => {
          resolve(rect);
        }).exec();
      });
    },
    
    // 计算最佳弹窗位置
    calculateOptimalPosition() {
      if (!this.triggerRect || !this.systemInfo) return;
      
      const { left, top, width, height } = this.triggerRect;
      const { windowWidth, windowHeight } = this.systemInfo;
      const margin = this.margin;
      
      // 弹窗尺寸估算
      const popupWidth = 280; // 弹窗宽度
      const popupHeight = this.menuItems.length * 50 + (this.title ? 60 : 20); // 弹窗高度
      
      // 计算各个方向的可用空间
      const spaces = {
        top: top - margin,
        bottom: windowHeight - (top + height) - margin,
        left: left - margin,
        right: windowWidth - (left + width) - margin
      };
      
      // 智能选择最佳位置
      const position = this.selectBestPosition(spaces, popupWidth, popupHeight);
      
      // 计算弹窗坐标
      const coords = this.calculatePopupCoords(position, popupWidth, popupHeight);
      
      // 设置弹窗样式
      this.setPopupStyles(coords, position);
      
      // 设置箭头样式
      this.setArrowStyles(coords, position);
      
      // 设置指示线样式
      if (this.showIndicator) {
        this.setIndicatorStyles(coords, position);
      }
    },
    
    // 选择最佳位置
    selectBestPosition(spaces, popupWidth, popupHeight) {
      // 优先级：右侧 > 左侧 > 下方 > 上方
      if (spaces.right >= popupWidth) {
        return spaces.top >= popupHeight ? 'right-top' : 
               spaces.bottom >= popupHeight ? 'right-bottom' : 'right-center';
      }
      
      if (spaces.left >= popupWidth) {
        return spaces.top >= popupHeight ? 'left-top' : 
               spaces.bottom >= popupHeight ? 'left-bottom' : 'left-center';
      }
      
      if (spaces.bottom >= popupHeight) {
        return spaces.left >= popupWidth/2 && spaces.right >= popupWidth/2 ? 'bottom-center' :
               spaces.right >= popupWidth ? 'bottom-right' : 'bottom-left';
      }
      
      if (spaces.top >= popupHeight) {
        return spaces.left >= popupWidth/2 && spaces.right >= popupWidth/2 ? 'top-center' :
               spaces.right >= popupWidth ? 'top-right' : 'top-left';
      }
      
      // 如果都放不下，选择空间最大的方向
      const maxSpace = Math.max(spaces.top, spaces.bottom, spaces.left, spaces.right);
      if (maxSpace === spaces.right) return 'right-center';
      if (maxSpace === spaces.left) return 'left-center';
      if (maxSpace === spaces.bottom) return 'bottom-center';
      return 'top-center';
    },
    
    // 计算弹窗坐标
    calculatePopupCoords(position, popupWidth, popupHeight) {
      const { left, top, width, height } = this.triggerRect;
      const { windowWidth, windowHeight } = this.systemInfo;
      const margin = this.margin;
      
      let popupLeft, popupTop;
      
      switch (position) {
        case 'right-top':
          popupLeft = left + width + 10;
          popupTop = top;
          break;
        case 'right-center':
          popupLeft = left + width + 10;
          popupTop = top + height/2 - popupHeight/2;
          break;
        case 'right-bottom':
          popupLeft = left + width + 10;
          popupTop = top + height - popupHeight;
          break;
        case 'left-top':
          popupLeft = left - popupWidth - 10;
          popupTop = top;
          break;
        case 'left-center':
          popupLeft = left - popupWidth - 10;
          popupTop = top + height/2 - popupHeight/2;
          break;
        case 'left-bottom':
          popupLeft = left - popupWidth - 10;
          popupTop = top + height - popupHeight;
          break;
        case 'bottom-center':
          popupLeft = left + width/2 - popupWidth/2;
          popupTop = top + height + 10;
          break;
        case 'bottom-right':
          popupLeft = left + width - popupWidth;
          popupTop = top + height + 10;
          break;
        case 'bottom-left':
          popupLeft = left;
          popupTop = top + height + 10;
          break;
        case 'top-center':
          popupLeft = left + width/2 - popupWidth/2;
          popupTop = top - popupHeight - 10;
          break;
        case 'top-right':
          popupLeft = left + width - popupWidth;
          popupTop = top - popupHeight - 10;
          break;
        case 'top-left':
          popupLeft = left;
          popupTop = top - popupHeight - 10;
          break;
      }
      
      // 确保弹窗在屏幕范围内
      popupLeft = Math.max(margin, Math.min(popupLeft, windowWidth - popupWidth - margin));
      popupTop = Math.max(margin, Math.min(popupTop, windowHeight - popupHeight - margin));
      
      return { left: popupLeft, top: popupTop, width: popupWidth, height: popupHeight, position };
    },
    
    // 设置弹窗样式
    setPopupStyles(coords, position) {
      this.popupStyle = {
        left: `${coords.left}px`,
        top: `${coords.top}px`,
        width: `${coords.width}px`,
        minHeight: `${coords.height}px`,
        transform: 'scale(1)',
        opacity: '1'
      };
    },
    
    // 设置箭头样式
    setArrowStyles(coords, position) {
      const { left: triggerLeft, top: triggerTop, width: triggerWidth, height: triggerHeight } = this.triggerRect;
      const arrowSize = 8;
      
      if (position.startsWith('right')) {
        this.arrowClass = 'arrow-left';
        this.arrowStyle = {
          left: `-${arrowSize}px`,
          top: `${triggerTop + triggerHeight/2 - coords.top - arrowSize}px`
        };
      } else if (position.startsWith('left')) {
        this.arrowClass = 'arrow-right';
        this.arrowStyle = {
          right: `-${arrowSize}px`,
          top: `${triggerTop + triggerHeight/2 - coords.top - arrowSize}px`
        };
      } else if (position.startsWith('bottom')) {
        this.arrowClass = 'arrow-top';
        this.arrowStyle = {
          left: `${triggerLeft + triggerWidth/2 - coords.left - arrowSize}px`,
          top: `-${arrowSize}px`
        };
      } else if (position.startsWith('top')) {
        this.arrowClass = 'arrow-bottom';
        this.arrowStyle = {
          left: `${triggerLeft + triggerWidth/2 - coords.left - arrowSize}px`,
          bottom: `-${arrowSize}px`
        };
      }
    },
    
    // 设置指示线样式
    setIndicatorStyles(coords, position) {
      const { left: triggerLeft, top: triggerTop, width: triggerWidth, height: triggerHeight } = this.triggerRect;
      
      // 计算指示线的起点和终点
      const triggerCenterX = triggerLeft + triggerWidth / 2;
      const triggerCenterY = triggerTop + triggerHeight / 2;
      const popupCenterX = coords.left + coords.width / 2;
      const popupCenterY = coords.top + coords.height / 2;
      
      // 计算线条长度和角度
      const deltaX = popupCenterX - triggerCenterX;
      const deltaY = popupCenterY - triggerCenterY;
      const length = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
      
      this.indicatorStyle = {
        left: `${triggerCenterX}px`,
        top: `${triggerCenterY}px`,
        width: `${length}px`,
        transform: `rotate(${angle}deg)`,
        transformOrigin: '0 50%'
      };
    },
    
    // 处理菜单项点击
    handleItemClick(item) {
      if (item.disabled) return;
      
      this.$emit('item-click', item);
      this.hidePopup();
    }
  }
};
</script>

<style scoped>
.smart-popup-container {
  position: relative;
  display: inline-block;
}

.trigger-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  min-height: 40rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trigger-btn:active {
  transform: scale(0.95);
}

.default-trigger {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.2s ease;
}

.indicator-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, #007AFF, transparent);
  z-index: 100000;
  opacity: 0.6;
}

.smart-popup {
  position: absolute;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid #e5e5e5;
  overflow: hidden;
  z-index: 100001;
  transform: scale(0.8);
  opacity: 0;
  animation: popupShow 0.3s ease forwards;
}

.popup-arrow {
  position: absolute;
  width: 0;
  height: 0;
  z-index: 100002;
}

.arrow-left {
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #ffffff;
}

.arrow-right {
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid #ffffff;
}

.arrow-top {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #ffffff;
}

.arrow-bottom {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #ffffff;
}

.popup-content {
  padding: 16rpx 0;
}

.popup-title {
  padding: 24rpx 32rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.popup-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.popup-item:last-child {
  border-bottom: none;
}

.popup-item:active {
  background-color: #f8f8f8;
}

.popup-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.popup-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
  width: 32rpx;
  text-align: center;
}

.popup-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes popupShow {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
